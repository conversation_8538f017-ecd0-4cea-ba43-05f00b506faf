<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/css/toastr.min.css"
    rel="stylesheet">

<!-- Before your closing body tag -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
<script>
    toastr.options = {
        "closeButton": true,
        "debug": false,
        "newestOnTop": true,
        "progressBar": true,
        "positionClass": "toast-top-right",
        "preventDuplicates": false,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": "5000",
        "extendedTimeOut": "1000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut"
    };
</script>
<script>
    $(document).ready(function(e) {


        // Delete Invoice
        $(document).on('click', '.deleteInvoiceBtn', function(e) {
            $('#DeleteInvoiceModal').attr('data-id', $(this).attr('data-id'))
            $('#DeleteInvoiceModal').modal('show');
        })

        $(document).on('click', '#DeleteInvoiceModal .confirmDeleteBtn', function() {
            $('#DeleteInvoiceModal').modal('hide');
            let id = $('#DeleteInvoiceModal').attr('data-id');
            let url =
                '{{ route(getRouteAlias() . '.invoices.remove.invoice', ':id') }}';
            url = url.replace(':id', id);
            $.ajax({
                url: url,
                method: 'GET',

                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr(
                        'content')
                },
                success: function(response) {
                    console.log(response.invoice.invoice_id);
                    $("#invoices-table-body").find(
                            `[data-id="${response.invoice.id}"]`)
                        .remove();
                    toastr.success('Invoice deleted successfully');
                },
                error: function(xhr, status, error) {
                    let errorResponse = JSON.parse(xhr.responseText);
                    if (xhr.status == 303) {
                        toastr.error(errorResponse.message);
                        invoicesTable.draw();
                    }
                    // console.error(error);
                }
            });
        })


        // Collect invoice
        $(document).on('click', '.collectPaymentBtn', function() {
            let invoiceId = $(this).attr('data-id');
            let PayableAmount = $('.invoiceTotalPayable' + invoiceId).text().trim()
                .replace(/\$/g, '');
            $('.Payableprice').val(PayableAmount);
            $('#collectPaymentForm').attr('data-id', invoiceId);
            $('#collectPaymentForm').attr('data-pa', invoiceId);
            $('.invoice-balance').text(PayableAmount).prepend('$');
            let clientID = $(this).attr('data-clientId');
            let id = $(this).attr('data-Id');
            let url =
                '{{ route(getRouteAlias() . '.invoices.client.amount', ':id') }}';
            url = url.replace(':id', id) + '?clientId=' + clientID;

            $.ajax({
                url: url,
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr(
                        'content')
                },
                success: function(response) {
                    console.log('response', response);
                    $('.error-message').remove();
                    if (response.invoiceDetails.transaction != null) {
                        console.log(response.invoiceDetails
                            .transaction.additional_information);
                        $('#collectPaymentForm .add-btn').addClass(
                            'd-none');
                        $('#collectPaymentForm .account_balance')
                            .addClass('d-none');
                        $('#collectPaymentForm input[name="transaction_date"]')
                            .attr(
                                'readonly', true);
                        var desiredDate = response.invoiceDetails
                            .transaction
                            .transaction_date; // Replace with your desired date
                        $("#customDateAppend").val(desiredDate);
                        $("#collectPaymentForm .date1").removeClass(
                            'custom-appending')
                        $("#collectPaymentForm .basic-single-select")
                            .removeClass(
                                'basic-single-select')
                        $('#collectPaymentForm .payment_method').val(
                            response.invoiceDetails
                            .transaction.payment_method).attr(
                            'disabled', true);
                        $('#collectPaymentForm #reasonChange').text(
                            response.invoiceDetails
                            .transaction.notes).attr('readonly',
                            true);
                        $('#collectPaymentForm #select2--container')
                            .text(response
                                .invoiceDetails
                                .transaction.payment_method);
                        if (response.invoiceDetails.transaction
                            .payment_method == 'Check') {
                            $('.additional_information_label').text(
                                'Check number');
                        } else if (response.invoiceDetails.transaction
                            .payment_method ==
                            'Bank Transfer') {
                            $('.additional_information_label').text(
                                'Confirmation number');
                        }
                        if (response.invoiceDetails.transaction
                            .payment_method == 'Check' ||
                            response.invoiceDetails.transaction
                            .payment_method ==
                            'Bank Transfer') {
                            $('.additional_information').removeClass(
                                'd-none');
                            $('.additional_information_value').val(
                                response.invoiceDetails
                                .transaction.additional_information);
                            $('.additional_information_value').attr(
                                'readonly',
                                true);
                        } else {
                            $('.additional_information').addClass(
                                'd-none');
                        }
                    } else {
                        $('.error-message').remove();
                        $('.additional_information').addClass('d-none');
                        $('.additional_information_value').val('');
                        $('#collectPaymentForm .transaction_date').val(
                            '').attr('readonly',
                            false);
                        datepickerInstance.setStartDate(new Date());
                        $("#collectPaymentForm .date1").addClass(
                            'custom-appending')
                        $('#collectPaymentForm input[name="transaction_date"]')
                            .attr(
                                'readonly', false);
                        $('#collectPaymentForm .add-btn').removeClass(
                            'd-none');
                        $('#collectPaymentForm .payment_method').val(
                            'Cash').attr(
                            'disabled', false);
                        $("#customDateAppend").val('');
                        $('#collectPaymentForm #reasonChange').text('')
                            .attr('readonly',
                                false);
                        $('#collectPaymentForm .account_balance')
                            .removeClass('d-none');
                        $('.total-account-balance').html(response
                                .ClientAvailableAmount)
                            .prepend('$');
                        $('.additional_information_value').attr(
                            'readonly',
                            false);
                        $('#collectPaymentForm #select2--container')
                            .text('Cash');
                    }

                    $('#collectPaymentModal').modal('show');

                },
                error: function(xhr, status, error) {
                    console.error(error);
                }
            });
        })

        $(document).on('change', '.payment_method', function() {
            let value = $(this).val();
            if (value == 'Check') {
                $('.additional_information').removeClass('d-none');
                $('.additional_information_label').text('Check number');
            } else if (value == 'Bank Transfer') {
                $('.additional_information').removeClass('d-none');
                $('.additional_information_label').text('Confirmation number');
            } else {
                $('.additional_information').addClass('d-none');
            }
        })



        // Collect payment Form

        $('#collectPaymentForm').on('submit', function(e) {
            e.preventDefault();
            let id = $(this).attr('data-id');
            let url =
                '{{ route(getRouteAlias() . '.invoices.collect.payment', ':id') }}';
            url = url.replace(':id', id);
            let data = $('#collectPaymentForm').serialize();
            $('#collectPaymentModal').modal('hide');
            $.ajax({
                url: url,
                method: 'POST',
                data: data,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr(
                        'content')
                },
                success: function(response) {
                    $('#collectPaymentForm')[0].reset();
                    $(".status" + id).addClass('approved').text('Paid')
                        .removeClass(
                            'warning');
                    $('.paymentBtn' + id).remove();
                    $(".deleteInvoice" + id).remove();
                    $(".editInvoice" + id).remove();
                    toastr.success(response.message);
                },
                error: function(request, xhr, status, error) {
                    $('#collectPaymentModal').modal('show');

                    let errorResponse = JSON.parse(request
                        .responseText);
                    $.each(errorResponse.errors, function(field_name,
                        error) {
                        $(document)
                            .find("#" + 'collectPaymentForm' +
                                " [name=" +
                                field_name +
                                "]")
                            .after(
                                '<span class="error-message" style="color: red;">' +
                                error + '</span>');

                    });
                }
            });
        })


        $('.clients_list_for_invoices .client_search').on('keyup', function(e) {
            // Get the search input value
            var searchValue = $(this).val().toLowerCase();

            // Count the number of matching records
            var matchCount = 0;

            // Loop through each row in the table body and show/hide based on the search value
            $('.clients_list_for_invoices table tbody tr').each(function() {
                var nameColumnText = $(this).find(
                        'td:first-child .profile-name').text()
                    .toLowerCase();
                var emailColumnText = $(this).find('td:nth-child(2)').text()
                    .toLowerCase();

                // Check if the search value matches the name or email column
                if (nameColumnText.includes(searchValue) || emailColumnText
                    .includes(
                        searchValue)) {
                    $(this).show();
                    matchCount++;
                } else {
                    $(this).hide();
                }
            });

            // Show "No record found" message if there are no matching records
            if (matchCount === 0) {
                $('.no-record-found').show();
            } else {
                $('.no-record-found').hide();
            }
        });


        var status = '';
        invoicesTable = $('.yajra-datatable').DataTable({
            processing: true,
            responsive: true,
            serverSide: true,
            scrollX: true,
            ajax: {
                url: "{{ route(getRouteAlias() . '.properties.data') }}",
                data: function(d) {
                    d.status = status
                }
            },
            columns: [{
                    data: 'property_name',
                    name: 'property_name',
                    orderable: false

                },
                {
                    data: 'address1',
                    name: 'address1',
                    orderable: false


                },
                {
                    data: 'city',
                    name: 'city',
                    orderable: false

                },
                {
                    data: 'state',
                    name: 'state',
                    orderable: false

                },
                {
                    data: 'zip',
                    name: 'zip',
                    orderable: false

                },
                {
                    data: 'company_name',
                    name: 'company_name',
                    orderable: false

                },

                {
                    data: 'account_owner_name',
                    name: 'account_owner_name',
                    orderable: false

                },


                {
                    data: 'action',
                    name: 'action',
                    orderable: false

                }
            ],
            language: {
                zeroRecords: "Sorry we could not find any results",
                paginate: {
                    "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                    "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
                },
            },
            dom: '<"top">rt<"bottom"lip><"clear">',
            createdRow: function(row, data, dataIndex) {
                $(row).attr('data-id', data.id);
            }
        });

        // Custome Search
        $('.propertyListing .property_search').on('keyup', function() {
            // alert(this.value);
            invoicesTable.search(this.value).draw();
        });

        // Custom filter
        $('.propertyTable #property_filter').on('change', function() {
            var selectedValue = $(this).val();
            status = selectedValue;
            invoicesTable.columns().search('').draw();
        });




    })

    function editSectionProperty(id) {
        $.ajax({
            type: "GET",
            url: "{{ route(getRouteAlias() . '.property.edit') }}",
            data: {
                id: id
            },
            success: function(response) {
                console.info(response);
                $('#property_name').val(response.name);
                $('#address1').val(response.address1);
                $('#address2').val(response.address2);
                $('#account').val(response.company_id);
                $('#city').val(response.city);
                $('#state').val(response.state);
                $('#zip_code').val(response.zip);
                $('#property_id').val(response.id);
            }
        });
    }

    var fileInput = $(".upload_file_wrapper .input_file");
    var label = $(".upload_file_wrapper label");
    var selectedFile;

    // Add an event listener to the input field
    fileInput.on("change", function(event) {
        // Get the selected file
        selectedFile = event.target.files[0];

        // Check if the selected file is an Excel file
        if (
            selectedFile.type === "application/vnd.ms-excel" ||
            selectedFile.type ===
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ) {
            // Get the name of the selected file
            var fileName = selectedFile.name;

            // Update the label text with the file name
            label.text(fileName);

            // Submit the form
            $(this).parent(".file_upload_button_form").submit();
        } else {
            // Reset the input field and label text
            fileInput.val("");
            label.text("Import Material");

            // Create a new element with the error message
            var newElement = $(
                "<label class='error custom_error'>Only Excel File Is Allowed</label>"
            );

            // Append the new element after the input field
            $(this).after(newElement);

            // Fade out the error message after 3 seconds
            newElement.fadeOut(3000, function() {
                $(this)
                    .remove(); // Remove the element from the DOM after it has faded out
            });
        }
    });


    $(".file_upload_button_form").on("submit", function(e) {
        e.preventDefault();
        let myForm = document.getElementById("importproperty");
        let formData = new FormData(myForm);
        var url = "{{ URL::route(getRouteAlias() . '.property.file-import') }}"



        // Show loading toast
        toastr.info('Processing import, please wait...', 'Importing', {
            timeOut: 0, // Don't auto-close
            extendedTimeOut: 0,
            closeButton: true
        });

        $.ajax({
            method: "POST",
            url: url,
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function(response) {
                // Clear loading toast
                toastr.clear();

                if (response.error_count > 0) {
                    // Handle partial success with errors
                    let errorMessage =
                        `Import completed with ${response.error_count} error(s). ${response.imported_count} records were imported successfully.`;

                    // Show main warning message
                    toastr.warning(errorMessage, 'Partial Success', {
                        timeOut: 10000, // Longer timeout for important messages
                        closeButton: true
                    });

                    // Show detailed errors for each row
                    if (response.errors && response.errors.length > 0) {
                        response.errors.forEach(error => {
                            toastr.error(
                                `Row ${error.row}: ${error.errors.join(', ')}`,
                                'Import Error', {
                                    timeOut: 15000,
                                    closeButton: true
                                }
                            );
                        });
                    }
                } else {
                    // Complete success
                    toastr.success(
                        `File imported successfully! ${response.imported_count} records were processed.`,
                        'Success', {
                            timeOut: 5000,
                            closeButton: true
                        }
                    );
                    window.location.reload();
                }

                // Refresh your table if needed
                if (typeof invoicesTable !== 'undefined') {
                    invoicesTable.draw();
                }
            },
            error: function(xhr) {
                // Clear loading toast
                toastr.clear();

                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    // Handle validation errors from the import
                    const errorsByRow = {};

                    // Group errors by row
                    xhr.responseJSON.errors.forEach(error => {
                        if (!errorsByRow[error.row]) {
                            errorsByRow[error.row] = [];
                        }
                        errorsByRow[error.row].push(...error.errors);
                    });

                    // Show each row's errors in a toast
                    Object.entries(errorsByRow).forEach(([row, errors]) => {
                        toastr.error(
                            `Row ${row}: ${errors.join(', ')}`,
                            'Import Error', {
                                timeOut: 10000,
                                closeButton: true
                            }
                        );
                    });

                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    // Handle other API errors
                    toastr.error(
                        xhr.responseJSON.message,
                        'Import Error', {
                            timeOut: 8000,
                            closeButton: true
                        }
                    );
                } else {
                    // Handle unexpected errors
                    toastr.error(
                        'An unexpected error occurred during import.',
                        'Error', {
                            timeOut: 5000,
                            closeButton: true
                        }
                    );
                }
            },
            complete: function() {
                // Reset form after completion
                myForm.reset();
            }
        });
    });




    $(document).on('click', '#deleteProperty', function(e) {

        $('.dynamic-content-data').html('');
        $('.dynamic-content-data').append(
            `<h2 class="title text-center">Delete Property</h2>
                <p class="para mt-3 text-center">This property is permanently deleted from the system. Are you sure you want to delete this property?</p>`
        );
        deleteId = $(this).data("value");
    });
    $('.deleteConfirmation').on('click', function() {
        deleteData();
    })

    function deleteData() {
        var url = "{{ URL::route(getRouteAlias() . '.property.delete', ':id') }}",
            url = url.replace(':id', deleteId);

        $.ajax({
            method: "DELETE",
            url: url,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            success: function(response) {
                invoicesTable.draw();
                $('#DeleteModal').modal('hide');
                // $('#successModal').modal('show');
                $('.showMessage').css('display', 'block');

                // After 3 seconds (3000ms), fade it out
                setTimeout(function() {
                    $('.showMessage').fadeOut('slow', function() {
                        // Optional: Reset the display property to 'none' after fading out
                        $(this).css('display', 'none');
                    });
                }, 3000);
                $('.showMessage').html('');
                $('.showMessage').append(
                    `<p class="para mt-3">Property Deleted Successfully!</p>`
                );
                $('.dynamic-success-data').html('');
                $('.dynamic-success-data').append(
                    ` <h2 class="title text-center">Done</h2>
         <p class="para mt-3 text-center">Property Deleted Successfully!</p>`
                );
            },
            error: function(response) {
                $('#DeleteModal').modal('hide');
            }
        })
    }
</script>
