@extends('layouts.admin.master')
@section('title', 'Property Details')
@section('section')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@include('organization.property.style')
<style>
    a {
        text-decoration: none !important;
        font-size: 14px !important;
    }
    span {
        font-size: 14px;
        color: #192a3e;
    }
    input::placeholder {
        font-size: 14px;
    }
    input {
        font-size: 14px !important;
    }
    button {
        font-size: 14px !important;
    }
    select {
        font-size: 14px !important;
    }
    .modal-large {
        width: 80% !important;
        max-width: 100% !important;
    }
    .table thead th {
        vertical-align: bottom;
        border-bottom: none !important;
        border: none !important;
    }
    .btn-close:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .btn-close {
        cursor: pointer;
    }
    .hidemodelbtn {
        font-size: 18px !important;
        color: #7e8a9d !important;
    }
    .hidemodelbtn:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .hidemodelbtn:hover {
        cursor: pointer !important;
    }
    @media screen and (max-width: 580px) {
        .hidemodelbtn {
            font-size: 15px !important;
            color: #7e8a9d !important;
        }
    }
    .file-other-icon {
        font-size: 21px;
    }
    .file-preview-other {
        margin-top: 68px !important;
        margin-left: 10px !important;
    }
    button:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .file-preview-frame {
        cursor: default;
        display: flex;
        align-items: center;
        border: none;
        background-color: #e1f4ff !important;
        box-shadow: none;
        border-radius: 8px;
        width: 100%;
        padding: 15px;
        margin: 8px 0px;
    }
    .kv-file-remove.file-remove {
        border: none;
        background-color: #e09191 !important;
        color: #fff;
        width: 25px;
        height: 25px;
        font-size: 12px;
        border-radius: 4px;
        margin: 0px 4px;
    }
    .file-detail .file-caption-name {
        color: #0074d9;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 6px;
    }
    .loststatus {
        width: 48px !important;
        height: 20px !important;
        padding: 6px 15px 6px 15px;
        gap: 8px;
        border-radius: 8px;
        opacity: 0px;
        background: #fce4e5;
        font-family: Poppins;
        font-size: 12px;
        font-weight: 500;
        line-height: 22px;
        text-transform: capitalize;
        text-align: left;
        color: #ff4b55;
    }
    .proposedstatus {
        width: 48px !important;
        height: 20px !important;
        padding: 6px 15px 6px 15px;
        gap: 8px;
        border-radius: 8px;
        opacity: 0px;
        background: #fff5eb;
        font-family: Poppins;
        font-size: 12px;
        font-weight: 500;
        line-height: 22px;
        text-transform: capitalize;
        text-align: left;
        color: #efa037;
    }
    .draftstatus {
        width: 48px !important;
        height: 20px !important;
        padding: 6px 15px 6px 15px;
        gap: 8px;
        border-radius: 8px;
        opacity: 0px;
        background: #efda70ff;
        font-family: Poppins;
        font-size: 12px;
        font-weight: 500;
        line-height: 22px;
        text-align: left;
        text-transform: capitalize;
        color: #ffffffff;
    }
    .wonsttus {
        width: 48px !important;
        height: 20px !important;
        padding: 6px 15px 6px 15px;
        gap: 8px;
        border-radius: 8px;
        opacity: 0px;
        background: #ecffec;
        font-family: Poppins;
        font-size: 12px;
        font-weight: 500;
        line-height: 22px;
        text-transform: capitalize;
        text-align: left;
        color: #27b737;
    }
</style>
<section class="dashboard_main pb-5">
    <div class="row">
        <div class="col-lg-4 mt-4">
                <div class="panel">
                    <div class="row">
                            <div class="col-12">
                                <ul class="nav company-client_tabs_property" id="company-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link active" id="" data-toggle="pill" data-target="#company-created"
                                            type="button" role="tab" aria-controls="details-content" aria-selected="true" >
                                            <p class="detail-heading">Property Details</p>

                                    </div>
                                    </li>
                                    <li class="nav-item ml-auto" role="presentation">
                                        <div class="nav-link" id="" style="width: 29.3px;
height: 28px;
padding: 3px 8.3px 8px 8px;
gap: 0px;
border-radius: 4px;
border: 1px 0px 0px 0px;
opacity: 0px;
angle: 180 deg;
background: #F6F6F6;
border: 1px solid #DFDFDF; cursor: pointer;
"
                                            type="button" onclick="editSectionProperty({{$property->id}})" data-toggle="modal"
                                            data-target="#editPropertyModal" role="tab" aria-selected="false" >

<svg width="13" height="13" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.49998 15.6662L16 15.6662M0.999999 15.6662L2.39545 15.6662C2.8031 15.6662 3.00693 15.6662 3.19874 15.6202C3.3688 15.5793 3.53138 15.512 3.6805 15.4206C3.84869 15.3175 3.99282 15.1734 4.28107 14.8852L14.75 4.41621C15.4404 3.72585 15.4404 2.60656 14.75 1.91621C14.0597 1.22585 12.9404 1.22585 12.25 1.9162L1.78105 12.3852C1.4928 12.6734 1.34867 12.8175 1.2456 12.9857C1.15422 13.1348 1.08688 13.2974 1.04605 13.4675C0.999999 13.6593 0.999999 13.8631 0.999999 14.2708L0.999999 15.6662Z" stroke="#667085" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                                    </div>
                                    </li>

                                </ul>
                            </div>
                            <div class="col-12">
                                <div class="tab-content" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                        <div class="property-details-wrapper">
                                            <div class="row">

                                                <div class="col-12" style="margin-top: -9px;
    margin-left: 9px;">
                                                    <h3 style="font-size: 18px;">{{$property->property_name}}</h3>
                                                    <label style="display: flex; gap: 9px">

<svg width="17" height="22" style="margin-top: 5px;" viewBox="0 0 17 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.48438 10.3242C9.03438 10.3242 9.50521 10.1284 9.89688 9.73672C10.2885 9.34505 10.4844 8.87422 10.4844 8.32422C10.4844 7.77422 10.2885 7.30339 9.89688 6.91172C9.50521 6.52005 9.03438 6.32422 8.48438 6.32422C7.93438 6.32422 7.46354 6.52005 7.07188 6.91172C6.68021 7.30339 6.48438 7.77422 6.48438 8.32422C6.48438 8.87422 6.68021 9.34505 7.07188 9.73672C7.46354 10.1284 7.93438 10.3242 8.48438 10.3242ZM8.48438 17.6742C10.5177 15.8076 12.026 14.1117 13.0094 12.5867C13.9927 11.0617 14.4844 9.70755 14.4844 8.52422C14.4844 6.70755 13.9052 5.22005 12.7469 4.06172C11.5885 2.90339 10.1677 2.32422 8.48438 2.32422C6.80104 2.32422 5.38021 2.90339 4.22187 4.06172C3.06354 5.22005 2.48438 6.70755 2.48438 8.52422C2.48438 9.70755 2.97604 11.0617 3.95938 12.5867C4.94271 14.1117 6.45104 15.8076 8.48438 17.6742ZM8.48438 20.3242C5.80104 18.0409 3.79688 15.9201 2.47188 13.9617C1.14688 12.0034 0.484375 10.1909 0.484375 8.52422C0.484375 6.02422 1.28854 4.03255 2.89688 2.54922C4.50521 1.06589 6.36771 0.324219 8.48438 0.324219C10.601 0.324219 12.4635 1.06589 14.0719 2.54922C15.6802 4.03255 16.4844 6.02422 16.4844 8.52422C16.4844 10.1909 15.8219 12.0034 14.4969 13.9617C13.1719 15.9201 11.1677 18.0409 8.48438 20.3242Z" fill="#BCBCBC"/>
</svg>

                                                   <label for="" style="margin-top: 4px; font-size: 14px; color: #7C8091;">{{$property->address1}}</label>

                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row mt-4 justify-content-around">
                                                <div class="col-5 py-3 text-center" style="background: #F6F7FB; border-radius: 10px; height: 70px">

                                                    <p style="font-weight: 900; color: #152A4A; font-size: 14px; margin-bottom: -4px">
                                                    {{ substr($property->company_name, 0, 17) }}

                                                    </p>
                                                    <span style="color: #7C8091;">Account</span>
                                                </div>
                                                <div class="col-5 py-3 text-center" style="background: #F6F7FB; border-radius: 10px; height: 70px;">

                                                    <p style="font-weight: 900; color: #152A4A; font-size: 14px; margin-bottom: -4px">
                                                    {{ substr($property->account_owner_name, 0, 17) }}

                                                    </p>
                                                    <span style="color: #7C8091;">Account Owner</span>
                                                </div>
                                            </div>
<div class="row mt-4">
    <div class="col-12">

 <?php
//  dd($property);
if($property->property_health == 'red')
{
    $color = '#FF4B55';
}elseif ($property->property_health == 'green') {
    $color = '#46D255';
}elseif ($property->property_health == 'yellow') {
    $color = '#FDD032';
}
else{
    $color = '#D8CDA6FF';
}
 ?>
<div class="dropdown">
  <button class="btn form-control py-4 font-weight-bolder dropdown-toggle "
          style="background: {{$color}}; border-radius: 12px;"
          type="button"
          id="dropdownMenuButton"
          data-toggle="dropdown"
          aria-expanded="false">
    <b style="font-weight: 900 !important; font-size: 16px">Property Health</b>
  </button>


  <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton" style="border-radius: 12px;">
    <li>
      <a class="dropdown-item dropdown-togglesmain text-success" href="#" data-value="green">Green</a>
    </li>
    <li>
      <a class="dropdown-item dropdown-togglesmain text-warning" href="#" data-value="yellow">Yellow</a>
    </li>
    <li>
      <a class="dropdown-item dropdown-togglesmain text-danger" href="#" data-value="red">Red</a>
    </li>
  </ul>
</div>

    </div>
</div>
<div class=" mt-4 ">
    <label for="" class="mx-1" style="font-size: 15px; font-weight: 800;">Company Notes</label>
    <br>

    <textarea class="form-control" id="company_notes" style="
    height: 112px;
    top: 423px;
    left: 20.16px;
    gap: 0px;
    border-radius: 10px;
    opacity: 0px;
    border: 1px solid #DFDFDF;
    padding: 6px;
    font-size: 14px;
    ">
{{$property?->company_notes}}
    </textarea>
</div>

                                        </div>







                                    </div>



                                </div>
                            </div>

                        </div>

                </div>
                <div class="panel mt-5">
                    <p class="detail-heading">Customer Issues</p>
                    <div class="row mt-3">
                        <div class="col-md-3" style="margin: 0px !important; padding: 0px !important;">
                            <div class="static_numbers" style="display: flex; flex-direction: column; gap: 15px; justify-content: center; height: 100%;">
                                <div class="box" data-toggle="modal" data-target="#exampleModalOpen" style="cursor: pointer; background-color: #FFF2E2; padding: 10px; border-radius: 10px; text-align: center;">
                                    <h2 style="color: #FF9900; margin: 0; font-size: 25px;">
                                        {{count($openissues)}}
                                    </h2>
                                    <p style="color: #FF9900; margin: 5px 0 0; font-size: 10px;">Open Issue</p>
                                </div>
                                <div class="box" data-toggle="modal" data-target="#exampleModalPending" style="cursor: pointer; background-color: #FFF9D6; padding: 10px; border-radius: 10px; text-align: center;">
                                    <h2 style="color: #FFDD00; margin: 0; font-size: 25px;">
                                    {{count($pendingissues)}}

                                    </h2>
                                    <p style="color: #FFDD00; margin: 5px 0 0; font-size: 10px;">Pending</p>
                                </div>
                                <div class="box" data-toggle="modal" data-target="#exampleModalCompleted" style="cursor: pointer; background-color: #E9F8EB; padding: 10px; border-radius: 10px; text-align: center;">
                                    <h2 style="color: #2ECC40; margin: 0; font-size: 25px;">
                                    {{count($completedissues)}}

                                    </h2>
                                    <p style="color: #2ECC40; margin: 5px 0 0; font-size: 10px;">Completed</p>
                                </div>
                            </div>

                        </div>
                        <div class="col-md-9" style="">
                            <canvas id="customerIssuesChart" style="height: 100% !important; width: 100% !important;"></canvas>
                        </div>
                    </div>

                </div>
            </div>
         <div class="col-lg-4 mt-4">
                <div class="panel">
                    <p class="detail-heading">Operations (20)</p>
                    <div class="row" style="margin-top: -18px;">

                            <div class="col-12">
                                <ul class="nav company-client_tabs mt-5" id="company-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link active" id="details-tab-btn" data-toggle="pill" data-target="#company-created-jobs"
                                            type="button" role="tab" aria-controls="details-content" aria-selected="true" >
                                            Active
                                    </div>
                                    </li>

                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link" id="opportunity-team-tab-btn" data-toggle="pill" data-target="#opportunity-team-content-jobs"
                                            type="button" role="tab" aria-controls="opportunity-team-content" aria-selected="false" >
                                            Completed
                                    </div>
                                    </li>

                                </ul>
                            </div>
                            <div class="col-12">
                                <div class="tab-content mt-4" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created-jobs" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                        @foreach($operations as $item)
                                        <div class="row justify-content-between">
                                            <div class="col-12"><label style="color: #00000099">Job# </label>&nbsp;&nbsp;<b>{{$item->opportunityid->job_no}}</b></div>

  <div class="col-12"><b style="font-size: 14px;">{{$item->opportunityid->opportunity_name}}</b></div>

 <div class="col-6"><label for="" style="color: #00000099">Due Date: </label>&nbsp;&nbsp;<b>{{ \Carbon\Carbon::parse($item->opportunityid->bid_due_date)->format('d F Y') }}
 </b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">PM: &nbsp;<b>{{$item->manager?->first_name}} {{$item->manager?->last_name}}</b></label></div>
                                        </div>
                                        <hr>
                                        @endforeach


                                    </div>

                                    <div class="tab-pane fade" id="opportunity-team-content-jobs" role="tabpanel" aria-labelledby="opportunity-team-tab-btn" tabindex="0">


                                         @foreach($operationscomplete as $item)
                                        <div class="row justify-content-between">
                                            <div class="col-12"><label style="color: #00000099">Job# </label>&nbsp;&nbsp;<b>{{$item->opportunityid->job_no}}</b></div>

  <div class="col-12"><b style="font-size: 14px;">{{$item->opportunityid->opportunity_name}}</b></div>

 <div class="col-6"><label for="" style="color: #00000099">Due Date: </label>&nbsp;&nbsp;<b>{{ \Carbon\Carbon::parse($item->opportunityid->bid_due_date)->format('d F Y') }}
 </b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">PM: &nbsp;<b>{{$item->manager?->first_name}} {{$item->manager?->last_name}}</b></label></div>
                                        </div>
                                        <hr>
                                        @endforeach


                                    </div>

                                </div>
                            </div>

                        </div>

                </div>
                <div class="panel mt-5">
                    <div class="row">
                        <div class="col-6"><p class="detail-heading">Contacts</p></div>
                        <div class="col-6 text-right">
                            <a href="#" class="btn btn-large btn-primary px-4 py-2" data-toggle="modal" data-target="#exampleModal">Add Contacts</a>
                        </div>
                    </div>

                    <div class="row">


                            <div class="col-12">
                                <div class="tab-content mt-4" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created-jobs" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                        @foreach($contacts as $item)
                                        @if($item->property_default == 1)
                                        <div style="width: 47px;
                                        height: 22px;
                                        background: #E6F1FB;

                                        gap: 10px;
                                        border-radius: 11px;
                                        text-align: center;
                                        opacity: 0px;
                                        font-size: 9px;
                                        color: #0074D9;
                                        "><span style="color: #0074D9; font-size: 10px;">Default</span></div>
                                        @endif
                                        <div class="row justify-content-between">

                                            <div class="col-8"><b>{{$item->prefix}} {{$item->first_name}} {{$item->last_name}}</b> ( {{$item->role_name}} )<br>

                                            </div>
                                            <div class="col-4 text-right">
                                                <a id="deleteContact" style="margin-right: 10px" data-toggle="modal" data-target="#DeleteModal" data-value="{{encodeId($item->id)}}"
                                                    href="#">
                                                    <img src="{{ asset('admin_assets/images/delete.png') }}" style="width: 13.5px;
                                                    height: 15px;
                                                    top: 1.5px;
                                                    left: 2.25px;
                                                    gap: 0px;
                                                    border: 1px 0px 0px 0px;
                                                    opacity: 0px;
                                                    "/>
                                                </a>
                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#updateContactModal" onclick="editSectionContact({{ $item->id }})">
                                                    <img src="{{ asset('admin_assets/images/edit.png') }}" style="width: 13.5px;
                                                    height: 15px;
                                                    top: 1.5px;
                                                    left: 2.25px;
                                                    gap: 0px;
                                                    border: 1px 0px 0px 0px;
                                                    opacity: 0px;
                                                    "/>
                                                </a>

</div>

                                        </div>
                                        <div class="d-flex" style="gap: 10px;">
                                            <img style="width: 13.18px;
                                            height: 13.18px; margin-top: 5px !important" src="{{ asset('admin_assets/images/call.png') }}" alt=""> <label>{{$item->phone_number}}</label>
                                        <img style="width: 13.18px;
                                        height: 13.18px; margin-top: 5px !important" src="{{ asset('admin_assets/images/email.png') }}" alt=""> <label>{{$item->email}}</label>
                                        </div>
                                        <hr>
                                        @endforeach

                                    </div>
                                    <div class="tab-pane fade" id="services-content" role="tabpanel" aria-labelledby="services-content-tab-btn" tabindex="0">
                                        <h2 class="my-5">Division and Line Items </h3>
                                            <div class="container-fluid division-container">

                                                    <div class="row mb-5">
                                                        <div class="division-name mb-4">division -> name </div>
                                                        <hr class="horizontal-line">
                                                        <div class="col-lg-12">

                                                        </div>
                                                    </div>

                                            </div>
                                    </div>
                                    <div class="tab-pane fade" id="opportunity-team-content-jobs" role="tabpanel" aria-labelledby="opportunity-team-tab-btn" tabindex="0">
                                        <div style="display: flex; justify-content: space-between; align-items: center;" class="mx-2 my-3">
                                            <p> Team</p>
                                            <button class="add_task_button task-btn" data-toggle="modal"
                                            data-target="#taskModal" approve-btn>Add </button>
                                         </div>



                                    </div>
                                    <div class="tab-pane fade" id="activity-content" role="tabpanel" aria-labelledby="activity-tab-btn" tabindex="0">
                                        <p>Activity </p>
                                    </div>
                                </div>
                            </div>

                        </div>

                </div>
            </div>

       <div class="col-lg-4 mt-4">
                <div class="panel">
                    <p class="detail-heading">Estimate</p>
                    <div class="row">

                                                      <div class="col-12">
                                <div class="tab-content mt-2" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created-Estimates" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                        @foreach($estimates as $item)
                                        @php
                                        $totalPriceadd = DB::table('estimate_items')
            ->where('opportunity_id', $item->id)
            ->sum('total_price');
                                        @endphp
                                        <div class="row justify-content-between">
                                            <div class="col-6"><label style="color: #00000099">Opportunity# </label>&nbsp;&nbsp;<b>{{$item->id}}</b></div>
                                            <div class="col-6 text-right"><a href="#" class="@if($item->generateEstimate->status == "won") wonsttus @elseif($item->generateEstimate->status == "lost") loststatus @elseif($item->generateEstimate->status == "proposed") proposedstatus @elseif($item->generateEstimate->status == "draft") draftstatus @endif">{{$item->generateEstimate->status}}</a></div>
  <div class="col-12"><b style="font-size: 14px;">{{$item->opportunity_name}}</b></div>

 <div class="col-12 d-flex" style="justify-content: space-between;"><label style="color: #00000099; font-size: 11px;
">Bid Due: &nbsp;<b>{{ \Carbon\Carbon::parse($item->bid_due_date)->format('d F Y') }}</b></label><label style="color: #00000099;
">Total: <b>${{$totalPriceadd}}</b></label><label style="color: #00000099;
">Estimator: <b>{{$item->estimator?->first_name}} {{$item->estimator?->last_name}}</b></label></div>
                                        </div>
                                        <hr>
                                        @endforeach


                                    </div>
                                    <div class="tab-pane fade" id="services-content" role="tabpanel" aria-labelledby="services-content-tab-btn" tabindex="0">
                                        <h2 class="my-5">Division and Line Items </h3>
                                            <div class="container-fluid division-container">

                                                    <div class="row mb-5">
                                                        <div class="division-name mb-4">division -> name </div>
                                                        <hr class="horizontal-line">
                                                        <div class="col-lg-12">

                                                        </div>
                                                    </div>

                                            </div>
                                    </div>
                                    <div class="tab-pane fade" id="opportunity-team-content-Estimates" role="tabpanel" aria-labelledby="opportunity-team-tab-btn" tabindex="0">
                                        <div class="row justify-content-between">
                                            <div class="col-6"><b>Michael james</b></div>
                                            <div class="col-6 text-right"><a href="#" style="width: Fixed (96px)px;
                                                height: Fixed (30px)px;
                                                padding: 8px 33px 8px 33px;
                                                gap: 8px;
                                                border-radius: 8px;
                                                opacity: 0px;
                                                background: #FFF5E9;
                                                font-family: Poppins;
font-size: 14px;
font-weight: 500;
line-height: 22px;
text-align: left;
color: #EF8D03;


                                                ">Open</a></div>
  <div class="col-6"><b>Creator.io</b></div>
  <div class="col-6 text-right"></div>
 <div class="col-6"><b>Michael james Property</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Estimaor Name: <b>John Doe</b></label></div>
                                        </div>
                                        <hr>
                                        <div class="row justify-content-between">
                                            <div class="col-6"><b>Michael james</b></div>
                                            <div class="col-6 text-right"><a href="#" style="width: Fixed (96px)px;
                                                height: Fixed (30px)px;
                                                padding: 8px 33px 8px 33px;
                                                gap: 8px;
                                                border-radius: 8px;
                                                opacity: 0px;
                                                background: #FFF5E9;
                                                font-family: Poppins;
font-size: 14px;
font-weight: 500;
line-height: 22px;
text-align: left;
color: #EF8D03;


                                                ">Open</a></div>
  <div class="col-6"><b>Creator.io</b></div>
  <div class="col-6 text-right"></div>
 <div class="col-6"><b>Michael james Property</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Estimaor Name: <b>John Doe</b></label></div>
                                        </div>
                                        <hr>



                                    </div>
                                    <div class="tab-pane fade" id="activity-content" role="tabpanel" aria-labelledby="activity-tab-btn" tabindex="0">
                                        <p>Activity </p>
                                    </div>
                                </div>
                            </div>

                        </div>

                </div>
                <div class="panel mt-5">
                    <div class="row">
                        <div class="col-6"><p class="detail-heading">File Manager</p></div>
                        <div class="col-6 text-right">
                            <a href="#" style="border: 1px solid #E7E7E7 !important; border-radius: 5px;" class="btn btn-large mx-3" id="bulk-download-icon"><svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M14.25 10.25V11.15C14.25 12.4101 14.25 13.0402 14.0048 13.5215C13.789 13.9448 13.4448 14.289 13.0215 14.5048C12.5402 14.75 11.9101 14.75 10.65 14.75H4.35C3.08988 14.75 2.45982 14.75 1.97852 14.5048C1.55516 14.289 1.21095 13.9448 0.995235 13.5215C0.75 13.0402 0.75 12.4101 0.75 11.15V10.25M11.25 6.5L7.5 10.25M7.5 10.25L3.75 6.5M7.5 10.25V1.25" stroke="#7C8091" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                </a>
                            <a href="#" class="btn btn-large btn-primary" style="padding: 6px 19px;" data-toggle="modal" data-target="#uploadfilemodal">Upload</a>

                        </div>
                    </div>

                    <div class="row" style="padding: 0px 15px;">

                            <div class="col-12">

                            </div>
                            <div class="col-12">
                                <div class="tab-content mt-4" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created-file" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                       @foreach($documents as $item)
                                        <div class="row mt-4" style="height: 58px;
                                        padding: 5px 2px 6px 0px;
                                        overflow: hidden;
                                        gap: 0px;
                                        border-radius: 9px;
                                        border: 1px 0px 0px 0px;
                                        justify: space-between;
                                        opacity: 0px;
                                        border: 1px solid var(--layout-border, #E1E5EA)
                                        ">
                                        <div class="col-2 text-center" >
                                            <div style="
                                            width: 40px !important;
                                            height: 40px;
                                            align-items: center;
                                            border-radius: 6px;
                                            margin-top: 3px;
                                            margin-left: -3px;
                                            background: #FFEBC6;">

                                        @if(in_array($item->type, ['jpg', 'jpeg', 'png', 'gif', 'webp']))

                    <img src="{{ Storage::url($item->path) }}" style="width: 36px;
                                        height: 32px; margin-top: 4px;" alt="{{ $item->document_name }}" class="img-thumbnail" />
                @else

                    <a href="{{ Storage::url($item->path) }}" target="_blank">

                                        <svg width="20" height="22" style="margin-top: 10px;" viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M18.8335 11C18.8335 12.8409 17.3411 14.3333 15.5002 14.3333C14.8344 14.3333 14.0495 14.2167 13.4021 14.3901C12.827 14.5442 12.3777 14.9935 12.2236 15.5686C12.0502 16.216 12.1668 17.0009 12.1668 17.6667C12.1668 19.5076 10.6744 21 8.8335 21M6.3335 6H13.3335M6.3335 10H9.3335M1.8335 9C1.8335 5.22876 1.8335 3.34315 3.0783 2.17157C4.32309 1 6.32656 1 10.3335 1H11.1062C14.3674 1 15.998 1 17.1304 1.79784C17.4549 2.02643 17.7429 2.29752 17.9858 2.60289C18.8335 3.66867 18.8335 5.20336 18.8335 8.27273V10.8182C18.8335 13.7814 18.8335 15.2629 18.3646 16.4462C17.6107 18.3486 16.0164 19.8491 13.9951 20.5586C12.7379 21 11.1637 21 8.01532 21C6.21625 21 5.31672 21 4.59828 20.7478C3.44329 20.3424 2.53225 19.4849 2.10146 18.3979C1.8335 17.7217 1.8335 16.8751 1.8335 15.1818V9Z" stroke="#F68500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>


                                    </a>
                @endif
</div>
                                        </div>
                                        @php
                                        $filePath = $item->path; // Path from the database
                                        $fileSize = Storage::size($filePath); // Size in bytes
                                        $fileSizeKB = intval($fileSize / 1024); // Convert to KB and round
                                    @endphp
<div class="col-8" style="overflow: hidden;">
    <label style="margin-top: 0px; font-size: 12px;">{{$item->document_name}}</label><div for="" style="color: #7C8091; margin-top: -10px;">{{$fileSizeKB}}KB</div>
</div>
<div class="col-2 text-right " style="">
    <div class="text-center" style="width: 32px; margin-left: auto; margin-top: 6px;
    height: 32px;
    padding: 2px 7px 8px 7px;
    border-radius: 8px;
    background-color: #D9EAF9;
    ">
    <a href="{{ Storage::url($item->path) }}" class="download_icon" download target="_blank">
    <img src="{{ asset('admin_assets/images/download.png') }}" alt=""></a>
    </div>
</div>
                                        </div>
                                        @endforeach
                                    </div>



                                </div>
                            </div>

                        </div>

                </div>
            </div>
    </div>
</section>
<div
    class="modal fade"
    id="uploadfilemodal"
    tabindex="-1"
    aria-labelledby="exampleModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #e1f4ff">
                <p class="detail-heading px-4" style="color: #0074d9">
                    File Upload
                </p>
                <button
                    type="button"
                    class="btn-close hidemodelbtn px-4"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="
                        color: #7e8a9d;
                        background: transparent;
                        border: none;
                    "
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>
            <div class="modal-body px-4">
                <form
                    action="{{ URL::route(getRouteAlias() . '.property.file-upload', ['prop_id' => encodeId($property->id)]) }}"
                    method="POST"
                    enctype="multipart/form-data"
                >
                    @csrf
                    <div class="mb-3 px-4">
                        <input
                            type="text"
                            required
                            name="file_name"
                            placeholder="File name here"
                            style="background: white"
                            class="form-control inputform"
                            id=""
                        />
                    </div>
                    <section class="bg-diffrent">
                        <div class="container">
                            <div class="row justify-content-center">
                                <div class="col-12">
                                    <div class="file-upload-contain">
                                        <input
                                            id="multiplefileupload"
                                            accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.csv"
                                            name="files[]"
                                            multiple
                                            style="display: none"
                                            type="file"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <div class="container">
                        <button
                            type="submit"
                            class="btn btn-primary form-control"
                            style="height: 32px"
                        >
                            Upload
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- add contact modal -->
<div
    class="modal fade select-client"
    id="exampleModal"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: #e1f4ff">
                <h4
                    class="modal-title"
                    id="addItemModalLabel"
                    style="color: #0074d9 !important"
                >
                    Add Contact
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>
            <form
                id="addItemForm"
                action="{{URL::route(getRouteAlias() . '.contact.add')}}"
                method="post"
            >
                @csrf
                <div class="modal-body">
                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >First Name
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <button
                                        class="btn dropdown-toggle menumrmrs"
                                        style="
                                            border-bottom: 1px solid #ced4da;
                                            border-left: 1px solid #ced4da;
                                            border-top: 1px solid #ced4da;
                                            border-right: none;
                                            background-color: white;
                                        "
                                        type="button"
                                        id="dropdownMenuButton2"
                                        data-toggle="dropdown"
                                        aria-haspopup="true"
                                        aria-expanded="false"
                                    >
                                        Mr.
                                    </button>
                                    <div
                                        class="dropdown-menu"
                                        aria-labelledby="dropdownMenuButton2"
                                    >
                                        <a
                                            class="dropdown-item"
                                            href="#"
                                            onclick="selectTitle2('Mr.')"
                                            >Mr.</a
                                        >
                                        <a
                                            class="dropdown-item"
                                            selected
                                            href="#"
                                            onclick="selectTitle2('Mrs.')"
                                            >Mrs.</a
                                        >
                                        <a
                                            class="dropdown-item"
                                            href="#"
                                            onclick="selectTitle2('Ms.')"
                                            >Ms.</a
                                        >
                                        <a
                                            class="dropdown-item"
                                            href="#"
                                            onclick="selectTitle2('Dr.')"
                                            >Dr.</a
                                        >
                                    </div>
                                </div>

                                <input
                                    type="hidden"
                                    id="name_title2"
                                    name="name_title"
                                    value="Mr."
                                />

                                <input
                                    type="hidden"
                                    value="{{$property->id}}"
                                    name="propertyt_id"
                                />

                                <input
                                    type="text"
                                    id=""
                                    name="first_name"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Enter first name"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Last Name
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id=""
                                name="last_name"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Enter last name"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >Suffix
                                <label class="" for=""> (Optional)</label></span
                            >
                            <input
                                type="text"
                                name="suffix"
                                style="height: 30px !important"
                                class="form-control"
                                id=""
                                placeholder="Enter suffix (Optional)"
                            />
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Title
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id=""
                                name="title"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Enter title"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Email
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="email"
                                    name="email"
                                    id=""
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Email"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Second Email
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="email"
                                name="second_email"
                                id=""
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Email"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Phone number
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    name="phone"
                                    id=""
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Phone number"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Second phone
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                name="second_phone"
                                id=""
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Second phone"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Account
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <select
                                    name="account"
                                    style="height: 30px !important"
                                    class="form-control"
                                    required
                                    id=""
                                >
                                    <option value="" selected>
                                        Select Account
                                    </option>
                                    <option value="{{$property->company_id}}">
                                        {{$property->company_name}}
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Role
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >

                            <select
                                name="role"
                                style="height: 30px !important"
                                class="form-control"
                                required
                                id=""
                            >
                                <option value="" selected>Select Role</option>
                                @foreach($roles as $item)
                                <option value="{{$item->id}}">
                                    {{$item->name}}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 97%">
                            <span for=""
                                >Mailing Address
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    name="mailing_address"
                                    id=""
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Mailing Address"
                                />
                            </div>
                        </div>
                    </div>

                    <div
                        class="d-flex"
                        style="gap: 17px; justify-content: start"
                    >
                        <input
                            type="checkbox"
                            id=""
                            name="default"
                            class="form-control"
                        />
                        <label
                            for="make_default"
                            style="
                                color: #3b4159;
                                margin-top: -5px !important;
                                margin-left: -14px !important;
                            "
                            >Set as default contact information
                        </label>
                    </div>

                    <div class="d-flex" style="gap: 17px; justify-content: end">
                        <button
                            type="button"
                            data-dismiss="modal"
                            aria-label="Close"
                            onclick="addNewItemModel()"
                            class="btn px-5 py-2"
                            style="
                                color: #0074d9;
                                border: 1px solid #0074d9;
                                background-color: white;
                            "
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            onclick="addNewItemModel()"
                            class="btn btn-primary px-5 py-2"
                        >
                            Add
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div
    class="modal fade select-client"
    id="updateContactModal"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: #e1f4ff">
                <h4
                    class="modal-title"
                    id="addItemModalLabel"
                    style="color: #0074d9 !important"
                >
                    Edit Contact
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>
            <form
                action="{{URL::route(getRouteAlias() . '.contacts.update')}}"
                method="post"
            >
                @csrf
                <div class="modal-body">
                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >First Name
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <button
                                        class="btn dropdown-toggle menumrmrs"
                                        style="
                                            border-bottom: 1px solid #ced4da;
                                            border-left: 1px solid #ced4da;
                                            border-top: 1px solid #ced4da;
                                            border-right: none;
                                            background-color: white;
                                        "
                                        type="button"
                                        id="dropdownMenuButton2"
                                        data-toggle="dropdown"
                                        aria-haspopup="true"
                                        aria-expanded="false"
                                    >
                                        Mr.
                                    </button>
                                    <div
                                        class="dropdown-menu"
                                        aria-labelledby="dropdownMenuButton2"
                                    >
                                        <a
                                            class="dropdown-item"
                                            href="#"
                                            onclick="selectTitle2('Mr.')"
                                            >Mr.</a
                                        >
                                        <a
                                            class="dropdown-item"
                                            selected
                                            href="#"
                                            onclick="selectTitle2('Mrs.')"
                                            >Mrs.</a
                                        >
                                        <a
                                            class="dropdown-item"
                                            href="#"
                                            onclick="selectTitle2('Ms.')"
                                            >Ms.</a
                                        >
                                        <a
                                            class="dropdown-item"
                                            href="#"
                                            onclick="selectTitle2('Dr.')"
                                            >Dr.</a
                                        >
                                    </div>
                                </div>

                                <input
                                    type="hidden"
                                    id="name_title2"
                                    name="name_title"
                                    value="Mr."
                                />
                                <input
                                    type="hidden"
                                    value="{{$property->id}}"
                                    name="propertyt_id"
                                />

                                <input
                                    type="hidden"
                                    id="contactt_id"
                                    name="contactt_id"
                                />

                                <input
                                    type="text"
                                    id="first_name"
                                    name="first_name"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Enter first name"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Last Name
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id="last_name"
                                name="last_name"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Enter last name"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >Suffix
                                <label class="" for=""> (Optional)</label></span
                            >

                            <input
                                type="text"
                                name="suffix"
                                style="height: 30px !important"
                                class="form-control"
                                id="suffix"
                                placeholder="Enter suffix (Optional)"
                            />
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Title
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id="title"
                                name="title"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Enter title"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Email
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="email"
                                    name="email"
                                    id="email"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Email"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Second Email
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="email"
                                name="second_email"
                                id="second_email"
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Email"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Phone number
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    name="phone"
                                    id="phone"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Phone number"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Second phone
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                name="second_phone"
                                id="second_phone"
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Second phone"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Account
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <select
                                    name="account"
                                    style="height: 30px !important"
                                    class="form-control"
                                    required
                                    id="account"
                                >
                                    <option value="" selected>
                                        Select Account
                                    </option>
                                    <option value="{{$property->company_id}}">
                                        {{$property->company_name}}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Role
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >

                            <select
                                name="role"
                                style="height: 30px !important"
                                class="form-control"
                                required
                                id="role"
                            >
                                <option value="" selected>Select Role</option>
                                @foreach($roles as $item)
                                <option value="{{$item->id}}">
                                    {{$item->name}}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 97%">
                            <span for=""
                                >Mailing Address
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    name="mailing_address"
                                    id="mailing_address"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Mailing Address"
                                />
                            </div>
                        </div>
                    </div>

                    <div
                        class="d-flex"
                        style="gap: 17px; justify-content: start"
                    >
                        <input
                            type="checkbox"
                            id="make_default"
                            name="default"
                            class="form-control"
                        />
                        <label
                            for="make_default"
                            style="
                                color: #3b4159;
                                margin-top: -5px !important;
                                margin-left: -14px !important;
                            "
                            >Set as default contact information
                        </label>
                    </div>
                    <div class="d-flex" style="gap: 17px; justify-content: end">
                        <button
                            type="button"
                            data-dismiss="modal"
                            aria-label="Close"
                            onclick="addNewItemModel()"
                            class="btn px-5 py-2"
                            style="
                                color: #0074d9;
                                border: 1px solid #0074d9;
                                background-color: white;
                            "
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            onclick="addNewItemModel()"
                            class="btn btn-primary px-5 py-2"
                        >
                            Update
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- =Update Property Modal -->
<div
    class="modal fade select-client"
    id="editPropertyModal"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: #e1f4ff">
                <h4 class="modal-title px-3" id="addItemModalLabel">
                    <b style="color: #0074d9 !important"
                        >Property Information</b
                    >
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn px-3"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>
            <div class="modal-body" style="padding: 13px 20px !important">
                <form
                    id="updatecontactform"
                    action="{{URL::route(getRouteAlias() . '.property.update')}}"
                    method="POST"
                >
                    @csrf
                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >Property Name
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="hidden"
                                    id="property_id"
                                    required
                                    class="form-control"
                                    name="property_id"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Enter company name"
                                />
                                <input
                                    type="text"
                                    id="property_name"
                                    required
                                    class="form-control"
                                    name="property_name"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Enter company name"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Address 1
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id="address1"
                                name="address1"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Address 1"
                            />
                        </div>
                    </div>
                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Address 2
                                <label class="" for="">(Optional)</label></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    id="address2"
                                    name="address2"
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Address 2"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >Account
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >

                            <select
                                name="account"
                                style="height: 30px !important"
                                class="form-control"
                                required
                                id="account"
                            >
                                <option value="" selected>
                                    Select Account
                                </option>
                                @foreach($accounts as $item)
                                <option value="{{$item->id}}">
                                    {{$item->company_name}}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >City
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    id="city"
                                    name="city"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="City"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                State
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id="state"
                                name="state"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="State"
                            />
                        </div>
                    </div>

                    <div class="form-group" style="width: 50%">
                        <span for="">
                            Zip Code
                            <label class="required_field" for="">*</label></span
                        >
                        <input
                            type="text"
                            id="zip_code"
                            name="zip_code"
                            required
                            style="height: 30px !important; text-align: left"
                            class="form-control"
                            placeholder="Zip Code"
                        />
                    </div>

                    <div class="d-flex" style="gap: 17px; justify-content: end">
                        <button
                            type="button"
                            data-dismiss="modal"
                            aria-label="Close"
                            class="btn px-5 py-2"
                            style="
                                color: #0074d9;
                                border: 1px solid #0074d9;
                                background-color: white;
                            "
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled="true"
                            id="disableremove"
                            onclick="addNewItemModel()"
                            class="btn btn-primary px-5 py-2"
                        >
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- open issue modal -->
<div
    class="modal fade select-client"
    id="exampleModalOpen"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-large">
        <div class="modal-content">
            <div class="modal-header" style="">
                <h4
                    class="modal-title"
                    id="addItemModalLabel"
                    style="color: black !important"
                >
                    <b>Open Issue</b>
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="
                        border: none;
                        background-color: transparent;
                        font-size: 18px !important;
                    "
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="container-fluid">
                    <div class="row">
                        <div
                            class="table-responsive"
                            style="border-radius: 15px !important"
                        >
                            <table class="table table-stripped">
                                <thead style="background-color: #dcf2ff">
                                    <tr>
                                        <th>{{ __("ISSUE #") }}</th>
                                        <th>{{ __("Subject") }}</th>

                                        <th>{{ __("Category") }}</th>

                                        <th>{{ __("Created By") }}</th>
                                        <th>{{ __("Date & Time") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($openissues as $key=>$issue)
                                    <tr>
                                        <td>{{ $key + 1 }}</td>
                                        <td>
                                            {{ Str::limit($issue->subject, 30, '...') }}
                                        </td>

                                        <td>{{ $issue->category }}</td>
                                        <td>{{ $issue->creator_full_name }}</td>
                                        <td>{{ $issue->created_at }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- pending  issue modal -->
<div
    class="modal fade select-client"
    id="exampleModalPending"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-large">
        <div class="modal-content">
            <div class="modal-header" style="">
                <h4
                    class="modal-title"
                    id="addItemModalLabel"
                    style="color: black !important"
                >
                    <b>Pending Issue</b>
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="
                        border: none;
                        background-color: transparent;
                        font-size: 18px !important;
                    "
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="container-fluid">
                    <div class="row">
                        <div
                            class="table-responsive"
                            style="border-radius: 15px !important"
                        >
                            <table class="table table-stripped">
                                <thead style="background-color: #dcf2ff">
                                    <tr>
                                        <th>{{ __("ISSUE #") }}</th>
                                        <th>{{ __("Subject") }}</th>

                                        <th>{{ __("Category") }}</th>

                                        <th>{{ __("Created By") }}</th>
                                        <th>{{ __("Date & Time") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($pendingissues as $key=>$issue)
                                    <tr>
                                        <td>{{ $key + 1 }}</td>
                                        <td>
                                            {{ Str::limit($issue->subject, 30, '...') }}
                                        </td>

                                        <td>{{ $issue->category }}</td>
                                        <td>{{ $issue->creator_full_name }}</td>
                                        <td>{{ $issue->created_at }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- completed  issue modal -->
<div
    class="modal fade select-client"
    id="exampleModalCompleted"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-large">
        <div class="modal-content">
            <div class="modal-header" style="">
                <h4
                    class="modal-title"
                    id="addItemModalLabel"
                    style="color: black !important"
                >
                    <b>Completed Issue</b>
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="
                        border: none;
                        background-color: transparent;
                        font-size: 18px !important;
                    "
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="container-fluid">
                    <div class="row">
                        <div
                            class="table-responsive"
                            style="border-radius: 15px !important"
                        >
                            <table class="table table-stripped">
                                <thead style="background-color: #dcf2ff">
                                    <tr>
                                        <th>{{ __("ISSUE #") }}</th>
                                        <th>{{ __("Subject") }}</th>

                                        <th>{{ __("Category") }}</th>

                                        <th>{{ __("Created By") }}</th>
                                        <th>{{ __("Date & Time") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($completedissues as $key=>$issue)
                                    <tr>
                                        <td>{{ $key + 1 }}</td>
                                        <td>
                                            {{ Str::limit($issue->subject, 30, '...') }}
                                        </td>

                                        <td>{{ $issue->category }}</td>
                                        <td>{{ $issue->creator_full_name }}</td>
                                        <td>{{ $issue->created_at }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@include('layouts.admin.confirmation-modal')
@include('layouts.partials.success-modal')

@push('scripts')

@include('organization.property.detailsscript')
<script>
        $(document).ready(function () {
      // When a dropdown item is clicked
      $('.dropdown-togglesmain').click(function (e) {
        e.preventDefault(); // Prevent default anchor behavior

        var selectedValue = $(this).data('value'); // Get the selected value
        var button = $('#dropdownMenuButton');
        var id="{{$property->id}}";
        // button.html('<b style="font-weight: 900 !important; font-size: 16px">' + $(this).text() + '</b>'); // Update button text

        // Send the selected value to the server via AJAX
        $.ajax({
          url: '{{ route("organization.update.propertyHealth") }}',  // Your route to handle the update
          method: 'POST',
          data: {
            _token: '{{ csrf_token() }}',  // CSRF token for security
            property_health: selectedValue,  // The selected value from the dropdown
            id:id
          },
          success: function (response) {
            if (response.success) {
              alert('Property Health updated successfully!');
              location.reload();
            } else {
              alert('Error updating property health.');
            }
          },
          error: function (xhr, status, error) {
            alert('AJAX request failed: ' + error);
          }
        });
      });
    });

    $(document).ready(function () {
        var debounceTimeout;

        // When input is typed in the textarea
        $('#company_notes').on('input', function () {
            clearTimeout(debounceTimeout);
            debounceTimeout = setTimeout(function () {
                var notes = $('#company_notes').val();  // Get the value of the textarea

                // Send the data to the server via AJAX
                $.ajax({
                    url: "{{route('organization.save.company.notes')}}",  // Replace with the actual URL for your save endpoint
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}', // CSRF token
                        company_notes: notes,         // The value to be saved
                        property_id: {{ $property->id }} // Send the property ID if necessary
                    },
                    success: function (response) {
                        // console.log('Notes saved successfully');
                    },
                    error: function (error) {
                        console.error('Error saving notes');
                    }
                });
            }, 1000);  // 500ms delay for debouncing
        });
    });
</script>
@endpush
@endsection
