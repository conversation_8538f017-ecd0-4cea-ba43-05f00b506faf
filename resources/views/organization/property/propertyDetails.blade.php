@extends('layouts.admin.master')
@section('title', 'Property Details')
@section('section')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@include('organization.property.style')
<style>
    a {
        text-decoration: none !important;
        font-size: 14px !important;
    }
    span {
        font-size: 14px;
        color: #192a3e;
    }
    input::placeholder {
        font-size: 14px;
    }
    input {
        font-size: 14px !important;
    }
    button {
        font-size: 14px !important;
    }
    select {
        font-size: 14px !important;
    }
    .modal-large {
        width: 80% !important;
        max-width: 100% !important;
    }
    .table thead th {
        vertical-align: bottom;
        border-bottom: none !important;
        border: none !important;
    }
    .btn-close:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .btn-close {
        cursor: pointer;
    }
    .hidemodelbtn {
        font-size: 18px !important;
        color: #7e8a9d !important;
    }
    .hidemodelbtn:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .hidemodelbtn:hover {
        cursor: pointer !important;
    }
    @media screen and (max-width: 580px) {
        .hidemodelbtn {
            font-size: 15px !important;
            color: #7e8a9d !important;
        }
    }
    .file-other-icon {
        font-size: 21px;
    }
    .file-preview-other {
        margin-top: 68px !important;
        margin-left: 10px !important;
    }
    button:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .file-preview-frame {
        cursor: default;
        display: flex;
        align-items: center;
        border: none;
        background-color: #e1f4ff !important;
        box-shadow: none;
        border-radius: 8px;
        width: 100%;
        padding: 15px;
        margin: 8px 0px;
    }
    .kv-file-remove.file-remove {
        border: none;
        background-color: #e09191 !important;
        color: #fff;
        width: 25px;
        height: 25px;
        font-size: 12px;
        border-radius: 4px;
        margin: 0px 4px;
    }
    .file-detail .file-caption-name {
        color: #0074d9;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 6px;
    }
    .loststatus {
        width: 48px !important;
        height: 20px !important;
        padding: 6px 15px 6px 15px;
        gap: 8px;
        border-radius: 8px;
        opacity: 0px;
        background: #FEE2E2;
        font-family: Poppins;
        font-size: 12px;
        font-weight: 500;
        line-height: 22px;
        text-transform: capitalize;
        text-align: left;
        color: #F14736;
    }
    .proposedstatus {
        width: 48px !important;
        height: 20px !important;
        padding: 6px 15px 6px 15px;
        gap: 8px;
        border-radius: 8px;
        opacity: 0px;
        background: #D3F6ED;
        font-family: Poppins;
        font-size: 12px;
        font-weight: 500;
        line-height: 22px;
        text-transform: capitalize;
        text-align: left;
        color: #04C295;
    }
    .draftstatus {
        width: 48px !important;
        height: 20px !important;
        padding: 6px 15px 6px 15px;
        gap: 8px;
        border-radius: 8px;
        opacity: 0px;
        background: #efda70ff;
        font-family: Poppins;
        font-size: 12px;
        font-weight: 500;
        line-height: 22px;
        text-align: left;
        text-transform: capitalize;
        color: #ffffffff;
    }
    .wonsttus {
        width: 48px !important;
        height: 20px !important;
        padding: 6px 15px 6px 15px;
        gap: 8px;
        border-radius: 8px;
        opacity: 0px;
        background: #CFFBFF;
        font-family: Poppins;
        font-size: 12px;
        font-weight: 500;
        line-height: 22px;
        text-transform: capitalize;
        text-align: left;
        color: #1BB6D5;
    }
    .preview-file-choose{

gap: 0px;
border-radius: 8px;
border: 1px solid #E4E4E4;
opacity: 0px;
text-align: center;
display: none;
}
.dz-started .preview-file-choose{
    display: inline !important;

gap: 0px;
border-radius: 8px !important;
border: 1px solid #E4E4E4  !important;

text-align: center !important;
}
.dz-preview{
    /* display: none !important; */
    min-height: 110px;
    width: 81px;
    /* margin: 0px 0px 0px 12px !important; */

}
.dz-started .dz-preview
    {
        display: inline-block !important;
    }
    .dropzone_library_customize{
        /* display: flex; */
    }
    .dz-filename span{
        /* display: none; */
    }
    .showMessage {
    display: none;
    padding: 0px 10px 7px 10px;
    background: #c3e6cb;
    color: #155724;
    text-align: left;
}
.showMessage p{
    color: #155724;
    padding: 8px 0px 0px 0px;
}
</style>
<section class="dashboard_main pb-5">
<div class="showMessage"></div>
    <div class="row">
        <div class="col-lg-4 mt-4">
                <div class="panel">
                    <div class="row">
                            <div class="col-12">
                                <ul class="nav company-client_tabs_property" id="company-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link active" id="" data-toggle="pill" data-target="#company-created"
                                            type="button" role="tab" aria-controls="details-content" aria-selected="true" >
                                            <p class="detail-heading">Property Details</p>

                                    </div>
                                    </li>
                                    <li class="nav-item ml-auto" role="presentation">
                                        <div class="nav-link" id="" style="width: 29.3px;
height: 28px;
padding: 3px 8.3px 8px 8px;
gap: 0px;
border-radius: 4px;
border: 1px 0px 0px 0px;
opacity: 0px;
angle: 180 deg;
background: #F6F6F6;
border: 1px solid #DFDFDF; cursor: pointer;
"
                                            type="button" onclick="editSectionProperty({{$property->id}})" data-toggle="modal"
                                            data-target="#editPropertyModal" role="tab" aria-selected="false" >

<svg width="13" height="13" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.49998 15.6662L16 15.6662M0.999999 15.6662L2.39545 15.6662C2.8031 15.6662 3.00693 15.6662 3.19874 15.6202C3.3688 15.5793 3.53138 15.512 3.6805 15.4206C3.84869 15.3175 3.99282 15.1734 4.28107 14.8852L14.75 4.41621C15.4404 3.72585 15.4404 2.60656 14.75 1.91621C14.0597 1.22585 12.9404 1.22585 12.25 1.9162L1.78105 12.3852C1.4928 12.6734 1.34867 12.8175 1.2456 12.9857C1.15422 13.1348 1.08688 13.2974 1.04605 13.4675C0.999999 13.6593 0.999999 13.8631 0.999999 14.2708L0.999999 15.6662Z" stroke="#667085" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                                    </div>
                                    </li>

                                </ul>
                            </div>
                            <div class="col-12">
                                <div class="tab-content" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                        <div class="property-details-wrapper">
                                            <div class="row">

                                                <div class="col-12" style="margin-top: -9px;
    margin-left: 9px;">
                                                    <h3 style="font-size: 18px;">{{$property->property_name}}</h3>
                                                    <label style="display: flex; gap: 9px">

<svg width="17" height="22" style="margin-top: 5px;" viewBox="0 0 17 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.48438 10.3242C9.03438 10.3242 9.50521 10.1284 9.89688 9.73672C10.2885 9.34505 10.4844 8.87422 10.4844 8.32422C10.4844 7.77422 10.2885 7.30339 9.89688 6.91172C9.50521 6.52005 9.03438 6.32422 8.48438 6.32422C7.93438 6.32422 7.46354 6.52005 7.07188 6.91172C6.68021 7.30339 6.48438 7.77422 6.48438 8.32422C6.48438 8.87422 6.68021 9.34505 7.07188 9.73672C7.46354 10.1284 7.93438 10.3242 8.48438 10.3242ZM8.48438 17.6742C10.5177 15.8076 12.026 14.1117 13.0094 12.5867C13.9927 11.0617 14.4844 9.70755 14.4844 8.52422C14.4844 6.70755 13.9052 5.22005 12.7469 4.06172C11.5885 2.90339 10.1677 2.32422 8.48438 2.32422C6.80104 2.32422 5.38021 2.90339 4.22187 4.06172C3.06354 5.22005 2.48438 6.70755 2.48438 8.52422C2.48438 9.70755 2.97604 11.0617 3.95938 12.5867C4.94271 14.1117 6.45104 15.8076 8.48438 17.6742ZM8.48438 20.3242C5.80104 18.0409 3.79688 15.9201 2.47188 13.9617C1.14688 12.0034 0.484375 10.1909 0.484375 8.52422C0.484375 6.02422 1.28854 4.03255 2.89688 2.54922C4.50521 1.06589 6.36771 0.324219 8.48438 0.324219C10.601 0.324219 12.4635 1.06589 14.0719 2.54922C15.6802 4.03255 16.4844 6.02422 16.4844 8.52422C16.4844 10.1909 15.8219 12.0034 14.4969 13.9617C13.1719 15.9201 11.1677 18.0409 8.48438 20.3242Z" fill="#BCBCBC"/>
</svg>

                                                   <label for="" style="margin-top: 4px; font-size: 14px; color: #7C8091;">{{$property->address1}}  {{isset($property->address2) ? ', '.$property?->address2 : ''}}</label>

                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row mt-4 justify-content-around">
                                                <div class="col-5 py-3 text-center" style="background: #F6F7FB; border-radius: 10px; height: 70px">

                                                    <p style="font-weight: 900; color: #152A4A; font-size: 14px; margin-bottom: -4px">
                                                    {{ substr($property->company_name, 0, 17) }}

                                                    </p>
                                                    <span style="color: #7C8091;">Account</span>
                                                </div>
                                                <div class="col-5 py-3 text-center" style="background: #F6F7FB; border-radius: 10px; height: 70px;">

                                                    <p style="font-weight: 900; color: #152A4A; font-size: 14px; margin-bottom: -4px">
                                                    {{ substr($property->account_owner_name, 0, 17) }}

                                                    </p>
                                                    <span style="color: #7C8091;">Account Owner</span>
                                                </div>
                                            </div>
<div class="row mt-4">
    <div class="col-12">

 <?php
//  dd($property);
if($property->property_health == 'red')
{
    $color = '#FF4B55';
    $text = 'Property Health';
}elseif ($property->property_health == 'green') {
    $color = '#46D255';
    $text = 'Property Health';
}elseif ($property->property_health == 'yellow') {
    $color = '#FDD032';
    $text = 'Property Health';
}
else{
    $color = '#c4c4c4';
    $text = 'Choose Default Property Health';
}
 ?>
<div class="dropdown">
  <button class="btn form-control py-4 font-weight-bolder dropdown-toggle "
          style="background: {{$color}}; border-radius: 12px;"
          type="button"
          id="dropdownMenuButton"
          data-toggle="dropdown"
          aria-expanded="false">
    <b style="font-weight: 900 !important; font-size: 16px">{{$text}}</b>
  </button>


  <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton" style="border-radius: 12px;">
    <li>
      <a class="dropdown-item dropdown-togglesmain text-success" href="#" data-value="green">Green</a>
    </li>
    <li>
      <a class="dropdown-item dropdown-togglesmain text-warning" href="#" data-value="yellow">Yellow</a>
    </li>
    <li>
      <a class="dropdown-item dropdown-togglesmain text-danger" href="#" data-value="red">Red</a>
    </li>
  </ul>
</div>

    </div>
</div>
<div class=" mt-4 ">
    <label for="" class="mx-1" style="font-size: 15px; font-weight: 800;">Company Notes</label>
    <br>

    <textarea class="form-control" id="company_notes" style="
    height: 112px;
    top: 423px;
    left: 20.16px;
    gap: 0px;
    border-radius: 10px;
    opacity: 0px;
    border: 1px solid #DFDFDF;
    padding: 6px;
    font-size: 14px;
    ">{{$property?->company_notes}}</textarea>
</div>

                                        </div>







                                    </div>



                                </div>
                            </div>

                        </div>

                </div>
                <div class="panel mt-5">
                    <p class="detail-heading">Customer Issues</p>
                    <div class="row mt-3">
                        <div class="col-md-3" style="margin: 0px !important; padding: 0px !important;">
                            <div class="static_numbers" style="display: flex; flex-direction: column; gap: 15px; justify-content: center; height: 100%;">
                                <div class="box" data-toggle="modal" data-target="#exampleModalOpen" style="cursor: pointer; background-color: #E2E4EA; padding: 10px; border-radius: 10px; text-align: center;">
                                    <h2 style="color: #3b4159; margin: 0; font-size: 25px;">
                                        {{count($openissues)}}
                                    </h2>
                                    <p style="color: #3b4159; margin: 5px 0 0; font-size: 10px;">Open Issue</p>
                                </div>
                                <div class="box" data-toggle="modal" data-target="#exampleModalPending" style="cursor: pointer; background-color: #FFFAE8; padding: 10px; border-radius: 10px; text-align: center;">
                                    <h2 style="color: #F1B602; margin: 0; font-size: 25px;">
                                    {{count($pendingissues)}}

                                    </h2>
                                    <p style="color: #F1B602; margin: 5px 0 0; font-size: 10px;">Pending</p>
                                </div>
                                <div class="box" data-toggle="modal" data-target="#exampleModalCompleted" style="cursor: pointer; background-color: #CFFBFF; padding: 10px; border-radius: 10px; text-align: center;">
                                    <h2 style="color: #1BB6D5; margin: 0; font-size: 25px;">
                                    {{count($completedissues)}}

                                    </h2>
                                    <p style="color: #1BB6D5; margin: 5px 0 0; font-size: 10px;">Completed</p>
                                </div>
                            </div>

                        </div>
                        <div class="col-md-9" style="">
                            <canvas id="customerIssuesChart" style="height: 100% !important; width: 100% !important;"></canvas>
                        </div>
                    </div>

                </div>
            </div>
         <div class="col-lg-4 mt-4">
                <div class="panel">
                    <p class="detail-heading">Operations</p>
                    <div class="row" style="margin-top: -18px;">

                            <div class="col-12">
                                <ul class="nav company-client_tabs mt-5" id="company-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link active" id="details-tab-btn" data-toggle="pill" data-target="#company-created-jobs"
                                            type="button" role="tab" aria-controls="details-content" aria-selected="true" >
                                            Active
                                    </div>
                                    </li>

                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link" id="opportunity-team-tab-btn" data-toggle="pill" data-target="#opportunity-team-content-jobs"
                                            type="button" role="tab" aria-controls="opportunity-team-content" aria-selected="false" >
                                            Completed
                                    </div>
                                    </li>

                                </ul>
                            </div>
                            <div class="col-12">
                                <div class="tab-content mt-4" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created-jobs" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                        @foreach($operations as $item)
                                        <div class="row justify-content-between">
                                            <div class="col-12"><label style="color: #00000099">Job# </label>&nbsp;&nbsp;<b>{{$item->opportunityid->job_no}}</b></div>

  <div class="col-12"><b style="font-size: 14px;">{{$item->opportunityid->opportunity_name}}</b></div>

 <div class="col-6"><label for="" style="color: #00000099">Due Date: </label>&nbsp;&nbsp;<b>{{ \Carbon\Carbon::parse($item->opportunityid->bid_due_date)->format('d F Y') }}
 </b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">PM: &nbsp;<b>{{$item->manager?->first_name}} {{$item->manager?->last_name}}</b></label></div>
                                        </div>
                                        <hr>
                                        @endforeach


                                    </div>

                                    <div class="tab-pane fade" id="opportunity-team-content-jobs" role="tabpanel" aria-labelledby="opportunity-team-tab-btn" tabindex="0">


                                         @foreach($operationscomplete as $item)
                                        <div class="row justify-content-between">
                                            <div class="col-12"><label style="color: #00000099">Job# </label>&nbsp;&nbsp;<b>{{$item->opportunityid->job_no}}</b></div>

  <div class="col-12"><b style="font-size: 14px;">{{$item->opportunityid->opportunity_name}}</b></div>

 <div class="col-6"><label for="" style="color: #00000099">Due Date: </label>&nbsp;&nbsp;<b>{{ \Carbon\Carbon::parse($item->opportunityid->bid_due_date)->format('d F Y') }}
 </b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">PM: &nbsp;<b>{{$item->manager?->first_name}} {{$item->manager?->last_name}}</b></label></div>
                                        </div>
                                        <hr>
                                        @endforeach


                                    </div>

                                </div>
                            </div>

                        </div>

                </div>
                <div class="panel mt-5">
                    <div class="row">
                        <div class="col-6"><p class="detail-heading">Contacts</p></div>
                        <div class="col-6 text-right">
                            <a href="#" class="btn btn-large btn-primary px-4 py-2" data-toggle="modal" data-target="#exampleModal">Add Contacts</a>
                        </div>
                    </div>

                    <div class="row">


                            <div class="col-12">
                                <div class="tab-content mt-4" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created-jobs" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                        @foreach($contacts as $item)
                                        @if($item->property_default == 1)
                                        <div style="width: 47px;
                                        height: 22px;
                                        background: #E6F1FB;

                                        gap: 10px;
                                        border-radius: 11px;
                                        text-align: center;
                                        opacity: 0px;
                                        font-size: 9px;
                                        color: #0074D9;
                                        "><span style="color: #0074D9; font-size: 10px;">Default</span></div>
                                        @endif
                                        <div class="row justify-content-between">

                                            <div class="col-8"><b>{{$item->prefix}} {{$item->first_name}} {{$item->last_name}}</b> <br>

                                            </div>
                                            <div class="col-4 text-right">
                                                <a id="deleteContact" style="margin-right: 10px" data-toggle="modal" data-target="#DeleteModal" data-value="{{encodeId($item->id)}}"
                                                    href="#">
                                                    <img src="{{ asset('admin_assets/images/delete.png') }}" style="width: 13.5px;
                                                    height: 15px;
                                                    top: 1.5px;
                                                    left: 2.25px;
                                                    gap: 0px;
                                                    border: 1px 0px 0px 0px;
                                                    opacity: 0px;
                                                    "/>
                                                </a>
                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#updateContactModal" onclick="editSectionContact({{ $item->id }})">
                                                    <img src="{{ asset('admin_assets/images/edit.png') }}" style="width: 13.5px;
                                                    height: 15px;
                                                    top: 1.5px;
                                                    left: 2.25px;
                                                    gap: 0px;
                                                    border: 1px 0px 0px 0px;
                                                    opacity: 0px;
                                                    "/>
                                                </a>

</div>

                                        </div>
                                        <div class="d-flex" style="gap: 10px;">
                                            <img style="width: 13.18px;
                                            height: 13.18px; margin-top: 5px !important" src="{{ asset('admin_assets/images/call.png') }}" alt=""> <label>{{$item->phone_number}}</label>
                                        <img style="width: 13.18px;
                                        height: 13.18px; margin-top: 5px !important" src="{{ asset('admin_assets/images/email.png') }}" alt=""> <label>{{$item->email}}</label>
                                        </div>
                                        <hr>
                                        @endforeach

                                    </div>
                                    <div class="tab-pane fade" id="services-content" role="tabpanel" aria-labelledby="services-content-tab-btn" tabindex="0">
                                        <h2 class="my-5">Division and Line Items </h3>
                                            <div class="container-fluid division-container">

                                                    <div class="row mb-5">
                                                        <div class="division-name mb-4">division -> name </div>
                                                        <hr class="horizontal-line">
                                                        <div class="col-lg-12">

                                                        </div>
                                                    </div>

                                            </div>
                                    </div>
                                    <div class="tab-pane fade" id="opportunity-team-content-jobs" role="tabpanel" aria-labelledby="opportunity-team-tab-btn" tabindex="0">
                                        <div style="display: flex; justify-content: space-between; align-items: center;" class="mx-2 my-3">
                                            <p> Team</p>
                                            <button class="add_task_button task-btn" data-toggle="modal"
                                            data-target="#taskModal" approve-btn>Add </button>
                                         </div>



                                    </div>
                                    <div class="tab-pane fade" id="activity-content" role="tabpanel" aria-labelledby="activity-tab-btn" tabindex="0">
                                        <p>Activity </p>
                                    </div>
                                </div>
                            </div>

                        </div>

                </div>
            </div>

       <div class="col-lg-4 mt-4">
                <div class="panel">
                    <p class="detail-heading">Opportunities</p>
                    <div class="row">
                    <!-- <div class="col-12">
                                <ul class="nav company-client_tabs mt-5" id="company-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link active" id="details-tab-btn" data-toggle="pill" data-target="#company-created-Estimates"
                                            type="button" role="tab" aria-controls="details-content" aria-selected="true" >
                                            Active
                                    </div>
                                    </li>

                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link" id="opportunity-team-tab-btn" data-toggle="pill" data-target="#services-content2"
                                            type="button" role="tab" aria-controls="opportunity-team-content" aria-selected="false" >
                                            Completed
                                    </div>
                                    </li>

                                </ul>
                            </div> -->

                                                      <div class="col-12">
                                <div class="tab-content mt-2" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created-Estimates" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">

                                        @foreach($estimates as $item)


                                        @php
                                        $totalPriceadd = DB::table('estimate_items')
            ->where('opportunity_id', $item->id)
            ->sum('total_price');
                                        @endphp
                                        <div class="row justify-content-between">
                                            <div class="col-6"><label style="color: #00000099">Opportunity# </label>&nbsp;&nbsp;<b>{{$item->id}}</b></div>

                                            <div class="col-6 text-right"><button type="button" class="btn @if($item?->status == "1") status-open @elseif($item?->status == "2") status-estimating @elseif($item?->status == "3") status-proposed @elseif($item?->status == "4") status-revision @elseif($item?->status == "5" && $item->generateEstimate?->client_status == 'approve') status-win-closed   @elseif($item?->status == "5" && $item->generateEstimate?->client_status == 'lost') status-lost-closed @endif" style="padding: 7px 18px; border-radius: 5px;">@if($item?->status == "1") Open @elseif($item?->status == "2") Estimating @elseif($item?->status == "3") Proposed @elseif($item?->status == "4") Revision @elseif($item?->status == "5" && $item->generateEstimate?->client_status == 'approve') Closed Win @elseif($item?->status == "5" && $item->generateEstimate?->client_status == 'lost') Closed Lost @endif</button></div>
  <div class="col-12"><b style="font-size: 14px;">{{$item->opportunity_name}}</b></div>

 <div class="col-12 d-flex" style="justify-content: space-between;"><label style="color: #00000099; font-size: 11px;
">Bid Due: &nbsp;<b>{{ \Carbon\Carbon::parse($item->bid_due_date)->format('d F Y') }}</b></label><label style="color: #00000099;
">Total: <b>${{$totalPriceadd}}</b></label><label style="color: #00000099;
">Estimator: <b>{{$item->estimator?->first_name}} {{$item->estimator?->last_name}}</b></label></div>
                                        </div>
                                        <hr>
                                        @endforeach


                                    </div>
                                    <div class="tab-pane fade" id="services-content2" role="tabpanel" aria-labelledby="services-content-tab-btn" tabindex="0">
                                    @foreach($estimatescomplete as $item)
                                        @php
                                        $totalPriceadd = DB::table('estimate_items')
            ->where('opportunity_id', $item->id)
            ->sum('total_price');
                                        @endphp
                                        <div class="row justify-content-between">
                                            <div class="col-6"><label style="color: #00000099">Opportunity# </label>&nbsp;&nbsp;<b>{{$item->id}}</b></div>
                                            <div class="col-6 text-right"><a href="#" class="@if($item->generateEstimate->status == "won") wonsttus @elseif($item->generateEstimate->status == "lost") loststatus @elseif($item->generateEstimate->status == "proposed") proposedstatus @elseif($item->generateEstimate->status == "draft") draftstatus @endif">{{$item->generateEstimate->status}}</a></div>
  <div class="col-12"><b style="font-size: 14px;">{{$item->opportunity_name}}</b></div>

 <div class="col-12 d-flex" style="justify-content: space-between;"><label style="color: #00000099; font-size: 11px;
">Bid Due: &nbsp;<b>{{ \Carbon\Carbon::parse($item->bid_due_date)->format('d F Y') }}</b></label><label style="color: #00000099;
">Total: <b>${{$totalPriceadd}}</b></label><label style="color: #00000099;
">Estimator: <b>{{$item->estimator?->first_name}} {{$item->estimator?->last_name}}</b></label></div>
                                        </div>
                                        <hr>
                                        @endforeach


                                    </div>
                                    <div class="tab-pane fade" id="opportunity-team-content-Estimates" role="tabpanel" aria-labelledby="opportunity-team-tab-btn" tabindex="0">
                                        <div class="row justify-content-between">
                                            <div class="col-6"><b>Michael james</b></div>
                                            <div class="col-6 text-right"><a href="#" style="width: Fixed (96px)px;
                                                height: Fixed (30px)px;
                                                padding: 8px 33px 8px 33px;
                                                gap: 8px;
                                                border-radius: 8px;
                                                opacity: 0px;
                                                background: #FFF5E9;
                                                font-family: Poppins;
font-size: 14px;
font-weight: 500;
line-height: 22px;
text-align: left;
color: #EF8D03;


                                                ">Open</a></div>
  <div class="col-6"><b>Creator.io</b></div>
  <div class="col-6 text-right"></div>
 <div class="col-6"><b>Michael james Property</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Estimaor Name: <b>John Doe</b></label></div>
                                        </div>
                                        <hr>
                                        <div class="row justify-content-between">
                                            <div class="col-6"><b>Michael james</b></div>
                                            <div class="col-6 text-right"><a href="#" style="width: Fixed (96px)px;
                                                height: Fixed (30px)px;
                                                padding: 8px 33px 8px 33px;
                                                gap: 8px;
                                                border-radius: 8px;
                                                opacity: 0px;
                                                background: #FFF5E9;
                                                font-family: Poppins;
font-size: 14px;
font-weight: 500;
line-height: 22px;
text-align: left;
color: #EF8D03;


                                                ">Open</a></div>
  <div class="col-6"><b>Creator.io</b></div>
  <div class="col-6 text-right"></div>
 <div class="col-6"><b>Michael james Property</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Estimaor Name: <b>John Doe</b></label></div>
                                        </div>
                                        <hr>



                                    </div>
                                    <div class="tab-pane fade" id="activity-content" role="tabpanel" aria-labelledby="activity-tab-btn" tabindex="0">
                                        <p>Activity </p>
                                    </div>
                                </div>
                            </div>

                        </div>

                </div>
                <div class="panel mt-5">
                    <div class="row">
                        <div class="col-6"><p class="detail-heading">File Manager</p></div>
                        <div class="col-6 text-right">
                            <a href="#" style="border: 1px solid #E7E7E7 !important; border-radius: 5px;" class="btn btn-large mx-3" id="bulk-download-icon"><svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M14.25 10.25V11.15C14.25 12.4101 14.25 13.0402 14.0048 13.5215C13.789 13.9448 13.4448 14.289 13.0215 14.5048C12.5402 14.75 11.9101 14.75 10.65 14.75H4.35C3.08988 14.75 2.45982 14.75 1.97852 14.5048C1.55516 14.289 1.21095 13.9448 0.995235 13.5215C0.75 13.0402 0.75 12.4101 0.75 11.15V10.25M11.25 6.5L7.5 10.25M7.5 10.25L3.75 6.5M7.5 10.25V1.25" stroke="#7C8091" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                </a>
                            <a href="#" class="btn btn-large btn-primary" style="padding: 6px 19px;" data-toggle="modal" data-target="#uploadfilemodal">Upload</a>

                        </div>
                    </div>

                    <div class="row" style="padding: 0px 15px;">

                            <div class="col-12">

                            </div>
                            <div class="col-12">
                                <div class="tab-content mt-4" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created-file" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                       @foreach($documents as $item)
                                        <div class="row mt-4" style="height: 58px;
                                        padding: 5px 2px 6px 0px;
                                        overflow: hidden;
                                        gap: 0px;
                                        border-radius: 9px;
                                        border: 1px 0px 0px 0px;
                                        justify: space-between;
                                        opacity: 0px;
                                        border: 1px solid var(--layout-border, #E1E5EA)
                                        ">
                                        <div class="col-2 text-center" >
                                            <div style="
                                            width: 40px !important;
                                            height: 40px;
                                            align-items: center;
                                            border-radius: 6px;
                                            margin-top: 3px;
                                            margin-left: -3px;
                                            background: #FFEBC6;">

                                        @if(in_array($item->type, ['jpg', 'jpeg', 'png', 'gif', 'webp']))

                    <img src="{{ Storage::url($item->path) }}" style="width: 36px;
                                        height: 32px; margin-top: 4px;" alt="{{ $item->document_name }}" class="img-thumbnail" />
                @else

                    <a href="{{ Storage::url($item->path) }}" target="_blank">

                                        <svg width="20" height="20" style="margin-top: 10px;" viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M18.8335 11C18.8335 12.8409 17.3411 14.3333 15.5002 14.3333C14.8344 14.3333 14.0495 14.2167 13.4021 14.3901C12.827 14.5442 12.3777 14.9935 12.2236 15.5686C12.0502 16.216 12.1668 17.0009 12.1668 17.6667C12.1668 19.5076 10.6744 21 8.8335 21M6.3335 6H13.3335M6.3335 10H9.3335M1.8335 9C1.8335 5.22876 1.8335 3.34315 3.0783 2.17157C4.32309 1 6.32656 1 10.3335 1H11.1062C14.3674 1 15.998 1 17.1304 1.79784C17.4549 2.02643 17.7429 2.29752 17.9858 2.60289C18.8335 3.66867 18.8335 5.20336 18.8335 8.27273V10.8182C18.8335 13.7814 18.8335 15.2629 18.3646 16.4462C17.6107 18.3486 16.0164 19.8491 13.9951 20.5586C12.7379 21 11.1637 21 8.01532 21C6.21625 21 5.31672 21 4.59828 20.7478C3.44329 20.3424 2.53225 19.4849 2.10146 18.3979C1.8335 17.7217 1.8335 16.8751 1.8335 15.1818V9Z" stroke="#F68500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>


                                    </a>
                @endif
</div>
                                        </div>
                                        @php
                                        $filePath = $item->path; // Path from the database

                                        $fileSize = Storage::size($filePath); // Size in bytes
                                        $fileSizeKB = intval($fileSize / 1024); // Convert to KB and round
                                    @endphp
<div class="col-8" style="overflow: hidden;">
    <label style="margin-top: 0px; font-size: 12px;">{{ \Illuminate\Support\Str::limit($item->name, 40, '...') }}</label><div for="" style="color: #7C8091; margin-top: -10px;">{{$fileSizeKB}}KB</div>
</div>
<div class="col-2 text-right " style="">
    <div class="text-center" style="width: 32px; margin-left: auto; margin-top: 6px;
    height: 32px;
    padding: 2px 7px 8px 7px;
    border-radius: 8px;
    background-color: #D9EAF9;
    ">
    <a href="{{ Storage::url($item->path) }}" class="download_icon" download target="_blank">
    <img src="{{ asset('admin_assets/images/download.png') }}" alt=""></a>
    </div>
</div>
                                        </div>
                                        @endforeach

                                        @foreach($oppdocuments as $item)
                                        <div class="row mt-4" style="height: 58px;
                                        padding: 5px 2px 6px 0px;
                                        overflow: hidden;
                                        gap: 0px;
                                        border-radius: 9px;
                                        border: 1px 0px 0px 0px;
                                        justify: space-between;
                                        opacity: 0px;
                                        border: 1px solid var(--layout-border, #E1E5EA)
                                        ">
                                        <div class="col-2 text-center" >
                                            <div style="
                                            width: 40px !important;
                                            height: 40px;
                                            align-items: center;
                                            border-radius: 6px;
                                            margin-top: 3px;
                                            margin-left: -3px;
                                            background: #FFEBC6;">
                                            <?php
    $fileExtension = pathinfo($item->name, PATHINFO_EXTENSION);
    ?>

                                        @if(in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif', 'webp']))

                    <img src="{{ Storage::url($item->path) }}" style="width: 36px;
                                        height: 32px; margin-top: 4px;" alt="{{ $item->document_name }}" class="img-thumbnail" />
                @else

                    <a href="{{ Storage::url($item->path) }}" target="_blank">

                                        <svg width="20" height="22" style="margin-top: 10px;" viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M18.8335 11C18.8335 12.8409 17.3411 14.3333 15.5002 14.3333C14.8344 14.3333 14.0495 14.2167 13.4021 14.3901C12.827 14.5442 12.3777 14.9935 12.2236 15.5686C12.0502 16.216 12.1668 17.0009 12.1668 17.6667C12.1668 19.5076 10.6744 21 8.8335 21M6.3335 6H13.3335M6.3335 10H9.3335M1.8335 9C1.8335 5.22876 1.8335 3.34315 3.0783 2.17157C4.32309 1 6.32656 1 10.3335 1H11.1062C14.3674 1 15.998 1 17.1304 1.79784C17.4549 2.02643 17.7429 2.29752 17.9858 2.60289C18.8335 3.66867 18.8335 5.20336 18.8335 8.27273V10.8182C18.8335 13.7814 18.8335 15.2629 18.3646 16.4462C17.6107 18.3486 16.0164 19.8491 13.9951 20.5586C12.7379 21 11.1637 21 8.01532 21C6.21625 21 5.31672 21 4.59828 20.7478C3.44329 20.3424 2.53225 19.4849 2.10146 18.3979C1.8335 17.7217 1.8335 16.8751 1.8335 15.1818V9Z" stroke="#F68500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>


                                    </a>
                @endif
</div>
                                        </div>
                                        @php

                                        $filePath = $item->path; // Path from the database

                                        $fileSize = Storage::size($filePath); // Size in bytes
                                        $fileSizeKB = intval($fileSize / 1024); // Convert to KB and round
                                    @endphp
<div class="col-8" style="overflow: hidden;">
    <label style="margin-top: 0px; font-size: 12px;">{{ \Illuminate\Support\Str::limit($item->name, 40, '...') }}</label><div for="" style="color: #7C8091; margin-top: -10px;">{{$fileSizeKB}}KB</div>
</div>
<div class="col-2 text-right " style="">
    <div class="text-center" style="width: 32px; margin-left: auto; margin-top: 6px;
    height: 32px;
    padding: 2px 7px 8px 7px;
    border-radius: 8px;
    background-color: #D9EAF9;
    ">
    <a href="{{ Storage::url($item->path) }}" class="download_icon" download target="_blank">
    <img src="{{ asset('admin_assets/images/download.png') }}" alt=""></a>
    </div>
</div>
                                        </div>
                                        @endforeach



                                        @foreach($estimatedocuments as $item)
                                        <div class="row mt-4" style="height: 58px;
                                        padding: 5px 2px 6px 0px;
                                        overflow: hidden;
                                        gap: 0px;
                                        border-radius: 9px;
                                        border: 1px 0px 0px 0px;
                                        justify: space-between;
                                        opacity: 0px;
                                        border: 1px solid var(--layout-border, #E1E5EA)
                                        ">
                                        <div class="col-2 text-center" >
                                            <div style="
                                            width: 40px !important;
                                            height: 40px;
                                            align-items: center;
                                            border-radius: 6px;
                                            margin-top: 3px;
                                            margin-left: -3px;
                                            background: #FFEBC6;">
                                            <?php
    $fileExtension = pathinfo($item->pdf_path, PATHINFO_EXTENSION);
    ?>

                                        @if(in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif', 'webp']))

                    <img src="{{ Storage::url($item->pdf_path) }}" style="width: 36px;
                                        height: 32px; margin-top: 4px;" alt="{{ $item->status_name }}" class="img-thumbnail" />
                @else

                    <a href="{{ Storage::url($item->pdf_path) }}" target="_blank">

                                        <svg width="20" height="22" style="margin-top: 10px;" viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M18.8335 11C18.8335 12.8409 17.3411 14.3333 15.5002 14.3333C14.8344 14.3333 14.0495 14.2167 13.4021 14.3901C12.827 14.5442 12.3777 14.9935 12.2236 15.5686C12.0502 16.216 12.1668 17.0009 12.1668 17.6667C12.1668 19.5076 10.6744 21 8.8335 21M6.3335 6H13.3335M6.3335 10H9.3335M1.8335 9C1.8335 5.22876 1.8335 3.34315 3.0783 2.17157C4.32309 1 6.32656 1 10.3335 1H11.1062C14.3674 1 15.998 1 17.1304 1.79784C17.4549 2.02643 17.7429 2.29752 17.9858 2.60289C18.8335 3.66867 18.8335 5.20336 18.8335 8.27273V10.8182C18.8335 13.7814 18.8335 15.2629 18.3646 16.4462C17.6107 18.3486 16.0164 19.8491 13.9951 20.5586C12.7379 21 11.1637 21 8.01532 21C6.21625 21 5.31672 21 4.59828 20.7478C3.44329 20.3424 2.53225 19.4849 2.10146 18.3979C1.8335 17.7217 1.8335 16.8751 1.8335 15.1818V9Z" stroke="#F68500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>


                                    </a>
                @endif
</div>
                                        </div>
                                        @php
                                        $filePath = $item->pdf_path; // Path from the database

                                        $fileSize = Storage::size($filePath); // Size in bytes
                                        $fileSizeKB = intval($fileSize / 1024); // Convert to KB and round
                                    @endphp
<div class="col-8" style="overflow: hidden;">
    <label style="margin-top: 0px; font-size: 12px;">{{ \Illuminate\Support\Str::limit($item->status_name, 40, '...') }}</label><div for="" style="color: #7C8091; margin-top: -10px;">{{$fileSizeKB}}KB</div>
</div>
<div class="col-2 text-right " style="">
    <div class="text-center" style="width: 32px; margin-left: auto; margin-top: 6px;
    height: 32px;
    padding: 2px 7px 8px 7px;
    border-radius: 8px;
    background-color: #D9EAF9;
    ">
    <a href="{{ Storage::url($item->pdf_path) }}" class="download_icon" download target="_blank">
    <img src="{{ asset('admin_assets/images/download.png') }}" alt=""></a>
    </div>
</div>
                                        </div>
                                        @endforeach
                                    </div>



                                </div>
                            </div>

                        </div>

                </div>
            </div>
    </div>
</section>

<div
    class="modal fade"
    id="uploadfilemodal"
    tabindex="-1"
    aria-labelledby="exampleModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #e1f4ff">
                <p class="detail-heading px-4" style="color: #0074d9">
                    File Upload
                </p>
                <button
                    type="button"
                    class="btn-close hidemodelbtn px-4"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="
                        color: #7e8a9d;
                        background: transparent;
                        border: none;
                    "
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>
            <div class="modal-body px-4">
                <form
                    action="{{ URL::route(getRouteAlias() . '.property.file-upload', ['prop_id' => encodeId($property->id)]) }}"
                    method="POST"
                    class="file-upload-form-property"
                    enctype="multipart/form-data"
                >
                    @csrf
                    <div class="mb-3 px-4">
                        <input
                            type="text"
                            required
                            name="file_name"
                            placeholder="File name here"
                            style="background: white"
                            class="form-control inputform"
                            id=""
                        />
                    </div>
                    <section class="bg-diffrent">
                        <div class="container">
                            <div class="row justify-content-center">
                                <div class="col-12">
                                <div class="mt-2">
                    <div class="dropzone_library_customize dropzone">
                    <div class="dz-preview dz-file-preview dz-processing dz-success needsclick dz-complete" id="my-dropzone" style="width: 81px !important;
height: 110px !important; text-align: center; cursor: pointer;">
                        <svg width="20" height="20" style="margin-top: 33px;" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                    </div>
                    <div id="imageSizeError"></div>
                </div>
                                    <!-- <div class="file-upload-contain">
                                        <input
                                            id="multiplefileupload"
                                            accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.csv"
                                            name="files[]"
                                            multiple
                                            style="display: none"
                                            type="file"
                                        />
                                    </div> -->
                                </div>
                            </div>
                        </div>
                    </section>

                    <div class="container" style="display: flex
;
    justify-content: end;
    margin-top: 22px;">
                        <button
                            type="submit"
                            class="btn primaryblue"

                        >
                            Upload
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- add contact modal -->
<div
    class="modal fade select-client"
    id="exampleModal"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: #e1f4ff">
                <h4
                    class="modal-title"
                    id="addItemModalLabel"
                    style="color: #0074d9 !important"
                >
                    Add Contact
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>
            <form
                id="addItemForm"

                method="post"
            >
                @csrf
                <div class="modal-body">
                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >First Name
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">

                            <select
        id="name_title"
        name="name_title"
        class="form-select"
        style="
            border-bottom: 1px solid #ced4da;
            border-left: 1px solid #ced4da;
            border-top: 1px solid #ced4da;
            border-right: none;
            background-color: white;
            width: 45px; /* Adjust width if needed */
        "
    >
        <option value="Mr." selected>Mr.</option>
        <option value="Mrs.">Mrs.</option>
        <option value="Ms.">Ms.</option>
        <option value="Dr.">Dr.</option>
    </select>


                                <input
                                    type="hidden"
                                    value="{{$property->id}}"
                                    name="propertyt_id"
                                />

                                <input
                                    type="text"
                                    id=""
                                    name="first_name"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Enter first name"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Last Name
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id=""
                                name="last_name"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Enter last name"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >Suffix
                                <label class="" for=""> (Optional)</label></span
                            >
                            <input
                                type="text"
                                name="suffix"
                                style="height: 30px !important"
                                class="form-control"
                                id=""
                                placeholder="Enter suffix (Optional)"
                            />
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Title
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id=""
                                name="title"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Enter title"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Email
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="email"
                                    name="email"
                                    id=""
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Email"
                                />
                            </div>
                            <small class="text-danger" id="email_error"></small>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Second Email
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="email"
                                name="second_email"
                                id=""
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Email"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Phone number
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    name="phone"
                                    oninput="maskPhoneNumber(event)"
                                    maxlength="12"
                                    minlength="12"
                                    id=""
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Phone number"
                                />
                            </div>
                            <small class="text-danger" id="phone_error"></small>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Second phone
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                name="second_phone"
                                oninput="maskPhoneNumber(event)"
                                    maxlength="12"
                                    minlength="12"
                                id=""
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Second phone"
                            />
                            <small class="text-danger" id="second_phone_error"></small>
                        </div>

                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 96%">
                            <span for=""
                                >Account
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <select
                                    name="account"
                                    style="height: 30px !important"
                                    class="form-control"
                                    required
                                    id=""
                                >
                                    <option value="" selected>
                                        Select Account
                                    </option>
                                    <option value="{{$property->company_id}}">
                                        {{$property->company_name}}
                                    </option>
                                </select>
                            </div>
                        </div>


                    </div>



                    <div
                        class="d-flex"
                        style="gap: 17px; justify-content: start"
                    >
                        <input
                            type="checkbox"
                            id=""
                            name="default"
                            class="form-control"
                        />
                        <label
                            for="make_default"
                            style="
                                color: #3b4159;
                                margin-top: -5px !important;
                                margin-left: -14px !important;
                            "
                            >Set as default contact information
                        </label>
                    </div>

                    <div class="d-flex" style="gap: 17px; justify-content: end">
                        <button
                            type="button"
                            data-dismiss="modal"
                            aria-label="Close"
                            onclick="addNewItemModel()"
                            class="btn px-5 py-2"
                            style="
                                color: #0074d9;
                                border: 1px solid #0074d9;
                                background-color: white;
                            "
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            onclick="addNewItemModel()"
                            class="btn btn-primary px-5 py-2"
                        >
                            Add
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div
    class="modal fade select-client"
    id="updateContactModal"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: #e1f4ff">
                <h4
                    class="modal-title"
                    id="addItemModalLabel"
                    style="color: #0074d9 !important"
                >
                    Edit Contact
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>
            <form
            id="contactupdate"
                method="post"
            >
                @csrf
                <div class="modal-body">
                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >First Name
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <div class="input-group-prepend">

                                                               </div>


                                <select
        id="name_title2"
        name="name_title"
        class="form-select select-prefix dropdownMenuButton23"
        style="
            border-bottom: 1px solid #ced4da;
            border-left: 1px solid #ced4da;
            border-top: 1px solid #ced4da;
            border-right: none;
            background-color: white;
            width: 45px; /* Adjust width if needed */
        "
    >
        <option value="Mr." selected>Mr.</option>
        <option value="Mrs.">Mrs.</option>
        <option value="Ms.">Ms.</option>
        <option value="Dr.">Dr.</option>
    </select>
                                <input
                                    type="hidden"
                                    value="{{$property->id}}"
                                    name="propertyt_id"
                                />

                                <input
                                    type="hidden"
                                    id="contactt_id"
                                    name="contactt_id"
                                />

                                <input
                                    type="text"
                                    id="first_name"
                                    name="first_name"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Enter first name"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Last Name
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id="last_name"
                                name="last_name"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Enter last name"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >Suffix
                                <label class="" for=""> (Optional)</label></span
                            >

                            <input
                                type="text"
                                name="suffix"
                                style="height: 30px !important"
                                class="form-control"
                                id="suffix"
                                placeholder="Enter suffix (Optional)"
                            />
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Title
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id="title"
                                name="title"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Enter title"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Email
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="email"
                                    name="email_update"
                                    id="email"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Email"
                                />
                            </div>
                            <small class="text-danger" id="email_update_error"></small>

                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Second Email
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="email"
                                name="second_email"
                                id="second_email"
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Email"
                            />
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Phone number
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    name="phone_update"
                                    id="phone"
                                    oninput="maskPhoneNumber(event)"
                                    maxlength="12"
                                    minlength="12"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Phone number"
                                />
                            </div>
                            <small class="text-danger" id="phone_update_error"></small>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Second phone
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                name="second_phone_update"
                                oninput="maskPhoneNumber(event)"
                                    maxlength="12"
                                    minlength="12"
                                id="second_phone"
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Second phone"
                            />
                            <small class="text-danger" id="second_phone_update_error"></small>
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 96%">
                            <span for=""
                                >Account
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <select
                                    name="account"
                                    style="height: 30px !important"
                                    class="form-control accountgetprop"
                                    required
                                    id="account"
                                >
                                    <!-- <option value="">
                                        Select Account
                                    </option> -->
                                    <option selected value="{{$property->company_id}}">
                                        {{$property->company_name}}
                                    </option>
                                </select>
                            </div>
                        </div>

                    </div>



                    <div
                        class="d-flex"
                        style="gap: 17px; justify-content: start"
                    >
                        <input
                            type="checkbox"
                            id="make_default"
                            name="default"
                            class="form-control"
                        />
                        <label
                            for="make_default"
                            style="
                                color: #3b4159;
                                margin-top: -5px !important;
                                margin-left: -14px !important;
                            "
                            >Set as default contact information
                        </label>
                    </div>
                    <div class="d-flex" style="gap: 17px; justify-content: end">
                        <button
                            type="button"
                            data-dismiss="modal"
                            aria-label="Close"
                            onclick="addNewItemModel()"
                            class="btn px-5 py-2"
                            style="
                                color: #0074d9;
                                border: 1px solid #0074d9;
                                background-color: white;
                            "
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            onclick="addNewItemModel()"
                            class="btn btn-primary px-5 py-2"
                        >
                            Update
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- =Update Property Modal -->
<div
    class="modal fade select-client"
    id="editPropertyModal"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: #e1f4ff">
                <h4 class="modal-title px-3" id="addItemModalLabel">
                    <b style="color: #0074d9 !important"
                        >Property Information</b
                    >
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn px-3"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>
            <div class="modal-body" style="padding: 13px 20px !important">
                <form
                    id="updatecontactform"
                    action="{{URL::route(getRouteAlias() . '.property.update')}}"
                    method="POST"
                >
                    @csrf
                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >Property Name
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="hidden"
                                    id="property_id"
                                    required
                                    class="form-control"
                                    name="property_id"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Enter company name"
                                />
                                <input
                                    type="text"
                                    id="property_name"
                                    required
                                    class="form-control"
                                    name="property_name"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Enter company name"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                Address 1
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id="address1"
                                name="address1"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Address 1"
                            />
                        </div>
                    </div>
                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Address 2
                                <label class="" for="">(Optional)</label></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    id="address2"
                                    name="address2"
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Address 2"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >Account
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >

                            <select
                                name="account"
                                style="height: 30px !important"
                                class="form-control accounteditget"
                                required
                                id="account"
                            >
                                <option value="" selected>
                                    Select Account
                                </option>
                                @foreach($accounts as $item)
                                <option value="{{$item->id}}">
                                    {{$item->company_name}}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >City
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    id="city"
                                    name="city"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="City"
                                />
                            </div>
                        </div>

                        <div class="form-group" style="width: 47%">
                            <span for="">
                                State
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id="state"
                                name="state"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="State"
                            />
                        </div>
                    </div>

                    <div class="form-group" style="width: 50%">
                        <span for="">
                            Zip Code
                            <label class="required_field" for="">*</label></span
                        >
                        <input
                            type="text"
                            id="zip_code"
                            name="zip_code"
                            required
                            style="height: 30px !important; text-align: left"
                            class="form-control"
                            placeholder="Zip Code"
                        />
                    </div>

                    <div class="d-flex" style="gap: 17px; justify-content: end">
                        <button
                            type="button"
                            data-dismiss="modal"
                            aria-label="Close"
                            class="btn px-5 py-2"
                            style="
                                color: #0074d9;
                                border: 1px solid #0074d9;
                                background-color: white;
                            "
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled="true"
                            id="disableremove"
                            onclick="addNewItemModel()"
                            class="btn btn-primary px-5 py-2"
                        >
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- open issue modal -->
<div
    class="modal fade select-client"
    id="exampleModalOpen"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-large">
        <div class="modal-content">
            <div class="modal-header" style="">
                <h4
                    class="modal-title"
                    id="addItemModalLabel"
                    style="color: black !important"
                >
                    <b>Open Issue</b>
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="
                        border: none;
                        background-color: transparent;
                        font-size: 18px !important;
                    "
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="container-fluid">
                    <div class="row">
                        <div
                            class="table-responsive"
                            style="border-radius: 15px !important"
                        >
                            <table class="table table-stripped">
                                <thead style="background-color: #dcf2ff">
                                    <tr>
                                        <th>{{ __("ISSUE #") }}</th>
                                        <th>{{ __("Subject") }}</th>

                                        <th>{{ __("Category") }}</th>

                                        <th>{{ __("Created By") }}</th>
                                        <th>{{ __("Date & Time") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($openissues as $key=>$issue)
                                    <tr>
                                        <td>{{ $key + 1 }}</td>
                                        <td>
                                            {{ Str::limit($issue->subject, 30, '...') }}
                                        </td>

                                        <td>{{ $issue->category }}</td>
                                        <td>{{ $issue->creator_full_name }}</td>
                                        <td>{{ \Carbon\Carbon::parse($issue->created_at)->format('F d, Y h:i A') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- pending  issue modal -->
<div
    class="modal fade select-client"
    id="exampleModalPending"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-large">
        <div class="modal-content">
            <div class="modal-header" style="">
                <h4
                    class="modal-title"
                    id="addItemModalLabel"
                    style="color: black !important"
                >
                    <b>Pending Issue</b>
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="
                        border: none;
                        background-color: transparent;
                        font-size: 18px !important;
                    "
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="container-fluid">
                    <div class="row">
                        <div
                            class="table-responsive"
                            style="border-radius: 15px !important"
                        >
                            <table class="table table-stripped">
                                <thead style="background-color: #dcf2ff">
                                    <tr>
                                        <th>{{ __("ISSUE #") }}</th>
                                        <th>{{ __("Subject") }}</th>

                                        <th>{{ __("Category") }}</th>

                                        <th>{{ __("Created By") }}</th>
                                        <th>{{ __("Date & Time") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($pendingissues as $key=>$issue)
                                    <tr>
                                        <td>{{ $key + 1 }}</td>
                                        <td>
                                            {{ Str::limit($issue->subject, 30, '...') }}
                                        </td>

                                        <td>{{ $issue->category }}</td>
                                        <td>{{ $issue->creator_full_name }}</td>
                                        <td>{{ \Carbon\Carbon::parse($issue->created_at)->format('F d, Y h:i A') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- completed  issue modal -->
<div
    class="modal fade select-client"
    id="exampleModalCompleted"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-large">
        <div class="modal-content">
            <div class="modal-header" style="">
                <h4
                    class="modal-title"
                    id="addItemModalLabel"
                    style="color: black !important"
                >
                    <b>Completed Issue</b>
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="
                        border: none;
                        background-color: transparent;
                        font-size: 18px !important;
                    "
                >
                    <i
                        class="fa fa-times"
                        style="color: #7e8a9d"
                        aria-hidden="true"
                    ></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="container-fluid">
                    <div class="row">
                        <div
                            class="table-responsive"
                            style="border-radius: 15px !important"
                        >
                            <table class="table table-stripped">
                                <thead style="background-color: #dcf2ff">
                                    <tr>
                                        <th>{{ __("ISSUE #") }}</th>
                                        <th>{{ __("Subject") }}</th>

                                        <th>{{ __("Category") }}</th>

                                        <th>{{ __("Created By") }}</th>
                                        <th>{{ __("Date & Time") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($completedissues as $key=>$issue)
                                    <tr>
                                        <td>{{ $key + 1 }}</td>
                                        <td>
                                            {{ Str::limit($issue->subject, 30, '...') }}
                                        </td>

                                        <td>{{ $issue->category }}</td>
                                        <td>{{ $issue->creator_full_name }}</td>
                                        <td>


                                            {{ \Carbon\Carbon::parse($issue->created_at)->format('F d, Y h:i A') }}
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@include('layouts.admin.confirmation-modal')
@include('layouts.partials.success-modal')


@push('scripts')
@include('organization.property.detailsscript')
<script>
    $(document).ready(function () {
    $('#contactupdate').on('submit', function (e) {
        e.preventDefault();
        // Clear previous errors
        $('small.text-danger').text('');

        let formData = $(this).serialize();

        $.ajax({
            url: '{{URL::route(getRouteAlias() . '.contacts.update')}}',
            method: 'POST',
            data: formData,
            success: function (response) {
                sessionStorage.setItem('contactUpdated', 'true');
                location.reload();
                    $('#updateContactModal').modal('hide');
                // }
            },
            error: function (xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    for (let key in errors) {
                        // console.info(key);
                        $('#' + key + '_error').text(errors[key][0]);
                    }
                    $('#updateContactModal').modal('show'); // Ensure modal stays open
                }
            }
        });
    });
});

</script>
<script>
    $(document).ready(function() {
    if (sessionStorage.getItem('contactUpdated')) {
        $('.showMessage').css('display', 'block');
        $('.showMessage').html('<p>Contact Updated Successfully</p>');

        // Hide the message after 3 seconds (3000 milliseconds)
        setTimeout(function() {
            $('.showMessage').fadeOut();
        }, 3000);

        // Remove the flag to ensure the message only shows once after the reload
        sessionStorage.removeItem('contactUpdated');
    }else if (sessionStorage.getItem('contactAddedProp')) {
        $('.showMessage').css('display', 'block');
        $('.showMessage').html('<p>Contact Added Successfully</p>');

        // Hide the message after 3 seconds (3000 milliseconds)
        setTimeout(function() {
            $('.showMessage').fadeOut();
        }, 3000);

        // Remove the flag to ensure the message only shows once after the reload
        sessionStorage.removeItem('contactAddedProp');
    }
});
</script>
<script>
    $(document).ready(function () {
    $('#addItemForm').on('submit', function (e) {
        e.preventDefault();
        // Clear previous errors
        $('small.text-danger').text('');

        let formData = $(this).serialize();

        $.ajax({
            url: '{{URL::route(getRouteAlias() . '.contact.add')}}',
            method: 'POST',
            data: formData,
            success: function (response) {
                sessionStorage.setItem('contactAddedProp', 'true');
                location.reload();
                    $('#selectClientModal').modal('hide');
                // }
            },
            error: function (xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    for (let key in errors) {
                        $('#' + key + '_error').text(errors[key][0]);
                    }
                    $('#selectClientModal').modal('show'); // Ensure modal stays open
                }
            }
        });
    });
});

</script>
<script>
                   $(document).ready(function() {
                $('.dropzone_library_customize .dz-message').html(
                    '<div class="drop-zone__prompt text-center"><svg width="34" height="34" viewBox="0 0 34 34" fill="none"xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd"d="M9.36275 2.85175C11.4866 1.02029 14.1955 0.00879434 17 0C22.7162 0 27.4614 4.25 27.9778 9.73038C31.3608 10.2085 34 13.0411 34 16.5176C34 20.3341 30.8168 23.375 26.9599 23.375H21.25C20.9682 23.375 20.698 23.2631 20.4987 23.0638C20.2994 22.8645 20.1875 22.5943 20.1875 22.3125C20.1875 22.0307 20.2994 21.7605 20.4987 21.5612C20.698 21.3619 20.9682 21.25 21.25 21.25H26.962C29.7054 21.25 31.875 19.0995 31.875 16.5176C31.875 13.9336 29.7075 11.7831 26.9599 11.7831H25.8974V10.7206C25.8995 6.00313 21.947 2.125 17 2.125C14.7047 2.13417 12.4883 2.96317 10.7504 4.4625C9.14175 5.848 8.30025 7.51825 8.30025 8.82938V9.78137L7.35462 9.8855C4.386 10.2106 2.125 12.648 2.125 15.5507C2.125 18.6681 4.73875 21.25 8.03463 21.25H12.75C13.0318 21.25 13.302 21.3619 13.5013 21.5612C13.7006 21.7605 13.8125 22.0307 13.8125 22.3125C13.8125 22.5943 13.7006 22.8645 13.5013 23.0638C13.302 23.2631 13.0318 23.375 12.75 23.375H8.03463C3.6295 23.375 0 19.9028 0 15.5507C0 11.8044 2.69025 8.70187 6.25175 7.91562C6.55562 6.08175 7.735 4.25425 9.36275 2.85175Z"fill="#90A0B7" /><path fill-rule="evenodd" clip-rule="evenodd"d="M16.2471 8.8102C16.3458 8.71125 16.4631 8.63275 16.5922 8.57918C16.7213 8.52562 16.8596 8.49805 16.9994 8.49805C17.1391 8.49805 17.2775 8.52562 17.4066 8.57918C17.5357 8.63275 17.6529 8.71125 17.7516 8.8102L24.1266 15.1852C24.3261 15.3847 24.4382 15.6553 24.4382 15.9374C24.4382 16.2196 24.3261 16.4902 24.1266 16.6897C23.9271 16.8892 23.6565 17.0013 23.3744 17.0013C23.0922 17.0013 22.8216 16.8892 22.6221 16.6897L18.0619 12.1273V30.8124C18.0619 31.0942 17.9499 31.3645 17.7507 31.5637C17.5514 31.763 17.2812 31.8749 16.9994 31.8749C16.7176 31.8749 16.4473 31.763 16.2481 31.5637C16.0488 31.3645 15.9369 31.0942 15.9369 30.8124V12.1273L11.3766 16.6897C11.1771 16.8892 10.9065 17.0013 10.6244 17.0013C10.3422 17.0013 10.0716 16.8892 9.87214 16.6897C9.67263 16.4902 9.56055 16.2196 9.56055 15.9374C9.56055 15.6553 9.67263 15.3847 9.87214 15.1852L16.2471 8.8102Z"fill="#90A0B7" /></svg><p class="placeholder-text font-14">Drop your logo here, or <span>browse</span></p><p class="placeholder-text font-14">PNG, JPEG, PDF , XlS, XLSX Max size: 5MB</p></div>'
                )
            })

            $(document).on('click', '#my-dropzone', function() {
    $('#imageSizeError').html('');
});

    Dropzone.autoDiscover = false; // Disable auto-discovery



    var uploadedDocumentMap = {};
    const myDropzone = new Dropzone("#my-dropzone", {
    url: "{{ route(getRouteAlias() . '.property.file-store') }}", // Replace with your server upload URL
    addRemoveLinks: true,
    dictRemoveFile: "Remove", // Proper text for remove button
    previewTemplate: `
        <div class="dz-preview dz-file-preview">

         <img class="dz-details-imagess" style="height: 60px;" src="" />
            <div class="dz-details">

                <div class="dz-icon"></div>
                <div class="dz-filename"><span data-dz-name></span></div>
            </div>
            <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>
            <div class="dz-error-message"><span data-dz-errormessage></span></div>

        </div>
    `,
    previewsContainer: ".dropzone_library_customize",
    acceptedFiles: ".webp,.jpeg,.jpg,.png,.gif,.pdf,.xls,.xlsx,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content') // Include CSRF token in the headers
    },
    init: function () {


        this.on("success", function (file, response) {
            // Assuming your API response contains a key 'filePath'
            $('.file-upload-form-property').append('<input type="hidden" name="images_opportunity[]" value="' + response.name + '">');
            uploadedDocumentMap[file.name] = response.name;
        });
        this.on("addedfile", function (file) {
            // Extract file extension
            let ext = file.name.split('.').pop().toLowerCase();

            // Default icon (if no match found)
            let iconSrc = "default-icon.png";

            // Icons for specific file types
            if (ext === "pdf") {
                iconSrc = "data:image/svg+xml;base64,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";
            } else if (ext === "xls" || ext === "xlsx") {
                iconSrc = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48cGF0aCBkPSdNMi44NTkwMiAyLjg3ODIyTDE1LjQyOSAxLjA4MjIyQzE1LjUgMS4wNzIwNCAxNS41NzIzIDEuMDc3MjMgMTUuNjQxIDEuMDk3NDRDMTUuNzA5OCAxLjExNzY1IDE1Ljc3MzQgMS4xNTI0IDE1LjgyNzUgMS4xOTkzNUMxNS44ODE3IDEuMjQ2MjkgMTUuOTI1MSAxLjMwNDMzIDE1Ljk1NDkgMS4zNjk1MkMxNS45ODQ2IDEuNDM0NzIgMTYgMS41MDU1NSAxNiAxLjU3NzIyVjIyLjQyNDJDMTYgMjIuNDk1OCAxNS45ODQ2IDIyLjU2NjUgMTUuOTU0OSAyMi42MzE2QzE1LjkyNTIgMjIuNjk2NyAxNS44ODE5IDIyLjc1NDcgMTUuODI3OSAyMi44MDE3QzE1Ljc3MzggMjIuODQ4NiAxNS43MTAzIDIyLjg4MzQgMTUuNjQxNyAyMi45MDM2QzE1LjU3MzEgMjIuOTIzOSAxNS41MDA5IDIyLjkyOTIgMTUuNDMgMjIuOTE5MkwyLjg1ODAyIDIxLjEyMzJDMi42MTk2NCAyMS4wODkzIDIuNDAxNTIgMjAuOTcwNCAyLjI0MzcxIDIwLjc4ODZDMi4wODU5MSAyMC42MDY3IDEuOTk5MDMgMjAuMzc0IDEuOTk5MDIgMjAuMTMzMlYzLjg2ODIyQzEuOTk5MDMgMy42Mjc0MyAyLjA4NTkxIDMuMzk0NzMgMi4yNDM3MSAzLjIxMjg2QzIuNDAxNTIgMy4wMzA5OSAyLjYyMDY0IDIuOTEyMTcgMi44NTkwMiAyLjg3ODIyWk0xNyAzLjAwMDIySDIxQzIxLjI2NTIgMy4wMDAyMiAyMS41MTk2IDMuMTA1NTcgMjEuNzA3MSAzLjI5MzExQzIxLjg5NDcgMy40ODA2NCAyMiAzLjczNSAyMiA0LjAwMDIyVjIwLjAwMDJDMjIgMjAuMjY1NCAyMS44OTQ3IDIwLjUxOTggMjEuNzA3MSAyMC43MDczQzIxLjUxOTYgMjAuODk0OSAyMS4yNjUyIDIxLjAwMDIgMjEgMjEuMDAwMkgxN1YzLjAwMDIyWk0xMC4yIDEyLjAwMDJMMTMgOC4wMDAyMkgxMC42TDkuMDAwMDIgMTAuMjg2Mkw3LjQwMDAyIDguMDAwMjJINS4wMDAwMkw3LjgwMDAyIDEyLjAwMDJMNS4wMDAwMiAxNi4wMDAySDcuNDAwMDJMOS4wMDAwMiAxMy43MTQyTDEwLjYgMTYuMDAwMkgxM0wxMC4yIDEyLjAwMDJaJyBmaWxsPSdibGFjaycvPjwvc3ZnPgo=";
            } else if (ext === "doc" || ext === "docx") {
                iconSrc = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgaWQ9IndvcmQiPjxwYXRoIGQ9Ik0yMy41IDIuNUgxNFYxYS41LjUgMCAwIDAtLjYxMi0uNDg3bC0xMyAzQS41LjUgMCAwIDAgMCA0djE3YS41LjUgMCAwIDAgLjQyNC40OTRsMTMgMmEuNDk2LjQ5NiAwIDAgMCAuNDAxLS4xMTVBLjQ5NC40OTQgMCAwIDAgMTQgMjN2LTEuNWg5LjVhLjUuNSAwIDAgMCAuNS0uNVYzYS41LjUgMCAwIDAtLjUtLjV6TTEwLjk4MSA5LjEzOGwtMiA3Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMEw2Ljk4IDEyLjUwM2MtLjA2LS4yMTUtLjI1Ni0uMzYzLS40OC0uMzYzcy0uNDIuMTQ4LS40OC4zNjNsLTEuMDM4IDMuNjM0Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMGwtMi03YS41LjUgMCAwIDEgLjk2Mi0uMjc0bDEuMDM4IDMuNjM1Yy4xMjEuNDMuODQuNDMuOTYxIDBsMS4wMzgtMy42MzRjLjEyMy0uNDI3Ljg0LS40MjYuOTYzIDBsMS4wMzggMy42MzVjLjEyMS40My44NC40My45NjEgMGwxLjAzOC0zLjYzNGEuNS41IDAgMCAxIC45NjMuMjczek0yMyAyMC41aC05di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoNy41YS41LjUgMCAwIDAgMC0xSDE0di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoOXYxN3oiPjwvcGF0aD48L3N2Zz4=";
            }else if (ext === "png" || ext === "jpg" || ext === "webp" || ext === "jpeg" || ext === "jpeg") {
                const reader = new FileReader();
                reader.onload = function (e) {
                    $(file.previewElement).find(".dz-details-imagess").css("width", '100%');
                    // Find the preview image element and set the source
                    $(file.previewElement).find(".dz-details-imagess").attr("src", e.target.result);
                };
                reader.readAsDataURL(file);
            }
            $(file.previewElement).find(".dz-details-imagess").css("width", '100%');
            // Update the preview element with the corresponding icon
            if (ext === "docx" || ext === "doc" || ext === "xlsx" || ext === "xls" || ext === "pdf") {
            $(file.previewElement).find(".dz-details-imagess").attr("src", iconSrc);
            }
        });

        this.on("removedfile", function (file) {
            console.info("File removed: ", file.name); // Log file removal

            file.previewElement.remove();
        var name = '';
        if (typeof file.file_name !== 'undefined') {
            name = file.file_name;
        } else {
            name = uploadedDocumentMap[file.name];
        }

            // Send an AJAX request to delete the file from the server
            fetch("{{ route(getRouteAlias() . '.property.file-delete') }}", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ filename: name })
            })
            .then(response => response.json())
            .then(data => {
                console.info('File deleted from server:', data);
                // You can add additional logic here to update UI or handle response data
            })
            .catch(error => {
                console.error('Error deleting file from server:', error);
            });
            $('.file-upload-form-property').find('input[name="images_opportunity[]"][value="' + name + '"]').remove();
        });

        // Add error handling if needed
        this.on("error", function (file, errorMessage) {
            console.error("Dropzone error:", errorMessage);
        });
    }
});

        </script>

<script>
        $(document).ready(function () {
      // When a dropdown item is clicked
      $('.dropdown-togglesmain').click(function (e) {
        e.preventDefault(); // Prevent default anchor behavior

        var selectedValue = $(this).data('value'); // Get the selected value
        var button = $('#dropdownMenuButton');
        var id="{{$property->id}}";
        // button.html('<b style="font-weight: 900 !important; font-size: 16px">' + $(this).text() + '</b>'); // Update button text

        // Send the selected value to the server via AJAX
        $.ajax({
          url: '{{ route("organization.update.propertyHealth") }}',  // Your route to handle the update
          method: 'POST',
          data: {
            _token: '{{ csrf_token() }}',  // CSRF token for security
            property_health: selectedValue,  // The selected value from the dropdown
            id:id
          },
          success: function (response) {
            if (response.success) {
            //   alert('Property Health updated successfully!');
              location.reload();
            } else {
              alert('Error updating property health.');
            }
          },
          error: function (xhr, status, error) {
            alert('AJAX request failed: ' + error);
          }
        });
      });
    });

    $(document).ready(function () {
        var debounceTimeout;

        // When input is typed in the textarea
        $('#company_notes').on('input', function () {
            clearTimeout(debounceTimeout);
            debounceTimeout = setTimeout(function () {
                var notes = $('#company_notes').val();  // Get the value of the textarea

                // Send the data to the server via AJAX
                $.ajax({
                    url: "{{route('organization.save.company.notes')}}",  // Replace with the actual URL for your save endpoint
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}', // CSRF token
                        company_notes: notes,         // The value to be saved
                        property_id: {{ $property->id }} // Send the property ID if necessary
                    },
                    success: function (response) {
                        // console.log('Notes saved successfully');
                    },
                    error: function (error) {
                        console.error('Error saving notes');
                    }
                });
            }, 1000);  // 500ms delay for debouncing
        });
    });
</script>
@endpush @endsection
