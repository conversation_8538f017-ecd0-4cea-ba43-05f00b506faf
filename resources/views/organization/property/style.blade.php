<style>
    .company-client_tabs .nav-item .nav-link.active {
    color: #2FCC40 !important;
    /* border-bottom: 1px solid #FE9B6A; */

    border-bottom: 3px solid #2FCC40 !important;
}
.contactmodal:focus{
    outline: none;

}
.inputform{
    height: 27px;
}
.fileinput-cancel-button{
    display: none !important;
}
</style>
<style>
    .issue-status {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
    }
    .status-box {
        justify-content: space-between;
        align-items: center;
        text-align: center;
        width: 100%;
        padding: 10px;
        border-radius: 10px;
        margin-bottom: 15px;
    }
    .status-box2 b,
     {
        color: #efa037;
    }
    .open-issue {
        background-color: #ffecc7;
    }
    .pending {
        background-color: #ffeb99;
    }
    .completed {
        background-color: #d6f7d9;
    }
    .completed b {
        color: #26a333;
    }
    .completed span {
        color: #26a333;
    }
</style>
<style>


/*----------multiple-file-upload-----------*/
.file-upload-contain{
    position: relative;
    margin-bottom: 40px;
}
.file-upload-contain .file-input,
.file-upload-contain .file-preview{
    position: initial;
}
.file-upload-contain .file-drop-zone{
    border: 1px dashed #E2E4EA;
    transition: 0.3s;
    margin: 0;
    padding: 0;
    border-radius: 20px;
    background: #F4F7FA;

    min-height: auto;
}
.file-upload-contain .file-drop-zone.clickable:hover,
.file-upload-contain .file-drop-zone.clickable:focus,
.file-upload-contain .file-highlighted{

}
.upload-area i {
    color: #1e80e8;
    font-size: 50px;
}
.upload-area p {
    margin-bottom: 30px;
    margin-top: 30px;
    font-size: 20px;
    font-weight: 600;
    color: #2580e8;
}
.upload-area p b {
    color: #1e80e8;
}
.upload-area button {
    padding: 8px 16px;
    min-width: 150px;
    font-size: 16px;
    font-weight: 600;
    color: #51566C;
    background: #FFFFFF;


border: none !important;
    border-radius: 9px;
    transition: 0.3s;
}
.upload-area button:focus{
    outline: none !important;
}
.file-preview{
    padding: 0;
    border: none;
    margin-bottom: 30px;
}
.file-preview .fileinput-remove{
    display: none;
}
.file-drop-zone-title{
    padding: 55px 10px;
}
.file-drop-zone .file-preview-thumbnails{
    cursor: pointer;
}
.file-preview-frame{
    cursor: default;
    display: flex;
    align-items: center;
    border: none;
    background-color: #2580e8;
    box-shadow: none;
    border-radius: 8px;
    width: 100%;
    padding: 15px;
    margin: 8px 0px;
}
.file-preview-frame:not(.file-preview-error):hover{
    border: none;
    box-shadow: 0 0 10px 0 rgb(0 0 0 / 20%);
}
.file-preview-frame .kv-file-content{
    min-width: 45px;
    min-height: 45px;
    width: 45px;
    height: 45px;
    border-radius: 4px;
    margin-right: 10px;
    background-color: #fff;
    padding: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.file-preview-image {
    border-radius: 4px;
}
.file-preview-frame .file-footer-caption{
    padding-top: 0;
}
.file-preview-frame .file-footer-caption{
    text-align: left;
    margin-bottom: 0;
}
.file-detail{
    font-size: 14px;
    height: auto;
    width: 100%;
    line-height: initial;
}
.file-detail .file-caption-name{
    color: #fff;
    font-size: 7px;
    font-weight: 600;
    margin-bottom: 6px;
}
.file-detail .file-size{
    color: #f1f8fe;
    font-size: 12px;
}
.kv-zoom-cache {
    display: none;
}
.file-preview-frame .file-thumbnail-footer{
    height: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}
.file-preview-frame .file-drag-handle,
.file-preview-frame .file-upload-indicator{
    float: none;
}
.file-preview-frame .file-footer-buttons{
    float: none;
    display: flex;
    align-items: center;
}
.file-preview-status.text-center {
    display: none;
}
.kv-file-remove.file-remove {
    border: none;
    background-color: #ef2f2f;
    color: #fff;
    width: 25px;
    height: 25px;
    font-size: 12px;
    border-radius: 4px;
    margin: 0px 4px;
}
.file-drag-handle.file-drag {
    border: none;
    background-color: #fff;
    color: #2580e8;
    width: 25px;
    height: 25px;
    font-size: 12px;
    border-radius: 4px;
    margin: 0px 4px;
}
.kv-file-upload.file-upload{
    border: none;
    background-color: #48bd22;
    color: #fff;
    width: 25px;
    height: 25px;
    font-size: 12px;
    border-radius: 4px;
    margin: 0px 4px;
}
.file-thumb-loading{
    background: none !important;
}
.file-preview-frame.sortable-chosen {
    background-color: #64a5ef;
    border-color: #64a5ef;
    box-shadow: none!important;
}
.file-preview-pdf{
    width: 100% !important;
    height: 45px !important;
}
</style>
