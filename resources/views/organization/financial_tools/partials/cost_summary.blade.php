@forelse ($operations as $item)
    <form class="costSummaryForm">
        <tr>
            <td>{{ optional($item->request)?->sales_order_number }}</td>
            <td>{{ $item->client->full_name }}</td>
            <td>{{ optional($item->request)?->project_name }}</td>
            <td>{{ Carbon\Carbon::parse($item?->completed_at)->format('F') }}
            </td>
            <td>{{ customDateFormat(\Carbon\Carbon::parse(optional($item?->latestScheduleDate)?->date) ?: now()) }}
            </td>
            <td>{{ customDateFormat(\Carbon\Carbon::parse($item?->completed_at) ?: now()) }}
            </td>
            <td class="grand_total">${{ $item?->grand_total }}</td>
            @php
                $laborHours =
                    ($item?->getestimatePlantMaterial->where('generate_estimate_id', $item->id)->count() > 0
                        ? $item?->getestimatePlantMaterial
                            ->where('generate_estimate_id', $item->id)
                            ->map(function ($item) {
                                // Calculate the total_cost by adding quantity and material install
                                return $item->quantity + ($item->material ? $item->material->install : 0);
                            })
                            ->sum()
                        : 0) +
                    ($item?->getestimateHardMaterial->where('generate_estimate_id', $item->id)->count() > 0
                        ? $item?->getestimateHardMaterial
                            ->where('generate_estimate_id', $item->id)
                            ->map(function ($item) {
                                // Calculate the total_cost by adding quantity and material labor
                                return $item->quantity + ($item->material ? $item->material->labor : 0);
                            })
                            ->sum()
                        : 0);
            @endphp
            <td>{{ custom_number_format($laborHours, 2) }}</td>
            <td>${{ custom_number_format($item?->getestimateLabor->where('generate_estimate_id', $item->id)->sum('total_cost') ?? 0, 2) }}
            </td>
            <td>${{ custom_number_format($item?->getestimateMaterial->where('generate_estimate_id', $item->id)->sum('total_cost'), 2) }}
            </td>
            <td>${{ custom_number_format(
                $item?->getestimateLabor->where('generate_estimate_id', $item->id)->sum(function ($item) use ($laborBurden) {
                    if ($laborBurden) {
                        $totalPayable = totalPriceWithLaborBurden(
                            $item->quantity,
                            $item->unit_cost,
                            $item->gross_margin,
                            $laborBurden,
                        );
                    } else {
                        $totalPayable = $item->total_cost + ($item->gross_margin / 100) * $item->total_cost;
                    }
                    return $item->total_cost;
                }),
                2,
            ) }}
            </td>
            <td>${{ custom_number_format(
                $item?->getestimateHardMaterial->where('generate_estimate_id', $item->id)->sum(function ($item) {
                    $total_price = totalPriceWithTax($item->quantity, $item->unit_cost, $item->gross_margin);
                    return $total_price;
                }),
                2,
            ) }}
            </td>
            <td>${{ custom_number_format(
                $item?->getestimatePlantMaterial->where('generate_estimate_id', $item->id)->sum(function ($item) {
                    $total_price = totalPriceWithTax($item->quantity, $item->unit_cost, $item->gross_margin);
                    return $total_price;
                }),
                2,
            ) }}
            </td>
            <td>${{ custom_number_format(
                $item?->getestimateOtherCost->where('generate_estimate_id', $item->id)->sum(function ($item) {
                    $item['unit_price'] = $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost;
                    $item['total_price'] =
                        $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity);
                    return $item->total_price;
                }),
                2,
            ) }}
            </td>
            <td>${{ custom_number_format(
                $item?->getestimateSubContractor->where('generate_estimate_id', $item->id)->sum(function ($item) {
                    $item['unit_price'] = $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost;
                    $item['total_price'] =
                        $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity);
                    return $item->total_price;
                }),
                2,
            ) }}
            </td>
            <input type="hidden" name="operation_id" value="{{ $item->id }}">
            <td><input type="number" name="hours" class="table-field"
                    value="{{ optional($item?->cost_summary)?->hours }}">
            </td>
            <td><input type="number" name="equipment_cost2" class="table-field"
                    value="{{ optional($item?->cost_summary)?->equipment_cost2 }}">
            </td>
            <td><input type="number" name="labor3" class="table-field"
                    value="{{ optional($item?->cost_summary)?->labor3 }}">
            </td>
            <td><input type="number" name="hard_material4" class="table-field"
                    value="{{ optional($item?->cost_summary)?->hard_material4 }}">
            </td>
            <td><input type="number" name="plant_material" class="table-field"
                    value="{{ optional($item?->cost_summary)?->plant_material }}">
            </td>
            <td><input type="number" name="other_jobCost" class="table-field"
                    value="{{ optional($item?->cost_summary)?->other_jobCost }}">
            </td>
            <td><input type="number" name="sub_contractor" class="table-field"
                    value="{{ optional($item?->cost_summary)?->sub_contractor }}">
            </td>
            <td><input type="number" name="accurals" class="table-field"
                    value="{{ optional($item?->cost_summary)?->accurals }}">
            </td>
            <td class="forcasted_gp">
                ${{ str_replace(',', '', $item?->grand_total) -
                    optional($item?->cost_summary)?->equipment_cost2 -
                    optional($item?->cost_summary)?->labor3 -
                    optional($item?->cost_summary)?->hard_material4 -
                    optional($item?->cost_summary)?->plant_material -
                    optional($item?->cost_summary)?->other_jobCost -
                    optional($item?->cost_summary)?->sub_contractor -
                    optional($item?->cost_summary)?->accurals }}
            </td>

            <td class="forcasted_gp_percentage">
                {{ number_format(
                    ((str_replace(',', '', $item?->grand_total) -
                        optional($item?->cost_summary)?->equipment_cost2 -
                        optional($item?->cost_summary)?->labor3 -
                        optional($item?->cost_summary)?->hard_material4 -
                        optional($item?->cost_summary)?->plant_material -
                        optional($item?->cost_summary)?->other_jobCost -
                        optional($item?->cost_summary)?->sub_contractor -
                        optional($item?->cost_summary)?->accurals) /
                        str_replace(',', '', $item?->grand_total == 0 ? 1 : $item?->grand_total)) *
                        100,
                    2,
                ) }}%
            </td>
            <td>{{ optional($item?->saleMan)?->first_name }}
                {{ optional($item?->saleMan)?->last_name }}</td>
            <td>{{ optional($item?->estimator)?->first_name }}
                {{ optional($item?->estimator)?->last_name }}</td>
            <td>{{ optional($item?->manager)?->first_name }}
                {{ optional($item?->manager)?->last_name }}</td>
            <td><input type="text" name="po_number" class="table-field"
                    value="{{ optional($item?->cost_summary)?->po_number }}">
            </td>
            <td>{{ $item?->workType?->name ?? '-------' }}
            </td>
            <td> {{ $item?->serviceLine?->name ?? '------' }} </td>




            {{-- @if (empty($item?->notes))
            <td class="svg-light">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none">
                    <path
                        d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z"
                        fill="#E7E7E7"></path>
                </svg>
            </td>
        @else
            <td class="svg-yellow ViewCostSummaryNoteBtn" style="cursor: pointer"
                data-notes="{{ $item?->notes }}">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none">
                    <path
                        d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z"
                        fill="#E7E7E7" />
                </svg>
            </td>
        @endif --}}
            <td class="svg-yellow addCostSummaryNoteBtn" style="cursor: pointer"
                data-notes="{{ optional($item?->cost_summary)?->notes }}" data-id="{{ $item->id }}">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none">
                    <path
                        d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z"
                        fill="#E7E7E7" />
                </svg>
            </td>




            <td> <button class="btn btn-lg" style="background: #192a3e;color:white">Save</button></td>
        </tr>
    </form>
@empty
    <tr class="text-center">
        <td colspan="10">Sorry we could not find any results</td>
    </tr>
@endforelse
