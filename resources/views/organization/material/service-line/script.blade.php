<script type="text/javascript">
    var status = '';
    var serviceType = '';

    var serviceLineTable = null;
    var workTypeTable = null;

    $(document).ready(function() {


        renderServiceLineTable();

        $('#hard_material').click(function() {
            $('.yajra_plant_material').removeClass('active-tab');
            if (!$('.yajra_hard_material').hasClass('active-tab')) {
                $('.yajra_hard_material').addClass('active-tab')
                serviceLineTable.draw();
            }
        })


        function renderServiceLineTable() {
            serviceLineTable = $('.yajra-datatable').DataTable({
                processing: true,
                responsive: true,
                // scrollX: true,
                serverSide: true,
                ajax: {
                    url: "{{ route(getRouteAlias() . '.material.service-line.table-list') }}",
                    data: function(d) {
                        d.status = status
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false
                    },
                    {
                        data: 'name',
                        name: 'name',
                        orderable: false
                    }

                ],
                language: {
                    zeroRecords: "Sorry we could not find any results",
                    paginate: {
                        "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                        "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
                    },


                },
                dom: '<"top">rt<"bottom"lip><"clear">',
            });
        }

        // Custom Search Filter
        $('.service_lines_table_filters #filter_search').on('keyup', function() {
            serviceLineTable.search(this.value).draw();
        });

        // Custom Select Filter
        $('.service_lines_table_filters #select_filter').on('change', function() {
            var selectedValue = $(this).val();
            if (selectedValue == "Clear") {
                resetFilters();
            } else {
                status = selectedValue;
                serviceLineTable.columns().search('').draw();
            }
        });


        // Reset all filters
        function resetFilters() {
            $('.service_lines_table_filters #filter_search').val('');
            $('.service_lines_table_filters #select_filter').val('');
            $('.service_lines_table_filters .table_tabs_filter input.all').prop('checked', true)
            planName = '';
            status = '';
            serviceType = '';
            serviceLineTable.search('').columns().search('').draw();
        }

        $('#plan_material').click(function() {
            $('.yajra_hard_material').removeClass('active-tab');
            if (!$('.yajra_plant_material').hasClass('active-tab')) {
                $('.yajra_plant_material').addClass('active-tab');
                getServiceLines(); // Assuming getServiceLines sets serviceType
            }
        });


        function ucwords(str) {
            return str.toLowerCase().replace(/(^|\s)\S/g, function(match) {
                return match.toUpperCase();
            });
        }

        function getServiceLines() {
            var url = "{{ URL::route(getRouteAlias() . '.material.service-line.pluck-list') }}"

            $.ajax({
                method: "get",
                url: url,
                success: function(response) {
                    var selectElement = document.getElementById("select__service_line_filter");
                    // Clear existing options (optional)
                    selectElement.innerHTML = '';
                    for (var key in response.services) {
                        if (response.services.hasOwnProperty(key)) {
                            var name = response.services[key];
                            var option = document.createElement("option");
                            option.value = key;
                            option.text = ucwords(name);
                            selectElement.appendChild(option);
                        }
                    }

                    serviceType = selectElement.value;

                    // After populating the dropdown, you can render the DataTable
                    if (!$.fn.DataTable.isDataTable('.yajra-datatable2')) {
                        renderWorkTypeTable();
                    } else {
                        workTypeTable.draw();
                    }

                },
                error: function(response) {}
            })
        }

        function renderWorkTypeTable() {

            workTypeTable = $('.yajra-datatable2').DataTable({
                processing: true,
                responsive: true,
                // scrollX: true,
                serverSide: true,
                ajax: {
                    url: "{{ route(getRouteAlias() . '.material.work-type.table-list') }}",
                    data: function(d) {
                        d.type = serviceType
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false
                    },
                    {
                        data: 'name',
                        name: 'name',
                        orderable: false
                    },
                    {
                        data: 'serviceLine.name',
                        name: 'serviceLine.name',
                        orderable: false
                    }
                ],
                language: {
                    zeroRecords: "Sorry we could not find any results",
                    paginate: {
                        "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                        "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
                    },


                },
                dom: '<"top">rt<"bottom"lip><"clear">',
            });
        }



        // Custome Search Filter
        $('.work_type_materials_table_filters #filter_search').on('keyup', function() {
            workTypeTable.search(this.value).draw();
        });

        // Custom Select Filter
        $('#select__service_line_filter').on('change', function() {
            console.log("change");
            var selectedValue = $(this).val();
            serviceType = selectedValue;
            workTypeTable.columns().search('').draw();
        });
        var fileInput = $(".upload_file_wrapper .input_file");
        var label = $(".upload_file_wrapper label");
        var selectedFile;

        // Add an event listener to the input field
        fileInput.on("change", function(event) {
            // Get the selected file
            selectedFile = event.target.files[0];

            // Check if the selected file is an Excel file
            if (
                selectedFile.type === "application/vnd.ms-excel" ||
                selectedFile.type ===
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            ) {
                // Get the name of the selected file
                var fileName = selectedFile.name;

                // Update the label text with the file name
                label.text(fileName);

                // Submit the form
                $(this).parent(".file_upload_button_form").submit();
            } else {
                // Reset the input field and label text
                fileInput.val("");
                label.text("Import Service Line's");

                // Create a new element with the error message
                var newElement = $(
                    "<label class='error custom_error'>Only Excel File Is Allowed</label>"
                );

                // Append the new element after the input field
                $(this).after(newElement);

                // Fade out the error message after 3 seconds
                newElement.fadeOut(3000, function() {
                    $(this).remove(); // Remove the element from the DOM after it has faded out
                });
            }
        });


        $(".file_upload_button_form").on("submit", function(e) {
            e.preventDefault();
            let myForm = document.getElementById("service-line-material-import");
            let formData = new FormData(myForm);
            var url = "{{ URL::route(getRouteAlias() . '.material.service-line.import') }}"

            $.ajax({
                method: "post",
                url: url,
                data: formData,
                cache: false,
                contentType: false,
                processData: false,


                success: function(response) {

                    $('.showMessage').html(`
                    <div class="alert alert-success alert-dismissible fade show showMessage">
                        File imported successfully!.
                        </div>

                    `);
                    serviceLineTable.draw();

                },
                error: function(response) {


                    $.each(response.responseJSON.errors, function(field_name, error) {
                        $('.showMessage').html('');

                        $('.showMessage').append(`
                           <div class="alert alert-danger alert-dismissible fade show showMessage">
                               ${error}
                           </div>

                        `);
                    });
                }
            })
        });
        //plant material import
        var plantFileInput = $(".file_wrapper .plant_input_file");
        var plantLabel = $(".file_wrapper label");
        var selectedPlantFile;

        // Add an event listener to the input field
        plantFileInput.on("change", function(event) {
            // Get the selected file
            selectedPlantFile = event.target.files[0];

            // Check if the selected file is an Excel file
            if (
                selectedPlantFile.type === "application/vnd.ms-excel" ||
                selectedPlantFile.type ===
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            ) {
                // Get the name of the selected file
                var plantFileName = selectedPlantFile.name;

                // Update the label text with the file name
                plantLabel.text(plantFileName);

                // Submit the form
                $(this).parent(".upload_button_form").submit();
            } else {
                // Reset the input field and label text
                plantFileInput.val("");
                plantLabel.text("Import Material");

                // Create a new element with the error message
                var newElement = $(
                    "<label class='error custom_error'>Only Excel File Is Allowed</label>"
                );

                // Append the new element after the input field
                $(this).after(newElement);

                // Fade out the error message after 3 seconds
                newElement.fadeOut(3000, function() {
                    $(this).remove(); // Remove the element from the DOM after it has faded out
                });
            }
        });


        $(".upload_button_form").on("submit", function(e) {
            e.preventDefault();
            let myForm = document.getElementById("work-type-material-import");
                        // Create a hidden input element
            let hiddenInput = document.createElement("input");

            // Set the input type to "hidden"
            hiddenInput.type = "hidden";

            // Set the name and value of the hidden input
            hiddenInput.name = "service_line_id"; // Replace with your desired name
            hiddenInput.value = serviceType; // Replace with your desired value

            // Append the hidden input to the form
            myForm.appendChild(hiddenInput);
            let formData = new FormData(myForm);
            var url = "{{ URL::route(getRouteAlias() . '.material.service-line.work-type.import') }}"

            $.ajax({
                method: "post",
                url: url,
                data: formData,
                cache: false,
                contentType: false,
                processData: false,


                success: function(response) {
                    $('.showSuccessMessage').html(`
                    <div class="alert alert-success alert-dismissible fade show showSuccessMessage">
                        File imported successfully!.
                        </div>

                    `);
                    workTypeTable.draw();

                },
                error: function(response) {
                    $.each(response.responseJSON.errors, function(field_name, error) {
                        $('.showSuccessMessage').html('');

                        $('.showSuccessMessage').append(`
                           <div class="alert alert-danger alert-dismissible fade show showSuccessMessage">
                               ${error}
                           </div>

                        `);
                    });
                }
            })
        });




    });
</script>
