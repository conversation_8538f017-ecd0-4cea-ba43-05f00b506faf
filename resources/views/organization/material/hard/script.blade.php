<script type="text/javascript">
    var status = '';
    var size = '';
    // var plant_materials_table;
    // var materials_table;
    $(document).ready(function() {

        getMaterialFilters('plantMaterial');
        getMaterialFilters('hardMaterial');


        function getMaterialFilters(key){
            var url = "{{ URL::route(getRouteAlias() . '.material.get-filters',':key') }}"
            url=url.replace(':key',key);

            $.ajax({
                method: "GET",
                url: url,
                success: function(response) {
                    var html = '';
                    $.each(response.data, function (key, value) {
                        html += '<option value="' + value.toLowerCase() + '">' + value + '</option>';
                    });

                    if (key == 'plantMaterial') {
                        $('#plan-material #select__plant_filter').append(html);
                    } else {
                        $('#hard-material #select_filter').append(html);
                    }
                },
                error: function(response) {

                }
            })
        }

        var materials_table = $('.yajra-datatable').DataTable({
            processing: true,
            responsive: true,
            // scrollX: true,
            serverSide: true,
            ajax: {
                url: "{{ route(getRouteAlias() . '.material.table-list') }}",
                data: function(d) {
                    d.status = status
                }
            },
            columns: [{
                    data: 'name',
                    name: 'name',
                    orderable: false
                }, {
                    data: 'uom',
                    name: 'uom',
                    orderable: false
                }, {
                    data: 'cost',
                    name: 'cost',
                    orderable: false
                },
                {
                    data: 'labor',
                    name: 'labor',
                    orderable: false
                },
                {
                    data: 'image_column',
                    name: 'image_column',
                    orderable: false,
                    searchable: false
                }

            ],
            language: {
                zeroRecords: "Sorry we could not find any results",
                paginate: {
                    "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                    "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
                },


            },
            dom: '<"top">rt<"bottom"lip><"clear">',
        });

        // Custom Search Filter
        $('.materials_table_filters #filter_search').on('keyup', function() {
            materials_table.search(this.value).draw();
        });

        // Custom Select Filter
        $('.materials_table_filters #select_filter').on('change', function() {
            var selectedValue = $(this).val();
            if (selectedValue == "Clear") {
                resetFilters();
            } else {
                status = selectedValue;
                materials_table.columns().search('').draw();
            }
        });


        // Reset all filters
        function resetFilters() {
            $('.materials_table_filters #filter_search').val('');
            $('.materials_table_filters #select_filter').val('');
            $('.materials_table_filters .table_tabs_filter input.all').prop('checked', true)
            planName = '';
            status = '';
            size = '';
            materials_table.search('').columns().search('').draw();
        }

        var plant_materials_table = $('.yajra-datatable2').DataTable({
            processing: true,
            responsive: true,
            // scrollX: true,
            serverSide: true,
            ajax: {
                url: "{{ route(getRouteAlias() . '.plant_material.table-list') }}",
                data: function(d) {
                    d.size = size
                }
            },
            columns: [{
                    data: 'name',
                    name: 'name',
                    orderable: false
                }, {
                    data: 'type',
                    name: 'type',
                    orderable: false
                }, {
                    data: 'size',
                    name: 'size',
                    orderable: false
                },
                {
                    data: 'cost',
                    name: 'cost',
                    orderable: false
                }, {
                    data: 'install',
                    name: 'install',
                    orderable: false
                }


            ],
            language: {
                zeroRecords: "Sorry we could not find any results",
                paginate: {
                    "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                    "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
                },


            },
            dom: '<"top">rt<"bottom"lip><"clear">',
        });

        // Custome Search Filter
        $('.plant_materials_table_filters #filter_search').on('keyup', function() {
            plant_materials_table.search(this.value).draw();
        });

        // Custom Select Filter
        $('.plant_materials_table_filters #select__plant_filter').on('change', function() {
            var selectedValue = $(this).val();
            if (selectedValue == "Clear") {
                resetFilters();
            } else {
                size = selectedValue;
                plant_materials_table.columns().search('').draw();
            }
        });
        var fileInput = $(".upload_file_wrapper .input_file");
        var label = $(".upload_file_wrapper label");
        var selectedFile;

        // Add an event listener to the input field
        fileInput.on("change", function(event) {
            // Get the selected file
            selectedFile = event.target.files[0];

            // Check if the selected file is an Excel file
            if (
                selectedFile.type === "application/vnd.ms-excel" ||
                selectedFile.type ===
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            ) {
                // Get the name of the selected file
                var fileName = selectedFile.name;

                // Update the label text with the file name
                label.text(fileName);

                // Submit the form
                $(this).parent(".file_upload_button_form").submit();
            } else {
                // Reset the input field and label text
                fileInput.val("");
                label.text("Import Material");

                // Create a new element with the error message
                var newElement = $(
                    "<label class='error custom_error'>Only Excel File Is Allowed</label>"
                );

                // Append the new element after the input field
                $(this).after(newElement);

                // Fade out the error message after 3 seconds
                newElement.fadeOut(3000, function() {
                    $(this).remove(); // Remove the element from the DOM after it has faded out
                });
            }
        });


        $(".file_upload_button_form").on("submit", function(e) {
            e.preventDefault();
            let myForm = document.getElementById("hard-material-import");
            // var div=$('#select-division').val();
            // alert(div);
            let formData = new FormData(myForm);
            // Div ko formData mein append karain
            //  formData.append('division', div);
            var url = "{{ URL::route(getRouteAlias() . '.materials.file-import') }}"

            $.ajax({
                method: "post",
                url: url,
                data: formData,
                cache: false,
                contentType: false,
                processData: false,


                success: function(response) {

                    $('.showMessage').html(`
                    <div class="alert alert-success alert-dismissible fade show showMessage">
                        File imported successfully!.
                        </div>

                    `);

                    getMaterialFilters('hardMaterial');
                    materials_table.draw();

                },
                error: function(response) {


                    $.each(response.responseJSON.errors, function(field_name, error) {
                        $('.showMessage').html('');

                        $('.showMessage').append(`
                           <div class="alert alert-danger alert-dismissible fade show showMessage">
                               ${error}
                           </div>

                        `);
                    });
                }
            });


        });
        //plant material import
        var plantFileInput = $(".file_wrapper .plant_input_file");
        var plantLabel = $(".file_wrapper label");
        var selectedPlantFile;

        // Add an event listener to the input field
        plantFileInput.on("change", function(event) {
            // Get the selected file
            selectedPlantFile = event.target.files[0];

            // Check if the selected file is an Excel file
            if (
                selectedPlantFile.type === "application/vnd.ms-excel" ||
                selectedPlantFile.type ===
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            ) {
                // Get the name of the selected file
                var plantFileName = selectedPlantFile.name;

                // Update the label text with the file name
                plantLabel.text(plantFileName);

                // Submit the form
                $(this).parent(".upload_button_form").submit();
            } else {
                // Reset the input field and label text
                plantFileInput.val("");
                plantLabel.text("Import Material");

                // Create a new element with the error message
                var newElement = $(
                    "<label class='error custom_error'>Only Excel File Is Allowed</label>"
                );

                // Append the new element after the input field
                $(this).after(newElement);

                // Fade out the error message after 3 seconds
                newElement.fadeOut(3000, function() {
                    $(this).remove(); // Remove the element from the DOM after it has faded out
                });
            }
        });


        $(".upload_button_form").on("submit", function(e) {
            e.preventDefault();
            let myForm = document.getElementById("plant-material-import");
            var div=$('#select-division').val();
            console.log(div);
            let formData = new FormData(myForm);
            // formData.append('division', div);
            var url = "{{ URL::route(getRouteAlias() . '.plant-materials.file-import') }}"
      $.ajax({
                method: "post",
                url: url,
                data: formData,
                cache: false,
                contentType: false,
                processData: false,


                success: function(response) {
                    $('.showSuccessMessage').html(`
                    <div class="alert alert-success alert-dismissible fade show showSuccessMessage">
                        File imported successfully!.
                        </div>

                    `);

                    getMaterialFilters('plantMaterial');
                    plant_materials_table.draw();

                },
                error: function(response) {
                    $.each(response.responseJSON.errors, function(field_name, error) {
                        $('.showSuccessMessage').html('');

                        $('.showSuccessMessage').append(`
                           <div class="alert alert-danger alert-dismissible fade show showSuccessMessage">
                               ${error}
                           </div>

                        `);
                    });
                }
            });

        });

    });

    function openImageUpload(materialId) {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.onchange = (e) => {
            const file = e.target.files[0];
            uploadImage(materialId, file);
        };
        input.click();
    }

    function uploadImage(materialId, file) {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('material_id', materialId);
        const url = "{{ URL::route(getRouteAlias() . '.hard-material-image-upload') }}"
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                // Refresh DataTable
                $('.yajra-datatable').DataTable().ajax.reload();

                // Optional: Show a small notification
                toastr.success('Image uploaded successfully');
            },
            error: function(xhr) {
                // Handle error
                toastr.error(xhr.responseJSON.message || 'Image upload failed');
            }
        });
    }

    function deleteImage(materialId) {
        let url = "{{ URL::route(getRouteAlias() . '.hard-material-image-delete',':key') }}"
        url = url.replace(':key',materialId);
        $.ajax({
            url: url,
            type: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('.yajra-datatable').DataTable().ajax.reload();

                // Optional: Show a small notification
                toastr.success('Image deleted successfully');
            },
            error: function(xhr) {
                // Handle error
                toastr.error(xhr.responseJSON.message || 'Image deletion failed');
            }
        });
    }
</script>
