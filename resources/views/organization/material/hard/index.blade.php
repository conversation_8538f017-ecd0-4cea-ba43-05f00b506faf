@extends('layouts.admin.master')
@section('title', 'Settings')

@section('section')
    <style>
        .select2-container--default{
            margin-top: 21px !important;
        }
        .table_filter_dropdown{
            margin-top: -45px !important;
        }
        .dropdown-toggle::after {
            display: none !important;

        }
        .table_tabs_filter input:checked + label{
            color: #FFFFFF;
            border: 1.5px solid var(--primaryblue);
            background-color: var(--primaryblue);
            padding: 7px;
            border-radius: 6px;
        }
        .table_tabs_filter .btn-tab {
            padding: .375rem .75rem;
        }
        .table_tabs_filter {
            align-items: center;
        }
        .delete-material-image i {
            color: #FFFFFF !important;
        }
    </style>

    <section class="dashboard_main">
        <div class="settings_tab_grid">
            <x-settings_component.settings_tab />

            <div class="settings_content">


                {{-- <select name="" id="select-division" style="padding: 9px;
    margin: 12px 0px;
    background: transparent;
    border: 1.5px solid #E3E1E1FF;
    border-radius: 5px; padding-right: 30px;">
            <option selected disabled>Select Division</option>
            @foreach($div as $item)
                    <option value="{{$item->id}}">{{$item->name}}</option>
            @endforeach
                </select> --}}

                <div class="d-flex align-items-center gap-4 flex-wrap">
                    <a href="{{ route(getRouteAlias() . '.materials.index') }}" style="margin-left: 0px !important;" class="btn primaryblue ">Materials</a>
                    <a href="{{ route(getRouteAlias() . '.material.equipment.index') }}"
                       class="btn placeholder_btn px-5">Equipment</a>
                    <a href="{{ route(getRouteAlias() . '.material.jobCost.index') }}" class="btn placeholder_btn px-5">Other
                        Job Cost</a>
                    <a href="{{ route(getRouteAlias() . '.material.service-line.index') }}" class="btn placeholder_btn px-5">Service Line</a>
                    <!-- <a href="{{ route(getRouteAlias() . '.material.labor.index') }}" class="btn placeholder_btn px-5">Labor</a> -->
                    <a href="{{ route(getRouteAlias() . '.material.contractor.index') }}" class="btn  placeholder_btn px-5">Sub Contractor</a>
                </div>

                <div class="table_tabs_filter mt-4 mb-4 nav" role="group">
                    <input type="radio" class="btn-check" name="material" id="hard_material" autocomplete="off" checked>
                    <label class="btn btn-tab" style="text-align: left;margin-left: -21px;" for="hard_material" data-toggle="tab" data-target="#hard-material"
                           aria-controls="hard-material">Hard Materials</label>

                    <input type="radio" class="btn-check" name="material" id="plan_material" autocomplete="off">
                    <label class="btn btn-tab yajra_plant_material" style="text-align: left;margin-left: -21px;" for="plan_material" data-toggle="tab"
                           data-target="#plan-material" aria-controls="plan-material">Plant Materials</label>
                </div>

                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade show active" id="hard-material" role="tabpanel"
                         aria-labelledby="hard-material-tab">
                        <x-settings_component.materials.items_hard_materials_table />
                    </div>
                    <div class="tab-pane fade" id="plan-material" role="tabpanel" aria-labelledby="plan-material-tab">
                        <x-settings_component.materials.items_plan_materials_table />
                    </div>
                </div>


            </div>
        </div>

        <x-settings_component.help_modal />


    </section>
    @push('scripts')
        @include('organization.material.hard.script')
    @endpush
@endsection
