<script type="text/javascript">
    var materials_table;
    var status = '';

    $(document).ready(function() {

        materials_table = $('.yajra-datatable').DataTable({
            processing: true,
            responsive: true,
            // scrollX: true,
            serverSide: true,
            ajax: {
                url: "{{ route(getRouteAlias() . '.material.jobCost.table-list') }}",
                data: function(d) {
                    d.status = status
                }
            },
            columns: [{
                    data: 'name',
                    name: 'name',
                    orderable: false
                }, {
                    data: 'uom',
                    name: 'uom',
                    orderable: false
                }, {
                    data: 'cost',
                    name: 'cost',
                    orderable: false
                },
                // {
                //     data: 'gross_margin',
                //     name: 'gross_margin',
                //     orderable: false
                // },
                // {
                //     data: 'gross_margin',
                //     name: 'gross_margin',
                //     orderable: false
                // }


            ],
            language: {
                zeroRecords: "Sorry we could not find any results",
                paginate: {
                    "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                    "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
                },


            },
            dom: '<"top">rt<"bottom"lip><"clear">',
        });

        // Custom Search Filter
        $('.equipment_table_filters #filter_search').on('keyup', function() {
            materials_table.search(this.value).draw();
        });

        // Custom Select Filter
        $('.equipment_table_filters #select_filter').on('change', function() {
            var selectedValue = $(this).val();
            if (selectedValue == "Clear") {
                resetFilters();
            } else {
                status = selectedValue;
                materials_table.columns().search('').draw();
            }
        });


        // Reset all filters
        function resetFilters() {
            $('.materials_table_filters #filter_search').val('');
            $('.materials_table_filters #select_filter').val('');
            $('.materials_table_filters .table_tabs_filter input.all').prop('checked', true)
            planName = '';
            status = ''
            materials_table.search('').columns().search('').draw();
        }


        var fileInput = $(".upload_file_wrapper .input_file");
        var label = $(".upload_file_wrapper label");
        var selectedFile;

        // Add an event listener to the input field
        fileInput.on("change", function(event) {
            // Get the selected file
            selectedFile = event.target.files[0];

            // Check if the selected file is an Excel file
            if (
                selectedFile.type === "application/vnd.ms-excel" ||
                selectedFile.type ===
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            ) {
                // Get the name of the selected file
                var fileName = selectedFile.name;

                // Update the label text with the file name
                label.text(fileName);

                // Submit the form
                $(this).parent(".file_upload_button_form").submit();
            } else {
                // Reset the input field and label text
                fileInput.val("");
                label.text("Import Material");

                // Create a new element with the error message
                var newElement = $(
                    "<label class='error custom_error'>Only Excel File Is Allowed</label>"
                );

                // Append the new element after the input field
                $(this).after(newElement);

                // Fade out the error message after 3 seconds
                newElement.fadeOut(3000, function() {
                    $(this).remove(); // Remove the element from the DOM after it has faded out
                });
            }
        });


        $(".file_upload_button_form").on("submit", function(e) {
            e.preventDefault();
            let myForm = document.getElementById("equipment-material");
            var div=$('#select-division').val();
            let formData = new FormData(myForm);
            // formData.append('division', div);
            var url = "{{ URL::route(getRouteAlias() . '.jobCost.file-import') }}"


            $.ajax({
                method: "post",
                url: url,
                data: formData,
                cache: false,
                contentType: false,
                processData: false,


                success: function(response) {
                    $('.showMessage').html(`
                    <div class="alert alert-success alert-dismissible fade show showMessage">
                        File imported successfully!.
                        </div>

                    `);
                    materials_table.draw();

                },
                error: function(response) {
                    $.each(response.responseJSON.errors, function(field_name, error) {
                        $('.showMessage').html('');

                        $('.showMessage').append(`
                           <div class="alert alert-danger alert-dismissible fade show showMessage">
                               ${error}
                           </div>

                        `);
                    });
                }
            });

        });

    });
</script>
