@extends('layouts.admin.master')
@section('title', 'Opportunities')
@section('section')
<style>
    .status-completed{
        color: #22B8D6 !important;
    }
    .status-closed{
        color: #51566C !important;
    }
    .fixed-col {
    position: sticky !important;
    right: 0px !important;
    width: 10px !important;
    /* padding: 16px 5px !important; */
    background: white !important;
}
.dropdown-toggle::after {
    display: none !important;

}
</style>
    <section class="dashboard_main pb-5 opportunities_table_filters">
    <div class="showMessage"></div>
        <div class="table_filter_header oppListing mb-4">
            <h2 class="sub_heading">Opportunities</h2>

            <div class="filters">
                <input type="search" placeholder="Search" id="filter_search" class="opportunities_Detail_Search filter_search">

                <form method="POST" id="export-opportunity" action="{{ route(getRouteAlias() . '.export.opportunities') }}">
                    @csrf
                    <!-- <button type="submit" style="
                            border: none !important;
                            background-color: white !important;
                        " class="btn primaryblue transparent px-5">Export</button> -->
                        <button
                        type="submit"
                        class="btn primaryblue transparent px-5"
                        style="border: 1px solid var(--lightgray); color: black !important"
                    >
                        <svg
                            width="16"
                            height="16"
                            style="margin-top: 3px"
                            viewBox="0 0 18 18"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M6 12L9 9M9 9L12 12M9 9V15.75M15 12.5571C15.9161 11.8005 16.5 10.656 16.5 9.375C16.5 7.09683 14.6532 5.25 12.375 5.25C12.2111 5.25 12.0578 5.1645 11.9746 5.0233C10.9965 3.36363 9.19082 2.25 7.125 2.25C4.0184 2.25 1.5 4.7684 1.5 7.875C1.5 9.42458 2.12659 10.8278 3.14021 11.8451"
                                stroke="#51566C"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                        <span
                            class=""
                            style="font-size: 12px; color: #514f6e !important"
                            >&nbsp;Export</span
                        >
                    </button>
                </form>


                @can('add_opportunity')
                    <form method="POST" action="" id="opportunity-import" class="file_upload_button_form upload_file_wrapper w-fit" enctype="multipart/form-data">
                        @csrf
                        <!-- <label class="btn primaryblue transparent px-5" for="import_file_data">Import Opportunities</label> -->
                        <label
                        class="btn primaryblue primaryblue22 transparent px-5 d-none"
                        for="import_file_data"
                        style="
                            border: 1px solid var(--lightgray);
                            background-color: white !important;
                            margin-top: 4px;
                        "
                        ><svg
                            style="margin-top: 3px"
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M5 5L8 8M8 8L11 5M8 8V1.25M14 4.44287C14.9161 5.19947 15.5 6.34405 15.5 7.625C15.5 9.90317 13.6532 11.75 11.375 11.75C11.2111 11.75 11.0578 11.8355 10.9746 11.9767C9.99654 13.6364 8.19082 14.75 6.125 14.75C3.0184 14.75 0.5 12.2316 0.5 9.125C0.5 7.57542 1.12659 6.17219 2.14021 5.15485"
                                stroke="#51566C"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                        &nbsp;
                        <span style="font-size: 12px; color: #514f6e !important"
                            >Import</span
                        ></label
                    >
                        <input class="input_file d-none" type="file" name="file" id="import_file_data" accept=".xls, .xlsx">
                    </form>
                    <a href="{{ route(getRouteAlias() . '.create.opportunity') }}" style="border: 1px solid var(--lightgray);" class="btn primaryblue">+ Add New Opportunity</a>
                @endcan
            </div>
        </div>

        <!-- <div class="showMessage"></div> -->
<div class="table-responsive">
        <table class="table table-striped custom_datatable yajra-datatable" id="opp_list" style="width:100%">
            <thead>
                <tr>
                    <th>{{ __('OPP #') }}</th>
                    <th>{{ __('Opportunity Name') }}</th>
                    <th>{{ __('Property Name') }}</th>
                    <th>{{ __('Account') }}</th>
                    <th>{{ __('Opportunity Owner') }}</th>
                    <th>{{ __('Opportunity Type') }}</th>
                    <th>{{ __('Division') }}</th>
                    <th>{{ __('Service Line') }}</th>
                    <th class="text-center">{{ __('Status') }}</th>
                    <th>{{ __('Date & Time') }}</th>
                    <th class="fixed-col" style="background-color: #dcf2ff !important;">{{ __('Action') }}</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
        </div>
    </section>
    @include('layouts.admin.confirmation-modal')
    @include('layouts.partials.success-modal')
    <div class="modal-small success-modal modal fade" id="requestModal" data-keyboard="false" tabindex="-1"
        aria-labelledby="requestModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">

                <div class="modal-body">
                    <div class="text-center mb-4">
                        <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/check-icon.png') }}"
                            alt="check icon">
                    </div>

                    <h2 class="title text-center">Opportunity Created</h2>
                    <p class="para mt-3 text-center">Your opportunity has been successfully created.</p>
                    <a href="{{ route(getRouteAlias() . '.estimate.index') }}" type="button"
                        class="btn primaryblue w-100 mt-5 " data-dismiss="modal">Go to list</a>
                </div>

            </div>

        </div>
    </div>

    <div class="modal-small success-modal modal fade" id="requestModalUpdate" data-keyboard="false" tabindex="-1"
        aria-labelledby="requestModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">

                <div class="modal-body">
                    <div class="text-center mb-4">
                        <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/check-icon.png') }}"
                            alt="check icon">
                    </div>

                    <h2 class="title text-center">Opportunity Updated</h2>
                    <p class="para mt-3 text-center">Your opportunity has been successfully updated.</p>
                    <a href="{{ route(getRouteAlias() . '.estimate.index') }}" type="button"
                        class="btn primaryblue w-100 mt-5 " data-dismiss="modal">Go to list</a>
                </div>

            </div>

        </div>
    </div>

    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered delete-modal">
            <div class="modal-content">
                <div class="modal-body">
                    {{-- <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button> --}}
                    <img src="{{ asset('admin_assets/images/icons/delete-icon.svg') }}" alt="delete icon"
                        class="delete-icon">
                    <h2 class="delete-request">Want to remove</h2>
                    <p class="are-sure">Are you sure you want to Remove this Opportunity?</p>
                    <div class="buttons-wraper">
                        <button type="button" class="cancel-btn" data-dismiss="modal">Cancel</button>
                        <button type="button" class="conform-btn">Yes</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        @include('organization.opportunity.script')
        <script>
            $('#opp_list').on('click', 'tr', function () {
    var url = $(this).data('url');  // Get the URL from the data-url attribute
    console.info(url);
    if (url) {
        window.location.href = url;  // Redirect to the link
    }
});
        </script>
        <script>
            $(document).ready(function() {
    const table = $('#opp_list').DataTable();

    // Add 'fixed-col' class to the last <td> in each row after DataTables has rendered the rows
    table.on('draw', function() {
        $('#opp_list tbody tr').each(function() {
            $(this).find('td:last-child').addClass('fixed-col');
        });
    });
});
        </script>
        @if (!empty(Session::get('showModal')))
            <script>
                $(function() {
                    $('#requestModal').modal('show');
                });
            </script>
        @endif
        @if (!empty(Session::get('showModalUpdate')))
            <script>
                $(function() {
                    $('#requestModalUpdate').modal('show');
                });
            </script>
        @endif
    @endpush
@endsection





