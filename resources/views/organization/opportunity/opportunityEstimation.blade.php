@extends('layouts.admin.master')
@section('title', 'Create Estimation')
@section('styles')
    <style>
        button:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        .divisions:focus {
            outline: none;
            box-shadow: none;

        }

        :root {
            --primary: white;
            --secondary: #ff5252;
            --background: #eee;
            --highlight: #ffda79;
            /* Theme color */
            --theme: var(--primary);
        }

        *,
        *::before,
        *::after {
            box-sizing: border-box;
        }

        .tab__label::after {
            color: #3E4756;
        }

        /* Core styles/functionality */
        .tab input {
            position: absolute;
            opacity: 0;
            z-index: -1;
        }

        .tab__content {
            max-height: 0;
            overflow: hidden;
            transition: all 0.35s;
        }

        .tab input:checked~.tab__content {
            max-height: fit-content;
        }

        /* Visual styles */
        .accordion {
            width: 100%;
            color: var(--theme);
            border: 2px solid;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .tab__label,
        .tab__close {
            display: flex;
            color: #90A0B7;
            background: var(--theme);
            cursor: pointer;
        }

        .tab__label {
            justify-content: space-between;
            padding: 1rem 0rem;
        }

        .tab__label::after {
            content: "\276F";
            width: 1em;
            height: 1em;
            text-align: center;
            transform: rotate(90deg);
            transition: all 0.35s;
        }

        .tab input:checked+.tab__label::after {
            transform: rotate(270deg);
            margin-right: 13px;
        }

        .tab__content p {
            margin: 0;
            padding: 1rem;
        }

        .tab__close {
            justify-content: flex-end;
            padding: 0.5rem 1rem;
            color: #90A0B7;
            font-size: 0.75rem;
        }

        .accordion--radio {
            --theme: var(--secondary);
        }

        /* Arrow animation */
        .tab input:not(:checked)+.tab__label:hover::after {
            animation: bounce .5s infinite;
        }

        @keyframes bounce {
            25% {
                transform: rotate(90deg) translate(.25rem);
            }

            75% {
                transform: rotate(90deg) translate(-.25rem);
            }
        }

        .accbtn:focus {
            outline: none;
            outline-color: white !important;
            box-shadow: none !important;
        }

        .dotss {
            width: 30px;
            height: 30px;
            padding: 0px 0px 0px 0px;
            text-align: center;
            border-radius: 5px;

            background: #F6F6F6;
            border: 1px solid #DFDFDF
        }

        .dnload {
            border-radius: 6px;
            border: 1.5px solid #0074D9
        }

        .dropdown-toggle::after {
            display: none !important;
            width: 0;
            height: 0;
            margin-left: .255em;
            vertical-align: .255em;
            content: "";
            border-top: .3em solid;
            border-right: .3em solid transparent;
            border-bottom: 0;
            border-left: .3em solid transparent;
        }

        .btn-sm {
            width: 22px;
            height: 22px;
        }

        .textcolorprimary {
            color: #7C8091;
        }

        .bottombox {
            width: 9.425%;
            border-right: 1px solid #E2E4EA;
            padding: 2px 10px;
            box-sizing: border-box;
        }

        .bottombox2 {
            width: 15%;
            padding: 2px 10px;
            box-sizing: border-box;
        }

        .panel2 {
            background: #ffffff;
            position: fixed;
            bottom: 0;
            height: 55px;
            box-shadow: 0px 0px 12px rgba(36, 185, 236, 0.08);
            border-radius: 8px;
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 0;
            /* Default no gap for larger screens */
        }

        .bottombox p {
            display: flex !important;
            justify-content: end !important;
            font-size: 14px;
        }

        .bottombox label {
            color: #0074d9;
            font-weight: bold;
            font-size: 15px;
        }

        .bottombox2 p {
            display: flex !important;
            justify-content: end !important;
            font-size: 14px;
        }

        .bottombox2 label {
            color: #0074d9;
            font-weight: bold;
            font-size: 15px;
        }

        /* Responsive adjustments */
        @media screen and (max-width: 768px) {
            .panel2 {
                gap: 10px !important;
                /* Space between the boxes on mobile */
                height: 100px;
                overflow-y: auto;
            }

            .bottombox,
            .bottombox2 {
                width: calc(25% - 10px);
                /* 2 items per row with space between */
                border-right: none;
                /* Remove border on mobile */
                padding: 10px;
                box-sizing: border-box;
            }

            .bottombox p,
            .bottombox2 p {
                margin-left: 0;
                text-align: right;
            }
        }

        @media screen and (max-width: 480px) {
            .panel2 {
                gap: 10px;
                /* Maintain space between boxes */
            }

            .bottombox,
            .bottombox2 {
                width: calc(50% - 10px);
                /* Adjust for spacing */
                padding: 10px;
                box-sizing: border-box;
            }

            .bottombox p,
            .bottombox2 p {
                text-align: right;
                margin-left: 0;
            }
        }

        .accordion-button:focus {
            outline: none;
            box-shadow: none;
        }
    </style>
    <style>
        /* Placeholder style during drag-and-drop */
        .sortable-placeholder {
            background-color: #e0e0e0;
            height: 40px;
            /* Same as row height */
            border: 2px dashed #ccc;
            visibility: visible !important;
        }

        /* Highlight the section header when row is dragged over it */
        .section-header.hovered {
            border: 2px dashed #007bff;
        }

        /* Style for the row while dragging */
        .dragging {
            opacity: 0.6;
        }

        .editinput {
            display: none;
            border: none;
            background-color: transparent;
            border-bottom: 2px solid #ab9d9d;
        }

        .okbutn {
            display: none;
            padding: 4px 20px 0px 7px;
        }

        .hovered {
            /*border: 2px dashed #007bff; !* Border color for valid drop target *!*/
            /*background-color: rgba(0, 123, 255, 0.1); !* Optional: Change background color for better visibility *!*/
        }

        .section-header.hovered {
            border: 2px solid blue;
            /* Blue border when hovered */
            transition: border 0.2s ease;
            /* Smooth transition for the border */
        }
    </style>
    <style>
        .tab__label {
            color: #333;
        }

        .accordion .active label {
            color: #0074D9;
            font-weight: bolder;
        }

        .tab__content {
            display: none;
        }

        .accordion .active .tab__content {
            display: block;
        }

        .tab__content {
            margin-top: -16px !important;
        }

        .hard_active {
            height: 20px;
            padding: 3px 10px;
            color: white !important;
            gap: 8px;
            border-radius: 100px;
            font-size: 11px;
            border: none;
            text-align: center;
            cursor: pointer;
            background: #0074D9;
        }

        .accordion-checkbox:checked+.tab__label+.tab__content {
            display: block;
        }

        .remove_active {
            height: 20px;
            padding: 3px 10px;
            color: #A8ABB5 !important;
            gap: 8px;
            border-radius: 100px;
            font-size: 11px;
            border: 1.5px solid #A8ABB5;
            text-align: center;
            cursor: pointer;
            background: transparent;
        }

        #hardmaterials {
            display: block;
        }

        #plantmaterials {
            display: none;
        }

        .samebtn {
            border-radius: 20px;
            background-color: #007bff;
            color: white;
        }

        input {
            text-align: center;
        }

        .serchinput {
            border-right: none !important;
            font-size: 16px !important;
        }

        .serchinput::placeholder {
            color: #E7E7E7 !important;
            font-size: 16px !important;
            padding-top: 7px !important;
        }

        .serchinput:focus {
            outline: none !important;
            box-shadow: none !important;
            border-right: none !important;
        }

        .favtbtn {
            color: #7C8091 !important;
            border: 1.5px solid #A8ABB5 !important;
        }

        .samebtn {
            color: white !important;
        }

        .disabledbtn {
            background-color: #A8ABB5 !important;
        }

        .table thead th {
            vertical-align: bottom;
            border-bottom: none !important;
        }

        .table td,
        .table th {
            padding: .75rem;
            vertical-align: top;
            border-top: none !important;
        }

        td {
            vertical-align: middle !important;
        }

        .detail_info .text {
            font-family: "Cabin";
            font-style: normal;
            font-weight: 400;
            font-size: 1.3rem;
            line-height: 2.4rem;
            margin-top: -8px !important;
            color: #192a3e;
            word-break: break-all;
        }

        .hidemodelbtn {
            font-size: 18px !important;
            color: #7e8a9d !important;
        }

        .hidemodelbtn:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        .hidemodelbtn:hover {
            cursor: pointer !important;
        }

        @media screen and (max-width: 580px) {
            .hidemodelbtn {
                font-size: 15px !important;
                color: #7e8a9d !important;
            }
        }

        .open {
            display: flex !important;
            justify-content: flex-start !important;
            align-items: normal !important;
            text-align: center !important;
            width: 18% !important;
            border-top-left-radius: 20px !important;
            border-bottom-left-radius: 20px !important;
            height: 35px !important;
        }

        .note-toolbar {
            position: relative !important;
            display: flex !important;
        }

        .note-btn-group {
            position: relative;
            display: flex !important;
            margin-right: 8px;
        }

        .note-btn {
            display: inline-block;
            font-weight: 400;
            margin-bottom: 0;
            text-align: center;
            vertical-align: middle;
            touch-action: manipulation;
            cursor: pointer;
            background-image: none;
            white-space: nowrap;
            outline: 0;
            color: #333;
            background-color: #fff;
            border: 1px solid #dae0e5;
            padding: 0 7px !important;
            font-size: 14px;
            line-height: 1.4;
            border-radius: 3px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .note-editor.note-airframe .note-editing-area .note-editable,
        .note-editor.note-frame .note-editing-area .note-editable {
            padding: 25px !important;
            overflow: auto;
            word-wrap: break-word;
        }

        .show td table {
            min-height: 50px !important;
        }

        /*.sortable-table-body tr:not([class]):not([id]) {
                height: 100px !important;
            }
            .sortable-placeholder {
                height: 40px;
                background-color: #f0f0f0;
                border: 1px dashed #ccc;
            }
            .dragging {
                opacity: 0.6;
            }
            .hovered {
                background-color: #e0ffe0;
            }*/
        .items-input {
            text-align: left;
        }

        .menu-accordian div button,
        .menu-accordian div button b {
            cursor: pointer;
        }
    </style>
@endsection
@section('section')
    <section class="dashboard_main">
        <div class="row justify-content-around">
            <div class="col-lg-3 pr-0">
                <div class="panel mt-4" style="margin-bottom: 0px; padding: 3.4rem !important;">
                    {{-- <div class="panel_header justify-content-between align-items-center"> --}}
                    {{-- <div class="dropdown-wrapper"> --}}
                    <select class="divisions form-control w-100" id="getdivision"
                        onchange="changematerials()" style="border: none; display: none;" id=""
                        data-id="">
                        <option selected disabled>Select Division</option>
                        @foreach ($div as $item)
                            <option value="{{ $item->id }}">{{ $item->name }}</option>
                        @endforeach
                    </select>
                    {{-- </div>  </div> --}}
                    <div class="row justify-center buttonss">
                        <a type="button" class="samebtn btn mx-auto allbtn" onclick="changematerials()"
                            style="padding: 3px 0 !important; padding-top: 5px !important; width: 45%; font-size: 15px">All</a>
                        <a type="button" class="btn mx-auto difbtn favtbtn" onclick="getFavItem()"
                            style="padding: 3px 0 !important; width: 45%; font-size: 15px">Favorite</a>
                    </div>
                    <div class="row mt-4">
                        <div class="input-group">
                            <input type="text" class="form-control serchinput serchinputss py-3"
                                oninput="searchItems()"
                                style="border: 1px solid #E7E7E7; padding: 6px 8px 6px 8px !important; border-top-left-radius: 6px; border-bottom-left-radius: 6px; text-align: left !important;"
                                placeholder="Search">
                            <div class="input-group-append input-group-append2"
                                style="border: 1px solid #E7E7E7; border-top-right-radius: 6px; border-bottom-right-radius: 6px;">
                                <button class="btn bg-white px-3" type="button"
                                    style="border-top-right-radius: 6px;
                                    border-bottom-right-radius: 6px; padding-top: 9px !important;">
                                    <svg width="16" height="16" viewBox="0 0 16 16"
                                        fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M16 15.1632L11.5307 10.6938C12.6047 9.40448 13.1402 7.75068 13.026 6.07649C12.9117 4.40231 12.1564 2.83663 10.9171 1.70516C9.67784 0.5737 8.05008 -0.036435 6.37243 0.00168439C4.69478 0.0398038 3.09641 0.723243 1.90983 1.90983C0.723243 3.09641 0.0398038 4.69478 0.00168439 6.37243C-0.036435 8.05008 0.5737 9.67784 1.70516 10.9171C2.83663 12.1564 4.40231 12.9117 6.07649 13.026C7.75068 13.1402 9.40448 12.6047 10.6938 11.5307L15.1632 16L16 15.1632ZM1.20478 6.53106C1.20478 5.47762 1.51716 4.44784 2.10242 3.57194C2.68768 2.69604 3.51953 2.01335 4.49278 1.61022C5.46603 1.20709 6.53697 1.10161 7.57016 1.30712C8.60336 1.51264 9.55241 2.01992 10.2973 2.76481C11.0422 3.50971 11.5495 4.45876 11.755 5.49195C11.9605 6.52515 11.855 7.59609 11.4519 8.56934C11.0488 9.54259 10.3661 10.3744 9.49018 10.9597C8.61428 11.545 7.5845 11.8573 6.53106 11.8573C5.11892 11.8558 3.76507 11.2941 2.76654 10.2956C1.76801 9.29705 1.20635 7.9432 1.20478 6.53106Z"
                                            fill="#A0A3BD" />
                                    </svg>

                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row itemsContainerss">
                        <ul id="itemsContainer" style="width: 100%;"></ul>
                    </div>
                    <div class="row mt-1" id="accordion-container">
                        <section class="accordion" style="">
                            <div class="tab">
                                <input type="checkbox" name="accordion-1" id="cbb"
                                    class="accordion-checkbox">
                                <label for="cbb" class="tab__label">Equipment</label>
                                <div class="tab__content">
                                    <ul id="equipmentdata" style="">

                                        @foreach ($equipment as $item)
                                            @php
                                                // Check if the labor item is in the list of item_ids from estimate_items
                                                $isDisabledequipment = in_array(
                                                    $item->id,
                                                    $equipmentsitems,
                                                );

                                                if ($item->is_favorite == 0) {
                                                    $sfg =
                                                        '<svg width="16" id="nofav-' .
                                                        $item->id .
                                                        '" height="14" onclick="addItemToFavourite(' .
                                                        $item->id .
                                                        ', \'equipment\')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path class="equc-' .
                                                        $item->id .
                                                        '" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>';
                                                } elseif ($item->is_favorite == 1) {
                                                    $sfg =
                                                        '<svg width="16" onclick="deleteItemToFavourite(' .
                                                        $item->id .
                                                        ', \'equipment\')" id="yesfav-' .
                                                        $item->id .
                                                        '" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke="red" class="equc-' .
                                                        $item->id .
                                                        '" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>';
                                                }

                                            @endphp
                                            <li class="menu-accordian mt-4">
                                                <div class="row">
                                                    <div class="col-1"
                                                        style="margin-top: 2px !important;">
                                                        {!! $sfg !!}
                                                    </div>
                                                    <div class="col-7 text-left">
                                                        <span
                                                            style="text-transform: capitalize;">{{ $item->name }}</span>
                                                    </div>
                                                    <div id="{{ $item->id }}" class="col-3 text-end"
                                                        style="padding-right: 0px;">
                                                        <button
                                                            id="equipmentplusbtn-{{ $item->id }}"
                                                            onclick="addItemTo({{ $item->id }})"
                                                            style="width: 24px;
              height: 24px;
              border-radius: 50%;
              text-align: center;
              margin-left: auto;
              color: white;
              border: none;
              font-size: 18px;
              background-color: #2FCC40;
              "><label
                                                                for=""><b
                                                                    style="color: white; font-size: 17px;">+</b></label></button>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                            <div class="tab">
                                <input type="checkbox" name="accordion-1" id="cb2"
                                    class="accordion-checkbox">
                                <label for="cb2" class="tab__label">Labor</label>
                                <div class="tab__content">
                                    <ul id="laborMenu" style="">
                                        @foreach ($labors as $item)
                                            @php
                                                // Check if the labor item is in the list of item_ids from estimate_items
                                                $isDisabled = in_array($item->id, $equipitems);

                                            @endphp
                                            <?php
                                            if ($item->is_favorite == 0) {
                                                $sfg5 =
                                                    '<svg width="16" id="nofavlabor-' .
                                                    $item->id .
                                                    '" height="14" onclick="addItemToFavourite(' .
                                                    $item->id .
                                                    ', \'labors\')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                      <path class="equc-' .
                                                    $item->id .
                                                    '" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                                                      </svg>';
                                            } elseif ($item->is_favorite == 1) {
                                                $sfg5 =
                                                    '<svg width="16" onclick="deleteItemToFavourite(' .
                                                    $item->id .
                                                    ', \'labors\')" id="yesfavlabor-' .
                                                    $item->id .
                                                    '" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                      <path stroke="red" class="equc-' .
                                                    $item->id .
                                                    '" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                                                      </svg>';
                                            }

                                            ?>
                                            <li class="menu-accordian mt-4">
                                                <div class="row">
                                                    <div class="col-1"
                                                        style="margin-top: 2px !important;">
                                                        {!! $sfg5 !!}
                                                    </div>
                                                    <div class="col-7 text-left">
                                                        <span
                                                            style="text-transform: capitalize;">{{ $item->name }}</span>
                                                    </div>
                                                    <div id="{{ $item->id }}" class="col-3 text-end"
                                                        style="padding-right: 0px;">
                                                        <button id="laborplusbtn-{{ $item->id }}"
                                                            onclick="addItemLabor({{ $item->id }})"
                                                            style="width: 24px;
              height: 24px;
              border-radius: 50%;
              text-align: center;
              margin-left: auto;
              color: white;
              border: none;
              font-size: 18px;
              background-color: #2FCC40;
              "><label
                                                                for=""><b
                                                                    style="color: white; font-size: 17px;">+</b></label></button>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach

                                    </ul>
                                </div>
                            </div>
                            <div class="tab">
                                <input type="checkbox" name="accordion-1" id="cb3"
                                    class="accordion-checkbox">
                                <label for="cb3" class="tab__label">Materials</label>
                                <div class="tab__content" style="margin-top: -9px !important">

                                    <a onclick="showhardItem()"
                                        class="hardbtnactive hard_active mx-3">Hard</a>
                                    <a onclick="showplantItem()"
                                        class="plantbtnactive remove_active">Plant</a>

                                    <ul id="hardmaterials" style="">
                                        @foreach ($hard_materials as $item)
                                            @php
                                                // Check if the labor item is in the list of item_ids from estimate_items
                                                $isDisabledhard = in_array($item->id, $hardssitems);

                                            @endphp
                                            <?php
                                            if ($item->is_favorite == 0) {
                                                $sfg2 =
                                                    '<svg width="16" id="nofavhard-' .
                                                    $item->id .
                                                    '" height="14" onclick="addItemToFavourite(' .
                                                    $item->id .
                                                    ', \'hard_materials\')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                              <path class="equc-' .
                                                    $item->id .
                                                    '" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                                                              </svg>';
                                            } elseif ($item->is_favorite == 1) {
                                                $sfg2 =
                                                    '<svg width="16" onclick="deleteItemToFavourite(' .
                                                    $item->id .
                                                    ', \'hard_materials\')" id="yesfavhard-' .
                                                    $item->id .
                                                    '" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                              <path stroke="red" class="equc-' .
                                                    $item->id .
                                                    '" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                                                              </svg>';
                                            }
                                            ?>
                                            <li class="menu-accordian mt-4">
                                                <div class="row">
                                                    <div class="col-1"
                                                        style="margin-top: 2px !important;">
                                                        {!! $sfg2 !!}
                                                    </div>
                                                    <div class="col-7 text-left">
                                                        <span
                                                            style="text-transform: capitalize;">{{ $item->name }}</span>
                                                    </div>
                                                    <div id="{{ $item->id }}"
                                                        class="col-3 text-end"
                                                        style="padding-right: 0px;">
                                                        <button id="hardmplusbtn-{{ $item->id }}"
                                                            onclick="addItemMaterial({{ $item->id }}, 'hard_materials')"
                                                            style="width: 24px;
              height: 24px;
              border-radius: 50%;
              text-align: center;
              margin-left: auto;
              color: white;
              border: none;
              font-size: 18px;
              background-color: #2FCC40;
              "><label
                                                                for=""><b
                                                                    style="color: white; font-size: 17px;">+</b></label></button>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                    <ul id="plantmaterials" style="">
                                        @foreach ($plant_materials as $item)
                                            @php
                                                // Check if the labor item is in the list of item_ids from estimate_items
                                                $isDisabledplant = in_array(
                                                    $item->id,
                                                    $plantsitems,
                                                );

                                            @endphp
                                            <?php
                                            if ($item->is_favorite == 0) {
                                                $sfg3 =
                                                    '<svg width="16" id="nofavplant-' .
                                                    $item->id .
                                                    '" height="14" onclick="addItemToFavourite(' .
                                                    $item->id .
                                                    ', \'plant_materials\')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                              <path class="equc-' .
                                                    $item->id .
                                                    '" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                                                              </svg>';
                                            } elseif ($item->is_favorite == 1) {
                                                $sfg3 =
                                                    '<svg width="16" onclick="deleteItemToFavourite(' .
                                                    $item->id .
                                                    ', \'plant_materials\')" id="yesfavplant-' .
                                                    $item->id .
                                                    '" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                              <path stroke="red" class="equc-' .
                                                    $item->id .
                                                    '" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                                                              </svg>';
                                            }
                                            ?>
                                            <li class="menu-accordian mt-4">
                                                <div class="row">
                                                    <div class="col-1"
                                                        style="margin-top: 2px !important;">
                                                        {!! $sfg3 !!}
                                                    </div>
                                                    <div class="col-7 text-left">
                                                        <span
                                                            style="text-transform: capitalize;">{{ $item->name }}</span>
                                                    </div>
                                                    <div id="{{ $item->id }}"
                                                        class="col-3 text-end"
                                                        style="padding-right: 0px;">
                                                        <button id="plantmplusbtn-{{ $item->id }}"
                                                            onclick="addItemMaterial({{ $item->id }}, 'plant_materials')"
                                                            style="width: 24px;
              height: 24px;
              border-radius: 50%;
              text-align: center;
              margin-left: auto;
              color: white;
              border: none;
              font-size: 18px;
              background-color: #2FCC40;
              "><label
                                                                for=""><b
                                                                    style="color: white; font-size: 17px;">+</b></label></button>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach

                                    </ul>
                                </div>
                            </div>

                            <div class="tab">
                                <input type="checkbox" name="accordion-1" id="cb4"
                                    class="accordion-checkbox">
                                <label for="cb4" class="tab__label">Other Cost</label>
                                <div class="tab__content">
                                    <ul id="othercost" style="">
                                        @foreach ($other_job_costs as $item)
                                            @php
                                                // Check if the labor item is in the list of item_ids from estimate_items
                                                $isDisabledother = in_array(
                                                    $item->id,
                                                    $othersitems,
                                                );
                                            @endphp
                                            <?php
                                            if ($item->is_favorite == 0) {
                                                $sfg4 =
                                                    '<svg width="16" id="nofavother-' .
                                                    $item->id .
                                                    '" height="14" onclick="addItemToFavourite(' .
                                                    $item->id .
                                                    ', \'other_costs\')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                      <path class="equc-' .
                                                    $item->id .
                                                    '" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                                      </svg>';
                                            } elseif ($item->is_favorite == 1) {
                                                $sfg4 =
                                                    '<svg width="16" onclick="deleteItemToFavourite(' .
                                                    $item->id .
                                                    ', \'other_costs\')" id="yesfavother-' .
                                                    $item->id .
                                                    '" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                      <path stroke="red" class="equc-' .
                                                    $item->id .
                                                    '" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                                      </svg>';
                                            }

                                            ?>
                                            <li class="menu-accordian mt-4">
                                                <div class="row">
                                                    <div class="col-1"
                                                        style="margin-top: 2px !important;">
                                                        {!! $sfg4 !!}
                                                    </div>
                                                    <div class="col-7 text-left">
                                                        <span
                                                            style="text-transform: capitalize;">{{ $item->name }}</span>
                                                    </div>
                                                    <div id="{{ $item->id }}"
                                                        class="col-3 text-end"
                                                        style="padding-right: 0px;">
                                                        <button id="otherplusbtn-{{ $item->id }}"
                                                            onclick="addItemCost({{ $item->id }})"
                                                            style="width: 24px;
              height: 24px;
              border-radius: 50%;
              text-align: center;
              margin-left: auto;
              color: white;
              border: none;
              font-size: 18px;
              background-color: #2FCC40;
              "><label
                                                                for=""><b
                                                                    style="color: white; font-size: 17px;">+</b></label></button>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                            @foreach ($sub_contractor as $item)
                                @php
                                    // Check if the labor item is in the list of item_ids from estimate_items
                                    $isDisabledcont = in_array($item->id, $contractorsitems);

                                @endphp
                                <?php
                                if ($item->is_favorite == 0) {
                                    $sfg6 =
                                        '<svg width="16" id="nofavcontractor-' .
                                        $item->id .
                                        '" height="14" onclick="addItemToFavourite(' .
                                        $item->id .
                                        ', \'contractors\')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path class="equc-' .
                                        $item->id .
                                        '" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                          </svg>';
                                } elseif ($item->is_favorite == 1) {
                                    $sfg6 =
                                        '<svg width="16" onclick="deleteItemToFavourite(' .
                                        $item->id .
                                        ', \'contractors\')" id="yesfavcontractor-' .
                                        $item->id .
                                        '" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path stroke="red" class="equc-' .
                                        $item->id .
                                        '" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                          </svg>';
                                }

                                ?>
                                <li class="menu-accordian mt-4">
                                    <div class="d-flex justify-content-between align-items-center">
                                        {{--                                        <div class="col-1" style="margin-top: 2px !important;"> --}}
                                        {{--                                            {!!$sfg6!!} --}}
                                        {{--                                        </div> --}}
                                        <div class="col-7 text-left px-0">
                                            <span
                                                style="text-transform: capitalize;">{{ $item->name }}</span>
                                        </div>
                                        <div id="{{ $item->id }}" class="col-3 text-end"
                                            style="padding-right: 0px;">
                                            <button id="contractorplusbtn-{{ $item->id }}"
                                                onclick="addItemContractor({{ $item->id }})"
                                                style="width: 24px;
              height: 24px;
              border-radius: 50%;
              text-align: center;
              /*margin-left: 6rem;*/
              color: white;
              border: none;
              font-size: 18px;
              background-color: #2FCC40;
              cursor: pointer;    font-weight: 600;">
                                                {{--                                                <label for=""><b style="color: white; font-size: 17px;">+</b></label> --}}
                                                +
                                            </button>
                                        </div>
                                    </div>
                                </li>
                            @endforeach
                            <!-- <div class="tab">
                <input type="checkbox" name="accordion-1" id="cb5" class="accordion-checkbox">
                <label for="cb5" class="tab__label">Sub Contractor</label>
                <div class="tab__content">
                  <ul id="sub_contractor_list" style="">

                  </ul>
                </div>
              </div> -->
                        </section>

                    </div>
                </div>
            </div>
            <div class="col-lg-9">
                <div class="accordion panel mt-4" style="padding: 0.5rem !important;"
                    id="accordionExample">
                    <div class="accordion-item form-control" style="border: none;">
                        <h2 class="accordion-header"
                            style="line-height: 0rem !important; margin-left: -1% !important;"
                            id="headingOne">
                            <button
                                class="accordion-button form-control accbtn text-left d-flex justify-content-between"
                                style="border: none;" type="button" data-toggle="collapse"
                                data-target="#collapseOne" aria-expanded="true"
                                aria-controls="collapseOne">
                                <h2>{{ $opportunity->opportunity_name }} <span
                                        style="color: #7C8091; font-size: 12px;">&nbsp;
                                        &nbsp;Opp#{{ $opportunity->opportunity_count }}</span></h2>
                                <label for=""
                                    style="width: 30px;
                                height: 30px;
                                padding: 7px 0px 0px 0px;
                                gap: 0px;
                                border-radius: 5px;
                                text-align: center;
                                border: 1px;
                                opacity: 0;
                                angle: -180 deg;
                                background: #F6F6F6;">
                                    <svg style="" width="10" height="6"
                                        viewBox="0 0 10 6" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 1L5 5L1 1" stroke="#667085" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </label>

                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse show"
                            aria-labelledby="headingOne" style="margin-top: -7px !important;"
                            data-parent="#accordionExample">
                            <div class="accordion-body">
                                <div class="">

                                    <div class="row d-flex justify-content-between">
                                        <div class="col-lg-2 col-md-4 col-sm-6 pt-1">
                                            <div class="detail_info">
                                                <label class="label">Account</label>
                                                <p class="text">
                                                    {{ $opportunity->account->company_name }}</p>
                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-md-4 col-sm-6 pt-1">
                                            <div class="detail_info">
                                                <label class="label">Property Name</label>
                                                <p class="text">
                                                    {{ $opportunity->propertyInformation->name }}</p>
                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-md-4 col-sm-6 pt-1">
                                            <div class="detail_info">
                                                <label class="label">Opportunity Owner</label>
                                                <p class="text">
                                                   {{ $opportunity->account_owner_name }}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-md-4 col-sm-6 pt-1">
                                            <div class="detail_info">
                                                <label class="label">Opportunity Type</label>
                                                <p class="text">{{ $opportunity->opportunity_type }}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-md-4 col-sm-6 pt-1">
                                            <div class="detail_info">
                                                <label class="label">Salesman</label>
                                                <p class="text">
                                                    {{ $opportunity->salesman?->first_name }}
                                                    {{ $opportunity->salesman?->last_name }}</p>
                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-md-4 col-sm-6 pt-1">
                                            <div class="detail_info">
                                                <label class="label">Estimator Name</label>
                                                <p class="text">
                                                    {{ $opportunity->estimator?->first_name }}
                                                    {{ $opportunity->estimator?->last_name }}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <input type="hidden" name="" value=""
                                        id="opportunity-id">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-6">
                        <h3>Estimate</h3>
                    </div>
                    <div class="col-6">
                        <div class="d-flex" style="margin-left: auto; justify-content: end; ">
                            <!-- <a href="#" class="dotss"><svg width="14" height="4" viewBox="0 0 14 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7.00065 2.66665C6.63246 2.66665 6.33398 2.36817 6.33398 1.99998C6.33398 1.63179 6.63246 1.33331 7.00065 1.33331C7.36884 1.33331 7.66732 1.63179 7.66732 1.99998C7.66732 2.36817 7.36884 2.66665 7.00065 2.66665Z" stroke="#667085" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M2.33398 2.66665C1.9658 2.66665 1.66732 2.36817 1.66732 1.99998C1.66732 1.63179 1.9658 1.33331 2.33398 1.33331C2.70217 1.33331 3.00065 1.63179 3.00065 1.99998C3.00065 2.36817 2.70217 2.66665 2.33398 2.66665Z" stroke="#667085" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M11.6673 2.66665C11.2991 2.66665 11.0007 2.36817 11.0007 1.99998C11.0007 1.63179 11.2991 1.33331 11.6673 1.33331C12.0355 1.33331 12.334 1.63179 12.334 1.99998C12.334 2.36817 12.0355 2.66665 11.6673 2.66665Z" stroke="#667085" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    </a> -->


                            <a href="{{ route('organization.estimation.download-pdf', ['opportunityId' => $opportunity->id]) }}"
                                class="btn px-3 dnload d-flex mx-3">
                                <svg width="18" height="18" viewBox="0 0 22 20"
                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M7 15L11 19M11 19L15 15M11 19V10M19 14.7428C20.2215 13.734 21 12.2079 21 10.5C21 7.46243 18.5376 5 15.5 5C15.2815 5 15.0771 4.886 14.9661 4.69774C13.6621 2.48484 11.2544 1 8.5 1C4.35786 1 1 4.35786 1 8.5C1 10.5661 1.83545 12.4371 3.18695 13.7935"
                                        stroke="#0074D9" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                                <b class="mt-1">&nbsp;&nbsp;Download</b>
                            </a>

                            <a href="{{ URL::route(getRouteAlias() . '.organization.preview', ['opportunityId' => encodeId($opportunity->id)]) }}"
                                class="btn px-4"
                                style="background-color:#E6F1FB; color: #117BDD; font-weight: 800;     align-items: center;
    justify-content: center;
    display: flex
;">Preview</a>
                            <!-- <a href="#" class="btn btn-primary px-4 mx-3 d-flex" onclick="saveBatchItems()" style="font-weight: 800; padding-top: 7px;">save as draft</a> -->

                            <a href="#" class="btn btn-primary px-4 mx-3 d-flex"
                                onclick="addSection()" style="font-weight: 800; padding-top: 7px;">+
                                Section</a>

                            <a href="#"
                                class="btn btn-primary px-4 mx-3 d-flex add-scope-btn-modal"
                                style="font-weight: 800; padding-top: 7px;" data-toggle="modal"
                                data-target="#definescope">Define Scope</a>

                            <a href="{{ URL::route(getRouteAlias() . '.organization.previewEstimation', ['opportunityId' => encodeId($opportunity->id)]) }}"
                                onclick="saveBatchItems()"
                                class="btn btn-large d-flex mx-3 btn-primary px-4"
                                style="font-weight: 800;">
                                <svg style="margin-top: 4px;" width="16" height="16"
                                    viewBox="0 0 22 20" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M7 15L11 19M11 19L15 15M11 19V10M19 14.7428C20.2215 13.734 21 12.2079 21 10.5C21 7.46243 18.5376 5 15.5 5C15.2815 5 15.0771 4.886 14.9661 4.69774C13.6621 2.48484 11.2544 1 8.5 1C4.35786 1 1 4.35786 1 8.5C1 10.5661 1.83545 12.4371 3.18695 13.7935"
                                        stroke="white" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                                <b style="margin-top: 4px; margin-left: 5px; color: white;">Send To
                                    Client</b></a>
                        </div>
                    </div>
                </div>


                <div class="row"> <!-- remove this class mt-4 for adjusting spacing from top -->
                    <div class="w-100" style="padding: 1.4rem !important;">
                        <div class="table-responsive">
                            <div class="panel" style="padding: 0px 0px 30px 0px !important"
                                id="estimateItemsTable">
                                <table class="table maintable main-table-dropable table-fixed"
                                    style="border-radius: 8px !important;">
                                    <colgroup>
                                        <col style="width: 4%;"> {{-- Drag handle --}}
                                        <col style="width: 16%;"> {{-- Item Name --}}
                                        <col style="width: 7%;"> {{-- Icon / Extra --}}
                                        <col style="width: 12%;"> {{-- Quantity --}}
                                        <col style="width: 9%;"> {{-- UoM --}}
                                        <col style="width: 9%;"> {{-- Unit Cost --}}
                                        <col style="width: 9%;"> {{-- Total Cost --}}
                                        <col style="width: 12%;"> {{-- Gross Margin --}}
                                        <col style="width: 9%;"> {{-- Total Price --}}
                                        <col style="width: 9%;"> {{-- Unit Price --}}
                                        <col style="width: 11%;"> {{-- Actions --}}
                                    </colgroup>
                                    <thead>
                                        <tr>
                                            <th scope="col">Items</th>
                                            <th scope="col"></th>
                                            <th class="border">Dimension</th>
                                            <th class="border" scope="col">Quantity</th>
                                            <th class="border" scope="col">UoM</th>
                                            <th class="border" scope="col">Unit Cost</th>
                                            <th class="border" scope="col">Total Cost</th>
                                            <th class="border" scope="col">Gross Margin</th>
                                            <th class="border" scope="col">Total Price</th>
                                            <th class="border" scope="col">Unit Price</th>
                                            <th class="border" scope="col"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="sortable-table-body" class="">
                                        @php
                                            $totalEquipmentAmount = 0;
                                            $totalLaborAmount = 0;
                                            $totalLaborHours = 0;
                                            $totalMaterialAmount = 0;
                                            $totalOtherAmount = 0;
                                            $totalSubcontractor = 0;
                                            $totalCostAmount = 0;
                                        @endphp

                                        @foreach ($sectionss as $item)
                                            @php $gitems = DB::table('estimate_items')->where('section_name', $item->id)->get(); @endphp
                                            <tr id="sectionParent-{{ $item->id }}"
                                                class="tablerows-{{ $item->id }} lastforsectionrow lastTypeSection"
                                                style="background: #f5faff">
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <input type="checkbox"
                                                            class="dynamic-checkbox d-flex align-items-center"
                                                            id="checkbox-{{ $item->id }}"
                                                            data-id="{{ $item->id }}"
                                                            title="Add Item" style="cursor: pointer;">
                                                    </div>
                                                </td>
                                                <td class="align-middle">
                                                    <div class="d-flex align-items-center">
                                                        <div id="section1Heading-{{ $item->id }}"
                                                            class="me-2 sectionNames">
                                                            {{ $item->section_name }}</div>
                                                        <input type="hidden"
                                                            style="text-align: left;"
                                                            class="editinputid"
                                                            id="editinputid-{{ $item->id }}"
                                                            data-sec-id="{{ $item->id }}"
                                                            value="{{ $item->id }}">
                                                        <input type="text"
                                                            style="text-align: left;"
                                                            value="{{ $item->section_name }}"
                                                            class="editinput"
                                                            id="editinput-{{ $item->id }}">
                                                        <a href="javascript:void(0)"
                                                            onclick="editSection2({{ $item->id }})"
                                                            id="editbutn-{{ $item->id }}"
                                                            class="text-decoration-none mt-1 mx-3 editbutn">
                                                            <svg class="" width="15"
                                                                height="15" viewBox="0 0 22 22"
                                                                fill="none"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M1.87604 17.1156C1.92198 16.7021 1.94496 16.4954 2.00751 16.3022C2.06301 16.1307 2.14143 15.9676 2.24064 15.8171C2.35246 15.6475 2.49955 15.5005 2.79373 15.2063L16 2C17.1046 0.895427 18.8955 0.895428 20 2C21.1046 3.10457 21.1046 4.89543 20 6L6.79373 19.2063C6.49955 19.5005 6.35245 19.6475 6.18289 19.7594C6.03245 19.8586 5.86929 19.937 5.69785 19.9925C5.5046 20.055 5.29786 20.078 4.88437 20.124L1.5 20.5L1.87604 17.1156Z"
                                                                    stroke="#7C8091" stroke-width="2"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round" />
                                                            </svg>
                                                        </a>
                                                        <a href="javascript:void(0)"
                                                            onclick="updateSection3({{ $item->id }})"
                                                            id="okbutn-{{ $item->id }}"
                                                            style="padding-right: 25px;"
                                                            class="btn btn-sm btn-primary text-decoration-none mt-1 mx-3 okbutn">
                                                            Save
                                                        </a>
                                                    </div>
                                                </td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td>
                                                    <div class="d-flex justify-content-center"
                                                        style="gap: 15px;">
                                                        <div class="dropdown">
                                                            <button
                                                                class="btn btn-sm bg-transparent dropdown-toggle"
                                                                type="button"
                                                                data-toggle="dropdown"
                                                                aria-expanded="false">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    width="16" height="16"
                                                                    fill="currentColor"
                                                                    class="bi bi-three-dots"
                                                                    viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z" />
                                                                </svg>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li>
                                                                    <a onclick="editSection2({{ $item->id }})"
                                                                        class="dropdown-item"
                                                                        href="javascript:void(0)">Edit</a>
                                                                </li>
                                                                <li>
                                                                    <a onclick="deleteSection3({{ $item->id }})"
                                                                        class="dropdown-item"
                                                                        href="javascript:void(0)">Delete</a>
                                                                </li>
                                                            </ul>
                                                        </div>

                                                        <button class="btn btn-sm"
                                                            id="mainbtnnow-{{ $item->id }}"
                                                            style="background: #f6f6f6; border: 1px solid #dfdfdf;"
                                                            type="button" data-toggle="collapse"
                                                            data-target="#section-{{ $item->id }}"
                                                            aria-expanded="false"
                                                            aria-controls="section-initial"
                                                            onclick="toggleAccordion2({{ $item->id }})">
                                                            <label for=""
                                                                class="changeiconall changeicon-{{ $item->id }}">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    width="12" height="12"
                                                                    fill="currentColor"
                                                                    class="bi bi-chevron-down arrow-icon "
                                                                    viewBox="0 0 16 16">
                                                                    <path fill-rule="evenodd"
                                                                        d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z" />
                                                                </svg>
                                                            </label>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>

                                            <tr id="section-{{ $item->id }}"
                                                class="collapse tavy tablerows-{{ $item->id }}">
                                                <td style="padding: 0 !important" colspan="11">
                                                    <div class="w-100 mb-4">
                                                        <table class="table table-fixed"
                                                            style="margin-bottom: 0;">
                                                            <!-- Define column widths to match parent table -->
                                                            <colgroup>
                                                                <col style="width: 4%;">
                                                                {{-- Drag handle --}}
                                                                <col style="width: 16%;">
                                                                {{-- Item Name --}}
                                                                <col style="width: 7%;">
                                                                {{-- Icon / Extra --}}
                                                                <col style="width: 12%;">
                                                                {{-- Quantity --}}
                                                                <col style="width: 9%;">
                                                                {{-- UoM --}}
                                                                <col style="width: 9%;">
                                                                {{-- Unit Cost --}}
                                                                <col style="width: 9%;">
                                                                {{-- Total Cost --}}
                                                                <col style="width: 12%;">
                                                                {{-- Gross Margin --}}
                                                                <col style="width: 9%;">
                                                                {{-- Total Price --}}
                                                                <col style="width: 9%;">
                                                                {{-- Unit Price --}}
                                                                <col style="width: 11%;">
                                                                {{-- Actions --}}
                                                                {{-- Actions --}}
                                                            </colgroup>
                                                            <tbody
                                                                class="sortable-content dropable-content-here accord-bodys accord-bodys-{{ $item->id }}"
                                                                data-section-id="{{ $item->id }}">
                                                                @foreach ($gitems as $data)
                                                                    @php
                                                                        if (
                                                                            $data->category_type ==
                                                                            'equipment'
                                                                        ) {
                                                                            $totalEquipmentAmount +=
                                                                                $data->total_price;
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'labors'
                                                                        ) {
                                                                            $totalLaborHours +=
                                                                                $data->quantity;
                                                                            $totalLaborAmount +=
                                                                                $data->total_price;
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                                'plant_materials' ||
                                                                            $data->category_type ==
                                                                                'hard_materials'
                                                                        ) {
                                                                            $totalMaterialAmount +=
                                                                                $data->total_price;
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'other_costs'
                                                                        ) {
                                                                            $totalOtherAmount +=
                                                                                $data->total_price;
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'contractors'
                                                                        ) {
                                                                            $totalSubcontractor +=
                                                                                $data->total_price;
                                                                        }
                                                                        $totalCostAmount +=
                                                                            $data->total_cost;
                                                                        $lb = '';
                                                                        $upida = '';
                                                                        $TJQ = '';
                                                                        if (
                                                                            $data->category_type ==
                                                                            'equipment'
                                                                        ) {
                                                                            $m = "Equipment's";
                                                                            $clid =
                                                                                'getitemsequipid2';
                                                                            $totalcostsequip =
                                                                                'totalcostsequip2';
                                                                            $unitcostsequ =
                                                                                'unitcostsequ2';
                                                                            $grossmarginequ =
                                                                                'grossmarginequ2';
                                                                            $quantityequ =
                                                                                'quantityequ2';
                                                                            $totalpriceequipmn =
                                                                                'totalpriceequi2';
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'hard_materials'
                                                                        ) {
                                                                            $m = 'Hard Material';
                                                                            $clid =
                                                                                'getitemshardid2';
                                                                            $totalcostsequip =
                                                                                'totalcostshard2';
                                                                            $unitcostsequ =
                                                                                'unitcostshar2';
                                                                            $grossmarginequ =
                                                                                'grossmarginhar2';
                                                                            $quantityequ =
                                                                                'quantityhar2';
                                                                            $totalpriceequipmn =
                                                                                'totalpricehardm2';
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'plant_materials'
                                                                        ) {
                                                                            $m = 'Plant Material';
                                                                            $clid =
                                                                                'getitemsplantid2';
                                                                            $totalcostsequip =
                                                                                'totalcostsplant2';
                                                                            $unitcostsequ =
                                                                                'unitcostspla2';
                                                                            $grossmarginequ =
                                                                                'grossmarginpla2';
                                                                            $quantityequ =
                                                                                'quantitypla2';
                                                                            $totalpriceequipmn =
                                                                                'totalpriceplantm2';
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'other_costs'
                                                                        ) {
                                                                            $m = 'Other Job Costs';
                                                                            $clid =
                                                                                'getitemsotherid2';
                                                                            $totalcostsequip =
                                                                                'totalcostscosts2';
                                                                            $unitcostsequ =
                                                                                'unitcostscos2';
                                                                            $grossmarginequ =
                                                                                'grossmargincos2';
                                                                            $quantityequ =
                                                                                'quantitycos2';
                                                                            $totalpriceequipmn =
                                                                                'totalpriceothercost2';
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'contractors'
                                                                        ) {
                                                                            $m = 'Contractor';
                                                                            $clid =
                                                                                'getitemscontractorid2';
                                                                            $totalcostsequip =
                                                                                'totalcostscontr2';
                                                                            $unitcostsequ =
                                                                                'unitcostscont2';
                                                                            $grossmarginequ =
                                                                                'grossmargincont2';
                                                                            $quantityequ =
                                                                                'quantitycontss2';
                                                                            $totalpriceequipmn =
                                                                                'totalpricecontractorm2';
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'labors'
                                                                        ) {
                                                                            $m = 'Labor';
                                                                            $clid = 'getitemsid2';
                                                                            $totalcostsequip =
                                                                                'totalcostslabor2';
                                                                            $unitcostsequ =
                                                                                'unitcostslabor2';
                                                                            $grossmarginequ =
                                                                                'grossmarginlabor2';
                                                                            $quantityequ =
                                                                                'quantitylabor2';
                                                                            $totalpriceequipmn =
                                                                                'totalpricelabor2';
                                                                            $lb2 = DB::table(
                                                                                'labors',
                                                                            )
                                                                                ->where(
                                                                                    'id',
                                                                                    $data->item_id,
                                                                                )
                                                                                ->first();
                                                                            $lb =
                                                                                $lb2->labor_burden;
                                                                            if (
                                                                                $data->labor_type ==
                                                                                'Laborers'
                                                                            ) {
                                                                                $TJQ =
                                                                                    'totalhourslabors';
                                                                            } else {
                                                                                $TJQ =
                                                                                    'totalhourssupervisions';
                                                                            }
                                                                        }
                                                                        $margin = DB::table(
                                                                            'margins',
                                                                        )
                                                                            ->where(
                                                                                'organization_id',
                                                                                getOrganizationId(),
                                                                            )
                                                                            ->where('name', $m)
                                                                            ->first();
                                                                        if (
                                                                            $data->category_type ==
                                                                            'equipment'
                                                                        ) {
                                                                            $mclass =
                                                                                'datarowsequ2';
                                                                            $tclass =
                                                                                'totalcostsequip2';
                                                                            $qtyclass =
                                                                                'quantityequ2';
                                                                            $ucclass =
                                                                                'unitcostsequ2';
                                                                            $gmclass =
                                                                                'grossmarginequ2';
                                                                            $tpclass =
                                                                                'equipmenttotalprice';
                                                                            $tcostcls =
                                                                                'equipmentcount';
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'labors'
                                                                        ) {
                                                                            $mclass =
                                                                                'datarowsLabor2';
                                                                            $tclass =
                                                                                'totalcostslabo2';
                                                                            $qtyclass =
                                                                                'quantitylabo2';
                                                                            $ucclass =
                                                                                'unitcostslabo2';
                                                                            $gmclass =
                                                                                'grossmarginlabo2';
                                                                            $tpclass =
                                                                                'labortotalprice';
                                                                            $tcostcls =
                                                                                'laborcount';
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'hard_materials'
                                                                        ) {
                                                                            $mclass =
                                                                                'datarowshard2';
                                                                            $tclass =
                                                                                'totalcostshard2';
                                                                            $qtyclass =
                                                                                'quantityhar2';
                                                                            $ucclass =
                                                                                'unitcostshar2';
                                                                            $gmclass =
                                                                                'grossmarginhar2';
                                                                            $tpclass =
                                                                                'materialtotalprice';
                                                                            $tcostcls =
                                                                                'materialcount';
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'plant_materials'
                                                                        ) {
                                                                            $mclass =
                                                                                'datarowsplant2';
                                                                            $tclass =
                                                                                'totalcostsplant2';
                                                                            $qtyclass =
                                                                                'quantitypla2';
                                                                            $ucclass =
                                                                                'unitcostspla2';
                                                                            $gmclass =
                                                                                'grossmarginpla2';
                                                                            $tpclass =
                                                                                'materialtotalprice';
                                                                            $tcostcls =
                                                                                'materialcount';
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'other_costs'
                                                                        ) {
                                                                            $mclass =
                                                                                'datarowsother2';
                                                                            $tclass =
                                                                                'totalcostscosts2';
                                                                            $qtyclass =
                                                                                'quantitycos2';
                                                                            $ucclass =
                                                                                'unitcostscos2';
                                                                            $gmclass =
                                                                                'grossmargincos2';
                                                                            $tpclass =
                                                                                'othertotalprice';
                                                                            $tcostcls =
                                                                                'othercostcount';
                                                                        } elseif (
                                                                            $data->category_type ==
                                                                            'contractors'
                                                                        ) {
                                                                            $mclass =
                                                                                'datarowsContractor2';
                                                                            $tclass =
                                                                                'totalcostscontr2';
                                                                            $qtyclass =
                                                                                'quantitycontss2';
                                                                            $ucclass =
                                                                                'unitcostscont2';
                                                                            $gmclass =
                                                                                'grossmargincont2';
                                                                            $tpclass =
                                                                                'contractortotalprice';
                                                                            $tcostcls =
                                                                                'subcontcount';
                                                                            $upida = 'upidss';
                                                                        }
                                                                    @endphp
                                                                    <tr class="border datarowsequ22-{{ $data->id }}"
                                                                        data-item-id="{{ $data->id }}">
                                                                        <td class="drag-handle">
                                                                            <img src="{{ asset('asset/assets/images/drag-drop-icon.svg') }}"
                                                                                alt="drag and drop icon">
                                                                        </td>
                                                                        <td class="border">
                                                                            <label for="">
                                                                                @if ($data->category_type == 'labors')
                                                                                    <input
                                                                                        style="text-align: start;"
                                                                                        type="text"
                                                                                        id="laboritemname-{{ $data->id }}"
                                                                                        oninput="changelaboritemname({{ $data->id }})"
                                                                                        value="{{ $data?->item_name }}" />
                                                                                @elseif($data->category_type == 'contractors')
                                                                                    <input
                                                                                        style="text-align: start;"
                                                                                        type="text"
                                                                                        id="contractoritemname-{{ $data->id }}"
                                                                                        oninput="changecontractoritemname({{ $data->id }})"
                                                                                        value="{{ $data?->item_name }}" />
                                                                                @else
                                                                                    {{ $data->item_name }}
                                                                                @endif
                                                                            </label>
                                                                            <input type="text"
                                                                                style="display: none;"
                                                                                class="{{ $clid }}-{{ $data->id }}"
                                                                                value="{{ $data->id }}">
                                                                        </td>

                                                                        @if ($data->category_type == 'hard_materials')
                                                                            <td>
                                                                                <div class="d-flex">
                                                                                    <span
                                                                                        class="getDetailsOfItem"
                                                                                        data-id="{{ $data->id }}"
                                                                                        style="cursor:pointer;">
                                                                                        <img style="width: 24px; height: 24px; margin-right: 4px;"
                                                                                            src="{{ asset('asset/assets/images/icons8-cube-64.png') }}"
                                                                                            alt="">
                                                                                    </span>
                                                                                </div>
                                                                            </td>
                                                                        @else
                                                                            <td></td>
                                                                        @endif

                                                                        <td class="border">
                                                                            <input type="number"
                                                                                oninput="changeQuantityVal2({{ $data->id }}, '{{ $data->category_type }}', '{{ $data->uom }}', '{{ $lb }}', '{{ $quantityequ }}', '{{ $grossmarginequ }}', '{{ $unitcostsequ }}', '{{ $totalcostsequip }}', '{{ $totalpriceequipmn }}', '{{ $data->labor_type }}')"
                                                                                value="{{ $data->quantity }}"
                                                                                min="1"
                                                                                style="width: 100%;"
                                                                                id="{{ $quantityequ }}-{{ $data->id }}"
                                                                                class="{{ $TJQ }}"
                                                                                placeholder="01" />
                                                                        </td>

                                                                        <td class="border">
                                                                            {{ $data->uom }}</td>

                                                                        <td class="border">
                                                                            @if ($data->category_type == 'contractors')
                                                                                <div
                                                                                    style="position: relative; display: inline-block;">
                                                                                    <span
                                                                                        style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;">$</span>
                                                                                    <input
                                                                                        type="number"
                                                                                        oninput="changeQuantityVal2({{ $data->id }}, '{{ $data->category_type }}', '{{ $data->uom }}', '{{ $lb }}', '{{ $quantityequ }}', '{{ $grossmarginequ }}', '{{ $unitcostsequ }}', '{{ $totalcostsequip }}', '{{ $totalpriceequipmn }}')"
                                                                                        value="{{ $data->unit_cost }}"
                                                                                        min="1"
                                                                                        style="width: 100%;"
                                                                                        id="{{ $unitcostsequ }}-{{ $data->id }}"
                                                                                        placeholder="01" />
                                                                                </div>
                                                                            @else
                                                                                <label
                                                                                    id="{{ $unitcostsequ }}-{{ $data->id }}">${{ $data->unit_cost }}</label>
                                                                            @endif
                                                                        </td>

                                                                        <td class="totalCostOfItem border {{ $tcostcls }}"
                                                                            id="{{ $tclass }}-{{ $data->id }}">
                                                                            ${{ $data->total_cost }}
                                                                        </td>

                                                                        <td class="border">
                                                                            <input type="text"
                                                                                style="display: none"
                                                                                id="sumofmarginequ-{{ $data->id }}"
                                                                                value=""
                                                                                class="sumofmargin" />
                                                                            <input
                                                                                class="default-margin-input"
                                                                                type="hidden"
                                                                                oninput="changeQuantityVal2({{ $data->id }}, '{{ $data->category_type }}', '{{ $data->uom }}', '{{ $lb }}', '{{ $quantityequ }}', '{{ $grossmarginequ }}', '{{ $unitcostsequ }}', '{{ $totalcostsequip }}', '{{ $totalpriceequipmn }}')"
                                                                                id="{{ $grossmarginequ }}-{{ $data->id }}"
                                                                                style="width: 100%;"
                                                                                value="{{ $data->gross_margin ?? $margin->default }}"
                                                                                placeholder="01"
                                                                                oninput="if (this.value < {{ $margin->minimum }}) this.value = {{ $margin->minimum }};" />
                                                                            <span
                                                                                class="margin-default-value"
                                                                                data-id="{{ $data->id }}"
                                                                                id="marginDefaultValue-{{ $data->id }}">{{ $data->gross_margin ?? $margin->default }}</span>
                                                                        </td>

                                                                        <td class="totalPriceOfItem border {{ $tpclass }}"
                                                                            id="{{ $totalpriceequipmn }}-{{ $data->id }}">
                                                                            ${{ $data->total_price }}
                                                                        </td>

                                                                        <td class="border"
                                                                            id="{{ $upida }}-{{ $data->id }}">
                                                                            ${{ $data->unit_price }}
                                                                        </td>

                                                                        <td class="border"
                                                                            style="padding-top: 12px; text-align: center;">
                                                                            <a href="javascript:void(0)"
                                                                                onclick="deletedatarowsequ2({{ $data->id }}, '{{ $tpclass }}', '{{ $TJQ }}', '{{ $data->total_price }}', '{{ $data->category_type }}')">
                                                                                <img src="{{ asset('asset/assets/images/trash-icon.svg') }}"
                                                                                    alt="trash icon">
                                                                            </a>
                                                                        </td>
                                                                    </tr>
                                                                @endforeach
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach

                                        @foreach ($gtems as $data)
                                            @php
                                                if ($data->category_type == 'equipment') {
                                                    $totalEquipmentAmount += $data->total_price;
                                                } elseif ($data->category_type == 'labors') {
                                                    $totalLaborHours += $data->quantity;
                                                    $totalLaborAmount += $data->total_price;
                                                } elseif (
                                                    $data->category_type == 'plant_materials' ||
                                                    $data->category_type == 'hard_materials'
                                                ) {
                                                    if ($data->category_type == 'hard_materials') {
                                                        $material = \App\Models\HardMaterial::find(
                                                            $data->item_id,
                                                        );
                                                        $laborPerUnit = $material?->labor ?? 0;
                                                    } else {
                                                        $material = \App\Models\PlantMaterial::find(
                                                            $data->item_id,
                                                        );
                                                        $laborPerUnit = $material?->install ?? 0;
                                                    }
                                                    $totalMaterialAmount += $data->total_price;
                                                    $totalLaborHours +=
                                                        $data->quantity * $laborPerUnit;
                                                } elseif ($data->category_type == 'other_costs') {
                                                    $totalOtherAmount += $data->total_price;
                                                } elseif ($data->category_type == 'contractors') {
                                                    $totalSubcontractor += $data->total_price;
                                                }
                                                $totalCostAmount += $data->total_cost;
                                                $lb = '';
                                                $TJQ = '';
                                                if ($data->category_type == 'equipment') {
                                                    $m = "Equipment's";
                                                    $clid = 'getitemsequipid2';
                                                    $totalcostsequip = 'totalcostsequip2';
                                                    $unitcostsequ = 'unitcostsequ2';
                                                    $grossmarginequ = 'grossmarginequ2';
                                                    $quantityequ = 'quantityequ2';
                                                    $totalpriceequipmn = 'totalpriceequi2';
                                                } elseif (
                                                    $data->category_type == 'hard_materials'
                                                ) {
                                                    $m = 'Hard Material';
                                                    $clid = 'getitemshardid2';
                                                    $totalcostsequip = 'totalcostshard2';
                                                    $unitcostsequ = 'unitcostshar2';
                                                    $grossmarginequ = 'grossmarginhar2';
                                                    $quantityequ = 'quantityhar2';
                                                    $totalpriceequipmn = 'totalpricehardm2';
                                                } elseif (
                                                    $data->category_type == 'plant_materials'
                                                ) {
                                                    $m = 'Plant Material';
                                                    $clid = 'getitemsplantid2';
                                                    $totalcostsequip = 'totalcostsplant2';
                                                    $unitcostsequ = 'unitcostspla2';
                                                    $grossmarginequ = 'grossmarginpla2';
                                                    $quantityequ = 'quantitypla2';
                                                    $totalpriceequipmn = 'totalpriceplantm2';
                                                } elseif ($data->category_type == 'other_costs') {
                                                    $m = 'Other Job Costs';
                                                    $clid = 'getitemsotherid2';
                                                    $totalcostsequip = 'totalcostscosts2';
                                                    $unitcostsequ = 'unitcostscos2';
                                                    $grossmarginequ = 'grossmargincos2';
                                                    $quantityequ = 'quantitycos2';
                                                    $totalpriceequipmn = 'totalpriceothercost2';
                                                } elseif ($data->category_type == 'contractors') {
                                                    $m = 'Contractor';
                                                    $clid = 'getitemscontractorid2';
                                                    $totalcostsequip = 'totalcostscontr2';
                                                    $unitcostsequ = 'unitcostscont2';
                                                    $grossmarginequ = 'grossmargincont2';
                                                    $quantityequ = 'quantitycontss2';
                                                    $totalpriceequipmn = 'totalpricecontractorm2';
                                                } elseif ($data->category_type == 'labors') {
                                                    $m = 'Labor';
                                                    $clid = 'getitemsid2';
                                                    $totalcostsequip = 'totalcostslabor2';
                                                    $unitcostsequ = 'unitcostslabor2';
                                                    $grossmarginequ = 'grossmarginlabor2';
                                                    $quantityequ = 'quantitylabor2';
                                                    $totalpriceequipmn = 'totalpricelabor2';
                                                    $lb2 = DB::table('labors')
                                                        ->where('id', $data->item_id)
                                                        ->first();
                                                    $lb = $lb2->labor_burden;
                                                    $TJQ = 'totalhourquantity';
                                                }
                                                $margin = DB::table('margins')
                                                    ->where('organization_id', getOrganizationId())
                                                    ->where('name', $m)
                                                    ->first();

                                                $upid = '';
                                                if ($data->category_type == 'equipment') {
                                                    $mclass = 'datarowsequ2';
                                                    $tclass = 'totalcostsequip2';
                                                    $qtyclass = 'quantityequ2';
                                                    $ucclass = 'unitcostsequ2';
                                                    $gmclass = 'grossmarginequ2';
                                                    $tpclass = 'equipmenttotalprice';
                                                    $tcostcls = 'equipmentcount';
                                                } elseif ($data->category_type == 'labors') {
                                                    $mclass = 'datarowsLabor2';
                                                    $tclass = 'totalcostslabo2';
                                                    $qtyclass = 'quantitylabo2';
                                                    $ucclass = 'unitcostslabo2';
                                                    $gmclass = 'grossmarginlabo2';
                                                    $tpclass = 'labortotalprice';
                                                    $tcostcls = 'laborcount';
                                                } elseif (
                                                    $data->category_type == 'hard_materials'
                                                ) {
                                                    $mclass = 'datarowshard2';
                                                    $tclass = 'totalcostshard2';
                                                    $qtyclass = 'quantityhar2';
                                                    $ucclass = 'unitcostshar2';
                                                    $gmclass = 'grossmarginhar2';
                                                    $tpclass = 'materialtotalprice';
                                                    $tcostcls = 'materialcount';
                                                } elseif (
                                                    $data->category_type == 'plant_materials'
                                                ) {
                                                    $mclass = 'datarowsplant2';
                                                    $tclass = 'totalcostsplant2';
                                                    $qtyclass = 'quantitypla2';
                                                    $ucclass = 'unitcostspla2';
                                                    $gmclass = 'grossmarginpla2';
                                                    $tpclass = 'materialtotalprice';
                                                    $tcostcls = 'materialcount';
                                                } elseif ($data->category_type == 'other_costs') {
                                                    $mclass = 'datarowsother2';
                                                    $tclass = 'totalcostscosts2';
                                                    $qtyclass = 'quantitycos2';
                                                    $ucclass = 'unitcostscos2';
                                                    $gmclass = 'grossmargincos2';
                                                    $tpclass = 'othertotalprice';
                                                    $tcostcls = 'othercostcount';
                                                } elseif ($data->category_type == 'contractors') {
                                                    $mclass = 'datarowsContractor2';
                                                    $tclass = 'totalcostscontr2';
                                                    $qtyclass = 'quantitycontss2';
                                                    $ucclass = 'unitcostscont2';
                                                    $gmclass = 'grossmargincont2';
                                                    $tpclass = 'contractortotalprice';
                                                    $tcostcls = 'subcontcount';
                                                    $upid = 'upidss';
                                                }
                                            @endphp
                                            <tr class="border datarowsequ-{{ $data->id }}"
                                                data-item-id="{{ $data->id }}">
                                                <td class="drag-handle">
                                                    <img src="{{ asset('asset/assets/images/drag-drop-icon.svg') }}"
                                                        alt="drag and drop icon">
                                                </td>
                                                <td class="border">
                                                    <label for="">
                                                        @if ($data->category_type == 'labors')
                                                            <input style="text-align: start;"
                                                                class="items-input" type="text"
                                                                id="laboritemname-{{ $data->id }}"
                                                                oninput="changelaboritemname({{ $data->id }})"
                                                                value="{{ $data?->item_name }}" />
                                                        @elseif($data->category_type == 'contractors')
                                                            <input style="text-align: start;"
                                                                type="text"
                                                                id="contractoritemname-{{ $data->id }}"
                                                                class="items-input"
                                                                oninput="changecontractoritemname({{ $data->id }})"
                                                                value="{{ $data?->item_name }}" />
                                                        @else
                                                            <span
                                                                id="item-name-{{ $data->id }}">{{ $data->item_name }}</span>
                                                            <input type="hidden"
                                                                id="depth-{{ $data->id }}"
                                                                placeholder="Depth" min="1"
                                                                oninput="changeDepth({{ $data->id }}, '{{ $data->uom }}')">
                                                            <input type="hidden"
                                                                id="calculatedQty-{{ $data->id }}">
                                                            <input type="hidden"
                                                                id="sqft-{{ $data->id }}"
                                                                placeholder="Sqft"
                                                                oninput="changeDepth({{ $data->id }},'{{ $data->uom }}')">
                                                        @endif
                                                    </label>
                                                    <input type="text" style="display: none;"
                                                        class="{{ $clid }}-{{ $data->id }}"
                                                        value="{{ $data->id }}">
                                                </td>
                                                @if ($data->category_type == 'hard_materials')
                                                    <td>
                                                        <div class="d-flex">
                                                            <span class="getDetailsOfItem"
                                                                data-id="{{ $data->id }}"
                                                                style="cursor:pointer;"> <img
                                                                    style="width: 24px; height: 24px; margin-right: 4px;"
                                                                    src="{{ asset('asset/assets/images/icons8-cube-64.png') }}"
                                                                    alt=""> </span>
                                                        </div>
                                                    </td>
                                                @else
                                                    <td></td>
                                                @endif
                                                <td class="border">
                                                    <input type="number"
                                                        oninput="changeQuantityVal2({{ $data->id }}, '{{ $data->category_type }}', '{{ $data->uom }}', '{{ $lb }}', '{{ $quantityequ }}', '{{ $grossmarginequ }}', '{{ $unitcostsequ }}', '{{ $totalcostsequip }}', '{{ $totalpriceequipmn }}')"
                                                        value="{{ $data->quantity }}" min="1"
                                                        style="width: 100%;"
                                                        id="{{ $quantityequ }}-{{ $data->id }}"
                                                        placeholder="01" />
                                                </td>
                                                <td class="border">{{ $data->uom }}</td>
                                                <td class="border">
                                                    @if ($data->category_type == 'contractors')
                                                        <div
                                                            style="position: relative; display: inline-block;">
                                                            <span
                                                                style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;">$</span>
                                                            <input type="number"
                                                                oninput="changeQuantityVal2({{ $data->id }}, '{{ $data->category_type }}', '{{ $data->uom }}', '{{ $lb }}', '{{ $quantityequ }}', '{{ $grossmarginequ }}', '{{ $unitcostsequ }}', '{{ $totalcostsequip }}', '{{ $totalpriceequipmn }}')"
                                                                value="{{ $data->unit_cost }}"
                                                                min="1" style="width: 100%;"
                                                                id="{{ $unitcostsequ }}-{{ $data->id }}"
                                                                placeholder="01" />
                                                        </div>
                                                    @else
                                                        <label
                                                            id="{{ $unitcostsequ }}-{{ $data->id }}">${{ $data->unit_cost }}</label>
                                                    @endif
                                                </td>
                                                <td class="totalCostOfItem border {{ $tcostcls }}"
                                                    id="{{ $totalcostsequip }}-{{ $data->id }}">
                                                    ${{ $data->total_cost }}
                                                </td>
                                                <td class="border">
                                                    <input type="text" style="display: none"
                                                        id="sumofmarginequ-{{ $data->id }}"
                                                        value="" class="sumofmargin" />
                                                    <input
                                                        class="gross-margin-input-{{ $data->id }}"
                                                        type="hidden"
                                                        oninput="changeQuantityVal2({{ $data->id }}, '{{ $data->category_type }}', '{{ $data->uom }}', '{{ $lb }}', '{{ $quantityequ }}', '{{ $grossmarginequ }}', '{{ $unitcostsequ }}', '{{ $totalcostsequip }}', '{{ $totalpriceequipmn }}')"
                                                        id="{{ $grossmarginequ }}-{{ $data->id }}"
                                                        style="width: 100%;"
                                                        value="{{ $data->gross_margin ?? $margin->default }}"
                                                        placeholder="01"
                                                        oninput="if (this.value < {{ $margin->minimum }}) this.value = {{ $margin->minimum }};" />
                                                    <span class="margin-default-value"
                                                        data-id="{{ $data->id }}"
                                                        id="marginDefaultValue-{{ $data->id }}">{{ $data->gross_margin ?? $margin->default }}</span>
                                                </td>
                                                <td class="totalPriceOfItem border {{ $tpclass }}"
                                                    id="{{ $totalpriceequipmn }}-{{ $data->id }}">
                                                    ${{ $data->total_price }}
                                                </td>
                                                <td class="border"
                                                    id="{{ $upid }}-{{ $data->id }}">
                                                    ${{ $data->unit_price }}
                                                </td>
                                                <td class="border"
                                                    style="padding-top: 12px; text-align: center;">
                                                    <a href="javascript:void(0)"
                                                        onclick="deletedatarowsequ2({{ $data->id }}, '{{ $tpclass }}', '{{ $TJQ }}', '{{ $data->total_price }}', '{{ $data->category_type }}')">
                                                        <img src="{{ asset('asset/assets/images/trash-icon.svg') }}"
                                                            alt="trash icon">
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                        <tr>
                                            <td style="width: 4%;"></td>
                                            <td style="width: 20%;"></td>
                                            <td style="width: 5%;"></td>
                                            <td style="width: 12%;"></td>
                                            <td style="width: 9%;"></td>
                                            <td style="width: 9%;"></td>
                                            <td style="width: 9%;"></td>
                                            <td style="width: 12%;"></td>
                                            <td style="width: 9%;"></td>
                                            <td style="width: 9%;"></td>
                                            <td style="width: 11%;"></td>
                                        </tr>
                                        <tr id="section-initial" class="collapse tavy tablerows">
                                            <td style="padding: 0px !important" colspan="11">
                                                <table class="table sortable-content">
                                                    <tbody class="sortable-content accord-bodys">
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!-- onclick="addNewItem()" -->
                            <a href="javascript:void(0)" type="button" onclick="emptyModelFields()"
                                class="btn btn-success mt-3" data-toggle="modal"
                                data-target="#addItemModal">Add Item</a>
                        </div>

                    </div>

                </div>

                <div class="row" style="margin-top: 0px;">

                    <div class="col">
                        <b class="mx-3" id="totalaveragegrossmargen" style="display: none;">0</b>
                    </div>
                </div>
                <div class="row mt-4" style="margin-bottom: 60px !important;">
                    <div class="col-md-6">
                        @php
                            $addedTotalPrice = $adjustSellingPrice?->added_total_price;
                            $addedMargin = $adjustSellingPrice?->added_margin;

                            $isTotalPriceZero = !is_null($addedTotalPrice) && $addedTotalPrice == 0;
                            $isMarginZero = !is_null($addedMargin) && $addedMargin == 0;
                        @endphp

                        <div class="d-flex justify-content-end mt-3">
                            <b>Adjust selling price by dollar amount</b>
                            <input type="number" id="forceselling" onchange="finalsellingP()"
                                value="{{ $addedTotalPrice ?? '' }}" placeholder="Enter here"
                                @if ($isTotalPriceZero) disabled @endif
                                @class(['mx-3 px-2' => true, 'disabledbtn' => $isTotalPriceZero])
                                style="border: 1px solid #D0D5DD; text-align: left; color: black; border-radius: 5px;">
                        </div>

                        <div class="d-flex justify-content-end mt-3">
                            <b>Adjust selling price by margin amount</b>
                            <input type="number" id="forcegm" onchange="finalsellingG()"
                                value="{{ $addedMargin ?? '' }}" placeholder="Enter here"
                                @if ($isMarginZero) disabled @endif
                                @class(['mx-3 px-2' => true, 'disabledbtn' => $isMarginZero])
                                style="border: 1px solid #D0D5DD; text-align: left; color: black; border-radius: 5px;">
                        </div>

                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end mt-3">
                            <label for="" class="textcolorprimary">Final selling price
                            </label>
                            <input type="number" id="forcefinalselling" class="mx-3 px-2"
                                placeholder="$"
                                style="border: 1px solid #D0D5DD; color: black; text-align: left; border-radius: 5px;cursor: not-allowed;"
                                @if ($adjustSellingPrice && $adjustSellingPrice->total_price) value="{{ $adjustSellingPrice->total_price }}" @endif
                                readonly>
                        </div>

                        <div class="d-flex justify-content-end mt-3">
                            <label for="" class="textcolorprimary">Final Gross Margin %
                            </label>
                            <input type="number" id="forcefinalgm" class="mx-3 px-2"
                                placeholder="60%"
                                style="border: 1px solid #D0D5DD; color: black; text-align: left; border-radius: 5px; cursor: not-allowed;"
                                @if ($adjustSellingPrice && $adjustSellingPrice->margin) value="{{ $adjustSellingPrice->margin }}" @endif
                                readonly>
                        </div>
                    </div>
                </div>

                <div class="row w-100 " style="margin-bottom: 60px; display: none;">
                    <div class="bottombox">
                        <label>Equipment</label>
                        <p id="totalcountequip">{{ Number::currency($totalEquipmentAmount) }}</p>
                    </div>
                    <div class="bottombox">
                        <label>Labor</label>
                        <p id="totalcountlabor">{{ Number::currency($totalLaborAmount) }}</p>
                    </div>
                    <div class="bottombox">
                        <label>Labor Burden</label>
                        <p id="totalcountburden">$
                            {{ Number::currency((($burderlabor->labor_burden ?? 0) / 100) * $totalLaborAmount) }}
                        </p>
                    </div>
                    <div class="bottombox">
                        <label>Material</label>
                        <p id="totalcountmaterial">${{ $totalCostSummaterial }}</p>
                    </div>
                    <div class="bottombox">
                        <label>Hours</label>
                        <p id="">0.00</p>
                    </div>
                    <div class="bottombox">
                        <label>Other</label>
                        <p id="totalcountother">${{ $totalCostSumother }}</p>
                    </div>
                    <div class="bottombox">
                        <label>Sub Contractor</label>
                        <p id="totalSubContractorsValu">${{ $totalCostSumcontractor }}</p>
                    </div>
                    <div class="bottombox">
                        <label>Total</label>
                    </div>
                    <div class="bottombox2">
                        <label>Estimated sales tax</label>
                        <p id="totalmaterialsaletex">$0.00</p>
                    </div>
                </div>
            </div>
            <div class="row panel2 w-100" id="totalSectionEstimateItems">
                <div class="bottombox">
                    <label>Equipment</label>
                    <p id="totalcountequipPrice">{{ Number::currency($totalEquipmentAmount) }}</p>

                </div>
                <div class="bottombox">
                    <label>Labor</label>
                    <p id="totalcountlaborPrice">{{ Number::currency($totalLaborAmount) }}</p>
                </div>
                <div class="bottombox">
                    <label>Labor Burden</label>
                    <p id="totalcountburdenPrice">
                        {{ Number::currency((($burderlabor->labor_burden ?? 0) / 100) * $totalLaborAmount) }}
                    </p>
                </div>
                <div class="bottombox">
                    <label>Labor Hours</label>
                    <p id="totalhourslabors">{{ Number::format($totalLaborHours) }}</p>
                </div>
                <div class="bottombox">
                    <label>Supervisor Hours</label>
                    <p id="totalhourssupervision">{{ $totalPriceSumsupervisionhourcount }}</p>
                </div>
                <div class="bottombox">
                    <label>Material</label>
                    <p id="totalcountmaterialPrice">{{ Number::currency($totalMaterialAmount) }}</p>
                </div>

                <div class="bottombox">
                    <label>Other</label>
                    <p id="totalcountotherPrice">{{ Number::currency($totalOtherAmount) }}</p>
                </div>
                <div class="bottombox">
                    <label>Sub Contractor</label>
                    <p id="totalSubContractorsValuPrice">{{ Number::currency($totalSubcontractor) }}
                    </p>
                </div>
                <div class="bottombox">
                    <label>Sales tax</label>
                    <p id="totalmaterialsaletexPrice">
                        {{ Number::currency($taxRate * $totalMaterialAmount) }}</p>
                </div>
                <div class="bottombox2">
                    <label>Total</label>
                    <p id="totalPriceCount">
                        {{ Number::currency($totalMaterialAmount + $totalSubcontractor + $totalOtherAmount + (($burderlabor->labor_burden ?? 0) / 100) * $totalLaborAmount + $totalLaborAmount + $totalEquipmentAmount) }}
                    </p>
                    <p id="totalallcount" style="display: none;">${{ $totalCostAmount }}</p>
                </div>

            </div>
            <input type="hidden" id="salestaxuser" value="{{ $organization->sale_tax }}">
            @if ($burderlabor)
                <input type="hidden" id="laborburdenval" value="{{ $burderlabor->labor_burden }}">
            @endif
            <!-- Modal -->
            <div class="modal fade" id="addItemModal" tabindex="-1"
                aria-labelledby="addItemModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-sm modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header" style="background: #E1F4FF;
">
                            <h4 class="modal-title" id="addItemModalLabel"
                                style="color: #0074D9 !important">Add new
                                items</h4>
                            <button type="button" class="btn-close hidemodelbtn px-3"
                                data-dismiss="modal" aria-label="Close"
                                style="border: none; background-color: transparent">
                                <i class="fa fa-times" style="color: #7E8A9D;"
                                    aria-hidden="true"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label for=""><b>Select Category</b></label>
                                <select name="" style="height: 30px !important;"
                                    class="form-control" id="category_list">
                                    <option value="">Choose...</option>
                                    <option value="equipment">Equipment</option>
                                    <!-- <option value="labors">Labor</option> -->
                                    <option value="hard_materials">Hard Materials</option>
                                    <option value="plant_materials">Plant Materials</option>
                                    <option value="other_costs">Other Job Cost</option>
                                    <!-- <option value="contractors">Sub Contractor</option> -->
                                </select>
                            </div>
                            <div class="form-group">
                                <b for=""> Name</b>
                                <input type="text" id="item_name"
                                    style="height: 30px !important; text-align: left;"
                                    class="form-control " placeholder=" Name">
                            </div>
                            <div class="form-group">
                                <b for=""> Quantity</b>
                                <input type="number" id="item_quantity"
                                    style="height: 30px !important; text-align: left;"
                                    class="form-control " placeholder="Item Quantity">
                            </div>
                            <div class="form-group">
                                <b for=""> Uom</b>
                                <select name="" id="item_uom" class="form-control">
                                    <option disabled selected>Choose Uom</option>
                                    <option value="Daily">Daily</option>
                                    <option value="Weekly">Weekly</option>
                                    <option value="Kathleen">Kathleen</option>
                                    <option value="Hour">Hour</option>
                                    <option value="klj">klj</option>
                                    <option value="Hours">Hours</option>
                                    <option value="Quart">Quart</option>
                                    <option value="Nereida">Nereida</option>
                                </select>
                            </div>
                            <div class="form-group" id="depth_fields">
                                <b for="">Depth</b>
                                <input type="number"
                                    style="height: 30px !important; text-align: left;"
                                    class="form-control" id="depth" min="0"
                                    placeholder="Enter Depth (In)">
                            </div>
                            <div class="form-group" id="sqft_fields">
                                <b for="">Sqft</b>
                                <input type="number"
                                    style="height: 30px !important; text-align: left;"
                                    class="form-control" id="sqft" min="0"
                                    placeholder="Enter Sqft">
                            </div>
                            <div class="form-group">
                                <b for="">Unit Cost</b>
                                <input type="number" id="item_unitcost"
                                    style="height: 30px !important; text-align: left;"
                                    class="form-control " placeholder="Item Unit Cost">
                            </div>
                            <button type="button" onclick="addNewItemModel()"
                                class="btn btn-primary form-control">Add Item</button>
                        </div>

                    </div>
                </div>
            </div>

            <!-- Bootstrap Modal -->
            <style>
                /* Remove number input arrows */
                input[type=number]::-webkit-outer-spin-button,
                input[type=number]::-webkit-inner-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }

                input[type=number] {
                    -moz-appearance: textfield;
                }

                #sqftVal::placeholder {
                    text-align: left;
                }

                #depthVal::placeholder {
                    text-align: left;
                }
            </style>

            <div class="modal fade" id="inputModal" tabindex="-1" role="dialog"
                aria-labelledby="inputModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content" style="width:70%">
                        <div class="modal-header" style="background-color:#dcf2ff">
                            <h5 class="modal-title" id="inputModalLabel"
                                style="color:#0074d9;font-weight:bold">Select Dimensions</h5>
                            <input type="hidden" id="hard_material_id" value="">
                            <input type="hidden" id="hard_material_uom">
                            <button type="button" class="close" data-dismiss="modal"
                                aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="inputForm">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="sqft">Sqft</label><span
                                                class="text-danger">*</span>
                                            <input type="number" class="form-control" id="sqftVal"
                                                min="0" placeholder="Enter Sqft" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="depth">Depth (In)</label><span
                                                class="text-danger">*</span>
                                            <input type="number" class="form-control" id="depthVal"
                                                min="0" placeholder="Enter Depth (In)"
                                                required>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary"
                                data-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" id="deptSqftModalBtn"
                                onclick="submitInput()">Add</button>
                        </div>
                    </div>
                </div>
            </div>


            <!-- scope modal -->

            <div class="modal fade" id="definescope" tabindex="-1"
                aria-labelledby="addItemModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header" style="background: #E1F4FF;
">
                            <h4 class="modal-title" id="addItemModalLabel"
                                style="color: #0074D9 !important; font-weight: bold;">Define Scope</h4>
                            <button type="button" class="btn-close hidemodelbtn px-3"
                                data-dismiss="modal" aria-label="Close"
                                style="border: none; background-color: transparent">
                                <i class="fa fa-times" style="color: #7E8A9D;"
                                    aria-hidden="true"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <b for="">Scope Title</b>
                                <input type="text" name="scope_title"
                                    value="{{ @$scope?->cov_title }}"
                                    style="height: 30px !important; text-align: left; font-size: 16px;"
                                    class="form-control scope_title" id="scope_title"
                                    placeholder="Title">
                            </div>
                            <div class="form-group">
                                <label for=""><b>Scope Description</b></label>
                                <textarea id="textEditor" class="wycwyg_editorb describe-project addNoteSummer2 scope_description"
                                    name="scope_description" id="addNoteSummer" style="height:200px">{!! $scope?->cov_sub_title !!}</textarea>
                            </div>
                            <button type="button" class="btn btn-primary doneBtnDefinescope"
                                style="font-size: 16px; float: inline-end" id="doneBtnDefinescope">Add
                                Scope
                            </button>
                        </div>
                    </div>
                </div>
            </div>


            <!--Success Modal -->
            <div class="modal-small success-modal modal fade" id="grossmarginmodel"
                data-keyboard="false" tabindex="-1" aria-labelledby="successModalLabel"
                aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-body">
                            <div class="text-center mb-4">
                                <img height="70px" width="70px"
                                    src="{{ asset('admin_assets/images/icons/check-icon.png') }}"
                                    alt="check icon">
                            </div>
                            <h2 class="title text-center">Company Margin Setup</h2>
                            <p class="para mt-3 text-center">First you have to set company margin in
                                settings</p>
                            <button type="button"
                                onclick="window.location.href='{{ route('organization.margin_setup') }}'"
                                class="btn primaryblue w-100 mt-5">Set Margin</button>
                        </div>
                    </div>
                </div>
            </div>
            <!--Success Modal -->

            <!-- edit item modal -->
            <div class="modal fade" id="editItemModal" tabindex="-1"
                aria-labelledby="editItemModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header" style="background: #E1F4FF;
">
                            <h4 class="modal-title" id="addItemModalLabel"
                                style="color: #0074D9 !important">Edit Item</h4>
                            <button type="button" class="btn-close hidemodelbtn px-3"
                                data-dismiss="modal" aria-label="Close"
                                style="border: none; background-color: transparent">
                                <i class="fa fa-times" style="color: #7E8A9D;"
                                    aria-hidden="true"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label for=""><b>Select Category</b></label>
                                <select name="" style="height: 30px !important;"
                                    class="form-control" id="edit_category_list"
                                    @disabled(true)>
                                    <option value="equipment">Equipment</option>
                                    <!-- <option value="labors">Labor</option> -->
                                    <option value="hard_materials">Hard Materials</option>
                                    <option value="plant_materials">Plant Materials</option>
                                    <option value="other_costs">Other Job Cost</option>
                                    <!-- <option value="contractors">Sub Contractor</option> -->
                                </select>
                            </div>
                            <div class="form-group">
                                <b for=""> Name</b>
                                <input type="text" id="edit_item_name"
                                    style="height: 30px !important; text-align: left;"
                                    class="form-control " placeholder=" Name" @readonly(true)>
                            </div>
                            <div class="form-group">
                                <b for=""> Quantity</b>
                                <input type="number" id="edit_item_quantity"
                                    style="height: 30px !important; text-align: left;"
                                    class="form-control " placeholder="Item Quantity"
                                    @readonly(true)>
                            </div>
                            <div class="form-group">
                                <b for=""> Uom</b>
                                <select name="" id="edit_item_uom" class="form-control"
                                    @disabled(true)>
                                    <option disabled selected>Choose Uom</option>
                                    <option value="Daily">Daily</option>
                                    <option value="Weekly">Weekly</option>
                                    <option value="Kathleen">Kathleen</option>
                                    <option value="Hour">Hour</option>
                                    <option value="klj">klj</option>
                                    <option value="Hours">Hours</option>
                                    <option value="Quart">Quart</option>
                                    <option value="Nereida">Nereida</option>
                                </select>
                            </div>
                            <div class="form-group" id="depth_fields">
                                <b for="">Depth</b>
                                <input type="number"
                                    style="height: 30px !important; text-align: left;"
                                    class="form-control" id="edit_depth" min="0"
                                    placeholder="Enter Depth (In)">
                            </div>
                            <div class="form-group" id="sqft_fields">
                                <b for="">Sqft</b>
                                <input type="number"
                                    style="height: 30px !important; text-align: left;"
                                    class="form-control" id="edit_sqft" min="0"
                                    placeholder="Enter Sqft">
                            </div>
                            <div class="form-group">
                                <b for="">Unit Cost</b>
                                <input type="number" id="edit_item_unitcost"
                                    style="height: 30px !important; text-align: left;"
                                    class="form-control " placeholder="Item Unit Cost"
                                    @readonly(true)>
                            </div>
                            <input type="hidden" id="edit_item_id">
                            <button type="button" class="btn btn-primary form-control"
                                id="updateEstimateItem">Update Item</button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </section>

@endsection
@section('extra-scripts')
    @include('organization.opportunity.estimation-scripts')
@endsection
