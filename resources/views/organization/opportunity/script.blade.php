<script type="text/javascript">
    var opportunities_table = null;
    $(document).ready(function() {
        // Initialize DataTable
        opportunities_table = $('.yajra-datatable').DataTable({
            processing: true,
            responsive: true,
            serverSide: true,
            ajax: {
                url: "{{ route(getRouteAlias() . '.opportunity.index') }}",
                type: 'GET' // Ensure the correct request method
            },
            columns: [
                { data: 'opportunity_id', name: 'opportunity_id', orderable: false },
                { data: 'opportunity_name', name: 'opportunity_name', orderable: false },
                { data: 'property_name', name: 'property_name', orderable: false },
                { data: 'account', name: 'account', orderable: false },
                { data: 'account_owner', name: 'account_owner', orderable: false },
                { data: 'opportunity_type', name: 'opportunity_type', orderable: false },
                { data: 'division_name', name: 'division_name', orderable: false },
                { data: 'service_line_name', name: 'service_line_name', orderable: false },
                { data: 'status', name: 'status', orderable: false, className: 'text-center' },
                { data: 'date_time', name: 'date_time', orderable: false },
                { data: 'action', name: 'action', orderable: false }
            ],
            language: {
                zeroRecords: "Sorry we could not find any results",
                paginate: {
                    "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                    "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
                }
            },
            dom: '<"top">rt<"bottom"lip><"clear">',
        });

        // Custom Search Filter
        $('.oppListing #filter_search').on('keyup', function() {
    opportunities_table.search(this.value).draw();

});


        // Additional code for modals and file uploads...
    });

    function showModal(id) {
        $('.dynamic-content-data').html(`
            <h2 class="title text-center">Delete Request</h2>
            <p class="para mt-3 text-center">Are you sure you want to delete this request.</p>
        `);
        deleteId = id;
    }

    $('.deleteConfirmation').on('click', function() {
        deleteData();
    });

    function deleteData() {
        var url = "{{ URL::route(getRouteAlias() . '.opportunity.delete', ':id') }}";
        url = url.replace(':id', deleteId);

        $.ajax({
            method: "DELETE",
            url: url,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            success: function(response) {
                $('#DeleteModal').modal('hide');
                $('#successModal').modal('show');
                $('.dynamic-success-data').html(`
                    <h2 class="title text-center">Done</h2>
                    <p class="para mt-3 text-center">Opportunity Deleted Successfully!</p>
                `);
                opportunities_table.draw();
            },
            error: function(response) {
                $('#DeleteModal').modal('hide');
            }
        });
    }

    var fileInput = $(".upload_file_wrapper .input_file");
    var label = $(".upload_file_wrapper label");
    var selectedFile;

    fileInput.on("change", function(event) {
        selectedFile = event.target.files[0];

        if (selectedFile.type === "application/vnd.ms-excel" ||
            selectedFile.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
            var fileName = selectedFile.name;
            label.text(fileName);

            $(this).parent(".file_upload_button_form").submit();
        } else {
            fileInput.val("");
            label.text("Import Opportunities");

            var newElement = $("<label class='error custom_error'>Only Excel File Is Allowed</label>");
            $(this).after(newElement);
            newElement.fadeOut(3000, function() {
                $(this).remove();
            });
        }
    });

    $(".file_upload_button_form").on("submit", function(e) {
        e.preventDefault();
        let myForm = document.getElementById("opportunity-import");
        let formData = new FormData(myForm);
        var url = "{{ URL::route(getRouteAlias() . '.opportunity.file-import') }}";

        $.ajax({
            method: "post",
            url: url,
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function(response) {
                $('.showMessage').html(`
                    <div class="alert alert-success alert-dismissible fade show">
                        File imported successfully!.
                    </div>
                `);
                opportunities_table.draw();
            },
            error: function(response) {
                $('#opportunity-import')[0].reset();
                $.each(response.responseJSON.errors, function(field_name, error) {
                    $('.showMessage').html('');
                    $('.showMessage').append(`
                        <div class="alert alert-danger alert-dismissible fade show">
                            ${error}
                        </div>
                    `);
                });
            }
        });
    });

    $(document).ready(function() {
        $('#deleteModal').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget);
            var opportunityId = button.data('opportunity-id');

            $(this).find('.conform-btn').data('opportunity-id', opportunityId);
        });

        $('.conform-btn').on('click', function() {
            var opportunityId = $(this).data('opportunity-id');
            var deleteUrl = "{{ route('organization.opportunity.delete', ['id' => ':id']) }}".replace(':id', opportunityId);

            $.ajax({
                url: deleteUrl,
                type: 'GET',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#deleteModal').modal('hide');
                    toastr.success('Opportunity deleted successfully!');
                    location.reload();
                },
                error: function(error) {
                    toastr.warning('Error deleting opportunity.');
                }
            });
        });
    });
</script>
