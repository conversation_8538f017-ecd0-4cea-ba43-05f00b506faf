@extends('layouts.admin.master')
@section('title', 'Estimation Preview')
@section('section')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">

<style>
     .btn-toggle {

	 padding: 0;
	 position: relative;
	 border: none;
	 height: 1.6rem;
	 width: 3.2rem;
	 border-radius: 1.5rem;
	 color: #6b7381;
	 background: #bdc1c8;
}
 .btn-toggle:focus, .btn-toggle:focus.active, .btn-toggle.focus, .btn-toggle.focus.active {
	 outline: none;
}
 .btn-toggle:before, .btn-toggle:after {
	 line-height: 1.5rem;
	 width: 4rem;
	 text-align: center;
	 font-weight: 600;
	 font-size: .75rem;
	 text-transform: uppercase;
	 letter-spacing: 2px;
	 position: absolute;
	 bottom: 0;
	 transition: opacity .25s;
}
 .btn-toggle:before {
    content: '';
	 left: -8rem;
     font-weight: 900;
     font-size: 10px;
     color: #7C8091;
     fill-opacity: #7C8091;
}
 .btn-toggle:after {
    content: '';
	 left: -8rem;
     font-weight: 900;
     font-size: 10px;

	 opacity: 1;
}
.active:after {
    color: #0074D9; /* New color */
}
 .btn-toggle > .handle {
	 position: absolute;
     margin-top: 0.7px !important;
	 top: 0.1875rem;
	 left: 0.1875rem;
	 width: 1.125rem;
	 height: 1.125rem;
	 border-radius: 1.125rem;
	 background: #fff;
	 transition: left .25s;
}
 .btn-toggle.active {
	 transition: background-color .25s;
}
 .btn-toggle.active {
	 background-color: #117BDD;
}
 .btn-toggle.active > .handle {
	 left: 1.6875rem;
	 transition: left .25s;
}
 .btn-toggle.active:before {
	 /* opacity: .5; */
     display: none;
}
 .btn-toggle.active:after {
	 opacity: 1;
}
.sidemenuul li{
    padding: 10px 5px;
    border: 1px solid #E2E4EA;
    border-radius: 8px;
}
.sidemenuul li p{
    color: #3B4159;
}
.dnbtn{
    width: 100%;
/* height: Hug (37px)px; */
padding: 6px 14px 6px 14px;
gap: 12px;
border-radius: 12px;
background: #E6F1FB;
opacity: 0px;
border: none;
font-size: 10px;
color: #117BDD;

}
.sendcbtn{
    width: 100%;
    background: #0074D9;
padding: 6px 14px 6px 14px;
gap: 12px;
border-radius: 12px;
opacity: 0px;
font-size: 10px;
color: white;
border: none;

}
.leftimg{
    position: absolute;
    top: 0px;
    left: 0;
}
.proposal {
    width: 205px;
    height: 50px;
    margin-top: 25px;
    /* margin-left: -21px; */
    /* padding: 13px 47px 19px 105px; */
    padding: 11px;
    text-align: center;
    font-size: 15px;
    gap: 10px;
    border-radius: 0px 100px 100px 0px;
    color: white;
    opacity: 0px;
    background: #0074D9;
}
.proposal_personal {
    width: 205px;
    height: 50px;
    margin-top: 25px;
    margin-left: -21px !important;
    /* padding: 13px 0px 19px 54px; */
    padding: 11px;
    text-align: center;
    font-size: 15px;
    gap: 10px;
    border-radius: 0px 100px 100px 0px;
    color: white;
    opacity: 0px;
    background: #0074D9;
}

.proposal b{
    color: white;
}
.proposal_personal b{
    color: white;
}

    .panel {
    background: #ffffff;
    box-shadow: 0px 0px 12px rgba(36, 185, 236, 0.08);
    border-radius: 8px;

}
.panel23{
    padding: 0px !important;
}
.table td, .table th {
border-bottom: none;
border-top: none;
}
.table thead th {

    border-bottom: none;
}
.main-div-sec{
    overflow-y: scroll;
    height: 793px;
    width: 100%;
}
.main-div-sec::-webkit-scrollbar {
    display: none; /* Webkit browsers ke liye scroll bar ko hide karna */
}

/* Firefox ke liye */
.main-div-sec {
    scrollbar-width: none; /* Firefox ke liye scroll bar hide karna */
}
.sidenavsec{
    height: 793px;
}
.custombtn{

    background-color: transparent;
    border: none;
    text-align: justify;
}
.bluetext b{
    color: #0074D9 !important;
}
.browntext b{
    color: #7C8091 !important;
}
.edit_cover_section{
    display: none;
}
.edit_about_section{
    display: none;
}
.edit_personal_section{
    display: none;
}
.edit_sample_section{
    display: none;
}
.edit_terms_section{
    display: none;
}
.edit_payment_section{
    display: none;
}
.input-group-append {
  cursor: pointer;
}
button{
    cursor: pointer;
}
</style>
<style>
  .image-container {
  position: relative;
  width: 100%;
  margin-top: 100px;
  max-width: 100%; /* Adjust to your needs */
  overflow: hidden;
}

.image-container img {
  width: 100%;
  height: auto;
  /* Applying a mask to create the curved effect */
  mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><path fill="white" d="M100,0 C80,0 20,10 0,40 L0,100 L100,100 Z"/></svg>');
  mask-size: 100% 100%;
  -webkit-mask-size: 100% 100%; /* For Safari support */
}

.centered-text {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24px;
  text-align: center;
  font-weight: bold;
}
.centered-text strong{
    color: #ffffff;
    font-weight: 800;
    font-size: 9.83mm;
    font-weight: 600;
    text-transform: uppercase;
    line-height: 15.83mm;
    --tw-text-opacity: 1;
}

.image-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 190px; /* Adjust height to control shadow size */
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent); /* Creates the fading shadow */
  pointer-events: none; /* Ensures the shadow doesn't interfere with any interactions */
}
.subtitle-text-main {
    background: #0074D9;
    width: fit-content;
    /* display: none; */
    height: auto;
    padding: 13px 61px;

    border-bottom-left-radius: 0.5rem;
    color: white;
}
.subtitle-text-main p{
    color: white;
}
input{
    height: 32px;
}
.pspdfkit-1thsp3e {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    /* display: none; */
    margin-top: 90px;
    max-height: 97mm;
    overflow: hidden;
    --tw-bg-opacity: 1;
    background-color: rgb(203 213 225 / var(--tw-bg-opacity));
    margin-top: 3rem;
}
.pspdfkit-1eoji5i {
    position: absolute;
    left: 0;
    top: 0;
    height: 80%;
    width: 100%;
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(0, 0, 0, 0) 100%);
    content: '';
}

.pspdfkit-1q9q9cv {
    -webkit-flex: 1 1 0%;
    -ms-flex: 1 1 0%;
    flex: 1 1 0%;
    background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
    --tw-gradient-from: rgb(245,246,252,0.52) var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(117,19,93,0.73) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from),var(--tw-gradient-to);
    object-fit: cover;
}
.panel22{
    height: auto !important;
    min-height: 683px !important;
    position: relative;
}
.bottom_row{
    position: absolute;
    bottom: 0;
    width: 100%;
    left: 15px;
}
#gtermstext{
    /* display: none; */
}
#multiplefileupload5{
    display: none;
}
    </style>

    <section class="dashboard_main">
        <div class="row justify-content-center">
            <div class="col-md-8 main-div-sec">
                <div class="panel panel23 coverd mt-4" id="main_cover" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="" style="position: relative;">
                        <div class="row">
                        <div class="leftimg">
                            <svg width="4" height="244" viewBox="0 0 4 244" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="4" height="163" fill="#0074D9"/>
                                <rect y="175" width="4" height="27" fill="#0074D9"/>
                                <rect y="214" width="4" height="9" fill="#0074D9"/>
                                <rect y="235" width="4" height="9" fill="#0074D9"/>
                                </svg>
                        </div>
                        <div class="proposal" style="margin-top: 60px;">
<b>
    Proposal
</b>
                        </div>
                    </div>
                    <div class="row">
                        <div class="logo" style="margin-top: 43px;
    margin-left: 10%;">
                            <img id="add_logo_image" class="add_logo_image" src="{{ $logo && $logo->cov_image ? Storage::url($logo->cov_image) : '' }}" style="
                        height: 52px;
                        top: 149px;
                        left: 47px;
                        gap: 0px;
                        opacity: 0px;
                        " alt="">
                        </div>


                    </div>
                    <div class="row2">
                        <div class="imge" style="margin-top: -107px;">


                            <div class="image-container">
                                <img id="preview_image" src="{{isset($cover->cov_image) ? Storage::url($cover->cov_image) : asset('admin_assets/images/12.png')}}" style="width: 100%;" alt="">
                                <div class="centered-text">
                                    <strong id="add_cov_tit">{{isset($cover) ? $cover->cov_title : ''}}</strong>
                                  </div>
                              </div>


                        </div>
                    </div>
                    <div class="row justify-content-end">
                        <div class="col-11" style="margin-left: auto;">
                            <div class="subtitle-text-main text-center" style="margin-left: auto;">
                                <p id="add_sub_tit">{{isset($cover) ? $cover->cov_sub_title : ''}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="container">
                        <div class="row px-4">
                            <div class="col-md-4">
                                <label for="" style="color: #7C8091;">
                                    Submitted to:
                                </label>
                                <p>
                                    <b>Sasha Nowrouzi</b>
                                </p>
                            </div>
                            <div class="col-md-4">
                                <label for="" style="color: #7C8091;">
                                    Submitted by:
                                </label>
                                <p>
                                    <b>Muhammad Arslan Anjum</b>
                                </p>
                                <label for="">
                                    DEVANICS Technology
                                </label>
                            </div>
                            <div class="col-md-4">
                                <label for="" style="color: #7C8091;">
                                    Date Submitted:
                                </label>
                                <p>
                                    <b>September 27th, 2024</b>
                                </p>

                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <svg width="100%" height="180" viewBox="0 0 800 180" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="-14" y="14" width="188" height="188" rx="23" transform="matrix(1 0 0 -1 5 300)" fill="white" stroke="#D9EAF9" stroke-width="28"/>
                            <rect x="802" y="186" width="614" height="34" transform="rotate(180 802 186)" fill="#D9EAF9" stroke="#D9EAF9" stroke-width="4"/>
                            <rect x="-14" y="14" width="184" height="188" rx="23" transform="matrix(1 0 0 -1 -95 216)" fill="white" stroke="#D9EAF9" stroke-width="28"/>
                            </svg>

                    </div>

                    </div>

                </div>
                <div class="panel panel22 about mt-4" id="main_about" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                        <div class="leftimg2">
                            <svg style="position: absolute;
    top: 0; left: 0;" width="4" height="244" viewBox="0 0 4 244" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="4" height="163" fill="#0074D9"/>
                                <rect y="175" width="4" height="27" fill="#0074D9"/>
                                <rect y="214" width="4" height="9" fill="#0074D9"/>
                                <rect y="235" width="4" height="9" fill="#0074D9"/>
                                </svg>
                        </div>


                    </div>

                    <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                        <div class="col-6">

                          <div class="proposal" style="margin-left: -21px;">
                              <b>
                                  About Us
                              </b>
                                                      </div>

                        </div>
                                                      <div class="col-6 d-flex px-3">
                                                          <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                                                              <img id="add_logo_image" class="add_logo_image" src="{{ $logo && $logo->cov_image ? Storage::url($logo->cov_image) : '' }}" style="
                                                                          height: 39px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                                                          </div>
                                                      </div>
                      </div>
                      <div class="container px-5 mt-5 pb-5">
                        <div class="row px-5">
                          <br>
                          <p class="px-5" id="add_about_text" style="color: #3B4159; font-size: 14px;">
{{isset($about) ? $about->intro : ''}}
                          </p>
                        </div>
                      </div>

                      <div class="pspdfkit-1thsp3e e1h2lg7j2 mt-5">

                        <div class="pspdfkit-1eoji5i e1h2lg7j0"></div>
                      <img src="{{isset($about) ? Storage::url($about->cov_image) : ''}}" alt="cover" id="preview_about_image" class="pspdfkit-1q9q9cv e1h2lg7j1">

                    </div>



                    <div class="row px-5 justify-content-between bottom_row py-2" style="background-color: #E6F1FB; margin-top: 0px;">
                        <div class="col-6">
                            <b style="color: #0068C3;">
                                Company Name
                            </b>
                        </div>
                        <div class="col-6" style="display: flex;">
                            <b style="color: #0068C3; margin-left: auto;">
                                Comapnywebsite.com
                            </b>
                        </div>

                    </div>

                    </div>

                </div>

                <div class="panel panel22 personal mt-4" id="main_personal" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                        <div class="leftimg2">
                            <svg style="position: absolute;
    top: 0; left: 0;" width="4" height="244" viewBox="0 0 4 244" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="4" height="163" fill="#0074D9"/>
                                <rect y="175" width="4" height="27" fill="#0074D9"/>
                                <rect y="214" width="4" height="9" fill="#0074D9"/>
                                <rect y="235" width="4" height="9" fill="#0074D9"/>
                                </svg>
                        </div>


                    </div>

                    <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                        <div class="col-6">

                          <div class="proposal_personal" style=" padding-right: 0px !important;">
                              <b>
                                Personal Introduction
                              </b>
                                                      </div>

                        </div>
                                                      <div class="col-6 d-flex px-3">
                                                          <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                                                              <img id="add_logo_image" class="add_logo_image" src="{{ $logo && $logo->cov_image ? Storage::url($logo->cov_image) : '' }}" style="
                                                                          height: 39px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                                                          </div>
                                                      </div>
                      </div>


                    <div class="container px-5 mt-5">
                      <div class="row px-5">
                        <br>
                        <p class="px-5" id="add_intro_letter" style="color: #3B4159; font-size: 14px;">
                             {{isset($intro) ? $intro->intro : ''}}
                        </p>
                      </div>
                    </div>
                    <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                        <div class="col-6">
                            <b style="color: #0068C3;">
                                Company Name
                            </b>
                        </div>
                        <div class="col-6" style="display: flex;">
                            <b style="color: #0068C3; margin-left: auto;">
                                Comapnywebsite.com
                            </b>
                        </div>

                    </div>

                    </div>

                </div>

                <div class="panel panel22 scop_of_work mt-4" id="main_scpe" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                        <div class="leftimg2">
                            <svg style="position: absolute;
    top: 0; left: 0;" width="4" height="244" viewBox="0 0 4 244" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="4" height="163" fill="#0074D9"/>
                                <rect y="175" width="4" height="27" fill="#0074D9"/>
                                <rect y="214" width="4" height="9" fill="#0074D9"/>
                                <rect y="235" width="4" height="9" fill="#0074D9"/>
                                </svg>
                        </div>


                    </div>

                    <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                        <div class="col-6">

                          <div class="proposal_personal" style=" padding-right: 0px !important;">
                              <b>
                                Scope of work
                              </b>
                                                      </div>

                        </div>
                                                      <div class="col-6 d-flex px-3">
                                                          <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                                                              <img id="add_logo_image" class="add_logo_image" src="{{ $logo && $logo->cov_image ? Storage::url($logo->cov_image) : '' }}" style="
                                                                          height: 39px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                                                          </div>
                                                      </div>
                      </div>


                    <div class="container px-5  mt-4">
                      <div class="row px-5">
                        <br>
                        <p class="px-5" style="color: #3B4159; font-size: 14px; text-align: center;">
                            <b id="scope_title" style="text-align: left;">{{isset($scope) ? $scope->cov_title : ''}}</b> <br>
                            <label id="scope_des" for="" style="text-align: left;">
                                {{isset($scope) ? $scope->cov_sub_title : ''}}
                            </label>
                        </p>
                      </div>

                      <div class="row px-5 mt-5 pb-5" style="justify-content: center;">
                        <div class="border" style="width: 100%; padding: 10px; border: 1px solid #E7E7E7; border-radius: 8px;">
                            <table class="table table-striped">
                               <thead style="background: #DCF2FF; border: 1px solid transparent;
">
                               <tr style="border: none;">
                                <th>Item / Description</th>
                                <th>Uom</th>
                                <th>Quantity</th>
                                <th>Unit Cost</th>
                                <th>Total Price</th>
                               </tr>
                               </thead>
                               <tbody>
                                @foreach ($opportunity as $item)
                                <tr>
                                    <td>{{ $item->item_name }}</td>
                                    <td>{{ $item->uom }}</td>
                                    <td>{{ $item->quantity }}</td>
                                    <td>{{ $item->unit_cost }}$</td>
                                    <td>{{ $item->total_cost }}$</td>
                                </tr>
                                @endforeach
                               </tbody>
                              </table>
                        </div>
                      </div>
                    </div>
                    <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 200px;">
                        <div class="col-6">
                            <b style="color: #0068C3;">
                                Company Name
                            </b>
                        </div>
                        <div class="col-6" style="display: flex;">
                            <b style="color: #0068C3; margin-left: auto;">
                                Comapnywebsite.com
                            </b>
                        </div>

                    </div>

                    </div>

                </div>





                <div class="panel panel22 terms_conditions mt-4" id="main_trms" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                        <div class="leftimg2">
                            <svg style="position: absolute;
    top: 0; left: 0;" width="4" height="244" viewBox="0 0 4 244" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="4" height="163" fill="#0074D9"/>
                                <rect y="175" width="4" height="27" fill="#0074D9"/>
                                <rect y="214" width="4" height="9" fill="#0074D9"/>
                                <rect y="235" width="4" height="9" fill="#0074D9"/>
                                </svg>
                        </div>


                    </div>

                    <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                        <div class="col-6">

                          <div class="proposal_personal" style=" padding-right: 0px !important;">
                              <b>
                                Term & Conditions
                              </b>
                                                      </div>

                        </div>
                                                      <div class="col-6 d-flex px-3">
                                                          <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                                                              <img id="add_logo_image" class="add_logo_image" src="{{ $logo && $logo->cov_image ? Storage::url($logo->cov_image) : '' }}" style="
                                                                          height: 39px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                                                          </div>
                                                      </div>
                      </div>


                    <div class="container px-5 mt-5 pb-5">
                      <div class=" px-5 ">
                        <br>
                        <b class="px-5" id="gtermstext">General Terms</b>
                        <br>

                        <p class="px-5" id="add_gterms" style="color: #3B4159; font-size: 12px;">

                            {{isset($terms) ? $terms->intro : ''}}

                        </p>



                      </div>
                    </div>
                    <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                        <div class="col-6">
                            <b style="color: #0068C3;">
                                Company Name
                            </b>
                        </div>
                        <div class="col-6" style="display: flex;">
                            <b style="color: #0068C3; margin-left: auto;">
                                Comapnywebsite.com
                            </b>
                        </div>

                    </div>

                    </div>

                </div>



                <div class="panel panel22 payment_scdul mt-4" id="main_pmnt" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                        <div class="leftimg2">
                            <svg style="position: absolute;
    top: 0; left: 0;" width="4" height="244" viewBox="0 0 4 244" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="4" height="163" fill="#0074D9"/>
                                <rect y="175" width="4" height="27" fill="#0074D9"/>
                                <rect y="214" width="4" height="9" fill="#0074D9"/>
                                <rect y="235" width="4" height="9" fill="#0074D9"/>
                                </svg>
                        </div>


                    </div>

                    <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                        <div class="col-6">

                          <div class="proposal_personal" style=" padding-right: 0px !important; width: 258px !important;">
                              <b>
                                Payments Scheduled & terms
                              </b>
                                                      </div>

                        </div>
                                                      <div class="col-6 d-flex px-3">
                                                          <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                                                              <img id="add_logo_image" class="add_logo_image" src="{{ $logo && $logo->cov_image ? Storage::url($logo->cov_image) : '' }}" style="
                                                                          height: 39px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                                                          </div>
                                                      </div>
                      </div>
                      <div class="container px-5 mt-5 pb-5">
                        <div class="">
                            <label id="add_prpslexpiry">Proposal Expiry Date</label>
                            <br>
                            <label id="add_date_expiry"><b id="add_fixdate"></b></label>
<br>

                            <p id="add_pmntschdle"><span>Payment Schedule</span></p>
                            <label id="add_payment_schedule"></label>
                            <br>
                            <label>Total</label>
                            <br>
                            <b id="totalamont">438$</b>
                            <br>
                            <br>
                            <label>Down Payments</label>


                            <br>
                            <div class="d-flex" style="gap: 13px;"><b id="downpaym1"></b>
                                <b id="downpaym2"></b></div>
                            <br>

                            <span>Balance</span><br>
                            <b id="totalbalancetext">345$</b>
                        </div>
                      </div>



                    <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                        <div class="col-6">
                            <b style="color: #0068C3;">
                                Company Name
                            </b>
                        </div>
                        <div class="col-6" style="display: flex;">
                            <b style="color: #0068C3; margin-left: auto;">
                                Comapnywebsite.com
                            </b>
                        </div>

                    </div>

                    </div>

                </div>


                <div class="panel panel22 mt-4" id="" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                        <div class="leftimg2">
                            <svg style="position: absolute;
    top: 0; left: 0;" width="4" height="244" viewBox="0 0 4 244" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="4" height="163" fill="#0074D9"/>
                                <rect y="175" width="4" height="27" fill="#0074D9"/>
                                <rect y="214" width="4" height="9" fill="#0074D9"/>
                                <rect y="235" width="4" height="9" fill="#0074D9"/>
                                </svg>
                        </div>


                    </div>

                    <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                        <div class="col-6">

                          <div class="proposal_personal" style=" padding-right: 0px !important;">
                              <b>
                                Acceptance
                              </b>
                                                      </div>

                        </div>
                                                      <div class="col-6 d-flex px-3">
                                                          <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                                                              <img id="add_logo_image" class="add_logo_image" src="{{ $logo && $logo->cov_image ? Storage::url($logo->cov_image) : '' }}" style="
                                                                          height: 39px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                                                          </div>
                                                      </div>
                      </div>



                    <div class="container px-5 mt-5">
                      <div class="row px-5 justify-content-evenly" style="margin-top: 80px;">
                     <div class="col-md-6">
                        <p style="font-family: Poppins;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 12px;
                        text-align: left;
                        color: #3E4756;
                        ">Client Name</p>
                        <p class="mt-3"><b style="font-family: Poppins;
                            font-size: 12px;
                            font-weight: 600;
                            line-height: 12px;
                            text-align: left;
                            color: #192A3E;
                            ">Johen Markes dion</b></p>
                        <hr class="mt-5" style="border: 1px dashed #90A0B7">
                        <p style="font-family: Poppins;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 12px;
                        text-align: left;
                        color: #3E4756;
                        ">Signature</p>
                        <hr class="mt-5" style="border: 1px dashed #90A0B7">
                        <p style="font-family: Poppins;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 12px;
                        text-align: left;
                        color: #3E4756;
                        ">Date</p>
                     </div>
                     <div class="col-md-6">
                        <p style="font-family: Poppins;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 12px;
                        text-align: left;
                        color: #3E4756;
                        ">Company Approval</p>
                        <p class="mt-3"><b style="font-family: Poppins;
                            font-size: 12px;
                            font-weight: 600;
                            line-height: 12px;
                            text-align: left;
                            color: #192A3E;
                            ">DEVANICS Technologies</b></p>
                        <hr class="mt-5" style="border: 1px dashed #90A0B7">
                        <p style="font-family: Poppins;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 12px;
                        text-align: left;
                        color: #3E4756;
                        ">Signature</p>
                        <hr class="mt-5" style="border: 1px dashed #90A0B7">
                        <p style="font-family: Poppins;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 12px;
                        text-align: left;
                        color: #3E4756;
                        ">Date</p>
                     </div>
                      </div>

                    </div>
                    <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                        <div class="col-6">
                            <b style="color: #0068C3;">
                                Company Name
                            </b>
                        </div>
                        <div class="col-6" style="display: flex;">
                            <b style="color: #0068C3; margin-left: auto;">
                                Comapnywebsite.com
                            </b>
                        </div>

                    </div>

                    </div>

                </div>
        </div>



    </section>
        @push('scripts')
        <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>




        @endpush



@endsection
