@extends('layouts.admin.master')

@push('tinymce-scripts')
<!-- TinyMCE -->
<script src="{{ asset('assets/tinymce/js/tinymce/tinymce.min.js') }}"></script>
@endpush
@section('title', 'Estimation Preview')
@section('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">
    <link rel="stylesheet" href="{{ asset('asset/assets/css/organization/proposal.css') }}">
@endsection
@section('section')
    <section class="dashboard_main">
        <div class="row justify-content-around">
            <div class="col-lg-4 sidenavsec">
                <div class="panel mt-4 mb-5">
                    <div class="main_side_menu">

                        <div class="row px-1">
                            <p style="color: #0074D9;">Select Proposal Pages

                                <br> <label for="" style="color: #90A0B7; font-size: 12px;">
                                    Customize and send your proposal  for  e-sign
                                </label>
                            </p>

                        </div>
                        <div class="row">
                            <ul class="w-100 px-2 sidemenuul">
                                <li class="d-flex mt-3 justify-content-between align-items-center w-100">
                                    <p class="mb-0">Proposal</p>
                                    <div class="d-flex editcover" style="margin-left: auto !important; gap: 10px;">
                                        <a href="#main_cover" class="custombtn cover_custom bluetext" onclick="showeditcover()">
                                            <b>Customize</b>
                                        </a>
                                        <button type="button" style="margin-top: 4px;" class="btn btn-toggle active coverdbtn" onclick="coverSection()" data-toggle="button" aria-pressed="false" autocomplete="off">
                                            <div class="handle"></div>
                                        </button>
                                    </div>
                                </li>
                                <li class="d-flex mt-3 justify-content-between align-items-center w-100">
                                    <p class="mb-0">About Us</p>

                                    <div class="d-flex editcover" style="margin-left: auto !important; gap: 10px;">
                                        <a href="#main_about" class="custombtn about_btn bluetext" onclick="showeditabout()">
                                            <b>Customize</b>
                                        </a>
                                        <button type="button" onclick="aboutSection()" style="margin-top: 4px;" class="btn btn-toggle active abutbtn" data-toggle="button" aria-pressed="false" autocomplete="off">
                                            <div class="handle"></div>
                                        </button>
                                    </div>


                                </li>
                                <li class="d-flex mt-3 justify-content-between align-items-center w-100">
                                    <p class="mb-0">Personal Introduction                                </p>

                                    <div class="d-flex editcover" style="margin-left: auto !important; gap: 10px;">
                                        <a href="#main_personal" class="custombtn intro_btn bluetext" onclick="showeditpersonal()">
                                            <b>Customize</b>
                                        </a>
                                        <button type="button" onclick="introSection()" style="margin-top: 4px;" class="btn btn-toggle active introbtn" data-toggle="button" aria-pressed="false" autocomplete="off">
                                            <div class="handle"></div>
                                        </button>
                                    </div>
                                </li>
                                <li class="d-flex mt-3 justify-content-between align-items-center w-100">
                                    <p class="mb-0">Gallery</p>

                                    <div class="d-flex editcover" style="margin-left: auto !important; gap: 10px;">
                                        <a href="#main_gallery" class="custombtn scope_btn bluetext" onclick="showeditgallery()">
                                            <b>Customize</b>
                                        </a>
                                        <button type="button" class="btn btn-toggle active" data-toggle="button" aria-pressed="false" autocomplete="off">
                                            <div class="handle"></div>
                                        </button>
                                    </div>



                                </li>
                                <li class="d-flex mt-3 justify-content-between align-items-center w-100">
                                    <p class="mb-0">Scope of work </p>

                                    <div class="d-flex editcover" style="margin-left: auto !important; gap: 10px;">
                                        <a href="#main_scpe" class="custombtn scope_btn bluetext" onclick="showeditsample()">
                                            <b>Customize</b>
                                        </a>
                                        <button type="button" onclick="scopeSection()" style="margin-top: 4px;" class="btn btn-toggle active scopbtn" data-toggle="button" aria-pressed="false" autocomplete="off">
                                            <div class="handle"></div>
                                        </button>
                                    </div>


                                </li>

                                <li class="d-flex mt-3 justify-content-between align-items-center w-100">
                                    <p class="mb-0">Terms & Conditions</p>

                                    <div class="d-flex editcover bluetext" style="margin-left: auto !important; gap: 10px;">
                                        <a href="#main_trms" class="custombtn terms_btn" onclick="showeditterms()">
                                            <b>Customize</b>
                                        </a>
                                        <button type="button" onclick="termsSection()" style="margin-top: 4px;" class="btn btn-toggle active termbtn" data-toggle="button" aria-pressed="false" autocomplete="off">
                                            <div class="handle"></div>
                                        </button>
                                    </div>

                                </li>
                                <li class="d-flex mt-3 justify-content-between align-items-center w-100">
                                    <p class="mb-0">Payment Schedule</p>

                                    <div class="d-flex editcover" style="margin-left: auto !important; gap: 10px;">
                                        <a href="#main_pmnt" class="custombtn payment_btn bluetext" onclick="showeditpayment()">
                                            <b>Customize</b>
                                        </a>
                                        <button type="button" onclick="paymentSection()" style="margin-top: 4px;" class="btn btn-toggle active paymntbtn" data-toggle="button" aria-pressed="false" autocomplete="off">
                                            <div class="handle"></div>
                                        </button>
                                    </div>

                                </li>

                            </ul>
                        </div>
                        <form action="{{URL::route(getRouteAlias() . '.send-mail.client')}}" method="post" enctype="multipart/form-data">
                            @csrf
                            <div class="row" style="margin-top: 31px;">
                                <div class="col-12">
                                    <a href="{{ route('organization.estimation.download-pdf', ['opportunityId' => $estimate->id]) }}" style="width: 100% !important; font-size: 15px; text-align: center;" class="downloadbtnnn">
                                        <svg width="13" height="13" viewBox="0 0 19 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M11.5827 1.36414V5.66678C11.5827 6.25017 11.5827 6.54186 11.6962 6.76469C11.7961 6.96069 11.9554 7.12005 12.1514 7.21991C12.3743 7.33345 12.666 7.33345 13.2494 7.33345H17.552M6.37435 14.625L9.49935 17.75M9.49935 17.75L12.6243 14.625M9.49935 17.75L9.49935 11.5M11.5827 1.08337H6.16602C4.41585 1.08337 3.54077 1.08337 2.8723 1.42398C2.28429 1.72358 1.80622 2.20165 1.50662 2.78965C1.16602 3.45813 1.16602 4.33321 1.16602 6.08337V16.9167C1.16602 18.6669 1.16602 19.542 1.50662 20.2104C1.80622 20.7984 2.28429 21.2765 2.8723 21.5761C3.54077 21.9167 4.41585 21.9167 6.16602 21.9167H12.8327C14.5828 21.9167 15.4579 21.9167 16.1264 21.5761C16.7144 21.2765 17.1925 20.7984 17.4921 20.2104C17.8327 19.542 17.8327 18.6669 17.8327 16.9167V7.33337L11.5827 1.08337Z" stroke="#0074D9" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>

                                        &nbsp;&nbsp;Download
                                    </a>
                                </div>

                            </div>
                        </form>
                    </div>

                    <div class="edit_gallery_section px-3" style="height: auto; padding: 0px 0px;">
                        <div class="row px-1">
                            <p style="color: #0074D9;">Customize Gallery Page</p>
                        </div>
                        <div class="row">
                            <div class="form-group w-100 mt-3">
                                <span for="coverpage">Title</span>
                                <input type="text" value="" id="gallery_title" class="form-control title_class" placeholder="Enter title">
                            </div>

                            <div class="form-group w-100">
                                <span for="">Upload Gallery Image</span>

                                <div class="file-upload-contain">
                                    <input id="multiplefileuploadgallery" type="file" accept=".jpg,.gif,.png"  />
                                </div>
                            </div>
                            <div class="form-group w-100 px-1" style="margin-top: 67px;">
                                <input type="checkbox" id="remem_settings_gallery" style="width: 12px;
                            height: 12px;
                            gap: 0px;
                            border-radius: 8px;

                            opacity: 0px;
                            border: 1px solid #B0D4F3
                            "><label for="remem_settings_gallery"> &nbsp;&nbsp;Remember these settings for Future Projects</label>

                            </div>
                            <div class="row w-100" style="">
                                <div class="col-6">
                                    <button class="dnbtn" id="cancelBtn" onclick="hideeditgallery()">

                                        &nbsp;&nbsp;Cancel
                                    </button>

                                </div>
                                <div class="col-6">
                                    <button class="sendcbtn d-flex justify-content-center" id="doneBtngallery">
                                        <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <p style="color: white; font-size: 12px;">Save</p>

                                    </button>
                                </div>

                            </div>

                        </div>
                    </div>








                    <!-- proposal cover page left panel starts here -->
                    <div class="edit_cover_section px-3" style="height: auto; padding: 0px 0px;">
                        <div class="row px-1">
                            <p style="color: #0074D9;">Customize Cover Page needs to change</p>
                        </div>
                        @if(!isset($template))
                            <div class="row">
                                <div class="form-group w-100 mt-3">
                                    <span style="font-size: 16px;" for="coverpage">Cover Title</span>
                                    <input type="text" value="{{ isset($cover) ? $cover->cov_title : '' }}" id="cov_title" class="form-control title_class" placeholder="Enter cover title" style="background-color : #FCFCFC; border: 1px solid #D6D6D6; font-size: 16px; border-radius: 3px;">
                                </div>
                                <div class="form-group w-100">
                                    <span style="font-size: 16px;" for="coverpage">Subtitle Text</span>
                                    <input type="text" value="{{isset($cover) ? $cover->cov_sub_title : ''}}" id="sub_tit" class="form-control title_class" placeholder="e.g Project name" style="background-color : #FCFCFC; border: 1px solid #D6D6D6; font-size: 16px; border-radius: 3px;">
                                </div>
                                <div class="form-group w-100">
                                    <span style="font-size: 16px;" for="">Upload Cover Image</span>

                                    <div class="file-upload-contain">
                                        <input id="multiplefileupload2" type="file" accept=".jpg,.gif,.png"  />
                                    </div>
                                </div>
                                <div class="form-group w-100 px-1" style="margin-top: 0px;">
                                    <input type="checkbox" id="remem_settings_cover" style="width: 12px;
                            height: 12px;
                            gap: 0px;
                            border-radius: 8px;

                            opacity: 0px;
                            border: 1px solid #B0D4F3
                            " {{ @$cover->project_checked ? 'checked' : '' }}><label style="font-size: 16px;" for="remem_settings_cover"> &nbsp;&nbsp;Remember these settings for Future Projects</label>

                                </div>
                                <div class="row w-100" style="">
                                    <div class="col-6">
                                        <button class="dnbtn" id="cancelBtn" onclick="hideeditcover()">

                                            &nbsp;&nbsp;Cancel
                                        </button>

                                    </div>
                                    <div class="col-6">
                                        <button class="sendcbtn d-flex justify-content-center" id="doneBtn">
                                            <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            <p style="color: white; font-size: 12px;">Save</p>

                                        </button>
                                    </div>

                                </div>


                            </div>
                        @endif
                        <div class="text-center mt-5"><h3>OR</h3></div>

                        <div class="form-group w-100">
                            <span style="font-size: 16px;" for="">Upload Your Own Template</span>

                            <div class="file-upload-contain">
                                <input id="multiplefileuploadtemplate" type="file" accept=".jpg,.gif,.png,.jpeg"  onchange="uploadFile(this)"/>
                            </div>

                            @if(isset($template))
                                <div class="file-preview-thumbnails hidetemplate" bis_skin_checked="1">
                                    <div class="file-preview-frame file-sortable  kv-preview-thumb" id="thumb-multiplefileuploadtemplate-611087_bg-two.jpg" data-fileindex="-1" data-fileid="611087_bg-two.jpg" data-template="image" data-zoom="" bis_skin_checked="1"><div class="kv-file-content" bis_skin_checked="1">
                                            <img src="{{isset($template->project_image) ? Storage::url($template->project_image) : asset('admin_assets/images/12.png')}}" class="file-preview-image kv-preview-data" title="bg-two.jpg" alt="bg-two.jpg" style="width: auto; height: auto; max-width: 100%; max-height: 100%; image-orientation: from-image;">
                                        </div><div class="file-thumbnail-footer" bis_skin_checked="1">
                                            <div class="file-detail" bis_skin_checked="1"><div class="file-caption-name" bis_skin_checked="1">bg-two.jpg</div>
                                                <div class="file-size" bis_skin_checked="1"> <samp>(596.76 KB)</samp></div>
                                            </div>   <div class="file-actions" bis_skin_checked="1">
                                                <div class="file-footer-buttons" bis_skin_checked="1">
                                                    <button type="button" class="kv-file-remove file-remove" onclick="window.location.href='{{ route('organization.deleteTemplate', encodeId($template->id)) }}'"
                                                            title="Remove file"><i class="fa fa-times"></i></button>
                                                </div>
                                            </div>
                                            <!-- <button class="file-drag-handle drag-handle-init file-drag" title="Move / Rearrange"><i class="fa fa-arrows-alt"></i></button> -->
                                            <div class="clearfix" bis_skin_checked="1"></div>
                                        </div>

                                        <div class="kv-zoom-cache" bis_skin_checked="1"></div></div>
                                    <button class="dnbtn mt-5" id="cancelBtn" onclick="hideeditcover()">

                                        &nbsp;&nbsp;Cancel
                                    </button>
                                </div>
                            @endif






                        </div>

                    </div>
                    <!-- proposal cover page left panel ends here -->


                    <!-- about us left panel starts here -->

                    <div class="edit_about_section px-3" style="height: auto">
                        <div class="row px-1">
                            <b style="color: #0074D9;">Customize About Us Page</b>
                        </div>
                        <div class="row">
                            <div class="form-group w-100 mt-3">
                                <span for="coverpage">Intro Text</span>
                                <textarea rows="10" id="textEditor" class="form-control title_class" placeholder="Intro text here.....">{!! $about?->cov_sub_title ?? '' !!}</textarea>
                            </div>

                            <div class="form-group w-100">
                                <span for="">Upload Cover Image</span>

                                <div class="file-upload-contain">
                                    <input id="multiplefileupload3" type="file" accept=".jpg,.gif,.png"/>
                                </div>
                            </div>
                            <div class="form-group w-100 px-1" style="margin-top: 87px;">
                                <input type="checkbox" id="remem_settings_about" style="width: 12px;
                            height: 12px;
                            gap: 0px;
                            border-radius: 8px;

                            opacity: 0px;
                            border: 1px solid #B0D4F3
                            " {{ @$about->project_checked ? 'checked' : '' }}><label for="remem_settings_about"> &nbsp;&nbsp;Remember these settings for Future Projects</label>

                            </div>
                            <div class="row w-100" style="">
                                <div class="col-6">
                                    <button class="dnbtn" onclick="hideeditabout()" id="cancelBtnabout">

                                        &nbsp;&nbsp;Cancel
                                    </button>

                                </div>
                                <div class="col-6">
                                    <button class="sendcbtn d-flex justify-content-center" id="doneBtnabout">
                                        <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <p style="color: white; font-size: 12px;">Save</p>

                                    </button>
                                </div>

                            </div>

                        </div>
                    </div>

                    <!-- about us left panel ends here -->




                    <div class="edit_personal_section px-3" style="height: auto">
                        <div class="row px-1">
                            <b style="color: #0074D9;">Customize Personal Introduction Page</b>
                        </div>
                        <div class="row">
                            <div class="form-group w-100 mt-3">
                                <span for="coverpage">Intro Text</span>
                                <textarea rows="10" id="intro_letter" class="form-control title_class" placeholder="Intro text here.....">{!!  $intro->cov_sub_title ?? ''!!}</textarea>
                            </div>

                            <div class="form-group w-100 px-1" style="margin-top: 254px;">
                                <input type="checkbox" id="remem_settings_intro" style="width: 12px;
                            height: 12px;
                            gap: 0px;
                            border-radius: 8px;

                            opacity: 0px;
                            border: 1px solid #B0D4F3
                            " {{ @$intro->project_checked ? 'checked' : '' }}><label for="remem_settings_intro"> &nbsp;&nbsp;Remember these settings for Future Projects</label>

                            </div>
                            <div class="row w-100" style="">
                                <div class="col-6">
                                    <button class="dnbtn" id="cancelBtnintro" onclick="hideeditpersonal()">

                                        &nbsp;&nbsp;Cancel
                                    </button>

                                </div>
                                <div class="col-6">
                                    <button class="sendcbtn d-flex justify-content-center" id="doneBtnintro">
                                        <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <p style="color: white; font-size: 12px;">Save</p>

                                    </button>
                                </div>

                            </div>

                        </div>
                    </div>


                    <div class="edit_sample_section px-3" style="height: auto">
                        <div class="row px-1">
                            <b style="color: #0074D9;">Customize Sample Work
                            </b>
                        </div>
                        <div class="row">
                            <div class="form-group w-100 mt-3">
                                <span for="coverpage">Title</span>
                                <input type="text" value="{{ $scope?->cov_title ?? '' }}" id="title_txt" class="form-control title_class" placeholder="Enter title">
                            </div>


                            <div class="form-group w-100 mt-3">
                                <span for="coverpage">Description</span>
                                <textarea rows="10" id="description_editor" class="form-control title_class" placeholder="Intro text here.....">{!! $scope?->cov_sub_title ?? '' !!}</textarea>
                            </div>
                            <div class="form-group w-100 px-1" style="margin-top: 26px;">
                                <input type="checkbox" id="remem_settings_scope" style="width: 12px;
                            height: 12px;
                            gap: 0px;
                            border-radius: 8px;

                            opacity: 0px;
                            border: 1px solid #B0D4F3
                            " {{ @$scope->project_checked ? 'checked' : '' }}><label for="remem_settings_scope"> &nbsp;&nbsp;Remember these settings for Future Projects</label>

                            </div>
                            <div class="row w-100" style="">
                                <div class="col-6">
                                    <button class="dnbtn" id="cancelBtnscope" onclick="hideeditsample()">

                                        &nbsp;&nbsp;Cancel
                                    </button>

                                </div>
                                <div class="col-6">
                                    <button class="sendcbtn d-flex justify-content-center" id="doneBtnscope">
                                        <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <p style="color: white; font-size: 12px;">Save</p>

                                    </button>
                                </div>

                            </div>

                        </div>
                    </div>


                    <div class="edit_terms_section px-3" style="height: auto">
                        <div class="row px-1">
                            <b style="color: #0074D9;">Terms & Conditions</b>
                        </div>
                        <div class="row">
                            <div class="form-group w-100 mt-3">
                                <span for="coverpage">General Terms & Conditions</span>
                                <textarea rows="10" id="gterms" class="form-control title_class" placeholder="Enter the term and conditions here">{!! $terms->cov_sub_title ?? ''  !!}</textarea>
                            </div>

                            <div class="form-group w-100 px-1" style="margin-top: 254px;">
                                <input type="checkbox" id="remem_settings_terms" style="width: 12px;
                            height: 12px;
                            gap: 0px
                            border-radius: 8px;

                            opacity: 0px;
                            border: 1px solid #B0D4F3
                            " {{ @$terms->project_checked ? 'checked' : '' }}><label for="remem_settings_terms"> &nbsp;&nbsp;Remember these settings for Future Projects</label>

                            </div>
                            <div class="row w-100" style="">
                                <div class="col-6">
                                    <button class="dnbtn" id="cancelgterms" onclick="hideeditterms()">

                                        &nbsp;&nbsp;Cancel
                                    </button>

                                </div>
                                <div class="col-6">
                                    <button class="sendcbtn d-flex justify-content-center" id="donebtngterms">
                                        <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <p style="color: white; font-size: 12px;">Save</p>

                                    </button>
                                </div>

                            </div>

                        </div>
                    </div>

                    <div class="edit_payment_section px-3" style="height: auto">
                        <div class="row px-1">
                            <b style="color: #0074D9;">Set Payment Schedule & Terms
                            </b>
                        </div>
                        <div class="row">
                            <div class="article-question w-100">
                                <span class="text-black">Does this Proposal Expire?</span><br>
                                <div class="radio-inline">
                                    <input class="radio-inline-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="yes"
                                        @checked(isset($payment) && $payment->expiry !== null)>

                                    <label class="radio-inline-label" for="inlineRadio1">Yes</label>
                                </div>
                                <div class="radio-inline">
                                    <input class="radio-inline-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="option2"
                                        @checked(!isset($payment) || $payment->expiry === null)>

                                    <label class="radio-inline-label" for="inlineRadio2">No</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group w-100 mt-3" id="proposalexpiry">
                                <span for="coverpage">Proposal Expiry Date</span>

                                <div class="input-group w-100 date" id="datepicker">
                                    <input type="date" value="{{isset($payment) ? $payment->expiry : ''}}" style="background: #FCFCFC; height: 27px;" class="form-control" placeholder="Pick a Date" id="date"/>


                                    <span class="input-group-append">
                                    <span class="input-group-text bg-light d-block">
                                      <i class="fa fa-calendar"></i>
                                    </span>
                                  </span>
                                </div>

                            </div>
                            <div class="form-group w-100">
                                <span for="coverpage">Payment Schedule</span>
                                <textarea rows="8" id="paymentschedle" class="form-control title_class" placeholder="Enter payment schedule here">{!! $payment?->cov_sub_title ?? '' !!}</textarea>
                            </div>
                            <div class="form-group w-100 mt-3">
                                <b for="coverpage">Total</b>
                                <p style="color: #7C8091; font-size: 12px;" id="gettotalpaym">${{$totalPriceSumss}}</p>
                            </div>
                            <div class="form-group w-100 mt-1">
                                <label for="coverpage">Down Payment</label>
                                @php
                                    $paymentSchedule = $payment?->payment_schedule ? json_decode($payment->payment_schedule) : null;
                                @endphp
                                <div class="d-flex" style="gap: 17px;">
                                    <input type="number" value="{{ $paymentSchedule?->down_payment ?? '' }}" oninput="getdown1()" min="0" max="{{$totalPriceSumss}}" id="getdown1" class="px-2" style="border: 1px solid #D6D6D6; background-color: #FCFCFC; width: 50%;" placeholder="$0.00"> <p>or</p> <input type="number" value="{{ $paymentSchedule?->down_payment_percent ?? ''}}" max="100" min="0" oninput="getdown2()" id="getdown2" class="px-2" style="border: 1px solid #D6D6D6; background-color: #FCFCFC; width: 50%;" placeholder="20%">
                                </div>
                            </div>
                            <div class="form-group w-100 px-1" style="margin-top: 80px;">
                                <input type="checkbox" id="remem_settings_payments" style="width: 12px;
                            height: 12px;
                            gap: 0px;
                            border-radius: 8px;

                            opacity: 0px;
                            border: 1px solid #B0D4F3
                            " {{ @$payment->project_checked ? 'checked' : '' }}><label for="remem_settings"> &nbsp;&nbsp;Remember these settings for Future Projects</label>

                            </div>
                            <div class="row w-100" style="">
                                <div class="col-6">
                                    <button class="dnbtn" onclick="hideeditpayment()">

                                        &nbsp;&nbsp;Cancel
                                    </button>

                                </div>
                                <div class="col-6">
                                    <button class="sendcbtn d-flex justify-content-center" id="donepaymentsave">
                                        <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <p style="color: white; font-size: 12px;">Save</p>

                                    </button>
                                </div>

                            </div>

                        </div>
                    </div>




                </div>
                <a href="{{route('organization.organization.opportunityestimation', encodeId($opportunityId))}}" class="sendcbtn" style="margin-top: 51px !important; color: white; font-size: 16px; padding: 7px 30px; cursor: pointer">
                    <i class="fa fa-long-arrow-left" style="color: white" aria-hidden="true"></i>


                    &nbsp;&nbsp;Back
                </a>
            </div>

            <div class="col-lg-8 main-div-sec">
                @if(isset($template))
                    <div class="panel panel23 coverd mt-4" id="main_cover" style="width: 90%; margin-left: 5%; overflow: hidden;">

                        <img src="{{ isset($template->project_image) ? Storage::url($template->project_image) : asset('admin_assets/images/12.png') }}" style="min-width: 100% !important" alt="">

                    </div>


                @else
                    <div class="panel panel23 coverd mt-4" id="main_cover" style="width: 90%; margin-left: 5%; overflow: hidden; position: relative;">
                        <div class="">
                            <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                                <div class="col-6">

                                    <div class="proposal" style="margin-left: -21px;">
                                        <b>
                                            Proposal
                                        </b>
                                    </div>

                                </div>
                                <div class="col-6 d-flex px-3">
                                    <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                        <img id="add_logo_image" class="add_logo_image" src="{{ Storage::url('user_images/' . $organization->profile_photo_path) }}" style="
                                                                          height: 65px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                    </div>
                                </div>
                            </div>


                            <div class="row">
                                <div class="logo" style="margin-top: 43px; margin-left: 7.5%;">
                                    <h2 id="add_cov_tit">{{isset($cover) ? $cover->cov_title : ''}}</h2>
                                </div>

                            </div>
                            <div class="row2">
                                <div class="imge" style="margin-top: -107px;">
                                    <div class="image-container">
                                        <img id="preview_image" src="{{isset($cover->cov_image) ? Storage::url($cover->cov_image) : asset('admin_assets/images/12.png')}}" style="width: 100%;" alt="">
                                        <div class="centered-text">

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row justify-content-end">
                                <div class="col-11" style="margin-left: auto;">
                                    <div class="subtitle-text-main text-center" style="margin-left: auto;">
                                        <p id="add_sub_tit">{{isset($cover) ? $cover->cov_sub_title : ''}}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="around-padding-all">
                                <div class="container mt-4" style="padding-bottom: 80px">
                                    <div class="row ">
                                        <div class="col-md-4">
                                            <label for="" style="color: #7C8091;">
                                                Submitted to:
                                            </label>
                                            <p>
                                                <b>{{$estimate->contactInformation->first_name}} {{$estimate->contactInformation->last_name}}</b>
                                            </p>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="" style="color: #7C8091;">
                                                Submitted by:
                                            </label>
                                            <p>
                                                <b>{{$organization->first_name}} {{$organization->last_name}}</b>
                                            </p>
                                            <label for="">
                                                {{$organization->company_name}}
                                            </label>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="" style="color: #7C8091;">
                                                Date Submitted:
                                            </label>
                                            <p>
                                                <b>September 27th, 2024</b>
                                            </p>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">

                                </b>
                            </div>

                        </div>
                    </div>
                @endif





               <!-- about us right panel starts here -->


                <div class="panel panel22 about mt-4" id="main_about" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                          </div>

                        <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                            <div class="col-6">

                                <div class="proposal" style="margin-left: -24px;">
                                    <b>
                                        About Us
                                    </b>
                                </div>

                            </div>
                            <div class="col-6 d-flex px-3">
                                <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                    <img id="add_logo_image" class="add_logo_image" src="{{ Storage::url('user_images/' . $organization->profile_photo_path) }}" style="
                                                                          height: 65px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                </div>
                            </div>
                        </div>
                        <div class="around-padding-all">
                            <div class="container mt-5 pb-5">
                                <div class="row px-1">
                                    <br>
                                    <p class="" id="add_about_text" style="color: #3B4159; font-size: 14px;">
                                        {!! $about->cov_sub_title ?? '' !!}
                                    </p>
                                </div>

                                <div class="row2">
                                    <div class="imge" style="margin-top: -107px;">
                                        <div class="image-container about-us-img">
                                            <img id="preview_image" src="{{isset($about) ? Storage::url($about->cov_image) : asset('admin_assets/images/12.png')}}" style="width: 100%; margin-top: 3%;" alt="">
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row px-5 justify-content-between bottom_row py-2" style="background-color: #E6F1FB; margin-top: 0px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">

                                </b>
                            </div>

                        </div>

                    </div>

                </div>


                <!-- about us right panel ends here -->








                <div class="panel panel22 personal mt-4" id="main_personal" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                        </div>

                        <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                            <div class="col-6">

                                <div class="proposal_personal" style="">
                                    <b>
                                        Personal Introduction
                                    </b>
                                </div>

                            </div>
                            <div class="col-6 d-flex px-3">
                                <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                    <img id="add_logo_image" class="add_logo_image" src="{{ Storage::url('user_images/' . $organization->profile_photo_path) }}" style="
                                                                          height: 65px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                </div>
                            </div>
                        </div>

                        <div class="around-padding-all">
                            <div class="container mt-5">
                                <div class="row px-1">
                                    <br>
                                    <p class="" id="add_intro_letter" style="color: #3B4159; font-size: 14px;">
                                        {!! $intro->cov_sub_title ?? '' !!}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">

                                </b>
                            </div>

                        </div>

                    </div>

                </div>

                <div class="panel panel22 scop_of_work mt-4" id="main_scpe" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                        </div>
                        <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                            <div class="col-6">

                                <div class="proposal_personal" style="">
                                    <b>
                                        Scope of work
                                    </b>
                                </div>

                            </div>
                            <div class="col-6 d-flex px-3">
                                <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                    <img id="add_logo_image" class="add_logo_image" src="{{ Storage::url('user_images/' . $organization->profile_photo_path) }}" style="
                                                                          height: 65px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                </div>
                            </div>
                        </div>

                        <div class="around-padding-all">
                            <div class="container  mt-4" style="padding-bottom: 40px;">
                                <div class="px-1">
                                    <br>
                                    <p class="" style="color: #3B4159; font-size: 14px; text-align: left;">
                                        <b id="scope_title" style="text-align: left;">{!! $scope?->cov_title ?? '' !!}</b> <br>
                                        <label id="scope_des" for="" style="text-align: left;">
                                            {!! $scope?->cov_sub_title ?? '' !!}
                                        </label>
                                    </p>
                                </div>

                                <div class="row mt-5 pb-5" style="justify-content: center;">
                                    <div class="border" style="width: 100%; padding: 10px; border: 1px solid #E7E7E7; border-radius: 8px;">
                                        <table class="table table-striped">
                                            <thead style="background: #DCF2FF; border: 1px solid transparent;
">
                                            <tr style="border: none;">
                                                <th>Item / Description</th>
                                                <th>Uom</th>
                                                <th>Quantity</th>
                                                <th>Unit Price</th>
                                                {{-- <th>Total Price</th> --}}
                                            </tr>
                                            </thead>
                                            <tbody>
                                            @foreach ($opportunity as $item)
                                                <tr>
                                                    <td>{{ $item->item_name }}</td>
                                                    <td>{{ $item->uom }}</td>
                                                    <td>{{ $item->quantity }}</td>
                                                    <td>${{ $item->unit_price }}</td>
                                                    {{-- <td>${{ $item->total_price }}</td> --}}
                                                </tr>
                                            @endforeach
                                            </tbody>
                                        </table>

                                    </div>

                                </div>
                                <div class="row text-right" style="display: contents; margin-bottom: 50px;">
                                    <p style="font-size: 12px;">Total: &nbsp;&nbsp; <b style="color: #0074D9; font-size: 16px;">$ {{$totalPriceSumss}}</b></p>
                                </div>
                            </div>
                        </div>
                        <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 200px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">

                                </b>
                            </div>

                        </div>

                    </div>

                </div>

                <div class="panel panel22 terms_conditions mt-4" id="main_trms" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                        </div>

                        <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                            <div class="col-6">

                                <div class="proposal_personal" style="">
                                    <b>
                                        Term & Conditions
                                    </b>
                                </div>

                            </div>
                            <div class="col-6 d-flex px-3">
                                <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                    <img id="add_logo_image" class="add_logo_image" src="{{ Storage::url('user_images/' . $organization->profile_photo_path) }}" style="
                                                                          height: 65px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                </div>
                            </div>
                        </div>

                        <div class="around-padding-all2">
                            <div class="container mt-5 pb-5">
                                <div class="">
                                    <br>
                                    <b class="" id="gtermstext">General Terms</b>
                                    <br>

                                    <p class="" id="add_gterms" style="color: #3B4159; font-size: 12px;">

                                        {!! $terms->cov_sub_title ?? '' !!}

                                    </p>



                                </div>
                            </div>
                        </div>
                        <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">

                                </b>
                            </div>

                        </div>

                    </div>

                </div>

                <div class="panel panel22 image_gallery mt-4" id="main_gallery" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">


                        </div>

                        <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                            <div class="col-6">

                                <div class="proposal_personal" style="">
                                    <b>
                                        Image gallery
                                    </b>
                                </div>

                            </div>
                            <div class="col-6 d-flex px-3">
                                <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                    <img src="{{ $logo && $logo->cov_image ? Storage::url($logo->cov_image) : '' }}" style="
                                                                          height: 39px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                </div>
                            </div>
                        </div>


                        <div class="container px-5 mt-5">
                            <div class="row px-3 justify-content-evenly">
                                @if(count($hardMaterialImages) > 0)
                                    @foreach ($hardMaterialImages as $image)
                                        <div class="col-md-6 position-relative" style="margin-bottom: 20px;">
                                            <!-- Image -->
                                            <img src="{{ Storage::url($image['image']) }}" style="width: 100%; height: 174px;" alt="" class="gallery-image">

                                            <!-- Caption -->
                                            <p class="text-left">{{ $image['name'] ?? '' }}</p>
                                        </div>
                                    @endforeach
                                @endif
                                @if(isset($gallerys))
                                    @foreach($gallerys as $gallery)
                                        <div class="col-md-6 position-relative" style="margin-bottom: 20px;">
                                            <!-- Image -->
                                            <img src="{{ Storage::url($gallery->cov_image) }}" style="width: 100%; height: 174px;" alt="" class="gallery-image">

                                            <!-- Delete Icon -->
                                            <button class="delete-icon btn btn-danger btn-sm" onclick="deleteImage(this)" data-image="{{ $gallery->id }}">
                                                <i class="fas fa-trash-alt" style="color: white"></i>
                                            </button>

                                            <!-- Caption -->
                                            <p class="text-left">{{ $gallery->cov_title ?? '' }}</p>
                                        </div>
                                    @endforeach
                                @endif
                            </div>


                        </div>
                        <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">

                                </b>
                            </div>

                        </div>

                    </div>

                </div>

                <div class="panel panel22 payment_scdul mt-4" id="main_pmnt" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                        </div>

                        <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                            <div class="col-6">

                                <div class="proposal_personal" style="">
                                    <b>
                                        Payments Scheduled & terms
                                    </b>
                                </div>

                            </div>
                            <div class="col-6 d-flex px-3">
                                <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                    <img id="add_logo_image" class="add_logo_image" src="{{ Storage::url('user_images/' . $organization->profile_photo_path) }}" style="
                                                                          height: 65px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                </div>
                            </div>
                        </div>
                        <div class="around-padding-all2">
                            <div class="container mt-5 pb-5">
                                <div class="">
                                    <label id="add_prpslexpiry">Proposal Expiry Date</label>
                                    <br>
                                    <label id="add_date_expiry"><b id="add_fixdate">
                                            @if(isset($payment) && $payment->expiry != null)
                                                {{ \Carbon\Carbon::parse($payment->expiry)->format('F d, Y') }}
                                                <!-- {{$payment->expiry}} -->
                                            @endif
                                        </b></label>
                                    <br>

                                    <p id="add_pmntschdle"><span>Payment Schedule</span></p>
                                    <label id="add_payment_schedule">
                                            {!! $payment->cov_sub_title ?? ''  !!}
                                        </label>
                                    <br>
                                    <label>Total</label>
                                    <br>
                                    <b id="totalamont"></b>
                                    <br>
                                    <br>
                                    <label>Down Payments</label>


                                    <br>
                                    <div class="d-flex" style="gap: 13px;"><b id="downpaym1">
                                            @php
                                                $paymentSchedule = $paymentorg?->payment_schedule ? json_decode($paymentorg->payment_schedule) : null;
                                            @endphp
                                            @if(isset($paymentSchedule) && isset($paymentSchedule->down_payment))
                                                ${{ number_format($paymentSchedule->down_payment, 2) }}
                                            @endif
                                        </b>
                                        <b id="downpaym2"> @if(isset($paymentSchedule) && $paymentSchedule->down_payment_percent)
                                                {{ number_format($paymentSchedule->down_payment_percent, 2) }}%
                                            @endif</b></div>
                                    <br>

                                    <!-- <span>Balance</span><br>
                                    <b id="totalbalancetext">345$</b> -->
                                </div>
                            </div>
                        </div>



                        <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">

                                </b>
                            </div>

                        </div>

                    </div>

                </div>


                <div class="panel panel22 mt-4" id="" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                        </div>

                        <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                            <div class="col-6">

                                <div class="proposal_personal" style=";">
                                    <b>
                                        Acceptance
                                    </b>
                                </div>

                            </div>
                            <div class="col-6 d-flex px-3">
                                <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                    <img id="add_logo_image" class="add_logo_image" src="{{ Storage::url('user_images/' . $organization->profile_photo_path) }}" style="
                                                                          height: 65px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                </div>
                            </div>
                        </div>

                        <div class="around-padding-all2">

                            <div class="container mt-5">
                                <div class="row justify-content-evenly" style="margin-top: 80px;">
                                    <div class="col-md-6">
                                        <p style="font-family: sans-serif;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 12px;
                        text-align: left;
                        color: #3E4756;
                        ">Client Name</p>
                                        <p class="mt-3"><b style="font-family: sans-serif;
                            font-size: 12px;
                            font-weight: 600;
                            line-height: 12px;
                            text-align: left;
                            color: #192A3E;
                            ">{{$estimate->contactInformation->first_name}} {{$estimate->contactInformation->last_name}}</b></p>
                                        <hr class="mt-5" style="border: 1px dashed #90A0B7">
                                        <p style="font-family: sans-serif;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 12px;
                        text-align: left;
                        color: #3E4756;
                        ">Signature</p>
                                        <hr class="mt-5" style="border: 1px dashed #90A0B7">
                                        <p style="font-family: sans-serif;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 12px;
                        text-align: left;
                        color: #3E4756;
                        ">Date</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p style="font-family: sans-serif;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 12px;
                        text-align: left;
                        color: #3E4756;
                        ">Company Approval</p>
                                        <p class="mt-3"><b style="font-family: sans-serif;
                            font-size: 12px;
                            font-weight: 600;
                            line-height: 12px;
                            text-align: left;
                            color: #192A3E;
                            ">{{$organization->company_name}}</b></p>
                                        <hr class="mt-5" style="border: 1px dashed #90A0B7">
                                        <p style="font-family: sans-serif;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 12px;
                        text-align: left;
                        color: #3E4756;
                        ">Signature</p>
                                        <hr class="mt-5" style="border: 1px dashed #90A0B7">
                                        <p style="font-family: sans-serif;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 12px;
                        text-align: left;
                        color: #3E4756;
                        ">Date</p>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">

                                </b>
                            </div>

                        </div>

                    </div>

                </div>
            </div>


            <!-- Modal -->
            <div class="modal fade" id="logoModel" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
                <div class="modal-dialog model-lg modal-logo">
                    <div class="modal-content">
                        <div class="modal-header" style="background: #E1F4FF;
">
                            <h4 class="modal-title px-3" id="addItemModalLabel">
                                <b style="color: #0074d9 !important">Add Logo</b>
                            </h4>
                            <button
                                type="button"
                                class="btn-close hidemodelbtn px-3"
                                data-dismiss="modal"
                                aria-label="Close"
                                style="border: none; background-color: transparent"
                            >
                                <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                            </button>
                        </div>
                        <div class="modal-body">

                            <div class="form-group">
                                <div class="file-upload-contain">
                                    <input id="multiplefileupload5" onchange="changeLog()" type="file" accept=".jpg,.gif,.png, .webp, .svg, .jpeg"/>
                                </div>

                                @if($logo != null)
                                    <div class="maind">
                                        <img style="width: 20%;" src="{{ $logo && $logo->cov_image ? Storage::url($logo->cov_image) : '' }}" alt="" class="imgadded">
                                        <button style="background-color: red; color: black; border: none; padding: 1px 11px; font-size: 13px; border-radius: 5px; margin-left: 4%;" class="deleted_logo">X</button>
                                    </div>
                                @endif


                            </div>


                            <button type="button" id="add_logo_btn" class="btn btn-primary form-control">Add Logo</button>

                        </div>

                    </div>
                </div>
            </div>

            <!-- Modal -->
            @include('organization.partials.send-email', [
              'to_email' => $estimate->contactInformation->accountget->email,
              'sentInvoiceAttachment' => false,
              'isExternalSubmitHandler' => 'sendEmailHandler',
              'isOperational' => true,
              'sales_order_number' => $estimate?->job_no,
          ])

    </section>
    @push('scripts')
        <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
        <!-- TinyMCE loaded via @push('tinymce-scripts') -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
        <script src="{{ asset('asset/assets/js/bootstrap.js') }}"></script>

        <script src="{{ asset('asset/assets/js/organization/proposal.js') }}"></script>

        <script>
            function uploadFile(input) {
                const file = input.files[0]; // Get the selected file
                if (!file) return; // Exit if no file is selected
                $('.hidetemplate').css('display', 'none');

                const formData = new FormData();
                formData.append('file', file);
                formData.append('_token', '{{ csrf_token() }}'); // CSRF token for security
                var opportunity_id= {{$opportunityId}};
                formData.append('opportunity_id', opportunity_id);

                $.ajax({
                    url: '{{ route("organization.image.upload.template") }}', // URL to the upload route
                    type: 'POST',
                    data: formData,
                    processData: false, // Prevent jQuery from processing the data
                    contentType: false, // Prevent jQuery from setting content type
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                            $('.hidetemplate').css('display', 'none');
                            // alert('Image uploaded/updated successfully!');
                        } else {
                            alert(response.message || 'Something went wrong!');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        alert('Failed to upload the image.');
                    }
                });
            }
        </script>
        <script>
            function deleteImage(element) {
                const imageName = element.getAttribute('data-image');

                // Perform the AJAX request directly without confirmation
                fetch('{{ route("organization.gallery.delete") }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ image_name: imageName }),
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.message) {
                            // alert(data.message); // Optional: Show success message
                            location.reload(); // Reload the page to reflect the changes
                        }
                    })
                    .catch(error => console.error('Error:', error));
            }
        </script>
        <script>
            $(document).on("sendEmailHandler", sendEmailHandler);

            function sendEmailHandler(e) {
                var formData = {};
                if (e.customData instanceof FormData) {
                    formData = e.customData;
                }

                formData.append('sentEmail', true);

                var downloadUrl = $('.saveAndDownload').attr('data-url');
                var fileName = $('.saveAndDownload').attr('data-file-name');

                $.ajax({
                    url: downloadUrl,
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    xhrFields: {
                        responseType: 'blob'
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        // Check if the response is not empty
                        // if (response.size > 0) {
                        // File download success, use FileSaver.js to trigger the download
                        // saveAs(response, fileName + '.pdf');
                        $('#emailModal').hide();
                        // toastr.success("Proposal Successfully Sent!");

                        $('#generateInvoiceConfirm .modal-body').html('');

                        // $("#successModal").modal("show");

                        // $(".dynamic-success-data").html("");
                        $("#generateInvoiceConfirm .modal-body").append(
                            ` <div class="text-center">
    <svg width="40" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="35" cy="35" r="35" fill="#27AE60" />
        <path d="M22.3369 36.81L29.5732 44.0463L47.6639 25.9556" stroke="white" stroke-width="6" stroke-linecap="round"
            stroke-linejoin="round" />
    </svg>
</div><h2 class="title text-center">Done</h2>
             <p class="para mt-3 text-center">Proposal Submitted Successfully!</p> <a href = "{{ route(getRouteAlias() . '.opportunity.index') }}" class="btn primaryblue w-100 mt-5 ">Close</a>`
                        );
                        $('#generateInvoiceConfirm').modal('show');

                    },
                    error: function(xhr, status, error) {
                        console.error(error);
                        toastr.error("Error occurred while downloading the file.");
                    }
                });
            }
        </script>

        <script>
            $(document).ready(function() {
                $(".saveAndDownload").click(function(e) {
                    // alert('alert');
                    e.preventDefault();
                    if ($(e.target).hasClass('send-email')) {
                        // Set Email Modal Header
                        $('#emailModal .modal-title').html(
                            "Email Estimate #{{ $estimate?->job_no }} to " +
                            "{{ $estimate->contactInformation->first_name }}" + ' ' + "{{ $estimate->contactInformation->last_name }}")

                        // Set Email Defualt Subject

                        $('#emailModal #subject').val(
                            'Estimate From {{ optional($organization)?->company_name }} - {{ \Carbon\Carbon::now()->format('F j, Y') }}'
                        )

                        var website = @json(optional($organization->companyAddress)->website_url);
                        var sumhtml = '<span class="im">' +
                            '<h1 style="font-style: normal; font-weight: 700; font-size: 24px; line-height: 33.6px; color: rgb(25, 42, 62);">Hi {{ $estimate->contactInformation->first_name }} {{ $estimate->contactInformation->last_name }}, </h1>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 24px;">Thank you for choosing {{ $organization->company_name }}. </p>' +
                            '<p style="font-family: Arial, Helvetica, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 24px;">The estimate total is <strong style="color: rgb(25, 42, 62);">' + '$' +
                            {{$totalPriceSumss}} +
                            '</strong></p>' +
                            '<p><span class="im" style="color: rgb(80, 0, 80); font-family: Arial, Helvetica, sans-serif; font-size: small;"></span><span class="im" style="color: rgb(80, 0, 80); font-family: Arial, Helvetica, sans-serif; font-size: small;"></span></p>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 10px;">If you have any questions, please do not hesitate to contact us at<span>&nbsp;</span><a target="_blank" rel="noopener noreferrer" href="mailto:{{ optional($organization)->email }}" style="color: rgb(17, 85, 204);"> {{ optional($organization)->email }}</a>.<br>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 10px;"><br>Regards, </p></br>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ $organization->company_name }} </strong> </p>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ $organization->email }} </strong> </p>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ optional($organization->companyAddress)->phone_no }} </strong> </p>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ optional($organization->companyAddress)->address1 }} </strong> </p>';

                        if (website) {
                            sumhtml +=
                                '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> ' +
                                website + ' </strong> </p>';
                        }
                        $('#emailModal .summernote').summernote('code', sumhtml);
                        // Set Email Defualt Message
                        $('#emailModal').modal('show')

                    }

                });

                $(".downloadPDF").click(function(e) {
                    e.preventDefault();
                    var downloadUrl = e.target.getAttribute('data-url');
                    var fileName = e.target.getAttribute('data-file-name');
                    $.ajax({
                        type: 'GET',
                        url: downloadUrl,
                        xhrFields: {
                            responseType: 'blob'
                        },
                        success: function(response) {
                            // Check if the response is not empty
                            if (response.size > 0) {
                                // File download success, use FileSaver.js to trigger the download

                                saveAs(response, fileName + '.pdf');
                            } else {
                                toastr.error("File not found!");
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error(error);
                            toastr.error("Error occurred while downloading the file.");
                        }
                    });
                });
            });
        </script>
        <script>
            const image = document.getElementById('preview_about_image');

            // Get the exact value of the src attribute
            const imgSrc = image.getAttribute('src');

            if (!imgSrc || imgSrc === "") {
                $('.pspdfkit-1thsp3e').css('display', 'none');
            } else {
                $('.pspdfkit-1thsp3e').css('display', 'block');
            }
        </script>

        <script>
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

        </script>
        <script>
            var t=$('#gettotalpaym').text();
            $('#totalamont').text(t);
            var t2=$('#totalbalance').text();
            $('#totalbalancetext').text(t2);

            function getdown1(){
                const max = "{{$totalPriceSumss}}"; // Maximum allowed value
                const input = document.getElementById("getdown1");

                // Check if the input value is greater than the maximum
                if (parseFloat(input.value) > max) {
                    input.value = max; // Set input to max if it exceeds
                }
                var id = $('#getdown1').val();
                var ttl="{{$totalPriceSumss}}";
                var dd=id/ttl;
                var dd2=dd*100;
                console.info(dd2);
                $('#downpaym1').text(id+'$');
                $('#downpaym2').text(dd2.toFixed(2)+'%');
                $('#getdown2').val(dd2.toFixed(2));


            }
            function getdown2(){
                const input = document.getElementById("getdown2");
                if (parseFloat(input.value) > 100) {
                    input.value = 100; // Set input to max if it exceeds
                }
                var id = $('#getdown2').val();
                var ttl="{{$totalPriceSumss}}";
                var dd2=id/100;
                var dd=dd2*ttl;
                $('#downpaym2').text(id+'%');
                $('#downpaym1').text(dd.toFixed(2)+'$');
                $('#getdown1').val(dd);


            }

        </script>

        <script>
            function changeLog()
            {
                $('.maind').css('display', 'none');
                $('.deleteprev').css('display', 'block');
            }
        </script>
        <script>
            $(document).ready(function() {
                // Event listener for the remove button inside the deleted_logo div
                $('.deleted_logo').on('click', function() {
                    // Action to perform when the button is clicked
                    var url = "{{ URL::route(getRouteAlias() . '.storeDefaultSettingsLogoDelete') }}";
                    $.ajax({
                        url: url, // Replace with your backend URL
                        type: 'GET', // Use POST method
                        success: function(response) {
// Set the image source
                            $('.add_logo_image').attr('src', '');
                            $('.maind').css('display', 'none');
                            console.log('Data sent successfully:', response);
                            // Optionally, you can perform actions here based on the response
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            // Handle any errors
                            console.error('Error sending data:', textStatus, errorThrown);
                        }
                    });

                });
            });

        </script>

        <script>
            document.getElementById('add_logo_btn').addEventListener('click', function() {
                // Fix current input values and image as final


                let formData = new FormData();
// Get the file input element
                let fileInput = document.getElementById('multiplefileupload5'); // Replace 'multiplefileupload2' with
// Check if a file is selected
                if (fileInput.files.length > 0) {
                    formData.append('image_file', fileInput.files[0]);
                }else{
                    alert('Please select an image file.');
                    return;
                }
                var url = "{{ URL::route(getRouteAlias() . '.storeDefaultSettingsLogo') }}";

// Send the data to the backend using AJAX
                $.ajax({
                    url: url, // Replace with your backend URL
                    type: 'POST', // Use POST method
                    data: formData,
                    processData: false, // Important: prevent jQuery from converting the data to a query string
                    contentType: false, // Important: set content type to false
                    success: function(response) {
                        // Handle the response from the server
                        var data2=response.data.cov_image;
                        console.info(response.data.cov_image)
                        var imageUrl = "{{ Storage::url('') }}" + data2; // Get the base URL from Laravel

                        // To hide the modal programmatically
                        $('#logoModel').modal('hide');
                        $('.maind').css('display', 'inline-block');
                        $('.deleteprev').css('display', 'none');

// Set the image source
                        $('.add_logo_image').attr('src', imageUrl);
                        $('.imgadded').attr('src', imageUrl);
                        console.log('Data sent successfully:', response);
                        // Optionally, you can perform actions here based on the response
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        // Handle any errors
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });

            });

        </script>
        <script>
            tinymce.init({
                license_key: 'free',
                selector: '#intro_letter',
                plugins: 'lists code textcolor',
                toolbar: 'bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist',
                height: 300,
                menubar: false,
                branding: false,
                toolbar_mode: 'wrap',
                content_style: `
                    body { font-family: Helvetica, Arial, sans-serif; font-size: 14px; line-height: 1.6; }
                    ul { list-style-type: disc; margin-left: 20px; padding-left: 0; }
                    ol { list-style-type: decimal; margin-left: 20px; padding-left: 0; }
                    li { margin-bottom: 5px; }
                    p { margin: 0 0 10px 0; }
                    h1, h2, h3, h4, h5, h6 { margin: 10px 0; }
                    .mce-content-body { text-decoration: inherit; }
                `,
                init_instance_callback: function(editor) {
                    // Ensure all formatting plugins are properly loaded
                    editor.execCommand('mceToggleFormat', false, 'strikethrough');
                    editor.execCommand('mceToggleFormat', false, 'strikethrough');
                },
                setup: function (editor) {
                    editor.on('init', function () {
                        // Set initial content to the div on load
                        document.getElementById('add_intro_letter').innerHTML = editor.getContent();
                    });
                    editor.on('input keyup setcontent change', function () {
                        // Get content and set it to the div with proper formatting
                        document.getElementById('add_intro_letter').innerHTML = editor.getContent();
                    });
                }
            });

            tinymce.init({
                selector: '#gterms',
                plugins: 'lists code textcolor',
                toolbar: 'bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist',
                height: 300,
                menubar: false,
                branding: false,
                toolbar_mode: 'wrap',
                content_style: `
                    body { font-family: Helvetica, Arial, sans-serif; font-size: 14px; line-height: 1.6; }
                    ul { list-style-type: disc; margin-left: 20px; padding-left: 0; }
                    ol { list-style-type: decimal; margin-left: 20px; padding-left: 0; }
                    li { margin-bottom: 5px; }
                    p { margin: 0 0 10px 0; }
                    h1, h2, h3, h4, h5, h6 { margin: 10px 0; }
                    .mce-content-body { text-decoration: inherit; }
                `,
                init_instance_callback: function(editor) {
                    // Ensure all formatting plugins are properly loaded
                    editor.execCommand('mceToggleFormat', false, 'strikethrough');
                    editor.execCommand('mceToggleFormat', false, 'strikethrough');
                },
                setup: function (editor) {
                    editor.on('init', function () {
                        // Set initial content to the div on load
                        document.getElementById('gtermstext').innerHTML = editor.getContent();
                    });
                    editor.on('input keyup setcontent change', function () {
                        // Get content and set it to the div with proper formatting
                        document.getElementById('gtermstext').innerHTML = editor.getContent();
                    });
                }
            });
            tinymce.init({
                selector: '#description_editor',
                plugins: 'lists code textcolor',
                toolbar: 'bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist',
                height: 300,
                menubar: false,
                branding: false,
                toolbar_mode: 'wrap',
                content_style: `
                    body { font-family: Helvetica, Arial, sans-serif; font-size: 14px; line-height: 1.6; }
                    ul { list-style-type: disc; margin-left: 20px; padding-left: 0; }
                    ol { list-style-type: decimal; margin-left: 20px; padding-left: 0; }
                    li { margin-bottom: 5px; }
                    p { margin: 0 0 10px 0; }
                    h1, h2, h3, h4, h5, h6 { margin: 10px 0; }
                    .mce-content-body { text-decoration: inherit; }
                `,
                init_instance_callback: function(editor) {
                    // Ensure all formatting plugins are properly loaded
                    editor.execCommand('mceToggleFormat', false, 'strikethrough');
                    editor.execCommand('mceToggleFormat', false, 'strikethrough');
                },
                setup: function (editor) {
                    editor.on('init', function () {
                        // Set initial content to the div on load
                        document.getElementById('scope_des').innerHTML = editor.getContent();
                    });
                    editor.on('input keyup setcontent change', function () {
                        // Get content and set it to the div with proper formatting
                        document.getElementById('scope_des').innerHTML = editor.getContent();
                    });
                }
            });

            tinymce.init({
                license_key: 'free',
                selector: '#textEditor',
                plugins: 'lists code textcolor',
                toolbar: 'bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist',
                height: 300,
                menubar: false,
                branding: false,
                toolbar_mode: 'wrap',
                content_style: `
                    body { font-family: Helvetica, Arial, sans-serif; font-size: 14px; line-height: 1.6; }
                    ul { list-style-type: disc; margin-left: 20px; padding-left: 0; }
                    ol { list-style-type: decimal; margin-left: 20px; padding-left: 0; }
                    li { margin-bottom: 5px; }
                    p { margin: 0 0 10px 0; }
                    h1, h2, h3, h4, h5, h6 { margin: 10px 0; }
                    .mce-content-body { text-decoration: inherit; }
                `,
                init_instance_callback: function(editor) {
                    // Ensure all formatting plugins are properly loaded
                    editor.execCommand('mceToggleFormat', false, 'strikethrough');
                    editor.execCommand('mceToggleFormat', false, 'strikethrough');
                },
                setup: function (editor) {
                    editor.on('init', function () {
                        // Set initial content to the div on load
                        document.getElementById('add_about_text').innerHTML = editor.getContent();
                    });
                    editor.on('input keyup setcontent change', function () {
                        // Get content and set it to the div with proper formatting
                        document.getElementById('add_about_text').innerHTML = editor.getContent();
                    });
                }
            });

            document.getElementById('doneBtnabout').addEventListener('click', function() {
                let formData = new FormData();

                let originalAboutUs = tinymce.get('textEditor').getContent();

                let fileInput = document.getElementById('multiplefileupload3'); // Replace 'multiplefileupload2' with your actual file input ID

                formData.append('cov_title', null);
                formData.append('cov_sub_title', originalAboutUs);
                formData.append('opportunity_id', {{ $opportunityId }});
                formData.append('setting_type', 'about');
                var isChecked = $('#remem_settings_about').prop('checked');
                formData.append('project_checked', isChecked);

                if (fileInput.files.length > 0) {
                    formData.append('image_file', fileInput.files[0]);
                }

                var url = "{{  URL::route(getRouteAlias() . '.update.company.default.settings.opportunity') }}";

                // Send the data to the backend using AJAX
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        console.log('Data sent successfully:', response);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });

                $('.main_side_menu').css('display', 'block');
                $('.edit_about_section').css('display', 'none');
            });

        </script>

        <script>
            // Save original input values with unique names
            let originalPaymentSchedule = document.getElementById('paymentschedle').value;
            let originalDateValue = document.getElementById('date').value;

            // Update text as user types in input fields
            document.getElementById('paymentschedle').addEventListener('input', function() {
                document.getElementById('add_payment_schedule').innerText = this.value;
            });

            document.addEventListener('DOMContentLoaded', function () {
                const dateInput = document.getElementById('date');
                // Input event for manual typing
                dateInput.addEventListener('input', function() {
                    console.log('Input event triggered: ' + this.value);
                    updateDateDisplay(this.value);
                });

                // Change event for date picker selection
                dateInput.addEventListener('change', function() {
                    console.log('Change event triggered: ' + this.value);
                    updateDateDisplay(this.value);
                });

                function updateDateDisplay(value) {
                    const date = new Date(value);
                    const formattedDate = date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: '2-digit',
                    });
                    document.getElementById('add_fixdate').innerText = formattedDate;
                    if (value === '') {
                        document.getElementById('add_date_expiry').style.display = 'none';
                        document.getElementById('add_prpslexpiry').style.display = 'none';
                    } else {
                        document.getElementById('add_date_expiry').style.display = 'block';
                        document.getElementById('add_prpslexpiry').style.display = 'block';
                    }
                }
            });

            // When "Done" button is clicked, confirm changes
            document.getElementById('doneBtn').addEventListener('click', function() {
                // Fix current input values as final
                originalPaymentSchedule = document.getElementById('paymentschedle').value;
                originalDateValue = document.getElementById('date').value;
                document.querySelector('.main_side_menu').style.display = 'block';
                document.querySelector('.edit_cover_section').style.display = 'none';
            });

            // When "Cancel" button is clicked, revert changes
            document.getElementById('cancelBtn').addEventListener('click', function() {
                // Revert inputs and text to original values
                document.getElementById('paymentschedle').value = originalPaymentSchedule;
                document.getElementById('add_payment_schedule').innerText = originalPaymentSchedule;
                document.getElementById('date').value = originalDateValue;
                document.getElementById('add_fixdate').innerText = originalDateValue;

                if (originalDateValue === '') {
                    document.getElementById('add_date_expiry').style.display = 'none';
                    document.getElementById('add_prpslexpiry').style.display = 'none';
                } else {
                    document.getElementById('add_date_expiry').style.display = 'block';
                    document.getElementById('add_prpslexpiry').style.display = 'block';
                }
            });
        </script>
        <script>
            // document.getElementById('proposalexpiry').style.display = 'none';
            // Function to show or hide the Proposal Expiry Date field based on the radio selection
            document.querySelectorAll('input[name="inlineRadioOptions"]').forEach(function(radio) {
                radio.addEventListener('change', function() {
                    // Check if the "Yes" option is selected
                    if (document.getElementById('inlineRadio1').checked) {
                        document.getElementById('proposalexpiry').style.display = 'block';
                        document.getElementById('expiry_hide_show').style.display = 'block';
                    } else {
                        document.getElementById('proposalexpiry').style.display = 'none';
                        document.getElementById('expiry_hide_show').style.display = 'none';
                    }
                });
            });
        </script>

        <script>
            // Save original input values with unique prefixes
            let originalGtermsText = document.getElementById('gterms').value;
            if (originalGtermsText == '') {
                //$('#gtermstext').css('display', 'none');
            }

            // Update text as user types in the input field
            document.getElementById('gterms').addEventListener('input', function() {
                // alert(this.value);
                document.getElementById('gtermstext').style.display = 'block';

                document.getElementById('add_gterms').innerText = this.value;

            });

            // When "Done" button is clicked, confirm changes
            document.getElementById('donebtngterms').addEventListener('click', function() {
                originalGtermsText = document.getElementById('gterms').value;
                let originalsub_tit = tinymce.get('gterms').getContent();
                let formData = new FormData();

                formData.append('cov_title', '');
                formData.append('cov_sub_title', originalsub_tit);
                formData.append('opportunity_id', {{ $opportunityId }});
                formData.append('setting_type', 'terms');
                formData.append('project_checked', $('#remem_settings_terms').prop('checked'));

                var url = "{{ URL::route(getRouteAlias() . '.update.company.default.settings.opportunity') }}";
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        console.log('Data sent successfully:', response);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });
// }
                $('.main_side_menu').css('display', 'block');
                $('.edit_terms_section').css('display', 'none');
            });
            document.getElementById('cancelgterms').addEventListener('click', function() {
                document.getElementById('gterms').value = originalGtermsText;
                document.getElementById('add_gterms').innerText = originalGtermsText;
                if (originalGtermsText=='') {
                    $('#gtermstext').css('display', 'none');

                }else{
                    $('#gtermstext').css('display', 'block');
                }
            });
        </script>


        <script>
            // Save original input values with unique prefixes
            let originalIntroLetter = document.getElementById('intro_letter').value;

            // Update text as user types in the input field
            document.getElementById('intro_letter').addEventListener('input', function() {
                document.getElementById('add_intro_letter').innerText = this.value;
            });

            // When "Done" button is clicked, confirm changes
            document.getElementById('doneBtnintro').addEventListener('click', function() {
                let formData = new FormData();

                let originalPersonalIntro = tinymce.get('intro_letter').getContent();

                formData.append('cov_title', null);
                formData.append('cov_sub_title', originalPersonalIntro);
                formData.append('setting_type', 'intro');
                formData.append('opportunity_id', {{ $opportunityId }});
                formData.append('project_checked', $('#remem_settings_intro').prop('checked'));

                var url = "{{  URL::route(getRouteAlias() . '.update.company.default.settings.opportunity') }}";

                // Send the data to the backend using AJAX
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        // alert(isChecked);
                        // Handle the response from the server
                        console.log('Data sent successfully:', response);
                        // Optionally, you can perform actions here based on the response
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        // Handle any errors
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });

// }

// Show the main side menu and hide the edit section
                $('.main_side_menu').css('display', 'block');
                $('.edit_personal_section').css('display', 'none');
            });

            // When "Cancel" button is clicked, revert changes
            document.getElementById('cancelBtnintro').addEventListener('click', function() {
                // Revert inputs and text to original values
                document.getElementById('intro_letter').value = originalIntroLetter;
                document.getElementById('add_intro_letter').innerText = originalIntroLetter;

                // Note: If there's an image to revert, add the code here
            });




            // When "Done" button is clicked, confirm changes
            document.getElementById('doneBtngallery').addEventListener('click', function() {
                // Fix current input value as final
                let originalIntroLetter = document.getElementById('gallery_title').value;

                if(!originalIntroLetter) {
                    tosatr.error('Please enter gallery title');
                    return;
                }

                // Create FormData object
                let formData = new FormData();

                // Get the file input element
                let fileInput = document.getElementById('multiplefileuploadgallery');
                if (fileInput.files.length === 0) {
                    alert('Please select an image file.');
                    return;
                }

                formData.append('image_file', fileInput.files[0]);

                // Append the input values to FormData
                formData.append('cov_title', originalIntroLetter);

                // Include CSRF token
                formData.append('_token', "{{ csrf_token() }}");

                // Get the checked status of the checkbox
                let isChecked = $('#remem_settings_gallery').prop('checked');
                formData.append('project_checked', isChecked);
                formData.append('opportunity_id', {{$opportunityId}});
                formData.append('setting_type', 'gallery');
                let url = "{{ URL::route(getRouteAlias() . '.update.company.default.settings.opportunity') }}";

                // Send the data to the backend using AJAX
                $.ajax({
                    url: url, // Backend URL
                    type: 'POST', // POST method
                    data: formData,
                    processData: false, // Prevent jQuery from processing FormData
                    contentType: false, // Prevent jQuery from overriding content type
                    success: function(response) {
                        console.log('Data sent successfully:', response);
                        // Optionally, perform actions based on the response
                        // alert('Gallery settings saved successfully!');
                        location.reload();

                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error sending data:', textStatus, errorThrown);
                        alert('Failed to save gallery settings. Please try again.');
                    }
                });
                // }

                // Show/hide sections based on action
                $('.main_side_menu').css('display', 'block');
                $('.edit_gallery_section').css('display', 'none');
            });



        </script>
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                let aboutTextElement = document.getElementById("about_text");
                let previewImageElement = document.getElementById("preview_about_image");
                let fileInputElement = document.getElementById("multiplefileupload3");
                let doneBtnAbout = document.getElementById("doneBtnabout");
                let donePaymentSave = document.getElementById("donepaymentsave");
                let cancelBtnAbout = document.getElementById("cancelBtnabout");

                let originalAboutTitle = aboutTextElement ? aboutTextElement.value : "";
                let originalAboutImageSrc = previewImageElement ? previewImageElement.src : "";

                if (!originalAboutImageSrc) {
                    $(".pspdfkit-1thsp3e").hide();
                }

                if (aboutTextElement) {
                    aboutTextElement.addEventListener("input", function () {
                        document.getElementById("add_about_text").innerText = this.value;
                    });
                }

                if (fileInputElement) {
                    fileInputElement.addEventListener("change", function () {

                        $(".pspdfkit-1thsp3e").css("display", "flex");

                        if (this.files.length > 0) {
                            let reader = new FileReader();
                            reader.onload = function (e) {
                                previewImageElement.src = e.target.result;
                            };
                            reader.readAsDataURL(this.files[0]);
                        }
                    });
                }

                if (donePaymentSave) {
                    donePaymentSave.addEventListener("click", function () {
                        let opportunity_id = "{{$opportunityId}}";
                        let formData = new FormData();
                        formData.append("opportunity_id", opportunity_id);
                        formData.append("date", document.getElementById("date").value);
                        formData.append("down_payment", document.getElementById("getdown1").value);
                        formData.append("down_payment_percent", document.getElementById("getdown2").value);
                        formData.append("cov_sub_title", document.getElementById("paymentschedle").value);

                        let isChecked = $("#remem_settings_payments").prop("checked");
                        formData.append("project_checked", isChecked);
                        formData.append('setting_type', 'payment');
                        var url = "{{ URL::route(getRouteAlias() . '.update.company.default.settings.opportunity') }}";

                        $.ajax({
                            url: url,
                            type: "POST",
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function (response) {
                                console.log("Data sent successfully:", response);
                            },
                            error: function (jqXHR, textStatus, errorThrown) {
                                console.error("Error sending data:", textStatus, errorThrown);
                            },
                        });

                        $(".main_side_menu").show();
                        $(".edit_payment_section").hide();
                    });
                }

                if (cancelBtnAbout) {
                    cancelBtnAbout.addEventListener("click", function () {
                        if (aboutTextElement) {
                            aboutTextElement.value = originalAboutTitle;
                            document.getElementById("add_about_text").innerText = originalAboutTitle;
                        }

                        if (previewImageElement) {
                            previewImageElement.src = originalAboutImageSrc;
                            if (!originalAboutImageSrc) {
                                $(".pspdfkit-1thsp3e").hide();
                            }
                        }
                    });
                }
            });

        </script>

        <script>
            if (document.getElementById('add_sub_tit').innerText=='') {
                $('.subtitle-text-main').css('display', 'none');
            }
            // Save original input values and image source
            let originalcov_title = document.getElementById('cov_title').value;
            let originalsub_tit = document.getElementById('sub_tit').value;
            let originalImageSrc = document.getElementById('preview_image').src;

            // Update text as user types in input fields
            document.getElementById('cov_title').addEventListener('input', function() {
                document.getElementById('add_cov_tit').innerText = this.value;
            });

            document.getElementById('sub_tit').addEventListener('input', function() {
                document.getElementById('add_sub_tit').innerText = this.value;
                if (this.value == '') {
                    $('.subtitle-text-main').css('display', 'none');
                } else {
                    $('.subtitle-text-main').css('display', 'block');
                }
            });

            // Update image preview when a new image is selected
            document.getElementById('multiplefileupload2').addEventListener('change', function() {
                //alert('1');
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('preview_image').src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });

            // When "Done" button is clicked, confirm changes
            document.getElementById('doneBtn').addEventListener('click', function() {

                // Create a FormData object to hold the data
                var opportunity_id= {{$opportunityId}};
                // alert(opportunity_id);
                let formData = new FormData();

                // Get values from input fields
                let originalcov_title = document.getElementById('cov_title').value;
                let originalsub_tit = document.getElementById('sub_tit').value;

                // Get the file input element
                let fileInput = document.getElementById('multiplefileupload2'); // Replace 'multiplefileupload2' with your actual file input ID

                // Append the input values to the FormData object
                formData.append('cov_title', originalcov_title);
                formData.append('cov_sub_title', originalsub_tit);
                formData.append('opportunity_id', opportunity_id);
                formData.append('setting_type', 'cover');

                // Check if a file is selected
                if (fileInput.files.length > 0) {
                    formData.append('image_file', fileInput.files[0]);
                }

                var isChecked = $('#remem_settings_cover').prop('checked');

                formData.append('project_checked', isChecked);
                var url = "{{ URL::route(getRouteAlias() . '.update.company.default.settings.opportunity') }}";


                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        console.log('Data sent successfully:', response);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });

                $('.main_side_menu').css('display', 'block');
                $('.edit_cover_section').css('display', 'none');
            });

            // When "Cancel" button is clicked, revert changes
            document.getElementById('cancelBtn').addEventListener('click', function() {
                // Revert inputs and text to original values
                document.getElementById('cov_title').value = originalcov_title;
                document.getElementById('add_cov_tit').innerText = originalcov_title;
                document.getElementById('sub_tit').value = originalsub_tit;
                document.getElementById('add_sub_tit').innerText = originalsub_tit;

                // Revert image to original
                document.getElementById('preview_image').src = originalImageSrc;

                if (originalsub_tit == '') {
                    $('.subtitle-text-main').css('display', 'none');
                } else {
                    $('.subtitle-text-main').css('display', 'inline-block');
                }
            });
        </script>
        <script>
            function gallerySection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.gallerybtn').hasClass("active")) {
                    $('.gallery_btn').addClass('browntext');
                    $('.gallery_btn').removeClass('bluetext');
                    $('.gallery_btn').attr('disabled', true);
                    var cse = 0;
                } else {
                    $('.gallery_btn').removeClass('browntext');
                    $('.gallery_btn').addClass('bluetext');
                    $('.gallery_btn').attr('disabled', false);
                    var cse = 1;
                }

                var opportunity_id= {{$opportunityId}};
                // alert(opportunity_id);
                var url = "{{ URL::route(getRouteAlias() . '.changeGalleryToggle') }}";
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {opp_id: opportunity_id, case: cse},
                    // processData: false,
                    // contentType: false,
                    success: function(response) {
                        console.log('Data sent successfully:', response);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });
                $('.image_gallery').toggle();
            }

            function paymentSection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.paymntbtn').hasClass("active")) {
                    $('.payment_btn').addClass('browntext');
                    $('.payment_btn').removeClass('bluetext');
                    $('.payment_btn').attr('disabled', true);
                    var cse = 0;
                } else {
                    $('.payment_btn').removeClass('browntext');
                    $('.payment_btn').addClass('bluetext');
                    $('.payment_btn').attr('disabled', false);
                    var cse = 1;
                }

                var opportunity_id= {{$opportunityId}};
                // alert(opportunity_id);
                var url = "{{ URL::route(getRouteAlias() . '.changePaymentsToggle') }}";
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {opp_id: opportunity_id, case: cse},
                    // processData: false,
                    // contentType: false,
                    success: function(response) {
                        console.log('Data sent successfully:', response);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });
                $('.payment_scdul').toggle();
            }
            function termsSection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.termbtn').hasClass("active")) {
                    $('.terms_btn').addClass('browntext');
                    $('.terms_btn').removeClass('bluetext');
                    $('.terms_btn').attr('disabled', true);
                    var cse = 0;
                } else {
                    $('.terms_btn').removeClass('browntext');
                    $('.terms_btn').addClass('bluetext');
                    $('.terms_btn').attr('disabled', false);
                    var cse = 1;
                }

                var opportunity_id= {{$opportunityId}};
                // alert(opportunity_id);
                var url = "{{ URL::route(getRouteAlias() . '.changeTermsToggle') }}";
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {opp_id: opportunity_id, case: cse},
                    // processData: false,
                    // contentType: false,
                    success: function(response) {
                        console.log('Data sent successfully:', response);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });
                $('.terms_conditions').toggle();
            }
            function scopeSection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.scopbtn').hasClass("active")) {
                    $('.scope_btn').addClass('browntext');
                    $('.scope_btn').removeClass('bluetext');
                    $('.scope_btn').attr('disabled', true);
                    var cse = 0;
                } else {
                    $('.scope_btn').removeClass('browntext');
                    $('.scope_btn').addClass('bluetext');
                    $('.scope_btn').attr('disabled', false);
                    var cse = 1;
                }
                var opportunity_id= {{$opportunityId}};
                // alert(opportunity_id);
                var url = "{{ URL::route(getRouteAlias() . '.changeScopeToggle') }}";
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {opp_id: opportunity_id, case: cse},
                    // processData: false,
                    // contentType: false,
                    success: function(response) {
                        console.log('Data sent successfully:', response);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });
                $('.scop_of_work').toggle();
            }
            function introSection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.introbtn').hasClass("active")) {
                    $('.intro_btn').addClass('browntext');
                    $('.intro_btn').removeClass('bluetext');
                    $('.intro_btn').attr('disabled', true);
                    var cse = 0;
                } else {
                    $('.intro_btn').removeClass('browntext');
                    $('.intro_btn').addClass('bluetext');
                    $('.intro_btn').attr('disabled', false);
                    var cse = 1;
                }
                var opportunity_id= {{$opportunityId}};
                // alert(opportunity_id);
                var url = "{{ URL::route(getRouteAlias() . '.changeIntroToggle') }}";
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {opp_id: opportunity_id, case: cse},
                    // processData: false,
                    // contentType: false,
                    success: function(response) {
                        console.log('Data sent successfully:', response);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });
                $('.personal').toggle();
            }
            function aboutSection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.abutbtn').hasClass("active")) {
                    $('.about_btn').addClass('browntext');
                    $('.about_btn').removeClass('bluetext');
                    $('.about_btn').attr('disabled', true);
                    var cse = 0;
                } else {
                    $('.about_btn').removeClass('browntext');
                    $('.about_btn').addClass('bluetext');
                    $('.about_btn').attr('disabled', false);
                    var cse = 1;
                }
                var opportunity_id= {{$opportunityId}};
                // alert(opportunity_id);
                var url = "{{ URL::route(getRouteAlias() . '.changeAboutToggle') }}";
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {opp_id: opportunity_id, case: cse},
                    // processData: false,
                    // contentType: false,
                    success: function(response) {
                        console.log('Data sent successfully:', response);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });
                $('#main_about').toggle();
            }
            function coverSection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.coverdbtn').hasClass("active")) {
                    $('.cover_custom').addClass('browntext');
                    $('.cover_custom').removeClass('bluetext');
                    $('.cover_custom').attr('disabled', true);
                    var cse = 0;


                } else {
                    $('.cover_custom').removeClass('browntext');
                    $('.cover_custom').addClass('bluetext');
                    $('.cover_custom').attr('disabled', false);
                    var cse = 1;

                }
                // alert(cse);
                var opportunity_id= {{$opportunityId}};
                // alert(opportunity_id);
                var url = "{{ URL::route(getRouteAlias() . '.changeCoverToggle') }}";
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {opp_id: opportunity_id, case: cse},
                    // processData: false,
                    // contentType: false,
                    success: function(response) {
                        console.log('Data sent successfully:', response);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });

                $('#main_cover').toggle();
            }
        </script>

        <script>
            function hideeditcover(id){
                $('.edit_cover_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
            function hideeditgallery(id){
                $('.edit_gallery_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
            function hideeditabout(){
                $('.edit_about_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
            function hideeditpersonal(){
                $('.edit_personal_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
            function hideeditsample(){
                $('.edit_sample_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
            function hideeditterms(){
                $('.edit_terms_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
            function hideeditpayment(){
                $('.edit_payment_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
        </script>
        <script>
            function showeditcover(id){
                $('.edit_cover_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
            function showeditabout(){
                $('.edit_about_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
            function showeditpersonal(){
                $('.edit_personal_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
            function showeditsample(){
                $('.edit_sample_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
            function showeditterms(){
                $('.edit_terms_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
            function showeditpayment(){
                $('.edit_payment_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
            function showeditgallery(){
                $('.edit_gallery_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
        </script>
        <script>
            $(document).ready(function() {
                $(".btn-toggle").on("click", function() {
                });
            });

        </script>
        <script>
            $("#multiplefileuploadtemplate").fileinput({
                theme: "explorer-fas",
                uploadUrl: "#",
                deleteUrl: "#",
                initialPreviewAsData: true,
                overwriteInitial: false,
                dropZoneTitle: '<div class="upload-area"><svg width="41" height="41" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.5" d="M11.334 30.5V30.35C11.334 28.9084 11.334 27.585 11.479 26.51C11.6373 25.325 12.0123 24.0717 13.0423 23.0434C14.0723 22.0117 15.3257 21.6367 16.509 21.4767C17.5857 21.3334 18.909 21.3334 20.3523 21.3334H20.649C22.0923 21.3334 23.4157 21.3334 24.4907 21.4784C25.6757 21.6367 26.929 22.0117 27.9573 23.0417C28.989 24.0717 29.364 25.325 29.524 26.5084C29.6656 27.57 29.6673 28.8684 29.6673 30.2867C33.9557 29.37 37.1673 25.6 37.1673 21.0867C37.1673 16.97 34.489 13.4667 30.759 12.1917C30.229 7.49004 26.1923 3.83337 21.294 3.83337C16.034 3.83337 11.7707 8.04671 11.7707 13.245C11.7707 14.395 11.979 15.495 12.3607 16.515C11.9048 16.4269 11.4416 16.3823 10.9773 16.3817C7.03232 16.3834 3.83398 19.5434 3.83398 23.4417C3.83398 27.34 7.03232 30.5 10.9773 30.5H11.334Z" fill="#0074D9"/><path fill-rule="evenodd" clip-rule="evenodd" d="M20.5007 23.8334C17.3573 23.8334 15.7873 23.8334 14.8107 24.81C13.834 25.7867 13.834 27.3567 13.834 30.5C13.834 33.6434 13.834 35.2134 14.8107 36.19C15.7873 37.1667 17.3573 37.1667 20.5007 37.1667C23.644 37.1667 25.214 37.1667 26.1906 36.19C27.1673 35.2134 27.1673 33.6434 27.1673 30.5C27.1673 27.3567 27.1673 25.7867 26.1906 24.81C25.214 23.8334 23.644 23.8334 20.5007 23.8334ZM23.509 28.9734L21.2857 26.7517C21.0773 26.5439 20.795 26.4272 20.5007 26.4272C20.2063 26.4272 19.924 26.5439 19.7157 26.7517L17.4923 28.9734C17.385 29.0756 17.2991 29.1983 17.2399 29.3342C17.1807 29.4702 17.1492 29.6166 17.1474 29.7648C17.1456 29.9131 17.1735 30.0602 17.2294 30.1975C17.2853 30.3348 17.3681 30.4596 17.4729 30.5644C17.5778 30.6693 17.7025 30.7521 17.8398 30.808C17.9772 30.8639 18.1243 30.8918 18.2725 30.89C18.4208 30.8881 18.5672 30.8567 18.7031 30.7975C18.839 30.7382 18.9617 30.6524 19.064 30.545L19.389 30.22V33.4634C19.389 33.7582 19.5061 34.041 19.7146 34.2494C19.9231 34.4579 20.2058 34.575 20.5007 34.575C20.7955 34.575 21.0782 34.4579 21.2867 34.2494C21.4952 34.041 21.6123 33.7582 21.6123 33.4634V30.22L21.9373 30.545C22.1474 30.7451 22.4274 30.8552 22.7175 30.8516C23.0077 30.8481 23.2849 30.7313 23.4901 30.5261C23.6952 30.3209 23.812 30.0437 23.8156 29.7536C23.8191 29.4635 23.7091 29.1835 23.509 28.9734Z" fill="#0074D9"/></svg><p><label style="color: #7C8091; font-size: 14px">Drop files here or </label> <b style="color: #0074D9">Browse</b></p> <div> </div></div>',
                dropZoneClickTitle: "",
                browseOnZoneClick: true,
                showRemove: false,
                showUpload: false,
                showZoom: false,
                showCaption: false,
                showBrowse: false,
                browseClass: "btn btn-danger",
                browseLabel: "",
                browseIcon: "<i class='fa fa-plus'></i>",
                fileActionSettings: {
                    showUpload: false,
                    showDownload: false,
                    showZoom: false,
                    showDrag: true,
                    removeIcon: "<i class='fa fa-times'></i>",
                    uploadIcon: "<i class='fa fa-upload'></i>",
                    dragIcon: "<i class='fa fa-arrows-alt'></i>",
                    uploadRetryIcon: "<i class='fa fa-undo-alt'></i>",
                    dragClass: "file-drag",
                    removeClass: "file-remove",
                    removeErrorClass: 'file-remove',
                    uploadClass: "file-upload",
                },
                frameClass: "file-sortable",
                layoutTemplates: {
                    preview:
                        '<div class="file-preview {class}">\n' +
                        '    <div class="{dropClass}">\n' +
                        '    <div class="clearfix"></div>' +
                        '    <div class="file-preview-status text-center text-success"></div>\n' +
                        '    <div class="kv-fileinput-error"></div>\n' +
                        "    </div>\n" +
                        "</div>" +
                        ' <div class="file-preview-thumbnails">\n' +
                        " </div>\n",
                    actionDrag: '<button class="file-drag-handle {dragClass}" title="{dragTitle}">{dragIcon}</button>',
                    footer: '<div class="file-thumbnail-footer">\n' + '<div class="file-detail">' + '<div class="file-caption-name">{caption}</div>\n' + '    <div class="file-size">{size}</div>\n' + "</div>" + "   {actions}\n" + "</div>",
                },
            });

        </script>
        <script>
            // Save original input values and image source
            let originalTitleText = document.getElementById('title_txt')?.value;
            let originalDescriptionText = document.getElementById('des_txt')?.value;
            let originalPreviewImageSrc = document.getElementById('preview_image').src;

            // Array to store the original image sources
            let originalImages = [];

            // Update text as user types in input fields
            document.getElementById('title_txt').addEventListener('input', function() {
                document.getElementById('scope_title').innerHTML = this.value;
            });

            document.getElementById('des_txt')?.addEventListener('input', function() {
                document.getElementById('scope_des').innerText = this.value;
            });

            // When "Done" button is clicked, confirm changes
            document.getElementById('doneBtnscope').addEventListener('click', function () {
                const finalTitleText = document.getElementById('title_txt').value;
                const finalDescriptionText = tinymce.get('description_editor').getContent();

                let formData = new FormData();
                formData.append('cov_title', finalTitleText);
                formData.append('cov_sub_title', finalDescriptionText);
                formData.append('project_checked', $('#remem_settings_scope').prop('checked'));
                formData.append('opportunity_id', {{$opportunityId}});
                formData.append('setting_type', 'scope');

                var url = "{{ URL::route(getRouteAlias() . '.update.company.default.settings.opportunity') }}";

                // Send the data to the backend using AJAX
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        console.info('Data sent successfully:', response);
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        console.info('Error sending data:', textStatus, errorThrown);
                    }
                });
// }
                $('.main_side_menu').css('display', 'block');
                $('.edit_sample_section').css('display', 'none');
            });

            // When "Cancel" button is clicked, revert changes
            document.getElementById('cancelBtnscope').addEventListener('click', function() {
                document.getElementById('title_txt').value = originalTitleText;
                document.getElementById('scope_title').innerHTML = originalTitleText;
                document.getElementById('des_txt').value = originalDescriptionText;
                document.getElementById('scope_des').innerText = originalDescriptionText;
                document.getElementById('preview_image').src = originalPreviewImageSrc;

                const mainBottom = document.getElementById('mainBottom');
                mainBottom.innerHTML = ''; // Clear the current images
                for (let i = 0; i < originalImages.length; i++) {
                    const colDiv = document.createElement('div');
                    colDiv.classList.add('col-6', 'mt-3', 'uploadimg');

                    const img = document.createElement('img');
                    img.src = originalImages[i];
                    img.alt = `Original Image ${i + 1}`;
                    img.style.width = '100%';

                    colDiv.appendChild(img);
                    mainBottom.appendChild(colDiv);
                }
            });
        </script>

    @endpush



@endsection
