<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">
    <link rel="stylesheet" href="{{ asset('admin_assets/styles/opportunity-pdf.css') }}">
    <style>
        @page {
            margin: 0;
            padding-bottom: 80px; /* Adds space for footer */
        }

        /* Body styling */
        body {
            margin: 0;
            padding-bottom: 80px; /* Ensure space for footer */
        }

        /* Footer Styling */
        .footer {
            position: fixed;
            bottom: 0;
            width: 100%;
            height: 36px;
            background-color: #E6F1FB;
            padding: 0px;
            text-align: center;
            color: #0068C3;
            font-weight: bold;
        }
        .footer table{
            padding-top: 5px;
            padding-left: 10px;
        }
        .footer2 {
            position: fixed;
            bottom: 0;
            width: 100%;
            /* height: 100px; */
            /* height: 16px; */
            background-color: transparent;
            padding: 0px;
            text-align: center;
            color: #0068C3;
            font-weight: bold;
        }
        .footer3 {
            position: fixed;
            bottom: 0;
            width: 100%;
            /* height: 16px; */
            background-color: #E6F1FB;
            /* padding: 10px; */
            text-align: center;
            color: #0068C3;
            font-weight: bold;
        }
        /* The content should not overlap with the footer */
        .panel {
            padding-bottom: 100px; /* Leave space for footer */
        }
        .btn-toggle {

            padding: 0;
            position: relative;
            border: none;
            height: 1.6rem;
            width: 3.2rem;
            border-radius: 1.5rem;
            color: #6b7381;
            background: #bdc1c8;
        }
        .btn-toggle:focus, .btn-toggle:focus.active, .btn-toggle.focus, .btn-toggle.focus.active {
            outline: none;
        }
        .btn-toggle:before, .btn-toggle:after {
            line-height: 1.5rem;
            width: 4rem;
            text-align: center;
            font-weight: 600;
            font-size: .75rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            position: absolute;
            bottom: 0;
            transition: opacity .25s;
        }
        .btn-toggle:before {
            content: '';
            left: -8rem;
            font-weight: 900;
            font-size: 10px;
            color: #7C8091;
            fill-opacity: #7C8091;
        }
        .btn-toggle:after {
            content: '';
            left: -8rem;
            font-weight: 900;
            font-size: 10px;

            opacity: 1;
        }
        .active:after {
            color: #0074D9; /* New color */
        }
        .btn-toggle > .handle {
            position: absolute;
            margin-top: 0.7px !important;
            top: 0.1875rem;
            left: 0.1875rem;
            width: 1.125rem;
            height: 1.125rem;
            border-radius: 1.125rem;
            background: #fff;
            transition: left .25s;
        }
        .btn-toggle.active {
            transition: background-color .25s;
        }
        .btn-toggle.active {
            background-color: #117BDD;
        }
        .btn-toggle.active > .handle {
            left: 1.6875rem;
            transition: left .25s;
        }
        .btn-toggle.active:before {
            /* opacity: .5; */
            display: none;
        }
        .btn-toggle.active:after {
            opacity: 1;
        }
        .sidemenuul li{
            padding: 10px 5px;
            border: 1px solid #E2E4EA;
            border-radius: 8px;
        }
        .sidemenuul li p{
            color: #3B4159;
        }
        .dnbtn{
            width: 100%;
            /* height: Hug (37px)px; */
            padding: 6px 14px 6px 14px;
            gap: 12px;
            border-radius: 12px;
            background: #E6F1FB;
            opacity: 0px;
            border: none;
            font-size: 10px;
            color: #117BDD;

        }
        .sendcbtn{
            width: 100%;
            background: #0074D9;
            padding: 6px 14px 6px 14px;
            gap: 12px;
            border-radius: 12px;
            opacity: 0px;
            font-size: 10px;
            color: white;
            border: none;

        }
        .leftimg{
            position: absolute;
            top: 0px;
            left: 0;
        }
        .proposal {
            width: auto;
            /*height: 23px;*/
            margin-top: 25px;
            padding: 12px 5px;
            text-align: center;
            font-size: 15px;
            gap: 10px;
            border-radius: 0px 100px 100px 0px;
            color: white;
            background: #0074D9;
        }
        .proposal_personal {
            width: auto;
            height: 23px;
            margin-top: 25px;
            margin-left: -21px !important;
            /* padding: 13px 0px 19px 54px; */
            padding: 12px 5%;
            text-align: center;
            font-size: 15px;
            gap: 10px;
            border-radius: 0px 100px 100px 0px;
            color: white;
            background: #0074D9;
        }

        .proposal b{
            color: #FFFFFF;
            font-size: 20px;
            font-weight: 600;
        }
        .proposal_personal b{
            color: white;
            font-size: 16px;
        }

        .panel {
            background: #ffffff;
            padding: 0px;
            box-shadow: 0px 0px 12px rgba(36, 185, 236, 0.08);
            border-radius: 8px;

        }
        .panel23{
            padding: 0px !important;
        }
        .table td, .table th {
            border-bottom: none;
            border-top: none;
        }
        .table thead th {

            border-bottom: none;
        }

        .main-div-sec{
            padding: 0px;
            overflow-y: scroll;
            height: 793px;
            width: 100%;
        }
        .main-div-sec::-webkit-scrollbar {
            display: none; /* Webkit browsers ke liye scroll bar ko hide karna */
        }

        /* Firefox ke liye */
        .main-div-sec {
            scrollbar-width: none; /* Firefox ke liye scroll bar hide karna */
        }
        .sidenavsec{
            height: 793px;
        }
        .custombtn{

            background-color: transparent;
            border: none;
            text-align: justify;
        }
        .bluetext b{
            color: #0074D9 !important;
        }
        .browntext b{
            color: #7C8091 !important;
        }
        .edit_cover_section{
            display: none;
        }
        .edit_about_section{
            display: none;
        }
        .edit_personal_section{
            display: none;
        }
        .edit_sample_section{
            display: none;
        }
        .edit_terms_section{
            display: none;
        }
        .edit_payment_section{
            display: none;
        }
        .input-group-append {
            cursor: pointer;
        }
        button{
            cursor: pointer;
        }
    </style>
    <style>
        .image-container {
            position: relative;
            width: 100%;
            margin-top: 100px;
            max-width: 100%; /* Adjust to your needs */
            overflow: hidden;
        }

        .image-container img {
            width: 100%;
            height: auto;
            border-radius: 75% 25% 25% 25% / 58% 0% 0% 0% !important;


            /* border-top-left-radius: 14%; */
            /* Applying a mask to create the curved effect */
            mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><path fill="white" d="M100,0 C80,0 20,10 0,40 L0,100 L100,100 Z"/></svg>');
            mask-size: 100% 100%;
            -webkit-mask-size: 100% 100%; /* For Safari support */
        }

        .centered-text {
            position: absolute;
            top: 40%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
            font-weight: bold;
        }
        .centered-text strong{
            color: #ffffff;
            font-weight: 800;
            font-size: 9.83mm;
            font-weight: 600;
            text-transform: uppercase;
            line-height: 15.83mm;
            --tw-text-opacity: 1;
        }

        .image-container::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 190px; /* Adjust height to control shadow size */
            background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent); /* Creates the fading shadow */
            pointer-events: none; /* Ensures the shadow doesn't interfere with any interactions */
        }
        .subtitle-text-main {
            background: #0074D9;
            width: 38% !important;
            height: auto;
            padding: 15px 20px;
            text-align: center;
            border-bottom-left-radius: 0.5rem;
            color: #FFFFFF;
        }
        .subtitle-text-main p{
            font-size: 19px;
            font-weight: 600;
            color: #FFFFFF;
            margin: 0;
        }
        input{
            height: 32px;
        }
        .pspdfkit-1thsp3e {
            position: relative;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            /* display: none; */
            margin-top: 90px;
            max-height: 97mm;
            overflow: hidden;
            --tw-bg-opacity: 1;
            background-color: rgb(203 213 225 / var(--tw-bg-opacity));
            margin-top: 3rem;
        }
        .pspdfkit-1eoji5i {
            position: absolute !important;
            left: 0 !important;
            top: 0 !important;
            height: 80% !important;
            width: 100% !important;
            background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(0, 0, 0, 0) 100%) !important;
            content: '' !important;
        }

        .pspdfkit-1q9q9cv {
            height: 350px !important;
            width: 100% !important;
            -webkit-flex: 1 1 0%;
            -ms-flex: 1 1 0%;
            flex: 1 1 0%;
            background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
            --tw-gradient-from: rgb(245,246,252,0.52) var(--tw-gradient-from-position);
            --tw-gradient-to: rgb(117,19,93,0.73) var(--tw-gradient-to-position);
            --tw-gradient-stops: var(--tw-gradient-from),var(--tw-gradient-to);
            object-fit: cover;
        }
        .panel22{
            height: auto !important;
            min-height: 683px !important;
            position: relative;
        }
        .bottom_row{
            position: absolute;
            bottom: 0;
            width: 100%;
            left: 15px;
        }
        #gtermstext{
            /* display: none; */
        }
        #multiplefileupload5{
            display: none;
        }
        .page-break {
            page-break-after: always;
        }
        .main-div-sec {
            page-break-before: avoid !important;
        }
        .paddingpdf{
            padding: 0px 6%;
        }
        p, b, strong, div label a span td, h1, h2, h3, h4, h5, h6 {
            font-size: 16px;
        }
        .signature {
            font-family: "Reenie Beanie" !important;
            font-size: 16px;
            /* font-style: normal; */
            font-weight: 400;
            line-height: 29px;
            color: #192a3e;
        }
        #add_cov_tit{
            font-size: 26px;
            font-weight: 700;
            color: #101725;
        }
    </style>
</head>

<body>

    <section class="dashboard_main" style="padding: 0px !important; margin: 0px !important;">

        <div style="padding: 0px;">
            <div class="main-div-sec">
                @if(isset($template))
                    <div class="panel panel23 coverd mt-4" id="main_cover" style="width: 90%; margin-left: 5%; overflow: hidden;">
                        <img src="{{ isset($template->project_image) ? asset('storage/' . $template->project_image) : asset('storage/admin_assets/images/12.png') }}" style="min-width: 100% !important; height: 100vh; padding-top: 18px;" alt="">
                    </div>
                @else
                    <div class="panel panel23 coverd mt-4" id="main_cover" style="width: 100%; ">
                        <div class="" style="position: relative;">
                            <div class="row"></div>
                            <table style="width: 100%; margin-top: 38px; border-collapse: collapse;">
                                <tr>
                                    <td style="width: 25%; vertical-align: baseline;">
                                        <div class="proposal" style="margin-left: -21px;">
                                            <b>Proposal</b>
                                        </div>
                                    </td>
                                    <td style="width: 50%; text-align: right; padding-right: 22px;">
                                        <div style="margin-top: 0;">
                                            <!-- <img id="add_logo_image" class="add_logo_image" src="{{ $logo && $logo->cov_image ? public_path('storage/' . $logo->cov_image) : public_path('storage/logo.png') }}"style="height: 65px;" alt=""> -->
                                            <img id="add_logo_image" class="add_logo_image" src="{{ asset('storage/user_images/' . $organization->profile_photo_path) }}" style="
                                                                              height: 65px;

                                                                              gap: 0px;
                                                                              opacity: 0px;
                                                                              " alt="">
                                        </div>
                                    </td>
                                </tr>
                            </table>

                            <div class="row">
                                <div class="logo" style="margin-top: 43px;margin-left: 7.5%;">
                                    <h1 id="add_cov_tit">{{ $cover?->cov_title ?? $coverorg?->cov_title ?? ''}}</h1>
                                </div>
                            </div>
                            <div class="row2" style="margin-top: 0;">
                                <div class="imge" style="margin-top: -40px;">
                                    <div class="image-container">
                                        <!-- <img id="preview_image" src="{{isset($cover->cov_image) ? Storage::url($cover->cov_image) : asset('admin_assets/images/12.png')}}" style="width: 100%;" alt=""> -->
{{--                                        <img id="preview_image" src="{{  asset('storage/' . $cover?->cov_image) ?? asset('storage/' . $coverorg?->cov_image) ?? asset('storage/banm.png') }}" style="width: 100%; height: 400px;" alt="">--}}
                                        <img id="preview_image" src="{{ $bannersimg ?? '' }}" style="width: 100%; height: 400px;" alt="">

                                        <div class="centered-text">
                                            <!-- <strong id="add_cov_tit">{{isset($cover) ? $cover->cov_title : ''}}</strong> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @if(isset($cover->cov_sub_title))
                                <div class="row justify-content-end">
                                    <div class="col-11" style="margin-left: auto;">
                                        <div class="subtitle-text-main text-center" style="margin-left: auto;">
                                            <p id="add_sub_tit">{{ $cover->cov_sub_title }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endif
                            <div class="container paddingpdf">
                                <table style="width: 100%; margin-top: 20px;">
                                    <tr>
                                        <td style="width: 33.3%; color: #7C8091; vertical-align: top;">
                                            <label>Submitted to:</label>
{{--                                            <p><b>{{$estimate->account->company_name}}</b></p>--}}
                                            <p style="color: #101725; font-size: 16px; font-weight: 600;"><b>{{$estimate->contactInformation->first_name}} {{$estimate->contactInformation->last_name}}</b></p>
                                        </td>
                                        <td style="width: 33.3%; color: #7C8091; text-align: center; vertical-align: top;">
                                            <label>Submitted by:</label>
                                            <p style="color: #101725; font-size: 16px; font-weight: 600; margin: 6px 0;"><b>{{$organization->first_name}} {{$organization->last_name}}</b></p>
                                            <label style="color: #51566C; font-size: 14px;">{{$organization->company_name}}</label>
                                        </td>
                                        <td style="width: 33.3%; color: #7C8091; text-align: right; vertical-align: top;">
                                            <label>Date Submitted:</label>
                                            <p style="color: #101725; font-size: 16px; font-weight: 600;"><b>{{ date("F dS, Y") }}</b></p>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="row mt-4"></div>
                        </div>
                    </div>
                    <div class="footer">
                        <table style="width: 100%; background-color: #E6F1FB;">
                            <tr>
                                <td style="width: 50%; color: #0068C3; font-weight: bold;">
                                    {{$organization->company_name}}
                                </td>
                                <td style="width: 50%; text-align: right; color: #0068C3; font-weight: bold;">
                                    {{ $organization?->companyAddress?->website_url ?? '' }}
                                </td>
                            </tr>
                        </table>
                    </div>
                @endif
                <div class="page-break"></div>





                <div class="panel panel22 about mt-4" id="main_about" style="position: relative; width: 100%; overflow: hidden;">
                    <div class=""  style="position: relative;">
                        <div class="row">
                            <table style="width: 100%; margin-top: 38px; border-collapse: collapse;">
                                <tr>
                                    <td style="width: 25%; vertical-align: baseline;">
                                        <div class="proposal" style="margin-left: -31px;">
                                            <b>About Us</b>
                                        </div>
                                    </td>
                                    <td style="width: 50%; text-align: right; padding-right: 22px;">
                                        <div style="margin-top: 10px;">
                                            <img id="add_logo_image" class="add_logo_image" src="{{ asset('storage/user_images/' . $organization->profile_photo_path) }}" style="
                                                                              height: 65px;

                                                                              gap: 0px;
                                                                              opacity: 0px;
                                                                              " alt="">
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="container px-5 mt-5 pb-5 paddingpdf" >
                            <div class="row px-5">
                                <br>
                                <p class="px-5" id="add_about_text" style="color: #3B4159; font-size: 16px;">
                                    {!! $about?->cov_sub_title ?? '' !!}
                                </p>

                                <div class="pspdfkit-1thsp3e e1h2lg7j2 mt-5 paddingpdf">
                                    <div class="pspdfkit-1eoji5i e1h2lg7j0"></div>
                                    <img src="{{ $aboutorg?->cov_image ? asset('storage/'.$aboutorg?->cov_image) : '' }}" alt="cover" id="preview_about_image" class="pspdfkit-1q9q9cv e1h2lg7j1">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>







                <div class="footer">
                    <table style="width: 100%; background-color: #E6F1FB;">
                        <tr>
                            <td style="width: 50%; color: #0068C3; font-weight: bold;">
                                {{$organization->company_name}}
                            </td>
                            <td style="width: 50%; text-align: right; color: #0068C3; font-weight: bold;">{{ $organization?->companyAddress?->website_url ?? '' }}</td>
                        </tr>
                    </table>
                </div>

                <div class="page-break"></div>
                <div class="panel panel22 personal mt-4" id="main_personal" style="width: 100%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                        </div>
                        <table style="width: 100%; margin-top: 38px; border-collapse: collapse;">
                            <tr>
                                <td style="width: 25%; vertical-align: baseline;">
                                    <div class="proposal" style="margin-left: -21px;">
                                        <b>Personal Introduction</b>
                                    </div>
                                </td>
                                <td style="width: 50%; text-align: right; padding-right: 22px;">
                                    <div style="margin-top: 0;">
                                        <img id="add_logo_image" class="add_logo_image" src="{{ asset('storage/user_images/' . $organization->profile_photo_path) }}" style="height: 65px;gap: 0;" alt="">
                                    </div>
                                </td>
                            </tr>
                        </table>
                        <div class="container px-5 mt-5 paddingpdf">
                            <div class="row px-5">
                                <br>
                                <p class="px-5" id="add_intro_letter" style="color: #3B4159; font-size: 16px;">
                                    {!! $intro?->cov_sub_title ?? '' !!}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer">
                    <table style="width: 100%; background-color: #E6F1FB;">
                        <tr>
                            <td style="width: 50%; color: #0068C3; font-weight: bold;">
                                {{$organization->company_name}}
                            </td>
                            <td style="width: 50%; text-align: right; padding-right: 5%; color: #0068C3; font-weight: bold;">{{ $organization?->companyAddress?->website_url ?? '' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="page-break"></div>

                <div class="panel panel22 scop_of_work mt-4" id="main_scpe" style="width: 100%;
                     overflow: hidden;">
                    <div class="">
                        <div class="row"></div>
                        <table style="width: 100%; margin-top: 38px; border-collapse: collapse;">
                            <tr>
                                <td style="width: 25%; vertical-align: baseline;">
                                    <div class="proposal" style="margin-left: -21px;">
                                        <b>Scope of work</b>
                                    </div>
                                </td>
                                <td style="width: 50%; text-align: right; padding-right: 22px;">
                                    <div style="margin-top: 0px;">
                                        <img id="add_logo_image" class="add_logo_image" src="{{ asset('storage/user_images/' . $organization->profile_photo_path) }}" style="
                                                                              height: 65px;

                                                                              gap: 0px;
                                                                              opacity: 0px;
                                                                              " alt="">
                                    </div>
                                </td>
                            </tr>
                        </table>
                        <div class="container px-5  mt-4 paddingpdf">
                            <div class="row px-5">
                                <br>
                                <p class="px-5" style="color: #3B4159; font-size: 16px; text-align: center;">
                                    <b id="scope_title" style="text-align: left;">{{ $scope?->cov_title ?? ''}}</b> <br>
                                </p>
                                <label id="scope_des" for="" style="text-align: center;">
                                    {!! $scope?->cov_sub_title ?? '' !!}
                                </label>
                            </div>
                            <div class="row mt-5 pb-5" style="justify-content: center; width: 100%;">
                                <div class="border" style="width: 100%; padding: 10px; border: 1px solid #E7E7E7; border-radius: 8px; width: 100%;">
                                    <table class="table table-striped" style="width: 100%; border: 1px black;">
                                        <thead style="background: #DCF2FF; border: 1px solid transparent;">
                                        <tr style="border: none;">
                                            <th>Item / Description</th>
                                            <th>Uom</th>
                                            <th>Quantity</th>
                                            <th>Unit Cost</th>
                                            <th>Total Price</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach ($opportunity as $item)
                                            <tr>
                                                <td>{{ $item->item_name }}</td>
                                                <td style="text-align: center;">{{ $item->uom }}</td>
                                                <td style="text-align: center;">{{ $item->quantity }}</td>
                                                <td style="text-align: center;">${{ $item->unit_price }}</td>
                                                <td style="text-align: center;">${{ $item->total_price }}</td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="row text-right" style="margin-bottom: 50px; text-align: right">
                                <p style="font-size: 14px; text-align: end;">Total: &nbsp;&nbsp; <b style="color: #0074D9; font-size: 20px;">$ {{$totalPriceSumss}}</b></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer">
                    <table style="width: 100%; background-color: #E6F1FB;">
                        <tr>
                            <td style="width: 50%; color: #0068C3; font-weight: bold;">
                                {{$organization->company_name}}
                            </td>
                            <td style="width: 50%; text-align: right; padding-right: 5%; color: #0068C3; font-weight: bold;">
                                {{ $organization?->companyAddress?->website_url ?? '' }}
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="page-break"></div>

                <div class="panel panel22 terms_conditions mt-4" id="main_trms" style="width: 100%; overflow: hidden;">
                    <div class="">
                        <div class="row"></div>
                        <table style="width: 100%; margin-top: 38px; border-collapse: collapse;">
                            <tr>
                                <td style="width: 25%; vertical-align: baseline;">
                                    <div class="proposal" style="margin-left: -21px;">
                                        <b>Term & Conditions</b>
                                    </div>
                                </td>
                                <td style="width: 50%; text-align: right; padding-right: 22px;">
                                    <div style="margin-top: 0px;">
                                        <img id="add_logo_image" class="add_logo_image" src="{{ asset('storage/user_images/' . $organization->profile_photo_path) }}" style="
                                                                              height: 65px;

                                                                              gap: 0px;
                                                                              opacity: 0px;
                                                                              " alt="">
                                    </div>
                                </td>
                            </tr>
                        </table>

                        <div class="container px-5 mt-5 pb-5 paddingpdf">
                            <div class=" px-5 ">
                                {{--<br>
                                <b class="px-5" id="gtermstext">General Terms</b>
                                <br>--}}

                                <p class="px-5" id="add_gterms" style="color: #3B4159; font-size: 16px;">
                                    {!! $terms?->cov_sub_title ?? '' !!}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer">
                    <table style="width: 100%; background-color: #E6F1FB;">
                        <tr>
                            <td style="width: 50%; color: #0068C3; font-weight: bold;">
                                {{$organization->company_name}}
                            </td>
                            <td style="width: 50%; text-align: right; padding-right: 5%; color: #0068C3; font-weight: bold;">{{ $organization?->companyAddress?->website_url ?? '' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="page-break"></div>
                 {{-- image gallary section here --}}
                <div class="panel panel22 image_gallery mt-4" id="main_gallery" style="width: 100%; overflow: hidden;">
                    <div class="">
                        <div class="row"></div>
                        <table style="width: 100%; margin-top: 38px; border-collapse: collapse;">
                            <tr>
                                <td style="width: 25%; vertical-align: baseline;">
                                    <div class="proposal" style="margin-left: -21px;">
                                        <b>Image Gallery</b>
                                    </div>
                                </td>
                                <td style="width: 50%; text-align: right; padding-right: 22px;">
                                    <div style="margin-top: 0px;">
                                        <img id="add_logo_image" class="add_logo_image" src="{{ asset('storage/user_images/' . $organization->profile_photo_path) }}" style="height: 65px;gap: 0;" alt="">
                                    </div>
                                </td>
                            </tr>
                        </table>


                        <div class="container px-5 mt-5">
                            <div class="row px-5 justify-content-evenly" style="margin-top: 20px;">

                                @if(isset($hardMaterialImages) && count($hardMaterialImages) > 0)
                                    <table style="width: 100%; border-collapse: collapse;">
                                        @foreach (collect($hardMaterialImages)->chunk(2) as $imagesChunk)
                                            <tr>
                                                @foreach($imagesChunk as $image)
                                                    <td style="width: 50%; text-align: center; padding: 10px;">
                                                        <img src="{{ asset($image['image']) }}" style="width: 100%; height: 174px;" alt="" class="gallery-image">
                                                        <p style="margin-top: 5px;">{{ $image['name'] ?? '' }}</p>
                                                    </td>
                                                @endforeach
                                                    @if($imagesChunk->count() < 2)
                                                        <td style="width: 50%;"></td>
                                                    @endif
                                            </tr>
                                        @endforeach
                                    </table>
                                @endif

                                @if(isset($gallerys))
                                    <table style="width: 100%; border-collapse: collapse;">
                                        @foreach($gallerys->chunk(2) as $row)
                                            <tr>
                                                @foreach($row as $gallery)
                                                    <td style="width: 50%; text-align: center; padding: 10px;">
                                                        <img src="{{ asset('storage/'.$gallery->cov_image) }}" style="width: 100%; height: 174px;" alt="" class="gallery-image">
                                                        <p style="margin-top: 5px;">{{ $gallery->cov_title ?? '' }}</p>
                                                    </td>
                                                @endforeach
                                                @if($row->count() < 2)
                                                    <td style="width: 50%;"></td>
                                                @endif
                                            </tr>
                                        @endforeach
                                    </table>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer">
                    <table style="width: 100%; background-color: #E6F1FB;">
                        <tr>
                            <td style="width: 50%; color: #0068C3; font-weight: bold;">
                                {{ $organization->company_name }}
                            </td>
                            <td style="width: 50%; text-align: right; padding-right: 5%; color: #0068C3; font-weight: bold;">{{ $organization?->companyAddress?->website_url ?? '' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="page-break"></div>

                <div class="panel panel22 payment_scdul mt-4" id="main_pmnt" style="width: 100%; overflow: hidden;">
                    <div class="">
                        <div class="row"></div>
                        <table style="width: 100%; margin-top: 38px; border-collapse: collapse;">
                            <tr>
                                <td style="width: 35%; vertical-align: baseline;">
                                    <div class="proposal" style="margin-left: -21px;">
                                        <b style="padding-left: 19px !important;">
                                            &nbsp;&nbsp;&nbsp;&nbsp;Payments Scheduled & Terms
                                        </b>
                                    </div>
                                </td>
                                <td style="width: 50%; text-align: right; padding-right: 22px;">
                                    <div style="margin-top: 0px;">
                                        <img id="add_logo_image" class="add_logo_image" src="{{ asset('storage/user_images/' . $organization->profile_photo_path) }}" style="
                                                                              height: 65px;

                                                                              gap: 0px;
                                                                              opacity: 0px;
                                                                              " alt="">
                                    </div>
                                </td>
                            </tr>
                        </table>


                        <div class="container px-5 mt-5 pb-5 paddingpdf" style="margin-top: 30px;">
                            <div class="">
                                <label id="add_prpslexpiry">Proposal Expiry Date</label>
                                <br>
                                <br>

                                <label id="add_date_expiry"><b id="add_fixdate" style="margin-top: 10px">
                                        {{ isset($payment) && $payment->expiry != null ? \Carbon\Carbon::parse($payment->expiry)->format('F d, Y') : '-' }}
                                    </b></label>
                                <br>

                                <p id="add_pmntschdle"><span>Payment Schedule</span></p>
                                <label id="add_payment_schedule">{!! $payment->cov_sub_title ?? '' !!}</label>
                                <br>
                                <br>
                                <label>Total</label>
                                <br>
                                <b id="totalamont">${{$totalPriceSumss}}</b>
                                <br>
                                <br>
                                <label>Down Payments</label>
                                <br>
                                @php
                                    $paymentSchedule = $paymentorg?->payment_schedule ? json_decode($paymentorg->payment_schedule) : null;
                                @endphp
                                <div class="d-flex" style="gap: 13px;"><b id="downpaym1">
                                        @if(isset($paymentSchedule->down_payment))
                                            ${{ number_format($paymentSchedule->down_payment, 2) }}
                                        @else
                                            ${{ number_format(0, 2) }}
                                        @endif
                                    </b>
                                    <b id="downpaym2">
                                        @if(isset($paymentSchedule) && $paymentSchedule->down_payment_percent)
                                            {{ number_format($paymentSchedule->down_payment_percent, 2) }}%
                                        @else
                                            {{ number_format(0, 2) }}%
                                        @endif
                                    </b></div>
                                <br>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer">
                    <table style="width: 100%; background-color: #E6F1FB;">
                        <tr>
                            <td style="width: 50%; color: #0068C3; font-weight: bold;">
                                {{$organization->company_name}}
                            </td>
                            <td style="width: 50%; text-align: right; padding-right: 5%; color: #0068C3; font-weight: bold;">{{ $organization?->companyAddress?->website_url ?? '' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="page-break"></div>

                <div class="panel panel22 mt-4" id="" style="width: 100%; overflow: hidden;">
                    <div class="">
                        <div class="row"></div>
                        <table style="width: 100%; margin-top: 38px; border-collapse: collapse;">
                            <tr>
                                <td style="width: 25%; vertical-align: baseline;">
                                    <div class="proposal" style="margin-left: -21px;">
                                        <b>Acceptance</b>
                                    </div>
                                </td>
                                <td style="width: 50%; text-align: right; padding-right: 22px;">
                                    <div style="margin-top: 0px;">
                                        <img id="add_logo_image" class="add_logo_image" src="{{ asset('storage/user_images/' . $organization->profile_photo_path) }}" style="
                                                                              height: 65px;

                                                                              gap: 0px;
                                                                              opacity: 0px;
                                                                              " alt="">
                                    </div>
                                </td>
                            </tr>
                        </table>



                        <div class="container px-5 mt-5 paddingpdf">
                            <div class="row px-5 justify-content-evenly" style="margin-top: 80px;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="width: 50%; vertical-align: top; padding-right: 15px;">
                                            <p style="font-family: sans-serif; font-size: 16px; font-weight: 400; line-height: 12px; text-align: left; color: #3E4756;">Client Name</p>
                                            <p class="mt-3">
                                                <b style="font-family: sans-serif; font-size: 16px; font-weight: 600; line-height: 12px; text-align: left; color: #192A3E;">{{$estimate->account->company_name}}</b>
                                            </p>

                                            <div class="div" style="margin-top: 14px">
                                                <span class="signature">{{$estimateAction?->signature_text}}</span>
                                                <hr class="mt-1" style="border: 1px dashed #90A0B7">
                                            </div>
                                            <p style="font-family: sans-serif; font-size: 16px; font-weight: 400; line-height: 12px; text-align: left; color: #3E4756;">Signature</p>
                                            <div class="div" style="margin-top: 14px">
                                                <span>{{ $estimateAction?->signature_date ? \Carbon\Carbon::parse($estimateAction->signature_date)->format('F jS, Y') : '' }}</span>
                                                <hr class="mt-1" style="border: 1px dashed #90A0B7">
                                            </div>
                                            <p style="font-family: sans-serif; font-size: 16px; font-weight: 400; line-height: 12px; text-align: left; color: #3E4756;">Date</p>
                                        </td>
                                        <td style="width: 50%; vertical-align: top; padding-left: 15px;">
                                            <p style="font-family: sans-serif; font-size: 16px; font-weight: 400; line-height: 12px; text-align: left; color: #3E4756;">Company Approval</p>
                                            <p class="mt-3">
                                                <b style="font-family: sans-serif; font-size: 16px; font-weight: 600; line-height: 12px; text-align: left; color: #192A3E;">{{$organization->company_name}}</b>
                                            </p>
                                            <hr class="mt-5" style="border: 1px dashed #90A0B7;">
                                            <p style="font-family: sans-serif; font-size: 16px; font-weight: 400; line-height: 12px; text-align: left; color: #3E4756;">Signature</p>
                                            <hr class="mt-5" style="border: 1px dashed #90A0B7;">
                                            <p style="font-family: sans-serif; font-size: 16px; font-weight: 400; line-height: 12px; text-align: left; color: #3E4756;">Date</p>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    Company Name
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">
                                    <!-- Comapnywebsite.com -->
                                </b>
                            </div>

                        </div>
                        <!-- <div class="page-break"></div> -->
                    </div>
                </div>
                <div class="footer">
                    <table style="width: 100%; background-color: #E6F1FB;">
                        <tr>
                            <td style="width: 50%; color: #0068C3; font-weight: bold;">
                                {{$organization->company_name}}
                            </td>
                            <td style="width: 50%; text-align: right; padding-right: 5%; color: #0068C3; font-weight: bold;">{{ $organization?->companyAddress?->website_url ?? '' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </section>
@push('scripts')
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
@endpush
</body>
</html>
