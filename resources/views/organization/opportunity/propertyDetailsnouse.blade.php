@extends('layouts.admin.master')
@section('title', 'Client Details')
@section('section')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@include('organization.property.style')
    <section class="dashboard_main pb-5">






        <div class="row">
            <div class="col-lg-4 mt-4">
                <div class="panel">
                    <div class="row">
                            <div class="col-12">
                                <ul class="nav company-client_tabs_property" id="company-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link active" id="" data-toggle="pill" data-target="#company-created"
                                            type="button" role="tab" aria-controls="details-content" aria-selected="true" >
                                            <b>Property Details</b>
                                    </div>
                                    </li>
                                    <li class="nav-item ml-auto" role="presentation">
                                        <div class="nav-link" id="" data-toggle="pill"
                                            type="button" role="tab" aria-selected="false" >
                                            <img style="width: 36.3px;
height: 36px;
top: 25px;
left: 428.5px;
padding: 8px 8.3px 8px 8px;
gap: 0px;
border-radius: 4px 0px 0px 0px;
border: 1px 0px 0px 0px;
opacity: 0px;
angle: 180 deg;
" src="{{ asset('admin_assets/images/edit-icon.png') }}" alt="">
                                    </div>
                                    </li>

                                </ul>
                            </div>
                            <div class="col-12">
                                <div class="tab-content mt-4" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                        <div class="property-details-wrapper">
                                            <div class="row">
                                                <div class="col-4">
                                                    <img style="width: 100.32px;
height: 100.32px;
gap: 0px;
border-radius: 10px 0px 0px 0px;
opacity: 0px;
" src="{{ asset('admin_assets/images/group1.png') }}" alt="">
                                                </div>
                                                <div class="col-8">
                                                    <h3>{{$property->property_name}}</h3>
                                                    <label>
                                                    <img style="width: 24px;
height: 24px;
top: 36px;
padding: 2px 4px 2px 4px;
gap: 0px;
opacity: 0px;

" src="{{ asset('admin_assets/images/location.png') }}" alt="">
                                                   {{$property->address1}}

                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row mt-4 justify-content-around">
                                                <div class="col-5 py-3 text-center" style="background: #F6F7FB; border-radius: 10px;">

                                                    <p style="font-weight: 900;">
                                                        Jk Dealers
                                                    </p>
                                                    <span>Company</span>
                                                </div>
                                                <div class="col-5 py-3 text-center" style="background: #F6F7FB; border-radius: 10px;">

                                                    <p style="font-weight: 900;">
                                                        Alex Hales
                                                    </p>
                                                    <span>Opportunity Owner</span>
                                                </div>
                                            </div>
<div class="row mt-4">
    <div class="col-12">
        <button class="btn form-control py-4 font-weight-bolder" style="background: #FEDC66; font-weight: 900 !important;
"><p>Property Health</p></button>
    </div>
</div>
<div class="row mt-4 ">
    <label for="" class="mx-4" style="font-size: 15px; font-weight: 800;">Company Notes</label>
    <p style="
    height: 112px;
    top: 423px;
    left: 20.16px;
    gap: 0px;
    border-radius: 10px;
    opacity: 0px;
    border: 1px solid #DFDFDF;
    padding: 6px;
    ">
Lorem ipsum dolor sit amet consectetur. Leo in et tortor in quam aliquet. Eget felis bibendum proin gravida a a ultrices.
    </p>
</div>

                                        </div>







                                    </div>



                                </div>
                            </div>

                        </div>

                </div>

                <div class="panel mt-5">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="issue-status">
                                <div class="status-box status-box2 open-issue">
                                    <b>9</b> <br />
                                    <span>Open Issue</span>
                                </div>
                                <div class="status-box status-box2 pending">
                                    <b>9</b> <br />
                                    <span>Pending</span>
                                </div>
                                <div class="status-box completed">
                                    <b>9</b> <br />
                                    <span>Completed</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <canvas id="customerIssuesChart" style="height: 350px; width: 100%; margin-left: -12%;"></canvas>
                        </div>
                    </div>

                </div>
            </div>
            <div class="col-lg-4 mt-4">
                <div class="panel">
                    <p class="detail-heading">Jobs (20)</p>
                    <div class="row">

                            <div class="col-12">
                                <ul class="nav company-client_tabs mt-5" id="company-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link active" id="details-tab-btn" data-toggle="pill" data-target="#company-created-jobs"
                                            type="button" role="tab" aria-controls="details-content" aria-selected="true" >
                                            Active
                                    </div>
                                    </li>

                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link" id="opportunity-team-tab-btn" data-toggle="pill" data-target="#opportunity-team-content-jobs"
                                            type="button" role="tab" aria-controls="opportunity-team-content" aria-selected="false" >
                                            Completed
                                    </div>
                                    </li>

                                </ul>
                            </div>
                            <div class="col-12">
                                <div class="tab-content mt-4" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created-jobs" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                        <div class="row justify-content-between">
                                            <div class="col-6"><label>10001234</label></div>
                                            <div class="col-6 text-right"><label style="color: #00000099;
">Contract Amount: <b>$100</b></label></div>
  <div class="col-6"><b>Kitchen Renovation</b></div>
  <div class="col-6 text-right"><label style="color: #00000099;
">Division: <b>Plumber</b></label></div>
 <div class="col-6"><b>Start Date: 10 Jan</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Service Line: <b>Enhancements</b></label></div>
                                        </div>
                                        <hr>
                                        <div class="row justify-content-between">
                                            <div class="col-6"><label>10001234</label></div>
                                            <div class="col-6 text-right"><label style="color: #00000099;
">Contract Amount: <b>$100</b></label></div>
  <div class="col-6"><b>Kitchen Renovation</b></div>
  <div class="col-6 text-right"><label style="color: #00000099;
">Division: <b>Plumber</b></label></div>
 <div class="col-6"><b>Start Date: 10 Jan</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Service Line: <b>Enhancements</b></label></div>
                                        </div>
                                        <hr>
                                        <div class="row justify-content-between">
                                            <div class="col-6"><label>10001234</label></div>
                                            <div class="col-6 text-right"><label style="color: #00000099;
">Contract Amount: <b>$100</b></label></div>
  <div class="col-6"><b>Kitchen Renovation</b></div>
  <div class="col-6 text-right"><label style="color: #00000099;
">Division: <b>Plumber</b></label></div>
 <div class="col-6"><b>Start Date: 10 Jan</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Service Line: <b>Enhancements</b></label></div>
                                        </div>
                                        <hr>
                                    </div>
                                    <div class="tab-pane fade" id="services-content" role="tabpanel" aria-labelledby="services-content-tab-btn" tabindex="0">
                                        <h2 class="my-5">Division and Line Items </h3>
                                            <div class="container-fluid division-container">

                                                    <div class="row mb-5">
                                                        <div class="division-name mb-4">division -> name </div>
                                                        <hr class="horizontal-line">
                                                        <div class="col-lg-12">

                                                        </div>
                                                    </div>

                                            </div>
                                    </div>
                                    <div class="tab-pane fade" id="opportunity-team-content-jobs" role="tabpanel" aria-labelledby="opportunity-team-tab-btn" tabindex="0">
                                        <!-- <div style="display: flex; justify-content: space-between; align-items: center;" class="mx-2 my-3">
                                            <p> Team</p>
                                            <button class="add_task_button task-btn" data-toggle="modal"
                                            data-target="#taskModal" approve-btn>Add </button>
                                         </div> -->

                                         <div class="row justify-content-between">
                                            <div class="col-6"><label>10001234</label></div>
                                            <div class="col-6 text-right"><label style="color: #00000099;
">Contract Amount: <b>$100</b></label></div>
  <div class="col-6"><b>Kitchen Renovation</b></div>
  <div class="col-6 text-right"><label style="color: #00000099;
">Division: <b>Plumber</b></label></div>
 <div class="col-6"><b>Start Date: 10 Jan</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Service Line: <b>Enhancements</b></label></div>
                                        </div>
                                        <hr>
                                        <div class="row justify-content-between">
                                            <div class="col-6"><label>10001234</label></div>
                                            <div class="col-6 text-right"><label style="color: #00000099;
">Contract Amount: <b>$100</b></label></div>
  <div class="col-6"><b>Kitchen Renovation</b></div>
  <div class="col-6 text-right"><label style="color: #00000099;
">Division: <b>Plumber</b></label></div>
 <div class="col-6"><b>Start Date: 10 Jan</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Service Line: <b>Enhancements</b></label></div>
                                        </div>
                                        <hr>

                                    </div>
                                    <div class="tab-pane fade" id="activity-content" role="tabpanel" aria-labelledby="activity-tab-btn" tabindex="0">
                                        <p>Activity </p>
                                    </div>
                                </div>
                            </div>

                        </div>

                </div>
                <div class="panel mt-5">
                    <div class="row">
                        <div class="col-6"><p class="detail-heading">Contacts</p></div>
                        <div class="col-6 text-right">
                            <a href="#" class="btn btn-large btn-primary px-4 py-2" data-toggle="modal" data-target="#exampleModal">Add Contacts</a>
                        </div>
                    </div>

                    <div class="row">

                            <!-- <div class="col-12">
                                <ul class="nav company-client_tabs mt-5" id="company-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link active" id="details-tab-btn" data-toggle="pill" data-target="#company-created-jobs"
                                            type="button" role="tab" aria-controls="details-content" aria-selected="true" >
                                            Active
                                    </div>
                                    </li>

                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link" id="opportunity-team-tab-btn" data-toggle="pill" data-target="#opportunity-team-content-jobs"
                                            type="button" role="tab" aria-controls="opportunity-team-content" aria-selected="false" >
                                            Completed
                                    </div>
                                    </li>

                                </ul>
                            </div> -->
                            <div class="col-12">
                                <div class="tab-content mt-4" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created-jobs" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                        <div class="row justify-content-between">
                                            <div class="col-8"><b>Mr. Michael James</b> ( Estimator )
                                                <br> <div class="d-flex justify-content-around">
                                                    <img style="width: 13.18px;
                                                    height: 13.18px; margin-top: 5px !important" src="{{ asset('admin_assets/images/call.png') }}" alt=""> <label>+1 242 *********</label>
                                                <img style="width: 13.18px;
                                                height: 13.18px; margin-top: 5px !important" src="{{ asset('admin_assets/images/email.png') }}" alt=""> <label><EMAIL></label>
                                                </div>
                                            </div>
                                            <div class="col-4 text-right">
                                                <a href="#">
                                                    <img src="{{ asset('admin_assets/images/delete.png') }}" style="width: 13.5px;
                                                    height: 15px;
                                                    top: 1.5px;
                                                    left: 2.25px;
                                                    gap: 0px;
                                                    border: 1px 0px 0px 0px;
                                                    opacity: 0px;
                                                    "/>
                                                </a>
                                                <a href="#">
                                                    <img src="{{ asset('admin_assets/images/edit.png') }}" style="width: 13.5px;
                                                    height: 15px;
                                                    top: 1.5px;
                                                    left: 2.25px;
                                                    gap: 0px;
                                                    border: 1px 0px 0px 0px;
                                                    opacity: 0px;
                                                    "/>
                                                </a>

</div>

                                        </div>
                                        <hr>
                                        <div class="row">
                                            <a href="#" style="width: Hug (47px)px;
                                            height: Hug (16px)px;
                                            padding: 0px 14px 0px 14px;
                                            gap: 10px;
                                            border-radius: 12px ;
                                            opacity: 0px;
                                            background: #E6F1FB;
                                            ">Default</a>
                                        </div>
                                        <div class="row justify-content-between">
                                            <div class="col-8"><b>Mr. Michael James</b> ( Estimator )
                                                <br> <div class="d-flex justify-content-around">
                                                    <img style="width: 13.18px;
                                                    height: 13.18px; margin-top: 5px !important" src="{{ asset('admin_assets/images/call.png') }}" alt=""> <label>+1 242 *********</label>
                                                <img style="width: 13.18px;
                                                height: 13.18px; margin-top: 5px !important" src="{{ asset('admin_assets/images/email.png') }}" alt=""> <label><EMAIL></label>
                                                </div>
                                            </div>
                                            <div class="col-4 text-right">
                                                <a href="#">
                                                    <img src="{{ asset('admin_assets/images/delete.png') }}" style="width: 13.5px;
                                                    height: 15px;
                                                    top: 1.5px;
                                                    left: 2.25px;
                                                    gap: 0px;
                                                    border: 1px 0px 0px 0px;
                                                    opacity: 0px;
                                                    "/>
                                                </a>
                                                <a href="#">
                                                    <img src="{{ asset('admin_assets/images/edit.png') }}" style="width: 13.5px;
                                                    height: 15px;
                                                    top: 1.5px;
                                                    left: 2.25px;
                                                    gap: 0px;
                                                    border: 1px 0px 0px 0px;
                                                    opacity: 0px;
                                                    "/>
                                                </a>

</div>

                                        </div>
                                        <hr>

                                        <div class="row justify-content-between">
                                            <div class="col-8"><b>Mr. Michael James</b> ( Estimator )
                                                <br> <div class="d-flex justify-content-around">
                                                    <img style="width: 13.18px;
                                                    height: 13.18px; margin-top: 5px !important" src="{{ asset('admin_assets/images/call.png') }}" alt=""> <label>+1 242 *********</label>
                                                <img style="width: 13.18px;
                                                height: 13.18px; margin-top: 5px !important" src="{{ asset('admin_assets/images/email.png') }}" alt=""> <label><EMAIL></label>
                                                </div>
                                            </div>
                                            <div class="col-4 text-right">
                                                <a href="#">
                                                    <img src="{{ asset('admin_assets/images/delete.png') }}" style="width: 13.5px;
                                                    height: 15px;
                                                    top: 1.5px;
                                                    left: 2.25px;
                                                    gap: 0px;
                                                    border: 1px 0px 0px 0px;
                                                    opacity: 0px;
                                                    "/>
                                                </a>
                                                <a href="#">
                                                    <img src="{{ asset('admin_assets/images/edit.png') }}" style="width: 13.5px;
                                                    height: 15px;
                                                    top: 1.5px;
                                                    left: 2.25px;
                                                    gap: 0px;
                                                    border: 1px 0px 0px 0px;
                                                    opacity: 0px;
                                                    "/>
                                                </a>

</div>

                                        </div>
                                        <hr>
                                        <div class="row justify-content-between">
                                            <div class="col-8"><b>Mr. Michael James</b> ( Estimator )
                                                <br> <div class="d-flex justify-content-around">
                                                    <img style="width: 13.18px;
                                                    height: 13.18px; margin-top: 5px !important" src="{{ asset('admin_assets/images/call.png') }}" alt=""> <label>+1 242 *********</label>
                                                <img style="width: 13.18px;
                                                height: 13.18px; margin-top: 5px !important" src="{{ asset('admin_assets/images/email.png') }}" alt=""> <label><EMAIL></label>
                                                </div>
                                            </div>
                                            <div class="col-4 text-right">
                                                <a href="#">
                                                    <img src="{{ asset('admin_assets/images/delete.png') }}" style="width: 13.5px;
                                                    height: 15px;
                                                    top: 1.5px;
                                                    left: 2.25px;
                                                    gap: 0px;
                                                    border: 1px 0px 0px 0px;
                                                    opacity: 0px;
                                                    "/>
                                                </a>
                                                <a href="#">
                                                    <img src="{{ asset('admin_assets/images/edit.png') }}" style="width: 13.5px;
                                                    height: 15px;
                                                    top: 1.5px;
                                                    left: 2.25px;
                                                    gap: 0px;
                                                    border: 1px 0px 0px 0px;
                                                    opacity: 0px;
                                                    "/>
                                                </a>

</div>

                                        </div>
                                        <hr>

                                    </div>
                                    <div class="tab-pane fade" id="services-content" role="tabpanel" aria-labelledby="services-content-tab-btn" tabindex="0">
                                        <h2 class="my-5">Division and Line Items </h3>
                                            <div class="container-fluid division-container">

                                                    <div class="row mb-5">
                                                        <div class="division-name mb-4">division -> name </div>
                                                        <hr class="horizontal-line">
                                                        <div class="col-lg-12">

                                                        </div>
                                                    </div>

                                            </div>
                                    </div>
                                    <div class="tab-pane fade" id="opportunity-team-content-jobs" role="tabpanel" aria-labelledby="opportunity-team-tab-btn" tabindex="0">
                                        <div style="display: flex; justify-content: space-between; align-items: center;" class="mx-2 my-3">
                                            <p> Team</p>
                                            <button class="add_task_button task-btn" data-toggle="modal"
                                            data-target="#taskModal" approve-btn>Add </button>
                                         </div>



                                    </div>
                                    <div class="tab-pane fade" id="activity-content" role="tabpanel" aria-labelledby="activity-tab-btn" tabindex="0">
                                        <p>Activity </p>
                                    </div>
                                </div>
                            </div>

                        </div>

                </div>
            </div>



            <div class="col-lg-4 mt-4">
                <div class="panel">
                    <p class="detail-heading">Proposed Estimates</p>
                    <div class="row">

                            <div class="col-12">
                                <ul class="nav company-client_tabs mt-5" id="company-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link active" id="details-tab-btn" data-toggle="pill" data-target="#company-created-Estimates"
                                            type="button" role="tab" aria-controls="details-content" aria-selected="true" >
                                            Active
                                    </div>
                                    </li>

                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link" id="opportunity-team-tab-btn" data-toggle="pill" data-target="#opportunity-team-content-Estimates"
                                            type="button" role="tab" aria-controls="opportunity-team-content" aria-selected="false" >
                                            Archive
                                    </div>
                                    </li>

                                </ul>
                            </div>
                            <div class="col-12">
                                <div class="tab-content mt-4" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created-Estimates" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                        <div class="row justify-content-between">
                                            <div class="col-6"><b>Michael james</b></div>
                                            <div class="col-6 text-right"><a href="#" style="width: Fixed (96px)px;
                                                height: Fixed (30px)px;
                                                padding: 8px 33px 8px 33px;
                                                gap: 8px;
                                                border-radius: 8px;
                                                opacity: 0px;
                                                background: #FFF5E9;
                                                font-family: Poppins;
font-size: 14px;
font-weight: 500;
line-height: 22px;
text-align: left;
color: #EF8D03;


                                                ">Open</a></div>
  <div class="col-6"><b>Creator.io</b></div>
  <div class="col-6 text-right"></div>
 <div class="col-6"><b>Michael james Property</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Estimaor Name: <b>John Doe</b></label></div>
                                        </div>
                                        <hr>
                                        <div class="row justify-content-between">
                                            <div class="col-6"><b>Michael james</b></div>
                                            <div class="col-6 text-right"><a href="#" style="width: Fixed (96px)px;
                                                height: Fixed (30px)px;
                                                padding: 8px 33px 8px 33px;
                                                gap: 8px;
                                                border-radius: 8px;
                                                opacity: 0px;
                                                background: #FFEFEF;
                                                font-family: Poppins;
font-size: 14px;
font-weight: 500;
line-height: 22px;
text-align: left;
color: #FF4B55;


                                                ">Open</a></div>
  <div class="col-6"><b>Creator.io</b></div>
  <div class="col-6 text-right"></div>
 <div class="col-6"><b>Michael james Property</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Estimaor Name: <b>John Doe</b></label></div>
                                        </div>
                                        <hr>
                                        <div class="row justify-content-between">
                                            <div class="col-6"><b>Michael james</b></div>
                                            <div class="col-6 text-right"><a href="#" style="width: Fixed (96px)px;
                                                height: Fixed (30px)px;
                                                padding: 8px 33px 8px 33px;
                                                gap: 8px;
                                                border-radius: 8px;
                                                opacity: 0px;
                                                background: #E8FFEB;
                                                font-family: Poppins;
font-size: 14px;
font-weight: 500;
line-height: 22px;
text-align: left;
color: #27B737;


                                                ">Open</a></div>
  <div class="col-6"><b>Creator.io</b></div>
  <div class="col-6 text-right"></div>
 <div class="col-6"><b>Michael james Property</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Estimaor Name: <b>John Doe</b></label></div>
                                        </div>
                                        <hr>
                                        <div class="row justify-content-between">
                                            <div class="col-6"><b>Michael james</b></div>
                                            <div class="col-6 text-right"><a href="#" style="width: Fixed (96px)px;
                                                height: Fixed (30px)px;
                                                padding: 8px 33px 8px 33px;
                                                gap: 8px;
                                                border-radius: 8px;
                                                opacity: 0px;
                                                background: #F4F7FA;
                                                font-family: Poppins;
font-size: 14px;
font-weight: 500;
line-height: 22px;
text-align: left;
color: #7C8091;


                                                ">Open</a></div>
  <div class="col-6"><b>Creator.io</b></div>
  <div class="col-6 text-right"></div>
 <div class="col-6"><b>Michael james Property</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Estimaor Name: <b>John Doe</b></label></div>
                                        </div>
                                        <hr>
                                    </div>
                                    <div class="tab-pane fade" id="services-content" role="tabpanel" aria-labelledby="services-content-tab-btn" tabindex="0">
                                        <h2 class="my-5">Division and Line Items </h3>
                                            <div class="container-fluid division-container">

                                                    <div class="row mb-5">
                                                        <div class="division-name mb-4">division -> name </div>
                                                        <hr class="horizontal-line">
                                                        <div class="col-lg-12">

                                                        </div>
                                                    </div>

                                            </div>
                                    </div>
                                    <div class="tab-pane fade" id="opportunity-team-content-Estimates" role="tabpanel" aria-labelledby="opportunity-team-tab-btn" tabindex="0">
                                        <div class="row justify-content-between">
                                            <div class="col-6"><b>Michael james</b></div>
                                            <div class="col-6 text-right"><a href="#" style="width: Fixed (96px)px;
                                                height: Fixed (30px)px;
                                                padding: 8px 33px 8px 33px;
                                                gap: 8px;
                                                border-radius: 8px;
                                                opacity: 0px;
                                                background: #FFF5E9;
                                                font-family: Poppins;
font-size: 14px;
font-weight: 500;
line-height: 22px;
text-align: left;
color: #EF8D03;


                                                ">Open</a></div>
  <div class="col-6"><b>Creator.io</b></div>
  <div class="col-6 text-right"></div>
 <div class="col-6"><b>Michael james Property</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Estimaor Name: <b>John Doe</b></label></div>
                                        </div>
                                        <hr>
                                        <div class="row justify-content-between">
                                            <div class="col-6"><b>Michael james</b></div>
                                            <div class="col-6 text-right"><a href="#" style="width: Fixed (96px)px;
                                                height: Fixed (30px)px;
                                                padding: 8px 33px 8px 33px;
                                                gap: 8px;
                                                border-radius: 8px;
                                                opacity: 0px;
                                                background: #FFF5E9;
                                                font-family: Poppins;
font-size: 14px;
font-weight: 500;
line-height: 22px;
text-align: left;
color: #EF8D03;


                                                ">Open</a></div>
  <div class="col-6"><b>Creator.io</b></div>
  <div class="col-6 text-right"></div>
 <div class="col-6"><b>Michael james Property</b></div>
 <div class="col-6 text-right"><label style="color: #00000099;
">Estimaor Name: <b>John Doe</b></label></div>
                                        </div>
                                        <hr>



                                    </div>
                                    <div class="tab-pane fade" id="activity-content" role="tabpanel" aria-labelledby="activity-tab-btn" tabindex="0">
                                        <p>Activity </p>
                                    </div>
                                </div>
                            </div>

                        </div>

                </div>
                <div class="panel mt-5">
                    <div class="row">
                        <div class="col-6"><p class="detail-heading">File Manager</p></div>
                        <div class="col-6 text-right">
                            <a href="#" class="btn btn-large btn-primary px-4 py-2" data-toggle="modal" data-target="#uploadfilemodal">Upload</a>
                        </div>
                    </div>

                    <div class="row">

                            <div class="col-12">
                                <ul class="nav company-client_tabs mt-5" id="company-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link active" id="details-tab-btn" data-toggle="pill" data-target="#company-created-file"
                                            type="button" role="tab" aria-controls="details-content" aria-selected="true" >
                                            Active
                                    </div>
                                    </li>

                                    <li class="nav-item" role="presentation">
                                        <div class="nav-link" id="opportunity-team-tab-btn" data-toggle="pill" data-target="#opportunity-team-content-file"
                                            type="button" role="tab" aria-controls="opportunity-team-content" aria-selected="false" >
                                            Archive
                                    </div>
                                    </li>

                                </ul>
                            </div>
                            <div class="col-12">
                                <div class="tab-content mt-4" id="jobSummary">
                                    <div class="tab-pane show active" id="company-created-file" role="tabpanel" aria-labelledby="company-tab-btn" tabindex="0">
                                        <div class="row" style="height: 58px;
                                        padding: 6px 8px 6px 8px;
                                        gap: 0px;
                                        border-radius: 9px;
                                        border: 1px 0px 0px 0px;
                                        justify: space-between;
                                        opacity: 0px;
                                        border: 1px solid var(--layout-border, #E1E5EA)
                                        ">
                                        <div class="col-2 text-center" >
                                            <div style="
                                            width: 44px !important;
                                            height: 44px;
                                            align-items: center;

                                            border-radius: 6px;

                                            background: #FFEBC6;">
                                        <img style="width: 17px;
                                        height: 20px; margin-top: 11px;" src="{{ asset('admin_assets/images/sitemap.png') }}" alt="">
</div>
                                        </div>
<div class="col-6">
    <p style="margin-top: -2px;">Sitemap</p><label for="" style="color: #7C8091;">Description info</label>
</div>
<div class="col-4 text-right " style="">
    <div class="text-center" style="width: 32px; margin-left: auto; margin-top: 6px;
    height: 32px;
    padding: 2px 7px 8px 7px;

    border-radius: 8px;

    background-color: #D9EAF9;
    ">
    <img src="{{ asset('admin_assets/images/download.png') }}" alt="">
    </div>
</div>
                                        </div>
                                        <div class="row mt-4" style="height: 58px;
                                        padding: 6px 8px 6px 8px;
                                        gap: 0px;
                                        border-radius: 9px;
                                        border: 1px 0px 0px 0px;
                                        justify: space-between;
                                        opacity: 0px;
                                        border: 1px solid var(--layout-border, #E1E5EA)
                                        ">
                                        <div class="col-2 text-center" >
                                            <div style="
                                            width: 44px !important;
                                            height: 44px;
                                            align-items: center;

                                            border-radius: 6px;

                                            background: #EBF1FF;">
                                        <img style="width: 17px;
                                        height: 20px; margin-top: 11px;" src="{{ asset('admin_assets/images/offer.png') }}" alt="">
</div>
                                        </div>
<div class="col-6">
    <p style="margin-top: -2px;">Offer</p><label for="" style="color: #7C8091;">120KB</label>
</div>
<div class="col-4 text-right " style="">
    <div class="text-center" style="width: 32px; margin-left: auto; margin-top: 6px;
    height: 32px;
    padding: 2px 7px 8px 7px;

    border-radius: 8px;

    background-color: #D9EAF9;
    ">
    <img src="{{ asset('admin_assets/images/download.png') }}" alt="">
    </div>
</div>
                                        </div>
                                        <div class="row mt-4" style="height: 58px;
                                        padding: 6px 8px 6px 8px;
                                        gap: 0px;
                                        border-radius: 9px;
                                        border: 1px 0px 0px 0px;
                                        justify: space-between;
                                        opacity: 0px;
                                        border: 1px solid var(--layout-border, #E1E5EA)
                                        ">
                                        <div class="col-2 text-center" >
                                            <div style="
                                            width: 44px !important;
                                            height: 44px;
                                            align-items: center;

                                            border-radius: 6px;

                                            background: #EBF1FF;">
                                        <img style="width: 17px;
                                        height: 20px; margin-top: 11px;" src="{{ asset('admin_assets/images/offer.png') }}" alt="">
</div>
                                        </div>
<div class="col-6">
    <p style="margin-top: -2px;">Contract for window</p><label for="" style="color: #7C8091;">120KB</label>
</div>
<div class="col-4 text-right " style="">
    <div class="text-center" style="width: 32px; margin-left: auto; margin-top: 6px;
    height: 32px;
    padding: 2px 7px 8px 7px;

    border-radius: 8px;

    background-color: #D9EAF9;
    ">
    <img src="{{ asset('admin_assets/images/download.png') }}" alt="">
    </div>
</div>
                                        </div>
                                        <div class="row mt-4" style="height: 58px;
                                        padding: 6px 8px 6px 8px;
                                        gap: 0px;
                                        border-radius: 9px;
                                        border: 1px 0px 0px 0px;
                                        justify: space-between;
                                        opacity: 0px;
                                        border: 1px solid var(--layout-border, #E1E5EA)
                                        ">
                                        <div class="col-2 text-center" >
                                            <div style="
                                            width: 44px !important;
                                            height: 44px;
                                            align-items: center;

                                            border-radius: 6px;

                                            background: #EBF1FF;">
                                        <img style="width: 17px;
                                        height: 20px; margin-top: 11px;" src="{{ asset('admin_assets/images/offer.png') }}" alt="">
</div>
                                        </div>
<div class="col-6">
    <p style="margin-top: -2px;">Home</p><label for="" style="color: #7C8091;">120KB</label>
</div>
<div class="col-4 text-right " style="">
    <div class="text-center" style="width: 32px; margin-left: auto; margin-top: 6px;
    height: 32px;
    padding: 2px 7px 8px 7px;

    border-radius: 8px;

    background-color: #D9EAF9;
    ">
    <img src="{{ asset('admin_assets/images/download.png') }}" alt="">
    </div>
</div>
                                        </div>





                                    </div>
                                    <div class="tab-pane fade" id="services-content" role="tabpanel" aria-labelledby="services-content-tab-btn" tabindex="0">
                                        <h2 class="my-5">Division and Line Items </h3>
                                            <div class="container-fluid division-container">

                                                    <div class="row mb-5">
                                                        <div class="division-name mb-4">division -> name </div>
                                                        <hr class="horizontal-line">
                                                        <div class="col-lg-12">

                                                        </div>
                                                    </div>

                                            </div>
                                    </div>
                                    <div class="tab-pane fade" id="opportunity-team-content-file" role="tabpanel" aria-labelledby="opportunity-team-tab-btn" tabindex="0">
                                        <div class="row" style="height: 58px;
                                        padding: 6px 8px 6px 8px;
                                        gap: 0px;
                                        border-radius: 9px;
                                        border: 1px 0px 0px 0px;
                                        justify: space-between;
                                        opacity: 0px;
                                        border: 1px solid var(--layout-border, #E1E5EA)
                                        ">
                                        <div class="col-2 text-center" >
                                            <div style="
                                            width: 44px !important;
                                            height: 44px;
                                            align-items: center;

                                            border-radius: 6px;

                                            background: #FFEBC6;">
                                        <img style="width: 17px;
                                        height: 20px; margin-top: 11px;" src="{{ asset('admin_assets/images/sitemap.png') }}" alt="">
</div>
                                        </div>
<div class="col-6">
    <p style="margin-top: -2px;">Sitemap</p><label for="" style="color: #7C8091;">Description info</label>
</div>
<div class="col-4 text-right " style="">
    <div class="text-center" style="width: 32px; margin-left: auto; margin-top: 6px;
    height: 32px;
    padding: 2px 7px 8px 7px;

    border-radius: 8px;

    background-color: #D9EAF9;
    ">
    <img src="{{ asset('admin_assets/images/download.png') }}" alt="">
    </div>
</div>
                                        </div>
                                        <div class="row" style="height: 58px;
                                        padding: 6px 8px 6px 8px;
                                        gap: 0px;
                                        border-radius: 9px;
                                        border: 1px 0px 0px 0px;
                                        justify: space-between;
                                        opacity: 0px;
                                        border: 1px solid var(--layout-border, #E1E5EA)
                                        ">
                                        <div class="col-2 text-center" >
                                            <div style="
                                            width: 44px !important;
                                            height: 44px;
                                            align-items: center;

                                            border-radius: 6px;

                                            background: #FFEBC6;">
                                        <img style="width: 17px;
                                        height: 20px; margin-top: 11px;" src="{{ asset('admin_assets/images/sitemap.png') }}" alt="">
</div>
                                        </div>
<div class="col-6">
    <p style="margin-top: -2px;">Sitemap</p><label for="" style="color: #7C8091;">Description info</label>
</div>
<div class="col-4 text-right " style="">
    <div class="text-center" style="width: 32px; margin-left: auto; margin-top: 6px;
    height: 32px;
    padding: 2px 7px 8px 7px;

    border-radius: 8px;

    background-color: #D9EAF9;
    ">
    <img src="{{ asset('admin_assets/images/download.png') }}" alt="">
    </div>
</div>
                                        </div>



                                    </div>
                                    <div class="tab-pane fade" id="activity-content" role="tabpanel" aria-labelledby="activity-tab-btn" tabindex="0">
                                        <p>Activity </p>
                                    </div>
                                </div>
                            </div>

                        </div>

                </div>
            </div>
        </div>
    </section>

    <!-- Button trigger modal -->
<!-- <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal">
    Launch demo modal
  </button> -->

  <!-- Modal -->
  <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header" style="background-color: #E1F4FF;">
            <p class="detail-heading" style="color: #0074D9;">Add New Contact</p>
          <button type="button" class="btn-close contactmodal" data-dismiss="modal" aria-label="Close" style="color: #7E8A9D; background: transparent; border: none; font-size: 16px;">X</button>
        </div>
        <div class="modal-body">
            <form>
                <div class="mb-3">
                  <p for="firstname" class="form-label">First Name</p>
                  <input type="text" placeholder="Enter first name" class="form-control inputform" id="firstname">
                </div>
                <div class="mb-3">
                    <p for="lastname" class="form-label">Last Name</p>
                    <input type="text" placeholder="Enter last name" class="form-control inputform" id="lastname">
                </div>
                <div class="mb-3">
                    <p for="title" class="form-label">Title</p>
                    <input type="text" placeholder="Enter title" class="form-control inputform" id="title">
                </div>
                <div class="mb-3">
                    <p for="email" class="form-label">Email</p>
                    <input type="email" placeholder="Enter email" class="form-control inputform" id="email">
                </div>
                <div class="mb-3">
                    <p for="office" class="form-label">Office</p>
                    <input type="text" placeholder="Enter office number" class="form-control inputform" id="office">
                </div>
                <div class="mb-3">
                    <p for="mobile" class="form-label">Mobile</p>
                    <input type="text" placeholder="Enter mobile number" class="form-control inputform" id="mobile">
                </div>
                <div class="mb-3 form-check">
                  <input type="checkbox" class="form-check-input" id="exampleCheck1">
                  <label class="form-check-label" for="exampleCheck1"> &nbsp;&nbsp;&nbsp;Set as default contact information </label>
                </div>
                <button type="submit" class="btn btn-primary form-control" style="height: 32px;">Add Contact</button>
              </form>
        </div>
        <!-- <div class="modal-footer"> -->
          <!-- <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          <button type="button" class="btn btn-primary">Save changes</button> -->
        <!-- </div> -->
      </div>
    </div>
  </div>


   <div class="modal fade" id="uploadfilemodal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header" style="background-color: #E1F4FF;">
            <p class="detail-heading" style="color: #0074D9;">Add New Contact</p>
          <button type="button" class="btn-close contactmodal" data-dismiss="modal" aria-label="Close" style="color: #7E8A9D; background: transparent; border: none; font-size: 16px;">X</button>
        </div>
        <div class="modal-body">
            <form>
                <div class="mb-3">
                  <!-- <p for="firstname" class="form-label">First Name</p> -->
                  <input type="text" placeholder="File name here" style="background: white" readonly class="form-control inputform" id="filename22">
                </div>
                <section class="bg-diffrent">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="file-upload-contain">
                    <input id="multiplefileupload" style="display: none;" type="file"/>
                </div>
            </div>
        </div>
    </div>
</section>


                <button type="submit" class="btn btn-primary form-control" style="height: 32px;">Upload</button>
              </form>
        </div>
        <!-- <div class="modal-footer"> -->
          <!-- <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          <button type="button" class="btn btn-primary">Save changes</button> -->
        <!-- </div> -->
      </div>
    </div>
  </div>



        @push('scripts')
  @include('organization.property.detailsscript')
        @endpush
@endsection
