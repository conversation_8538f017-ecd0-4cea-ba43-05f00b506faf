@php
    $totalEquipmentAmount = 0;
    $totalLaborAmount = 0;
    $totalLaborHours = 0;
    $totalMaterialAmount = 0;
    $totalOtherAmount = 0;
    $totalSubcontractor = 0;
    $totalCostAmount = 0;
@endphp

@foreach($sectionss as $item)
        <?php $gitems = DB::table('estimate_items')->where('section_name', $item->id)->get(); ?>

    @foreach($gitems as $data)
        @php
            if($data->category_type == 'equipment'){
                $totalEquipmentAmount += $data->total_price;
            }else if ($data->category_type == 'labors'){
                $totalLaborHours += $data->quantity;
                $totalLaborAmount += $data->total_price;
            }else if($data->category_type == 'plant_materials' || $data->category_type == 'hard_materials') {
                $totalMaterialAmount += $data->total_price;
            }else if($data->category_type == 'other_costs') {
                $totalOtherAmount += $data->total_price;
            }else if($data->category_type == 'contractors') {
                $totalSubcontractor += $data->total_price;
            }
            $totalCostAmount += $data->total_cost;

        @endphp
    @endforeach
@endforeach

@foreach($gtems as $data)
    @php
        if($data->category_type == 'equipment'){
            $totalEquipmentAmount += $data->total_price;
        }else if ($data->category_type == 'labors'){
            $totalLaborHours += $data->quantity;
            $totalLaborAmount += $data->total_price;
        }else if($data->category_type == 'plant_materials' || $data->category_type == 'hard_materials') {
            if($data->category_type == 'hard_materials'){
                $material = \App\Models\HardMaterial::find($data->item_id);
                $laborPerUnit = $material?->labor ?? 0;
            }else {
                $material = \App\Models\PlantMaterial::find($data->item_id);
                $laborPerUnit = $material?->install ?? 0;
            }
            $totalMaterialAmount += $data->total_price;
            $totalLaborHours += $data->quantity * $laborPerUnit;
        }else if($data->category_type == 'other_costs') {
            $totalOtherAmount += $data->total_price;
        }else if($data->category_type == 'contractors') {
            $totalSubcontractor += $data->total_price;
        }
        $totalCostAmount += $data->total_cost;
    @endphp
@endforeach

<div class="bottombox">
    <label>Equipment</label>
    <p id="totalcountequipPrice">{{ Number::currency($totalEquipmentAmount) }}</p>
</div>
<div class="bottombox">
    <label>Labor</label>
    <p id="totalcountlaborPrice">{{ Number::currency($totalLaborAmount) }}</p>
</div>
<div class="bottombox">
    <label>Labor Burden</label>
    <p id="totalcountburdenPrice">{{ Number::currency((($burderlabor->labor_burden ?? 0) / 100) * $totalLaborAmount) }}</p>
</div>
<div class="bottombox">
    <label>Labor Hours</label>
    <p id="totalhourslabors">{{ Number::format($totalLaborHours) }}</p>
</div>
<div class="bottombox">
    <label>Supervisor Hours</label>
    <p id="totalhourssupervision">{{ $supervisorHours }}</p>
</div>
<div class="bottombox">
    <label>Material</label>
    <p id="totalcountmaterialPrice">{{ Number::currency($totalMaterialAmount) }}</p>
    <!-- <p id="totalcountmaterial">$0.00</p> -->
</div>

<div class="bottombox">
    <label>Other</label>
    <p id="totalcountotherPrice">{{ Number::currency($totalOtherAmount) }}</p>
    <!-- <p id="totalcountother">$0.00</p> -->
</div>
<div class="bottombox">
    <label>Sub Contractor</label>
    <p id="totalSubContractorsValuPrice">{{ Number::currency($totalSubcontractor) }}</p>
    <!-- <p id="totalSubContractorsValu">$0.00</p> -->
</div>
<div class="bottombox">
    <label>Sales tax</label>
    <p id="totalmaterialsaletexPrice">{{ Number::currency($taxRate * $totalMaterialAmount) }}</p>
</div>
<div class="bottombox2">
    <label>Total</label>
    <p id="totalPriceCount">{{ Number::currency($totalMaterialAmount+$totalSubcontractor+$totalOtherAmount+ ((($burderlabor->labor_burden ?? 0) / 100) * $totalLaborAmount) + $totalLaborAmount + $totalEquipmentAmount) }}</p>
    <p id="totalallcount" style="display: none;">${{ $totalCostAmount }}</p>
</div>
