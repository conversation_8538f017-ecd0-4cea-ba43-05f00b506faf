<table class="table  maintable main-table-dropable" style="border-radius: 8px !important;">
    <thead>
    <tr>
        <th scope="col" style="width: 4%;" colspan="">Items</th>
        <th scope="col" style="width: 20%;"></th>
        <!-- new headers -->
        <th class="border" style="width:5%">Dimension</th>
        <th class="border" scope="col" style="width: 12%;">Quantity</th>
        <th class="border" scope="col" style="width: 9%;">UoM</th>
        <th class="border" scope="col" style="width: 9%;">Unit Cost ($)</th>
        <th class="border" scope="col" style="width: 9%;">Total Cost ($)</th>
        <th class="border" scope="col" style="width: 12%;">Gross Margin</th>
        <th class="border" scope="col" style="width: 9%;">Total Price ($)</th>
        <th class="border" scope="col">Unit Price ($)</th>
        <th class="border" scope="col"></th>
    </tr>
    </thead>
    <tbody id="sortable-table-body" class="">
    @php
        $totalEquipmentAmount = 0;
        $totalLaborAmount = 0;
        $totalLaborHours = 0;
        $totalMaterialAmount = 0;
        $totalOtherAmount = 0;
        $totalSubcontractor = 0;
    @endphp
    @foreach($sectionss as $item)
            <?php $gitems = DB::table('estimate_items')->where('section_name', $item->id)->get(); ?>
        <tr class="tablerows-{{$item->id}} lastforsectionrow" style="background: #f5faff">
            <td>
                <div class="d-flex align-items-center">
                    <input type="checkbox"
                           class="dynamic-checkbox d-flex align-items-center"
                           id="checkbox-{{$item->id}}"
                           data-id="{{$item->id}}"
                           title="Add Item"
                           style="cursor: pointer;">
                </div>
            </td>
            <td class="align-middle">
                <div class="d-flex align-items-center">
                    <div id="section1Heading-{{$item->id}}"
                         class="me-2 sectionNames">{{$item->section_name}}</div>
                    <input type="hidden" style="text-align: left;" class="editinputid"
                           id="editinputid-{{$item->id}}" data-sec-id="{{$item->id}}"
                           value="{{$item->id}}">
                    <input type="text" style="text-align: left;"
                           value="{{$item->section_name}}" class="editinput"
                           id="editinput-{{$item->id}}">
                    <a href="#" onclick="editSection2({{$item->id}})"
                       id="editbutn-{{$item->id}}"
                       class="text-decoration-none mt-1 mx-3 editbutn">
                        <svg
                            class=""
                            width="15"
                            height="15"
                            viewBox="0 0 22 22"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M1.87604 17.1156C1.92198 16.7021 1.94496 16.4954 2.00751 16.3022C2.06301 16.1307 2.14143 15.9676 2.24064 15.8171C2.35246 15.6475 2.49955 15.5005 2.79373 15.2063L16 2C17.1046 0.895427 18.8955 0.895428 20 2C21.1046 3.10457 21.1046 4.89543 20 6L6.79373 19.2063C6.49955 19.5005 6.35245 19.6475 6.18289 19.7594C6.03245 19.8586 5.86929 19.937 5.69785 19.9925C5.5046 20.055 5.29786 20.078 4.88437 20.124L1.5 20.5L1.87604 17.1156Z"
                                stroke="#7C8091"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                    </a>
                    <a href="#" onclick="updateSection3({{$item->id}})"
                       id="okbutn-{{$item->id}}" style="padding-right: 25px;"
                       class="btn btn-sm btn-primary text-decoration-none mt-1 mx-3 okbutn">
                        Save
                    </a>
                </div>
            </td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td class="d-flex" style="gap: 15px">
                <div class="dropdown">
                    <button
                        class="btn btn-sm bg-transparent dropdown-toggle"
                        type="button"
                        data-toggle="dropdown"
                        aria-expanded="false"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            fill="currentColor"
                            class="bi bi-three-dots"
                            viewBox="0 0 16 16"
                        >
                            <path
                                d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"
                            />
                        </svg>
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a onclick="editSection2({{$item->id}})"
                               class="dropdown-item" href="#">Edit</a>
                        </li>
                        <li>
                            <a onclick="deleteSection3({{$item->id}})"
                               class="dropdown-item" href="#">Delete</a>
                        </li>
                    </ul>
                </div>

                <button
                    class="btn btn-sm"
                    id="mainbtnnow-{{$item->id}}"
                    style="background: #f6f6f6; border: 1px solid #dfdfdf;"
                    type="button"
                    data-toggle="collapse"
                    data-target="#section-{{$item->id}}"
                    aria-expanded="false"
                    aria-controls="section-initial"
                    onclick="toggleAccordion2({{$item->id}})"
                >
                    <label for="" class="changeiconall changeicon-{{$item->id}}">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="12"
                            height="12"
                            fill="currentColor"
                            class="bi bi-chevron-down arrow-icon "
                            viewBox="0 0 16 16"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"
                            />
                        </svg>
                    </label>
                </button>
            </td>
        </tr>

        <tr id="section-{{$item->id}}" class="collapse tavy tablerows-{{$item->id}}">
            <td style="padding: 0px !important" colspan="11">
                <table class="table">
                    <tbody class="sortable-content dropable-content-here accord-bodys accord-bodys-{{$item->id}}" data-section-id="{{$item->id}}">
                    @foreach($gitems as $data)

                        @php
                            if($data->category_type == 'equipment'){
                                $totalEquipmentAmount += $data->total_price;
                            }else if ($data->category_type == 'labors'){
                                $totalLaborHours += $data->quantity;
                                $totalLaborAmount += $data->total_price;
                            }else if($data->category_type == 'plant_materials' || $data->category_type == 'hard_materials') {
                                $totalMaterialAmount += $data->total_price;
                            }else if($data->category_type == 'other_costs') {
                                $totalOtherAmount += $data->total_price;
                            }else if($data->category_type == 'contractors') {
                                $totalSubcontractor += $data->total_price;
                            }
                                $lb='';
                $upida="";
                $TJQ = '';
                if($data->category_type == 'equipment'){
                    $m="Equipment's";
                    $clid='getitemsequipid2';

                    $totalcostsequip="totalcostsequip2";
                    $unitcostsequ="unitcostsequ2";
                    $grossmarginequ="grossmarginequ2";
                    $quantityequ="quantityequ2";
                    $totalpriceequipmn="totalpriceequi2";
                }else if($data->category_type == 'hard_materials'){
                    $m="Hard Material";
                    $clid='getitemshardid2';

                    $totalcostsequip="totalcostshard2";
                    $unitcostsequ="unitcostshar2";
                    $grossmarginequ="grossmarginhar2";
                    $quantityequ="quantityhar2";
                    $totalpriceequipmn="totalpricehardm2";
                }else if($data->category_type == 'plant_materials'){
                    $m="Plant Material";
                    $clid='getitemsplantid2';

                    $totalcostsequip="totalcostsplant2";
                    $unitcostsequ="unitcostspla2";
                    $grossmarginequ="grossmarginpla2";
                    $quantityequ="quantitypla2";
                    $totalpriceequipmn="totalpriceplantm2";
                }else if($data->category_type == 'other_costs'){
                    $m="Other Job Costs";
                    $clid='getitemsotherid2';

                    $totalcostsequip="totalcostscosts2";
                    $unitcostsequ="unitcostscos2";
                    $grossmarginequ="grossmargincos2";
                    $quantityequ="quantitycos2";
                    $totalpriceequipmn="totalpriceothercost2";
                }else if($data->category_type == 'contractors'){
                    $m="Contractor";
                    $clid='getitemscontractorid2';

                    $totalcostsequip="totalcostscontr2";
                    $unitcostsequ="unitcostscont2";
                    $grossmarginequ="grossmargincont2";
                    $quantityequ="quantitycontss2";
                    $totalpriceequipmn="totalpricecontractorm2";
                }else if($data->category_type == 'labors'){
                    $m="Labor";
                    $clid='getitemsid2';

                    $totalcostsequip="totalcostslabor2";
                    $unitcostsequ="unitcostslabor2";
                    $grossmarginequ="grossmarginlabor2";
                    $quantityequ="quantitylabor2";
                    $totalpriceequipmn="totalpricelabor2";
                   $lb2 = DB::table('labors')->where('id', $data->item_id)->first();
                   $lb = $lb2->labor_burden;
                   if($data->labor_type == 'Laborers')
                   {
                   $TJQ='totalhourslabors';
                }else{
                    $TJQ='totalhourssupervisions';
                }
                }
                $margin = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', $m)->first();
                if ($data->category_type =='equipment') {
$mclass='datarowsequ2';
$tclass="totalcostsequip2";
$qtyclass="quantityequ2";
$ucclass="unitcostsequ2";
$gmclass="grossmarginequ2";
$tpclass="equipmenttotalprice";

$tcostcls='equipmentcount';

}else if ($data->category_type =='labors') {
$mclass='datarowsLabor2';
$tclass="totalcostslabo2";
$qtyclass="quantitylabo2";
$ucclass="unitcostslabo2";
$gmclass="grossmarginlabo2";
$tpclass="labortotalprice";
$tcostcls='laborcount';
}else if ($data->category_type =='hard_materials') {
$mclass='datarowshard2';
$tclass="totalcostshard2";
$qtyclass="quantityhar2";
$ucclass="unitcostshar2";
$gmclass="grossmarginhar2";
$tpclass="materialtotalprice";
$tcostcls='materialcount';
}else if ($data->category_type =='plant_materials') {
$mclass='datarowsplant2';
$tclass="totalcostsplant2";
$qtyclass="quantitypla2";
$ucclass="unitcostspla2";
$gmclass="grossmarginpla2";
$tpclass="materialtotalprice";
$tcostcls='materialcount';
}else if ($data->category_type =='other_costs') {
$mclass='datarowsother2';
$tclass="totalcostscosts2";
$qtyclass="quantitycos2";
$ucclass="unitcostscos2";
$gmclass="grossmargincos2";
$tpclass="othertotalprice";
$tcostcls='othercostcount';
}else if ($data->category_type =='contractors') {
$mclass='datarowsContractor2';
$tclass="totalcostscontr2";
$qtyclass="quantitycontss2";
$ucclass="unitcostscont2";
$gmclass="grossmargincont2";
$tpclass="contractortotalprice";
$tcostcls='subcontcount';
$upida="upidss";
}
                        @endphp
                        <tr class="border datarowsequ22-{{$data->id}}" data-item-id="{{$data->id}}">
                            <td class="drag-handle" style="width: 4%">
                                <img src="{{ asset('asset/assets/images/drag-drop-icon.svg') }}" alt="drag and drop icon">
                            </td>
                            <td class="border" style="width: 20% !important;">
                                <label for="">
                                    @if ($data->category_type =='labors')
                                        <input type="text" id="laboritemname-{{$data->id}}" oninput="changelaboritemname({{$data->id}})" value="{{$data?->item_name}}"/>
                                    @elseif($data->category_type =='contractors')
                                        <input type="text" id="contractoritemname-{{$data->id}}" oninput="changecontractoritemname({{$data->id}})" value="{{$data?->item_name}}"/>
                                    @else
                                        {{$data->item_name}}
                                    @endif
                                </label>
                                <input type="text" style="display: none;" class="{{$clid}}-{{$data->id}}" value="{{$data->id}}">
                            </td>

                            @if($data->category_type == 'hard_materials')
                                <td style="width: 5% !important;">
                                    <div class="d-flex">
                                        <span class="getDetailsOfItem" data-id="{{ $data->id }}" style="cursor:pointer;"> <img style="width: 24px; height: 24px; margin-right: 4px;" src="{{ asset('asset/assets/images/icons8-cube-64.png') }}" alt=""> </span>
                                    </div>
                                </td>
                            @else
                                <td></td>
                            @endif
                            <td class="border" colspan="1" style="width: 12%;"><input
                                    type="number"
                                    oninput="changeQuantityVal2({{$data->id}}, '{{$data->category_type}}', '{{$data->uom}}', '{{$lb}}', '{{$quantityequ}}', '{{$grossmarginequ}}', '{{$unitcostsequ}}', '{{$totalcostsequip}}', '{{$totalpriceequipmn}}', '{{$data->labor_type}}')"
                                    value="{{$data->quantity}}" min="1"
                                    style="width: 100%;"
                                    id="{{$quantityequ}}-{{$data->id}}" class="{{$TJQ}}"
                                    placeholder="01"/></td>
                            <td class="border" colspan="1" style="width: 9% !important">{{$data->uom}}</td>
                            <td class="border" style="width: 9% !important" id="" colspan="1">
                                @if($data->category_type == 'contractors')
                                    <div style="position: relative; display: inline-block;">
                                        <span style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;">$</span>
                                        <input type="number" oninput="changeQuantityVal2({{$data->id}}, '{{$data->category_type}}', '{{$data->uom}}', '{{$lb}}', '{{$quantityequ}}', '{{$grossmarginequ}}', '{{$unitcostsequ}}', '{{$totalcostsequip}}', '{{$totalpriceequipmn}}')"
                                               value="{{$data->unit_cost}}" min="1"
                                               style="width: 100%;" id="{{$unitcostsequ}}-{{$data->id}}" placeholder="01"/>
                                    </div>
                                @else
                                    <label id="{{$unitcostsequ}}-{{$data->id}}">${{$data->unit_cost}}</label>
                                @endif
                            </td>
                            <td class="border {{$tcostcls}}" style="width: 9% !important" id="{{$tclass}}-{{$data->id}}"
                                colspan="1">${{$data->total_cost}}</td>
                            <td class="border " style="width: 12% !important" colspan="1">
                                <input type="text" style="display: none" id="sumofmarginequ-{{ $data->id }}" value="" class="sumofmargin"/>
                                <input class="default-margin-input" type="hidden" oninput="changeQuantityVal2({{ $data->id }}, '{{ $data->category_type }}', '{{ $data->uom }}', '{{ $lb }}', '{{ $quantityequ }}', '{{ $grossmarginequ }}', '{{ $unitcostsequ }}', '{{$totalcostsequip}}', '{{$totalpriceequipmn}}')"
                                       id="{{ $grossmarginequ }}-{{ $data->id }}" style="width: 100% !important" value="{{ $data->gross_margin ?? $margin->default }}" placeholder="01"
                                       oninput="if (this.value < {{ $margin->minimum }}) this.value = {{ $margin->minimum }};"/>
                                <span class="margin-default-value" data-id="{{ $data->id }}" id="marginDefaultValue-{{ $data->id }}">{{ $data->gross_margin ?? $margin->default }}</span>
                            </td>
                            <td class="border {{$tpclass}}" style="width: 9% !important" id="{{$totalpriceequipmn}}-{{$data->id}}" colspan="1">
                                ${{$data->total_price}}
                            </td>
                            <td class="border" style="width: 9% !important" id="{{$upida}}-{{$data->id}}">
                                ${{ $data->unit_price }}
                            </td>
                            <td class="border"
                                style="padding-top: 12px; text-align: center;">
                                <a href="javascript:void(0)"
                                   onclick="deletedatarowsequ2({{$data->id}}, '{{$tpclass}}', '{{$TJQ}}', '{{$data->total_price}}', '{{$data->category_type}}')">
                                    <img src="{{ asset('asset/assets/images/trash-icon.svg') }}" alt="trash icon">
                                </a>
                            </td>
                        </tr>
                    @endforeach

                    </tbody>
                </table>
            </td>
        </tr>
    @endforeach

    @foreach($gtems as $data)
        @php
            if($data->category_type == 'equipment'){
                $totalEquipmentAmount += $data->total_price;
            }else if ($data->category_type == 'labors'){
                $totalLaborHours += $data->quantity;
                $totalLaborAmount += $data->total_price;
            }else if($data->category_type == 'plant_materials' || $data->category_type == 'hard_materials') {
                if($data->category_type == 'hard_materials'){
                    $material = \App\Models\HardMaterial::find($data->item_id);
                    $laborPerUnit = $material?->labor ?? 0;
                }else {
                    $material = \App\Models\PlantMaterial::find($data->item_id);
                    $laborPerUnit = $material?->install ?? 0;
                }
                $totalMaterialAmount += $data->total_price;
                $totalLaborHours += $data->quantity * $laborPerUnit;
            }else if($data->category_type == 'other_costs') {
                $totalOtherAmount += $data->total_price;
            }else if($data->category_type == 'contractors') {
                $totalSubcontractor += $data->total_price;
            }
                $lb='';
                $TJQ = '';
                if($data->category_type == 'equipment'){
                    $m="Equipment's";
                    $clid='getitemsequipid2';

                    $totalcostsequip="totalcostsequip2";
                    $unitcostsequ="unitcostsequ2";
                    $grossmarginequ="grossmarginequ2";
                    $quantityequ="quantityequ2";
                    $totalpriceequipmn="totalpriceequi2";
                }else if($data->category_type == 'hard_materials'){
                    $m="Hard Material";
                    $clid='getitemshardid2';

                    $totalcostsequip="totalcostshard2";
                    $unitcostsequ="unitcostshar2";
                    $grossmarginequ="grossmarginhar2";
                    $quantityequ="quantityhar2";
                    $totalpriceequipmn="totalpricehardm2";
                }else if($data->category_type == 'plant_materials'){
                    $m="Plant Material";
                    $clid='getitemsplantid2';

                    $totalcostsequip="totalcostsplant2";
                    $unitcostsequ="unitcostspla2";
                    $grossmarginequ="grossmarginpla2";
                    $quantityequ="quantitypla2";
                    $totalpriceequipmn="totalpriceplantm2";
                }else if($data->category_type == 'other_costs'){
                    $m="Other Job Costs";
                    $clid='getitemsotherid2';

                    $totalcostsequip="totalcostscosts2";
                    $unitcostsequ="unitcostscos2";
                    $grossmarginequ="grossmargincos2";
                    $quantityequ="quantitycos2";
                    $totalpriceequipmn="totalpriceothercost2";
                }else if($data->category_type == 'contractors'){
                    $m="Contractor";
                    $clid='getitemscontractorid2';

                    $totalcostsequip="totalcostscontr2";
                    $unitcostsequ="unitcostscont2";
                    $grossmarginequ="grossmargincont2";
                    $quantityequ="quantitycontss2";
                    $totalpriceequipmn="totalpricecontractorm2";
                }else if($data->category_type == 'labors'){
                    $m="Labor";
                    $clid='getitemsid2';

                    $totalcostsequip="totalcostslabor2";
                    $unitcostsequ="unitcostslabor2";
                    $grossmarginequ="grossmarginlabor2";
                    $quantityequ="quantitylabor2";
                    $totalpriceequipmn="totalpricelabor2";

                   $lb2 = DB::table('labors')->where('id', $data->item_id)->first();
                   $lb = $lb2->labor_burden;

                   $TJQ='totalhourquantity';
                }
                $margin = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', $m)->first();

                $upid="";
                if ($data->category_type =='equipment') {
                    $mclass='datarowsequ2';
                    $tclass="totalcostsequip2";
                    $qtyclass="quantityequ2";
                   $ucclass="unitcostsequ2";
                   $gmclass="grossmarginequ2";
                   $tpclass="equipmenttotalprice";

                   $tcostcls='equipmentcount';

                }else if ($data->category_type =='labors') {
                    $mclass='datarowsLabor2';
                    $tclass="totalcostslabo2";
                    $qtyclass="quantitylabo2";
                    $ucclass="unitcostslabo2";
                    $gmclass="grossmarginlabo2";
                    $tpclass="labortotalprice";
                    $tcostcls='laborcount';
                }else if ($data->category_type =='hard_materials') {
                    $mclass='datarowshard2';
                    $tclass="totalcostshard2";
                    $qtyclass="quantityhar2";
                    $ucclass="unitcostshar2";
                    $gmclass="grossmarginhar2";
                    $tpclass="materialtotalprice";
                    $tcostcls='materialcount';
                }else if ($data->category_type =='plant_materials') {

                    $mclass='datarowsplant2';
                    $tclass="totalcostsplant2";
                    $qtyclass="quantitypla2";
                    $ucclass="unitcostspla2";
                    $gmclass="grossmarginpla2";
                    $tpclass="materialtotalprice";
                    $tcostcls='materialcount';
                }else if ($data->category_type =='other_costs') {
                    $mclass='datarowsother2';
                    $tclass="totalcostscosts2";
                    $qtyclass="quantitycos2";
                    $ucclass="unitcostscos2";
                    $gmclass="grossmargincos2";
                    $tpclass="othertotalprice";
                    $tcostcls='othercostcount';

                }else if ($data->category_type =='contractors') {
                    $mclass='datarowsContractor2';
                    $tclass="totalcostscontr2";
                    $qtyclass="quantitycontss2";
                    $ucclass="unitcostscont2";
                    $gmclass="grossmargincont2";
                    $tpclass="contractortotalprice";
                    $tcostcls='subcontcount';
                    $upid="upidss";
                }
        @endphp
        <tr class="border datarowsequ-{{$data->id}}" data-item-id="{{$data->id}}">
            <td class="drag-handle" style="width: 4%">
                <img src="{{ asset('asset/assets/images/drag-drop-icon.svg') }}" alt="drag and drop icon">
            </td>
            <td class="border" style="width: 20% !important;">
                <label for="">
                    @if ($data->category_type =='labors')
                        <input class="items-input" type="text" id="laboritemname-{{$data->id}}"
                               oninput="changelaboritemname({{$data->id}})"
                               value="{{$data?->item_name}}"/>
                    @elseif($data->category_type =='contractors')
                        <input type="text" id="contractoritemname-{{$data->id}}" class="items-input"
                               oninput="changecontractoritemname({{$data->id}})"
                               value="{{$data?->item_name}}"/>
                    @else
                        <span id="item-name-{{$data->id}}">{{$data->item_name}}</span>
                        <input type="hidden" id="depth-{{$data->id}}" placeholder="Depth" min="1"
                               oninput="changeDepth({{$data->id}}, '{{$data->uom}}')">
                        <input type="hidden" id="calculatedQty-{{$data->id}}">

                        <input type="hidden" id="sqft-{{$data->id}}" placeholder="Sqft"
                               oninput="changeDepth({{$data->id}},'{{$data->uom}}')">
                    @endif
                </label>
                <input type="text" style="display: none;"
                       class="{{$clid}}-{{$data->id}}" value="{{$data->id}}">
            </td>
            @if($data->category_type == 'hard_materials')
                <td>
                    <div class="d-flex">
                        <span class="getDetailsOfItem" data-id="{{ $data->id }}" style="cursor:pointer;"> <img style="width: 24px; height: 24px; margin-right: 4px;" src="{{ asset('asset/assets/images/icons8-cube-64.png') }}" alt=""> </span>
                        {{--                                                        <span style="margin-right: 8px; font-size: 14px; font-weight: 500;">{{ calculateDepthSqFt($data?->depth ?? 0, $data->sqft ?? 0, $data->uom == 'Bag', $data->quantity ?? 0) }}</span>--}}
                        {{--<span class="getDetailsOfItem" data-id="{{ $data->id }}" style="cursor:pointer;"><svg class="" width="15" height="15" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1.87604 17.1156C1.92198 16.7021 1.94496 16.4954 2.00751 16.3022C2.06301 16.1307 2.14143 15.9676 2.24064 15.8171C2.35246 15.6475 2.49955 15.5005 2.79373 15.2063L16 2C17.1046 0.895427 18.8955 0.895428 20 2C21.1046 3.10457 21.1046 4.89543 20 6L6.79373 19.2063C6.49955 19.5005 6.35245 19.6475 6.18289 19.7594C6.03245 19.8586 5.86929 19.937 5.69785 19.9925C5.5046 20.055 5.29786 20.078 4.88437 20.124L1.5 20.5L1.87604 17.1156Z" stroke="#7C8091" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg></span>--}}
                    </div>
                </td>
            @else
                <td></td>
            @endif
            <td class="border" colspan="1" style="width: 12%;">
                <input type="number"
                       oninput="changeQuantityVal2({{$data->id}}, '{{$data->category_type}}', '{{$data->uom}}', '{{$lb}}', '{{$quantityequ}}', '{{$grossmarginequ}}', '{{$unitcostsequ}}', '{{$totalcostsequip}}', '{{$totalpriceequipmn}}')"
                       value="{{$data->quantity}}"
                       min="1"
                       style="width: 100%;"
                       id="{{$quantityequ}}-{{$data->id}}"
                       placeholder="01"/>
            </td>
            <td class="border" colspan="1"
                style="width: 9% !important">{{$data->uom}}</td>
            <td class="border" style="width: 9% !important" colspan="1">
                @if($data->category_type == 'contractors')
                    <div style="position: relative; display: inline-block;">
                        <span style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;">$</span>
                        <input type="number"
                               oninput="changeQuantityVal2({{$data->id}}, '{{$data->category_type}}', '{{$data->uom}}', '{{$lb}}', '{{$quantityequ}}', '{{$grossmarginequ}}', '{{$unitcostsequ}}', '{{$totalcostsequip}}', '{{$totalpriceequipmn}}')"
                               value="{{$data->unit_cost}}" min="1" style="width: 100%;"
                               id="{{$unitcostsequ}}-{{$data->id}}" placeholder="01"/>
                    </div>

                @else

                    <label id="{{$unitcostsequ}}-{{$data->id}}">${{$data->unit_cost}}</label>
                @endif
            </td>
            <td class="border {{$tcostcls}}" style="width: 9% !important" id="{{$totalcostsequip}}-{{$data->id}}" colspan="1">
                ${{$data->total_cost}}
            </td>

            <td class="border " style="width: 12% !important" colspan="1">
                <input type="text" style="display: none" id="sumofmarginequ-{{$data->id}}" value="" class="sumofmargin"/>
                <input class="gross-margin-input-{{ $data->id }}" type="hidden"
                       oninput="changeQuantityVal2({{$data->id}}, '{{$data->category_type}}', '{{$data->uom}}', '{{$lb}}', '{{$quantityequ}}', '{{$grossmarginequ}}', '{{$unitcostsequ}}', '{{$totalcostsequip}}', '{{$totalpriceequipmn}}')"
                       id="{{ $grossmarginequ }}-{{$data->id}}" style="width: 100% !important" value="{{ $data->gross_margin ?? $margin->default }}"
                       placeholder="01" oninput="if (this.value < {{$margin->minimum}}) this.value = {{ $margin->minimum }};"/>
                <span class="margin-default-value" data-id="{{ $data->id }}" id="marginDefaultValue-{{ $data->id }}">{{ $data->gross_margin ?? $margin->default }}</span>
            </td>
            <td class="border {{$tpclass}}" style="width: 9% !important" id="{{$totalpriceequipmn}}-{{$data->id}}" colspan="1">
                ${{$data->total_price}}
            </td>
            <td class="border" style="width: 9% !important" id="{{$upid}}-{{$data->id}}">
                ${{$data->unit_price}}
            </td>

            <td class="border" style="padding-top: 12px; text-align: center;">
                <a href="javascript:void(0)"
                   onclick="deletedatarowsequ2({{$data->id}}, '{{$tpclass}}', '{{$TJQ}}', '{{$data->total_price}}', '{{$data->category_type}}')">
                    <img src="{{ asset('asset/assets/images/trash-icon.svg') }}" alt="trash icon">
                </a>
            </td>
        </tr>
    @endforeach
    <tr>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <tr id="section-initial" class="collapse tavy tablerows">
        <td style="padding: 0px !important" colspan="10">
            <table class="table sortable-content">
                <tbody class="sortable-content accord-bodys"></tbody>
            </table>
        </td>
    </tr>
    </tbody>
</table>
