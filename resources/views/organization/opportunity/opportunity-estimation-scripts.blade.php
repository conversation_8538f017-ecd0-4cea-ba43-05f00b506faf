<script>
    $(document).ready(function () {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // add items to favorite
        $(document).on('click','.addItemToFavorite',function () {
            const itemId = $(this).attr('data-item-id');
            const itemType = $(this).attr('data-item-type');
            addItemToFavourite(itemId,itemType,$(this));
        });

        //remove items from favorite
        $(document).on('click','.removeItemFromFavorite',function () {
            const itemId = $(this).attr('data-item-id');
            const itemType = $(this).attr('data-item-type');
            deleteItemToFavourite(itemId,itemType,$(this));
        });

        // show hard materials section & make it active
        $(document).on('click','.hardbtnactive',function () {
            $(this).addClass('hard_active').removeClass('remove_active');
            $('.plantbtnactive').removeClass('hard_active').addClass('remove_active');
            $('#plantMaterials').addClass('d-none');
            $('#hardMaterials').removeClass('d-none');
        });

        // show plant materials section & make it active
        $(document).on('click','.plantbtnactive',function () {
            $(this).addClass('hard_active').removeClass('remove_active');
            $('.hardbtnactive').removeClass('hard_active').addClass('remove_active');
            $('#plantMaterials').removeClass('d-none');
            $('#hardMaterials').addClass('d-none');
        });

        // add new section to estimation
        $(document).on('click','.addNewSection',function () {
            addSection();
        });
    })
    function addItemToFavourite(id, type) {
        let data = {
            id: id,
            type: type
        }
        const url = "{{ URL::route(getRouteAlias() . '.division.addFavItem') }}";
        $.ajax({
            method: "get",
            url: url,
            data: data,
            success: function (response) {
                if (response) {
                    $(`#${type}FavoriteItem-${id}`).addClass('d-none');
                    $(`#${type}NotFavoriteItem-${id}`).removeClass('d-none');
                }
            },
            error: function (error) {
                console.error(error)
            }
        });
    }

    function deleteItemToFavourite(id, type) {
        let data = {
            id: id,
            type: type
        }
        const url = "{{ URL::route(getRouteAlias() . '.division.deleteFavItem') }}";
        $.ajax({
            method: "get",
            url: url,
            data: data,
            success: function (response) {
                if(response) {
                    $(`#${type}FavoriteItem-${id}`).removeClass('d-none');
                    $(`#${type}NotFavoriteItem-${id}`).addClass('d-none');
                }
            },
            error: function (error) {

            }
        });
    }

    function searchItems() {
        const search = $('.serchinputss').val()?.trim();
        console.info(search);

        const $accordion = $('#accordion-container');
        const $itemsContainer = $('.itemsContainerss');
        const $resultsContainer = $('#itemsContainer');

        // If search is empty, show accordion and hide items container
        if (!search) {
            $accordion.show();
            $itemsContainer.hide();
            return;
        }

        // Hide accordion initially, show items container later if needed
        $accordion.hide();

        $.ajax({
            type: "GET",
            url: "{{ route('organization.estimation.search-item') }}",
            data: { search },
            success: function (response) {
                let allItems = '';

                const functionMap = {
                    equipment: 'addItemTo',
                    labors: 'addItemLabor',
                    hard_materials: 'addItemhardM',
                    plant_materials: 'addItemhardP',
                    other_costs: 'addItemCost',
                    sub_contractors: 'addItemContractor'
                };

                // Iterate over response categories
                $.each(response, function (category, items) {
                    const functionName = functionMap[category];

                    if (!functionName) {
                        console.error(`Unknown category: ${category}`);
                        return;
                    }

                    // Iterate over items in each category
                    $.each(items, function (_, item) {
                        let isFavorite = `<img id="${category}FavoriteItem-${item.id}"
                                 class="${item.is_favorite == 1 ? 'd-none' : ''} cursor-pointer addItemToFavorite"
                                 data-item-id="${item.id}"
                                 data-item-type="${category}"
                                 src="/asset/assets/images/not-favorite.svg"
                                 alt="non favorite item icon">
                            <img id="${category}NotFavoriteItem-${item.id}"
                                 class="${item.is_favorite == 0 ? 'd-none' : ''} cursor-pointer removeItemFromFavorite"
                                 data-item-id="${item.id}"
                                 data-item-type="${category}"
                                 src="/asset/assets/images/favorite.svg"
                                 alt="favorite item icon">
                        `;
                        allItems += `
                        <li class="menu-accordian mt-4">
                            <div class="row">
                                <div class="col-1" style="margin-top: 2px !important;">${isFavorite}</div>
                                <div class="col-7 text-left">
                                    <span>${item.name}</span>
                                </div>
                                <div id="${item.id}" class="col-3 d-flex justify-content-end">
                                    <button id="${category}plusbtn-${item.id}" onclick="${functionName}(${item.id})"
                                        class="btn btn-success btn-sm rounded-circle font-weight-bolder"
                                        style="width: 24px; height: 24px; font-size: 11px;padding: 3px 0;">
                                        <i class="fa-solid fa-plus text-light"></i>
                                    </button>
                                </div>
                            </div>
                        </li>`;
                    });
                });

                $resultsContainer.empty().append(allItems);
                $itemsContainer.show();
            },
            error: function (xhr, status, error) {
                console.error("Search failed:", error);
            }
        });
    }

    // Define a reusable function to generate favorite SVG
    function generateFavoriteImageHTML(id, type, isFavorite) {
        const notFavClass = isFavorite == 1 ? 'd-none' : '';
        const favClass = isFavorite == 0 ? 'd-none' : '';

        return `
                        <img id="${type}FavoriteItem-${id}"
                             class="cursor-pointer addItemToFavorite ${notFavClass}"
                             data-item-id="${id}" data-item-type="${type}"
                             src="/asset/assets/images/not-favorite.svg"
                             alt="non favorite item icon">

                        <img id="${type}NotFavoriteItem-${id}"
                             class="cursor-pointer removeItemFromFavorite ${favClass}"
                             data-item-id="${id}" data-item-type="${type}"
                             src="/asset/assets/images/favorite.svg"
                             alt="favorite item icon">
                    `;
    }

    function getAllMaterialsListing() {
        const material = document.getElementById('getDivisions')?.value;
        const url = "{{ URL::route(getRouteAlias() . '.division.getdata') }}";

        $.ajax({
            method: "GET",
            url: url,
            data: { material },
            success: function (response) {
                console.log(response.data);

                // Toggle buttons
                $('.allbtn').addClass('samebtn').removeClass('favtbtn');
                $('.difbtn').removeClass('samebtn').addClass('favtbtn');

                // Reusable function to render list items
                const renderList = (items, type, buttonHandler, targetSelector) => {
                    let html = '';
                    items.forEach(item => {
                        html += `
                        <li class="menu-accordian mt-4">
                            <div class="row">
                                <div class="col-1">
                                    ${generateFavoriteImageHTML(item.id, type, item.is_favorite)}
                                </div>
                                <div class="col-7 text-left">
                                    <span>${item.name}</span>
                                </div>
                                <div id="${item.id}" class="col-3 d-flex justify-content-end">
                                    <button id="${type}plusbtn-${item.id}" onclick="${buttonHandler}(${item.id})"
                                            class="btn btn-success btn-sm rounded-circle"
                                            style="width: 24px; height: 24px; font-size: 17px;">
                                        +
                                    </button>
                                </div>
                            </div>
                        </li>`;
                    });
                    $(targetSelector).empty().append(html);
                };

                // Render each category
                renderList(response.equipment, 'equipment', 'addItemTo', '#equipmentdata');
                renderList(response.hard_materials, 'hard_materials', 'addItemhardM', '#hardmaterials');
                renderList(response.plant_materials, 'plant_materials', 'addItemhardP', '#plantmaterials');
                renderList(response.other_job_costs, 'other_costs', 'addItemCost', '#othercost');
                renderList(response.labors, 'labors', 'addItemLabor', '#laborMenu');
                renderList(response.sub_contractor, 'contractors', 'addItemContractor', '#sub_contractor_list');
            },
            error: function (response) {
                console.error("Error fetching material data", response);
            }
        });
    }

    function getFavoriteItemsListing() {
        var material = document.getElementById('getDivisions').value;
        var url = "{{ URL::route(getRouteAlias() . '.division.getdataFavItem') }}";

        $.ajax({
            method: "get",
            url: url,
            data: { material },
            success: function (response) {
                $('.allbtn').removeClass('samebtn').addClass('favtbtn');
                $('.difbtn').removeClass('favtbtn').addClass('samebtn');

                // Mapping for containers and functions
                const mappings = [
                    { key: 'equipment', btnFunc: 'addItemTo', container: '#equipmentdata' },
                    { key: 'hard_materials', btnFunc: 'addItemhardM', container: '#hardmaterials' },
                    { key: 'plant_materials', btnFunc: 'addItemhardP', container: '#plantmaterials' },
                    { key: 'other_job_costs', btnFunc: 'addItemCost', container: '#othercost' },
                    { key: 'labors', btnFunc: 'addItemLabor', container: '#laborMenu' },
                    { key: 'sub_contractor', btnFunc: 'addItemContractor', container: '#sub_contractor_list', type: 'contractors' },
                ];

                mappings.forEach(({ key, btnFunc, container, type }) => {
                    let html = '';
                    $.each(response[key], function (index, item) {
                        let typeKey = type || key;
                        let btnId = `${typeKey.replace(/_/g, '')}plusbtn-${item.id}`;
                        let button = `
                        <button id="${btnId}" onclick="${btnFunc}(${item.id})" style="
                            width: 24px;
                            height: 24px;
                            border-radius: 50%;
                            text-align: center;
                            margin-left: auto;
                            color: white;
                            border: none;
                            font-size: 18px;
                            background-color: #2FCC40;">
                            <label><b style="color: white; font-size: 17px;">+</b></label>
                        </button>
                    `;

                        html += generateListItemHTML(item, typeKey, button);
                    });

                    $(container).empty().append(html);
                });
            },
            error: function (response) {
                console.error("Failed to fetch favorite items.");
            }
        });
    }

    function generateListItemHTML(item, type, buttonHTML) {
        return `
        <li class="menu-accordian mt-4" id="remove${type}li-${item.id}">
            <div class="row">
                <div class="col-1" style="margin-top: 2px !important;">
                    ${generateFavoriteImageHTML(item.id, type, item.is_favorite)}
                </div>
                <div class="col-7 text-left">
                    <span>${item.name}</span>
                </div>
                <div id="${item.id}" class="col-3 text-end" style="padding-right: 0px;">
                    ${buttonHTML}
                </div>
            </div>
        </li>`;
    }

    function randomUniqueNumber(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // Example usage for adding a section
    function addSection() {
        let uniqueNo = randomUniqueNumber(1000, 9999);
        saveSection(uniqueNo, false); // Pass false for adding a section
    }

    function saveSection(uni, isEdit = false) {
        const opportunityId = {{$opportunity->id}};
        let sectionName = $('#editinput-' + uni).val()?.trim() || "Section";
        const sectionId = isEdit ? $('#section-id-' + uni).val() : null;

        window.sectionId = sectionId;

        $.ajax({
            url: "{{ route('organization.sections.save') }}",
            type: 'POST',
            data: {
                opportunity_id: opportunityId,
                section_name: sectionName,
                section_id: sectionId,
                _token: '{{ csrf_token() }}'
            },
            success: function (response) {
                const newUni = response.section_id;
                window.sectionId = newUni;

                // UI Updates
                $(".editinput, .okbutn, .sectionNames, .editbutn").hide();
                $('#editinput-' + newUni).show();
                $('#okbutn-' + newUni).show();
                $('#section1Heading-' + newUni).hide();
                $('#editbutn-' + newUni).hide();
                $(".sectionNames").show();
                $(".editbutn").show();

                // Append new row
                const sectionRowHtml = `
                <tr class="tablerows-${newUni} lastforsectionrow" style="background: #f5faff">
                    <td>
                        <div class="d-flex align-items-center">
                            <input type="checkbox" class="dynamic-checkbox" id="checkbox-${newUni}" data-id="${newUni}" style="cursor:pointer;">
                        </div>
                    </td>
                    <td class="align-middle">
                        <div class="d-flex align-items-center">
                            <div id="section1Heading-${newUni}" class="me-2 sectionNames" style="display: none;">${sectionName}</div>
                            <input type="hidden" class="editinputid" id="editinputid-${newUni}" data-sec-id="${newUni}" value="${newUni}">
                            <input type="text" class="editinput" id="editinput-${newUni}" style="display: inline-block;" placeholder="Enter Section Name">
                            <a href="javascipt:void(0)" onclick="editSection2(${newUni})" id="editbutn-${newUni}" class="text-decoration-none mt-1 mx-3 editbutn" style="display: none">
                                <!-- SVG Icon -->
                            </a>
                            <a href="javascipt:void(0)" onclick="updateSection3(${newUni})" id="okbutn-${newUni}" class="btn btn-sm btn-primary text-decoration-none mt-1 mx-3 okbutn" style="display: inline-block;">
                                Save
                            </a>
                        </div>
                    </td>
                    <td colspan="9"></td>
                    <td class="d-flex" style="gap: 15px">
                        <div class="dropdown">
                            <button class="btn btn-sm bg-transparent dropdown-toggle" type="button" data-toggle="dropdown" aria-expanded="false">
                                <!-- Dots SVG -->
                            </button>
                            <ul class="dropdown-menu">
                                <li><a onclick="editSection2(${newUni})" class="dropdown-item" href="#">Edit</a></li>
                                <li><a onclick="deleteSection3(${newUni})" class="dropdown-item" href="#">Delete</a></li>
                            </ul>
                        </div>
                        <button class="btn btn-sm collapsed" id="mainbtnnow-${newUni}" style="background: #f6f6f6; border: 1px solid #dfdfdf;" type="button" data-toggle="collapse" data-target="#section-${newUni}" aria-expanded="false" onclick="toggleAccordion2(${newUni})">
                            <label class="changeiconall changeicon-${newUni}">
                                <!-- Chevron SVG -->
                            </label>
                        </button>
                    </td>
                </tr>
                <tr id="section-${newUni}" class="collapse tavy tablerows-${newUni}">
                    <td colspan="10" style="padding: 0px !important;">
                        <table class="table">
                            <tbody class="sortable-content accord-bodys accord-bodys-${newUni}"></tbody>
                        </table>
                    </td>
                </tr>`;
                $('.maintable').prepend(sectionRowHtml);
            },
            error: function (xhr) {
                alert(xhr.responseJSON?.message || "An error occurred.");
            }
        });
    }


</script>
