@extends('layouts.admin.master')
@section('title', 'Snow Opportunity Estimation')
@section('styles')
    <link rel="stylesheet" href="{{ asset('asset/assets/css/organization/Opportunity/snow-opportunity-estimation.css') }}">
@endsection

@section('section')

<div class="dashboard_main">

    <div class="row justify-content-around">

    <div class="container" data-opportunity-id="{{ $opportunity->id }}">
        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <button class="tab-btn active" data-filter="all">All</button>
                <button class="tab-btn" data-filter="favorites">Favorite</button>
            </div>

            <!-- Search Bar -->
            <div class="search-container">
                <input type="text" placeholder="Search" class="search-input">
                <i class="fas fa-search search-icon"></i>
            </div>

            <!-- T&M Rates -->
            <div class="rates-section">
                <div class="rates-header">
                    <i class="fas fa-cog"></i>
                    <span>Snow T&M Rates</span>
                    <i class="fas fa-plus-circle add-icon"></i>
                </div>
            </div>


            <!-- Equipment Section -->
            <div class="sidebar-section">
                <div class="section-header" data-section="equipment" aria-expanded="true">
                    <i class="fas fa-chevron-right chevron"></i>
                    <span>Equipment</span>
                </div>
                <div class="section-content" id="equipment-content">
                    @if(!empty($equipments))
                        @foreach($equipments as $item)
                            <div class="item" data-category="{{ \App\Models\SnowSetup::getCategoryDisplayName(\App\Models\SnowSetup::EQUIPMENT_CATEGORY) }}" data-unit-cost="{{ $item->unit_cost }}" data-uom="{{ $item->uom }}" data-item-name="{{ $item->lineitem }}">
                                <i class="far fa-heart heart-icon" data-item="{{ $item->id }}"></i>
                                <span>{{ $item->lineitem }} , per {{ $item->uom }}</span>
                                <i class="fas fa-plus-circle item-icon"></i>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>

            <!-- Labor Section -->
            <div class="sidebar-section">
                <div class="section-header" data-section="labor" aria-expanded="false">
                    <i class="fas fa-chevron-right chevron"></i>
                    <span>Labor</span>
                </div>
                <div class="section-content collapsed" id="labor-content">
                    @if(!empty($labor))
                        @foreach($labor as $item)
                            <div class="item" data-category="{{ \App\Models\SnowSetup::getCategoryDisplayName(\App\Models\SnowSetup::LABOR_CATEGORY) }}" data-unit-cost="{{ $item->unit_cost }}" data-uom="{{ $item->uom }}" data-item-name="{{ $item->lineitem }}">
                                <i class="far fa-heart heart-icon" data-item="{{ $item->id }}"></i>
                                <span>{{ $item->lineitem }} , per {{ $item->uom }}</span>
                                <i class="fas fa-plus-circle item-icon"></i>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>

            <!-- Materials Section -->
            <div class="sidebar-section">
                <div class="section-header" data-section="materials" aria-expanded="false">
                    <i class="fas fa-chevron-right chevron"></i>
                    <span>Materials</span>
                </div>
                <div class="section-content collapsed" id="materials-content">
                    @if(!empty($material))
                        @foreach($material as $item)
                            <div class="item" data-category="{{ \App\Models\SnowSetup::getCategoryDisplayName(\App\Models\SnowSetup::MATERIAL_CATEGORY) }}" data-unit-cost="{{ $item->unit_cost }}" data-uom="{{ $item->uom }}" data-item-name="{{ $item->lineitem }}">
                                <i class="far fa-heart heart-icon" data-item="{{ $item->id }}"></i>
                                <span>{{ $item->lineitem }} , per {{ $item->uom }}</span>
                                <i class="fas fa-plus-circle item-icon"></i>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>

            <!-- Stand By Section -->
            <div class="sidebar-section">
                <div class="section-header" data-section="standby" aria-expanded="false">
                    <i class="fas fa-chevron-right chevron"></i>
                    <span>Stand By</span>
                </div>
                <div class="section-content collapsed" id="standby-content">
                    @if(!empty($standby))
                        @foreach($standby as $item)
                            <div class="item" data-category="{{ \App\Models\SnowSetup::getCategoryDisplayName(\App\Models\SnowSetup::STAND_BY_CATEGORY) }}" data-unit-cost="{{ $item->unit_cost }}" data-uom="{{ $item->uom }}" data-item-name="{{ $item->lineitem }}">
                                <i class="far fa-heart heart-icon" data-item="{{ $item->id }}"></i>
                                <span>{{ $item->lineitem }} , per {{ $item->uom }}</span>
                                <i class="fas fa-plus-circle item-icon"></i>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">

            <!-- Account Details -->
            <div class="account-details">
                <div class="opportunity-info">
                    <h1>{{ $opportunity->opportunity_name }}</h1>
                    <span class="opportunity-id">Oppsss#{{ $opportunity->opportunity_count }}</span>
                    <i class="fas fa-chevron-down fa-chevron-down1"></i>
                </div>
                <div class="account-details-header">
                <div class="detail-row">
                    <div class="detail-item num1">
                        <span class="label">Account</span>
                        {{ $opportunity->account->company_name }}</p>
                    </div>
                    <div class="detail-item">
                        <span class="label">Account Owner</span>
                        <span class="value">{{ $opportunity->account_owner_name }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">Property name</span>
                        <span class="value link">{{ $opportunity->propertyInformation->name }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">Opportunity Type</span>
                        <span class="value">{{ $opportunity->opportunity_type ?? 'N/A' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">Division</span>
                        <span class="value">{{ $opportunity->division->name ?? 'N/A' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">Service Line</span>
                        <span class="value">{{ $opportunity->serviceLine->name ?? 'N/A' }}</span>
                    </div>
                    <div class="detail-item num2">
                        <span class="label">Estimator Name</span>
                        <span class="value">{{ $opportunity->estimator_name ?? 'N/A' }}</span>
                    </div>
                </div>
                </div>
            </div>
             {{-- estimate header --}}
             <div class="estimate-header">
                <h2>Estimate</h2>
                <div class="action-buttons">
                    <button class="btn btn-outline" id="download-btn">
                        <i class="fas fa-download" style="color:rgb(21, 150, 255)"></i>
                        Download
                    </button>
                    <button style="background-color: #E6F1FB; border: 1px solid #E6F1FB" class="btn btn-outline" id="preview-btn">Preview</button>

                    <button class="btn btn-primary" id="send-client-btn">
                        <i class="fas fa-paper-plane"  style="color:aliceblue" ></i>
                        Send to client
                    </button>
                </div>
            </div>
            <!-- Estimate Section -->
            <div class="estimate-section">


                <!-- Estimate Table -->
                <div class="estimate-table">
                    <div class="table-header">
                        <div class="col-items colchange">Items</div>
                        <div class="col-quantity">Quantity</div>
                        <div class="col-unit-cost">Unit Cost</div>
                        <div class="col-uom">UoM</div>
                        <div class="col-actions"></div>
                    </div>

                    <div class="table-content">
                        <!-- Dynamic sections will be added here when items are selected -->
                        <div id="dynamic-sections-container">
                            <!-- Category sections will be dynamically created here -->
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    </div>

</div>
@endsection

@section('extra-scripts')
 <!-- Scripts -->
    <script>
        // Make opportunity ID available globally
        window.OPPORTUNITY_ID = {{ $opportunity->id }};
        window.OPPORTUNITY_DATA = {
            id: {{ $opportunity->id }},
            name: "{{ $opportunity->opportunity_name }}",
            count: "{{ $opportunity->opportunity_count }}"
        };
        // URLs for action buttons
        window.PREVIEW_URL = "{{ URL::route(getRouteAlias() . '.organization.preview', ['opportunityId' => encodeId($opportunity->id)]) }}";
        window.SEND_URL = "{{ URL::route(getRouteAlias() . '.organization.previewEstimation', ['opportunityId' => encodeId($opportunity->id)]) }}";
    </script>
    <script src="{{ asset('asset/assets/js/organization/opportunity/snow-estimates.js') }}"></script>

    <script>
        $(document).ready(function() {
            // Initialize Snow Estimation
            console.log('Snow Estimation initialized with opportunity ID:', window.OPPORTUNITY_ID);
        });
    </script>
@endsection


