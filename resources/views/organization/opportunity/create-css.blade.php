<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.0.8/css/fileinput.min.css">
{{--<style>
    /* Default styling for the placeholder-like effect */
    input[type="date"].placeholder-shown,
    input[type="time"].placeholder-shown {
        color: #e8e8e8 !important;
        font-style: italic;
    }
    select.modal-field {
        /* color: #e8e8e8 !important; Placeholder color */
    }

    /* Valid selection color */
    select.validss .select2 .selection .select2-selection .select2-selection__rendered{
        color: red !important;
    }
</style>
<style>
    .panel .field label.error, .panel .field .laravel_error{
        position: relative !important;
        /* top: 98%; */
    }
    .select2-container{
        height: 44px !important;
    }
    /* Add this CSS to your custom styles */
    .select2-results__option--highlighted .acont_own_whole {
        color: white !important; /* Change the text color to white on hover */
    }

    .basic-single-select + .select2 .select2-selection, .custom_selectBox + .select2 .select2-selection {
        background: #ffffff;
        border: 1px solid var(--bordercolor);
        border-top-right-radius: 6px !important;
        border-top-left-radius: 6px !important;
        border-bottom-left-radius: 6px !important;
        border-bottom-right-radius: 6px !important;
        height: 40px;
    }

    /* .select2-container--open .select2-dropdown--below {
        border-top: none;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        margin-top: -7px !important;
    }
     */
    .basic-single-select + .select2 .select2-selection, .custom_selectBox + .select2 .select2-selection {
        /* margin-bottom: -38px !important; */
    }

    .hidemodelbtn {
        font-size: 25px !important;
        color: #7e8a9d !important;
    }
    .hidemodelbtn:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .hidemodelbtn:hover {
        cursor: pointer !important;
    }
    @media screen and (max-width: 580px) {
        .large-modal {
            max-width: 95% !important;
            width: 95% !important;
        }
        .hidemodelbtn {
            font-size: 15px !important;
            color: #7e8a9d !important;
        }
    }

    span {
        font-size: 14px;
        color: #192a3e;
    }
    input::placeholder {
        font-size: 14px;
    }
    input {
        font-size: 14px !important;
    }
    button {
        font-size: 14px !important;
    }
    select {
        font-size: 14px !important;
    }
</style>
<style>

    @import url('https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900&display=swap');

    button:focus,
    input:focus{
        outline: none;
        box-shadow: none;
    }
    a,
    a:hover{
        text-decoration: none;
    }
    .input-group.file-caption-main{
        display: none;
    }
    .close.fileinput-remove{
        display: none;
    }
    .file-drop-zone{
        margin: 0px;
        border: 1px solid #fff;
        background-color: #fff;
        padding: 0px;
        display: contents;
    }
    .file-drop-zone.clickable:hover{
        border-color: #fff;
    }
    .file-drop-zone .file-preview-thumbnails{
        display: inline;
    }
    .file-drop-zone-title{
        padding: 15px;
        height: 120px;
        width: 120px;
        font-size: 12px;
    }
    .file-input-ajax-new{
        display: inline-block;
    }
    /* .file-input{
        display: flex !important;
    } */
    .file-input.theme-fas{
        display: inline-block;
        width: 100%;
    }
    .file-input.theme-fa{
        display: flex;
    }
    .file-preview{
        padding: 0px;
        border: none;
        display: inline;
        width: fit-content !important;

    }
    .file-drop-zone-title{
        display: none;
    }
    .file-footer-caption{
        display: none !important;
    }
    .kv-file-upload{
        display: none;
    }
    .file-upload-indicator{
        display: none;
    }
    .file-drag-handle.drag-handle-init.text-info{
        display: none;
    }
    .krajee-default.file-preview-frame .kv-file-content{
        width: 90px;
        height: 27px;
        display: flex;
        text-align: center;
        align-items: center;
    }
    .krajee-default.file-preview-frame{
        background-color: #fff;
        margin: 3px;
        border-radius: 15px;
        overflow: hidden;
    }
    .krajee-default.file-preview-frame:not(.file-preview-error):hover{
        box-shadow: none;
        border-color: #0074D9;
    }
    .krajee-default.file-preview-frame:not(.file-preview-error):hover .file-preview-image{
        transform: scale(1.1);
    }
    .krajee-default.file-preview-frame{
        box-shadow: none;
        border-color: #fff;
        max-width: 150px;
        margin: 5px;
        padding: 0px;
        transition: 0.5s;
    }
    .file-thumbnail-footer,
    .file-actions{
        width: 100%;
        /* height: 20px !important; */
        position: absolute !important;
        top: 5px;
        right: 0px;
    }
    .fileinput-remove span{
        font-size: 38px !important;
    }
    .file-thumbnail-footer-section{
        background-color: rgba(0,0,0, 0.4);
    }

    .file-thumbnail-footer
    {
        background-color: rgba(0,0,0, 0.4);
    }

    .kv-file-remove:focus,
    .kv-file-remove:active{
        outline: none !important;
        box-shadow: none !important;
    }
    .kv-file-remove{
        border-radius: 50%;
        z-index: 1;
        right: 0;
        position: absolute;
        top: 0;
        text-align: center;
        color: #fff;
        background-color: #0074D9;
        border: 1px solid #0074D9;
        padding: 2px 6px;
        font-size: 11px;
        transition: 0.5s;
    }
    .kv-file-remove:hover{
        border-color: #DCF2FF;
        background-color: #DCF2FF;
        color: #0074D9;
    }
    .kv-preview-data.file-preview-video{
        width: 100% !important;
        height: 100% !important;
    }
    .btn-outline-secondary.focus, .btn-outline-secondary:focus{
        box-shadow: none;
    }
    .btn-toggleheader,
    .btn-fullscreen,
    .btn-borderless{
        display: none;
    }
    .btn-kv.btn-close{
        color: #fff;
        border: none;
        background-color: #0074D9;
        font-size: 11px;
        width: 18px;
        height: 18px;
        text-align: center;
        padding: 0px;
    }
    .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
    .btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
    .show>.btn-outline-secondary.dropdown-toggle:focus{
        background-color: #DCF2FF;
        color: #DCF2FF;
        box-shadow: none;
        color: #0074D9;
    }
    .file-preview-status{
        display: none !important;
    }
    .kv-file-content .file-preview-image{
        width: 90px !important;
        height: 90px !important;
        max-width: 90px !important;
        max-height: 90px !important;
        transition: 0.5s;
    }
    .btn-danger.btn-file:focus{
        box-shadow: none !important;
        outline: none !important;
    }
    .btn-danger.btn-file{
        padding: 0px;
        height: 95px;
        width: 95px;
        display: inline-block;
        margin: 5px;
        /* border-color: #DCF2FF; */
        border: 1px solid #E4E4E4;
        background-color: white;
        color: #0074D9;
        border-radius: 15px;
        padding-top: 30px;
        transition: 0.5s;
    }
    .btn-danger.btn-file:active,
    .btn-danger.btn-file:hover{
        background-color: #DCF2FF;
        color: #0074D9;
        border-color: #DCF2FF;
        box-shadow: none;
    }
    .btn-danger.btn-file i{
        font-size: 30px;
        color: #0074D9 !important;
    }
    textarea{
        font-size: 16px !important;
    }

    @media (max-width: 350px){
        .krajee-default.file-preview-frame:not([data-template=audio]) .kv-file-content{
            width: 90px;
        }
    }
    .btn-danger:not(:disabled):not(.disabled).active, .btn-danger:not(:disabled):not(.disabled):active, .show>.btn-danger.dropdown-toggle {
        color: #0074D9 !important;
        background-color: #DCF2FF !important;
        border-color: #DCF2FF !important;
    }
    /* #multiplefileupload-error{
    display: none !important;
    } */
    .select2-dropdown, .dropdown-menu {
        background: #ffffff;
        box-shadow: 0px 0px 15.125px rgba(52, 84, 207, 0.2) !important;
        border-radius: 8px !important;
        border: none;
        min-width: fit-content !important;
    }
    .preview-file-choose{

        gap: 0px;
        border-radius: 8px;
        border: 1px solid #E4E4E4;
        opacity: 0px;
        text-align: center;
        display: none;
    }
    .dz-started .preview-file-choose{
        display: inline !important;

        gap: 0px;
        border-radius: 8px !important;
        border: 1px solid #E4E4E4  !important;

        text-align: center !important;
    }
    .dz-preview{
        /* display: none !important; */
        min-height: 110px;
        width: 81px;
        /* margin: 0px 0px 0px 12px !important; */

    }
    .dz-started .dz-preview
    {
        display: inline-block !important;
    }
    .dropzone_library_customize{
        /* display: flex; */
    }
    .dz-filename:hover{
        .dz-filename span{
            display: block;
        }
    }

    /* .basic-single-select + .select2 .select2-selection .select2-selection__rendered, .custom_selectBox + .select2 .select2-selection .select2-selection__rendered {
        padding: 8px 32px 8px 16px;
        font-family: "Cabin";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: #A0A3BD !important;
    } */
</style>
<style>
    .table-wrapper {
        border-width: 1px;

        border-style: solid;

        border-color: #E3E8EF;


        border-radius: 10px;
        overflow: hidden; /* Ensures the rounded corners are applied */
    }
    .table-wrapper .table{
        border: none !important;
    }
    .table-wrapper .table thead tr th{
        border: none !important;
    }
    .basic-single-select + .select2 .select2-selection .select2-selection__rendered, .custom_selectBox + .select2 .select2-selection .select2-selection__rendered {
        padding: 8px !important;
    }
</style>--}}

<style>
    /*
  * Optimized CSS for Client Form
  * Consolidated from multiple style blocks
  */

    /* Base Form Styles */
    .panel .field label.error,
    .panel .field .laravel_error {
        position: relative !important;
        color: #dc3545;
        font-size: 12px;
        margin-top: 4px;
    }

    /* Select2 Styling */
    .select2-container {
        height: 44px !important;
    }

    .basic-single-select + .select2 .select2-selection,
    .custom_selectBox + .select2 .select2-selection {
        background: #ffffff;
        border: 1px solid var(--bordercolor);
        border-radius: 6px !important;
        height: 40px;
    }

    .basic-single-select + .select2 .select2-selection .select2-selection__rendered,
    .custom_selectBox + .select2 .select2-selection .select2-selection__rendered {
        padding: 8px !important;
        color: #192a3e !important;
    }

    .select2-results__option--highlighted .acont_own_whole {
        color: white !important;
    }

    .select2-dropdown,
    .dropdown-menu {
        background: #ffffff;
        box-shadow: 0px 0px 15.125px rgba(52, 84, 207, 0.2) !important;
        border-radius: 8px !important;
        border: none;
        min-width: fit-content !important;
    }

    .acont_own {
        color: #A8ABB5 !important;
        font-size: 12px !important;
        margin-left: 5px !important;
    }

    /* Modal Styling */
    .hidemodelbtn {
        font-size: 25px !important;
        color: #7e8a9d !important;
        outline: none !important;
        box-shadow: none !important;
        cursor: pointer !important;
    }

    .modal-header {
        background-color: white;
        padding: 0px 0px !important;
    }

    .modal-body {
        padding-left: 0px;
        padding-right: 0px;
    }

    /* Input and Form Controls */
    input[type="date"].placeholder-shown,
    input[type="time"].placeholder-shown {
        color: #e8e8e8 !important;
        font-style: italic;
    }

    input, button, select, textarea {
        font-size: 14px !important;
    }

    input::placeholder {
        font-size: 14px;
    }

    span {
        font-size: 14px;
        color: #192a3e;
    }

    .placeholder-text {
        color: #98A2B3;
        font-size: 14px;
    }

    textarea {
        font-size: 16px !important;
    }

    /* Property Item Styling */
    .property-item {
        display: flex;
    }

    .property-item label {
        margin-left: 10px !important;
        margin-top: 9px;
    }

    .property-item input[type="radio"] {
        margin-top: 13px;
    }

    /* Table Styling */
    .table-wrapper {
        border: 1px solid #E3E8EF;
        border-radius: 10px;
        overflow: hidden;
    }

    .table-wrapper .table,
    .table-wrapper .table thead tr th {
        border: none !important;
    }

    .table-wrapper .table thead tr {
        border-radius: 8px;
        background: #F2F4F7;
    }

    /* Dropzone File Upload */
    .dropzone_library_customize {
        display: flex;
        flex-wrap: wrap;
    }

    .dz-preview {
        min-height: 110px;
        width: 81px;
        margin: 5px;
        text-align: center;
    }

    .dz-started .dz-preview {
        display: inline-block !important;
    }

    .dz-preview.dz-file-preview.dz-processing.dz-success.dz-complete {
        width: 81px !important;
        height: 110px !important;
        text-align: center;
        cursor: pointer;
    }

    .file-drop-zone {
        margin: 0;
        border: 1px solid #fff;
        background-color: #fff;
        padding: 0;
        display: contents;
    }

    .file-drop-zone.clickable:hover {
        border-color: #fff;
    }

    .dz-details-imagess {
        height: 60px;
        width: 100%;
    }

    .dz-filename {
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 12px;
        margin-top: 5px;
    }

    .btn-danger.btn-file {
        padding: 0px;
        height: 95px;
        width: 95px;
        display: inline-block;
        margin: 5px;
        border: 1px solid #E4E4E4;
        background-color: white;
        color: #0074D9;
        border-radius: 15px;
        padding-top: 30px;
        transition: 0.5s;
    }

    .btn-danger.btn-file:hover,
    .btn-danger.btn-file:active {
        background-color: #DCF2FF;
        color: #0074D9;
        border-color: #DCF2FF;
        box-shadow: none;
    }

    .btn-danger.btn-file i {
        font-size: 30px;
        color: #0074D9 !important;
    }

    .laravel_error {
        color: #dc3545;
        font-size: 12px;
        margin-top: 4px;
    }

    /* Button Styling */
    .btn {
        font-size: 14px !important;
        border-radius: 8px;
        padding: 10px 15px;
    }

    .btn.primaryblue {
        background-color: #0074D9;
        color: white;
    }

    .btn.transparent {
        background-color: transparent;
        color: #0074D9;
        border: 1px solid #0074D9;
    }

    .btn.cancel {
        color: #0074D9;
        background-color: white;
        border: 1px solid #0074D9;
    }

    .add-new {
        cursor: pointer;
    }

    .add-new b {
        color: #0074D9;
        font-size: 14px;
    }

    /* Responsive Design */
    @media screen and (max-width: 580px) {
        .large-modal {
            max-width: 95% !important;
            width: 95% !important;
        }

        .hidemodelbtn {
            font-size: 15px !important;
        }

        .row {
            margin-left: -5px;
            margin-right: -5px;
        }

        [class*="col-"] {
            padding-left: 5px;
            padding-right: 5px;
        }
    }

    @media (max-width: 350px) {
        .krajee-default.file-preview-frame:not([data-template=audio]) .kv-file-content {
            width: 90px;
        }

        .dz-preview {
            width: 70px;
        }
    }
</style>
