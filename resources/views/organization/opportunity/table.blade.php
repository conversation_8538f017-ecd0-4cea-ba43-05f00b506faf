<x-jet.listing-table>
    <x-slot name="header">
        <th>{{ __('Opportunity ID') }}</th>
        <th>{{ __('Opportunity Name') }}</th>
        <th>{{ __('Property Name') }}</th>
        <th>{{ __('Account') }}</th>
        <th>{{ __('Account Owner') }}</th>
        <th>{{ __('Division') }}</th>
        <th>{{ __('Date and Time') }}</th>
        <th>{{ __('Status') }}</th>
        <th>{{ __('Action') }}</th>
    </x-slot>
    <x-slot name="body">
        @forelse($opportunities as $opportunity)
            <tr>
                <td>{{ $opportunity->id }}</td>
                <td>{{ $opportunity->opportunity_name }}</td>
                <td>{{ $opportunity->property_name }}</td>
                <td>{{ $opportunity->account }}</td>
                <td>{{ $opportunity->account_owner }}</td>
                <td>{{ $opportunity->opportunity_type }}</td>
                <td>{{ $opportunity->date_time->format('Y-m-d H:i') }}</td>
                <td>{{ $opportunity->status }}</td>
                <td>
                    <div class="dropdown mx-auto w-fit">
                        <div id="dropdown{{ $opportunity->id }}" data-toggle="dropdown" aria-expanded="false">
                            <img height="24px" width="24px"
                                src="{{ asset('admin_assets/images/icons/vertical-dots.svg') }}" alt="vertical dots">
                        </div>
                        <ul class="dropdown-menu" aria-labelledby="dropdown{{ $opportunity->id }}">
                            <li><a class="dropdown-item"
                                    href="{{ route(getRouteAlias() . '.opportunity.detail', encodeId($opportunity->id)) }}">View
                                    Details</a></li>
                            <li><a class="dropdown-item"
                                    href="{{ route(getRouteAlias() . '.opportunity.edit', encodeId($opportunity->id)) }}">Edit
                                    Details</a></li>
                            <li><a class="dropdown-item"
                                    href="#" onclick="showModal({{ $opportunity->id }})">Delete</a></li>
                        </ul>
                    </div>
                </td>
            </tr>
        @empty
            @include('layouts.partials.no-data')
        @endforelse
    </x-slot>
    <x-slot name="pagination">
        @if (isset($opportunities))
            {{ $opportunities->links() }}
        @endif
    </x-slot>
</x-jet.listing-table>
