@extends('layouts.admin.master')
@section('title', 'Create Estimation')
@section('styles')
    <style>
        button:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        .divisions:focus {
            outline: none;
            box-shadow: none;

        }

        :root {
            --primary: white;
            --secondary: #ff5252;
            --background: #eee;
            --highlight: #ffda79;
            /* Theme color */
            --theme: var(--primary);
        }

        *, *::before, *::after {
            box-sizing: border-box;
        }

        .tab__label::after {
            color: #3E4756;
        }

        /* Core styles/functionality */
        .tab input {
            position: absolute;
            opacity: 0;
            z-index: -1;
        }

        .tab__content {
            max-height: 0;
            overflow: hidden;
            transition: all 0.35s;
        }

        .tab input:checked ~ .tab__content {
            max-height: fit-content;
        }

        /* Visual styles */
        .accordion {
            width: 100%;
            color: var(--theme);
            border: 2px solid;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .tab__label,
        .tab__close {
            display: flex;
            color: #90A0B7;
            background: var(--theme);
            cursor: pointer;
        }

        .tab__label {
            justify-content: space-between;
            padding: 1rem 0rem;
        }

        .tab__label::after {
            content: "\276F";
            width: 1em;
            height: 1em;
            text-align: center;
            transform: rotate(90deg);
            transition: all 0.35s;
        }

        .tab input:checked + .tab__label::after {
            transform: rotate(270deg);
            margin-right: 13px;
        }

        .tab__content p {
            margin: 0;
            padding: 1rem;
        }

        .tab__close {
            justify-content: flex-end;
            padding: 0.5rem 1rem;
            color: #90A0B7;
            font-size: 0.75rem;
        }

        .accordion--radio {
            --theme: var(--secondary);
        }

        /* Arrow animation */
        .tab input:not(:checked) + .tab__label:hover::after {
            animation: bounce .5s infinite;
        }

        @keyframes bounce {
            25% {
                transform: rotate(90deg) translate(.25rem);
            }
            75% {
                transform: rotate(90deg) translate(-.25rem);
            }
        }

        .accbtn:focus {
            outline: none;
            outline-color: white !important;
            box-shadow: none !important;
        }

        .dotss {
            width: 30px;
            height: 30px;
            padding: 0px 0px 0px 0px;
            text-align: center;
            border-radius: 5px;

            background: #F6F6F6;
            border: 1px solid #DFDFDF
        }

        .dnload {
            border-radius: 6px;
            border: 1.5px solid #0074D9
        }

        .dropdown-toggle::after {
            display: none !important;
            width: 0;
            height: 0;
            margin-left: .255em;
            vertical-align: .255em;
            content: "";
            border-top: .3em solid;
            border-right: .3em solid transparent;
            border-bottom: 0;
            border-left: .3em solid transparent;
        }

        .btn-sm {
            width: 22px;
            height: 22px;
        }

        .textcolorprimary {
            color: #7C8091;
        }

        .bottombox {
            width: 9.425%;
            border-right: 1px solid #E2E4EA;
            padding: 2px 10px;
            box-sizing: border-box;
        }

        .bottombox2 {
            width: 15%;
            padding: 2px 10px;
            box-sizing: border-box;
        }

        .panel2 {
            background: #ffffff;
            position: fixed;
            bottom: 0;
            height: 55px;
            box-shadow: 0px 0px 12px rgba(36, 185, 236, 0.08);
            border-radius: 8px;
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 0; /* Default no gap for larger screens */
        }

        .bottombox p {
            display: flex !important;
            justify-content: end !important;
            font-size: 10px;
        }

        .bottombox label {
            color: #0074d9;
            font-weight: bold;
        }

        .bottombox2 p {
            display: flex !important;
            justify-content: end !important;
            font-size: 10px;
        }

        .bottombox2 label {
            color: #0074d9;
            font-weight: bold;
        }

        /* Responsive adjustments */
        @media screen and (max-width: 768px) {
            .panel2 {
                gap: 10px !important; /* Space between the boxes on mobile */
                height: 100px;
                overflow-y: auto;
            }

            .bottombox, .bottombox2 {
                width: calc(25% - 10px); /* 2 items per row with space between */
                border-right: none; /* Remove border on mobile */
                padding: 10px;
                box-sizing: border-box;
            }

            .bottombox p, .bottombox2 p {
                margin-left: 0;
                text-align: right;
            }
        }

        @media screen and (max-width: 480px) {
            .panel2 {
                gap: 10px; /* Maintain space between boxes */
            }

            .bottombox, .bottombox2 {
                width: calc(50% - 10px); /* Adjust for spacing */
                padding: 10px;
                box-sizing: border-box;
            }

            .bottombox p, .bottombox2 p {
                text-align: right;
                margin-left: 0;
            }
        }

        .accordion-button:focus {
            outline: none;
            box-shadow: none;
        }
    </style>
    <style>
        /* Placeholder style during drag-and-drop */
        .sortable-placeholder {
            background-color: #e0e0e0;
            height: 40px; /* Same as row height */
            border: 2px dashed #ccc;
            visibility: visible !important;
        }

        /* Highlight the section header when row is dragged over it */
        .section-header.hovered {
            border: 2px dashed #007bff;
        }

        /* Style for the row while dragging */
        .dragging {
            opacity: 0.6;
        }

        .editinput {
            display: none;
            border: none;
            background-color: transparent;
            border-bottom: 2px solid #ab9d9d;
        }

        .okbutn {
            display: none;
            padding: 4px 20px 0px 7px;
        }

        .hovered {
            /*border: 2px dashed #007bff; !* Border color for valid drop target *!*/
            /*background-color: rgba(0, 123, 255, 0.1); !* Optional: Change background color for better visibility *!*/
        }

        .section-header.hovered {
            border: 2px solid blue; /* Blue border when hovered */
            transition: border 0.2s ease; /* Smooth transition for the border */
        }


    </style>
    <style>
        .tab__label {
            color: #333;
        }

        .accordion .active label {
            color: #0074D9;
            font-weight: bolder;
        }

        .tab__content {
            display: none;
        }

        .accordion .active .tab__content {
            display: block;
        }

        .tab__content {
            margin-top: -16px !important;
        }

        .hard_active {
            height: 20px;
            padding: 3px 10px;
            color: white !important;
            gap: 8px;
            border-radius: 100px;
            font-size: 11px;
            border: none;
            text-align: center;
            cursor: pointer;
            background: #0074D9;
        }

        .accordion-checkbox:checked + .tab__label + .tab__content {
            display: block;
        }

        .remove_active {
            height: 20px;
            padding: 3px 10px;
            color: #A8ABB5 !important;
            gap: 8px;
            border-radius: 100px;
            font-size: 11px;
            border: 1.5px solid #A8ABB5;
            text-align: center;
            cursor: pointer;
            background: transparent;
        }

        #hardmaterials {
            display: block;
        }

        #plantmaterials {
            display: none;
        }

        .samebtn {
            border-radius: 20px;
            background-color: #007bff;
            color: white;
        }

        input {
            text-align: center;
        }

        .serchinput {
            border-right: none !important;
            font-size: 16px !important;
        }

        .serchinput::placeholder {
            color: #E7E7E7 !important;
            font-size: 16px !important;
            padding-top: 7px !important;
        }

        .serchinput:focus {
            outline: none !important;
            box-shadow: none !important;
            border-right: none !important;
        }

        .favtbtn {
            color: #7C8091 !important;
            border: 1.5px solid #A8ABB5 !important;
        }

        .samebtn {
            color: white !important;
        }

        .disabledbtn {
            background-color: #A8ABB5 !important;
        }

        .table thead th {
            vertical-align: bottom;
            border-bottom: none !important;
        }

        .table td, .table th {
            padding: .75rem;
            vertical-align: top;
            border-top: none !important;
        }

        td {
            vertical-align: middle !important;
        }

        .detail_info .text {
            font-family: "Cabin";
            font-style: normal;
            font-weight: 400;
            font-size: 1.3rem;
            line-height: 2.4rem;
            margin-top: -8px !important;
            color: #192a3e;
            word-break: break-all;
        }

        .hidemodelbtn {
            font-size: 18px !important;
            color: #7e8a9d !important;
        }

        .hidemodelbtn:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        .hidemodelbtn:hover {
            cursor: pointer !important;
        }

        @media screen and (max-width: 580px) {
            .hidemodelbtn {
                font-size: 15px !important;
                color: #7e8a9d !important;
            }
        }

        .open {
            display: flex !important;
            justify-content: flex-start !important;
            align-items: normal !important;
            text-align: center !important;
            width: 18% !important;
            border-top-left-radius: 20px !important;
            border-bottom-left-radius: 20px !important;
            height: 35px !important;
        }

        .note-toolbar {
            position: relative !important;
            display: flex !important;
        }

        .note-btn-group {
            position: relative;
            display: flex !important;
            margin-right: 8px;
        }

        .note-btn {
            display: inline-block;
            font-weight: 400;
            margin-bottom: 0;
            text-align: center;
            vertical-align: middle;
            touch-action: manipulation;
            cursor: pointer;
            background-image: none;
            white-space: nowrap;
            outline: 0;
            color: #333;
            background-color: #fff;
            border: 1px solid #dae0e5;
            padding: 0 7px !important;
            font-size: 14px;
            line-height: 1.4;
            border-radius: 3px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .note-editor.note-airframe .note-editing-area .note-editable, .note-editor.note-frame .note-editing-area .note-editable {
            padding: 25px !important;
            overflow: auto;
            word-wrap: break-word;
        }

        .show td table {
            min-height: 50px !important;
        }

        .sortable-table-body tr:not([class]):not([id]) {
            height: 100px !important;
        }

    </style>
    <style>
        /* Remove number input arrows */
        input[type=number]::-webkit-outer-spin-button,
        input[type=number]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type=number] {
            -moz-appearance: textfield;
        }
        #sqftVal::placeholder {
            text-align: left;
        }
        #depthVal::placeholder {
            text-align: left;
        }


    </style>
@endsection
@section('section')
    <section class="dashboard_main">
        <div class="row justify-content-around">
            <!-- items sections start -->
            <div class="col-lg-3 pr-0">
                <div class="panel mt-4" style="margin-bottom: 0; padding: 3.4rem !important;">
                    <select class="divisions form-control w-100" id="getDivisions" style="border: none; display: none;" id="" data-id="">
                        <option selected>Select Division</option>
                        @foreach($divisions as $item)
                            <option value="{{$item->id}}">{{$item->name}}</option>
                        @endforeach
                    </select>

                    <div class="row justify-center buttonss">
                        <a href="javascript:void(0)" onclick="getAllMaterialsListing()" class="samebtn btn mx-auto allbtn" style="padding: 3px 0 !important; padding-top: 5px !important; width: 45%; font-size: 15px">All</a>
                        <a href="javascript:void(0)" onclick="getFavoriteItemsListing()" class="btn mx-auto difbtn favtbtn" style="padding: 3px 0 !important; width: 45%; font-size: 15px">Favorite</a>
                    </div>
                    <div class="row mt-4">
                        <div class="input-group">
                            <input type="text" class="form-control serchinput serchinputss py-3" oninput="searchItems()" style="border: 1px solid #E7E7E7; padding: 6px 8px 6px 8px !important; border-top-left-radius: 6px;border-bottom-left-radius: 6px; text-align: left !important;" placeholder="Search">
                            <div class="input-group-append input-group-append2" style="border: 1px solid #E7E7E7; border-top-right-radius: 6px;border-bottom-right-radius: 6px;">
                                <button class="btn bg-white px-3" type="button" style="border-top-right-radius: 6px; border-bottom-right-radius: 6px; padding-top: 9px !important;">
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M16 15.1632L11.5307 10.6938C12.6047 9.40448 13.1402 7.75068 13.026 6.07649C12.9117 4.40231 12.1564 2.83663 10.9171 1.70516C9.67784 0.5737 8.05008 -0.036435 6.37243 0.00168439C4.69478 0.0398038 3.09641 0.723243 1.90983 1.90983C0.723243 3.09641 0.0398038 4.69478 0.00168439 6.37243C-0.036435 8.05008 0.5737 9.67784 1.70516 10.9171C2.83663 12.1564 4.40231 12.9117 6.07649 13.026C7.75068 13.1402 9.40448 12.6047 10.6938 11.5307L15.1632 16L16 15.1632ZM1.20478 6.53106C1.20478 5.47762 1.51716 4.44784 2.10242 3.57194C2.68768 2.69604 3.51953 2.01335 4.49278 1.61022C5.46603 1.20709 6.53697 1.10161 7.57016 1.30712C8.60336 1.51264 9.55241 2.01992 10.2973 2.76481C11.0422 3.50971 11.5495 4.45876 11.755 5.49195C11.9605 6.52515 11.855 7.59609 11.4519 8.56934C11.0488 9.54259 10.3661 10.3744 9.49018 10.9597C8.61428 11.545 7.5845 11.8573 6.53106 11.8573C5.11892 11.8558 3.76507 11.2941 2.76654 10.2956C1.76801 9.29705 1.20635 7.9432 1.20478 6.53106Z" fill="#A0A3BD"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="row itemsContainerss">
                        <ul id="itemsContainer" style="width: 100%;"></ul>
                    </div>
                    <div class="row mt-1" id="accordion-container">
                        <section class="accordion" style="">

                            {{-- equipment sections start --}}
                            <div class="tab">
                                <input type="checkbox" name="accordion-1" id="cbb" class="accordion-checkbox">
                                <label for="cbb" class="tab__label">Equipment</label>
                                <div class="tab__content">
                                    <ul id="equipmentdata" style="">
                                        @foreach($equipments as $equipment)
                                            <li class="menu-accordian mt-4">
                                                <div class="row">
                                                    <div class="col-1" style="margin-top: 2px !important;">
                                                            <img id="equipmentFavoriteItem-{{$equipment->id}}" @class(['d-none' => $equipment->is_favorite == 1, 'cursor-pointer addItemToFavorite' => true]) data-item-id="{{ $equipment->id }}" data-item-type="equipment"
                                                                 src="{{ asset('asset/assets/images/not-favorite.svg') }}" alt="non favorite item icon">
                                                            <img id="equipmentNotFavoriteItem-{{$equipment->id}}"  @class(['d-none' => $equipment->is_favorite == 0, 'cursor-pointer removeItemFromFavorite' => true]) data-item-id="{{ $equipment->id }}" data-item-type="equipment"
                                                                  src="{{ asset('asset/assets/images/favorite.svg') }}" alt="favorite item icon">
                                                    </div>
                                                    <div class="col-7 text-left">
                                                        <span style="text-transform: capitalize;">{{ $equipment->name }}</span>
                                                    </div>
                                                    <div class="col-3 text-end" style="padding-right: 0px;">
                                                        <button class="equipment-add-btn" data-id="{{ $equipment->id }}" id="equipmentAddBtn-{{ $equipment->id }}" style="width: 24px;
              height: 24px;
              border-radius: 50%;
              text-align: center;
              margin-left: auto;
              color: white;
              border: none;
              font-size: 18px;
              background-color: #2FCC40;
              ">
                                                            <label for=""><b style="color: white; font-size: 17px;">+</b></label>
                                                        </button>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                            {{-- equipment sections end --}}

                            {{-- labors sections start --}}
                            <div class="tab">
                                <input type="checkbox" name="accordion-1" id="cb2" class="accordion-checkbox">
                                <label for="cb2" class="tab__label">Labor</label>
                                <div class="tab__content">
                                    <ul id="laborMenu" style="">
                                        @foreach($labors as $labor)
                                            <li class="menu-accordian mt-4">
                                                <div class="row">
                                                    <div class="col-1" style="margin-top: 2px !important;">
                                                        <img id="laborsFavoriteItem-{{$labor->id}}" @class(['d-none' => $labor->is_favorite == 1, 'cursor-pointer addItemToFavorite' => true]) data-item-id="{{ $labor->id }}" data-item-type="labors"
                                                             src="{{ asset('asset/assets/images/not-favorite.svg') }}" alt="non favorite item icon">
                                                        <img id="laborsNotFavoriteItem-{{$labor->id}}"  @class(['d-none' => $labor->is_favorite == 0, 'cursor-pointer removeItemFromFavorite' => true]) data-item-id="{{ $labor->id }}" data-item-type="labors"
                                                             src="{{ asset('asset/assets/images/favorite.svg') }}" alt="favorite item icon">
                                                    </div>
                                                    <div class="col-7 text-left">
                                                        <span style="text-transform: capitalize;">{{ $labor->name }}</span>
                                                    </div>
                                                    <div class="col-3 text-end" style="padding-right: 0px;">
                                                        <button id="laborAddBtn-{{ $labor->id }}" style="width: 24px;
              height: 24px;
              border-radius: 50%;
              text-align: center;
              margin-left: auto;
              color: white;
              border: none;
              font-size: 18px;
              background-color: #2FCC40;
              ">
                                                            <label for=""><b style="color: white; font-size: 17px;">+</b></label>
                                                        </button>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                            {{-- labors sections end --}}

                            {{-- materials section start --}}
                            <div class="tab">
                                <input type="checkbox" name="accordion-1" id="cb3" class="accordion-checkbox">
                                <label for="cb3" class="tab__label">Materials</label>
                                <div class="tab__content" style="margin-top: -9px !important">
                                    <a class="hardbtnactive hard_active mx-3">Hard</a>
                                    <a class="plantbtnactive remove_active">Plant</a>

                                    {{-- hard materials sections --}}
                                    <ul id="hardMaterials">
                                        @foreach($hard_materials as $hardMaterial)
                                            <li class="menu-accordian mt-4">
                                                <div class="row">
                                                    <div class="col-1" style="margin-top: 2px !important;">
                                                        <img id="hard_materialsFavoriteItem-{{$hardMaterial->id}}" @class(['d-none' => $hardMaterial->is_favorite == 1, 'cursor-pointer addItemToFavorite' => true]) data-item-id="{{ $hardMaterial->id }}" data-item-type="hard_materials"
                                                             src="{{ asset('asset/assets/images/not-favorite.svg') }}" alt="non favorite item icon">
                                                        <img id="hard_materialsNotFavoriteItem-{{$hardMaterial->id}}"  @class(['d-none' => $hardMaterial->is_favorite == 0, 'cursor-pointer removeItemFromFavorite' => true]) data-item-id="{{ $hardMaterial->id }}" data-item-type="hard_materials"
                                                             src="{{ asset('asset/assets/images/favorite.svg') }}" alt="favorite item icon">
                                                    </div>
                                                    <div class="col-7 text-left">
                                                        <span style="text-transform: capitalize;">{{ $hardMaterial->name }}</span>
                                                    </div>
                                                    <div class="col-3 text-end" style="padding-right: 0px;">
                                                        <button id="hardMaterialAddBtn-{{$hardMaterial->id}}" style="width: 24px;
              height: 24px;
              border-radius: 50%;
              text-align: center;
              margin-left: auto;
              color: white;
              border: none;
              font-size: 18px;
              background-color: #2FCC40;
              ">
                                                            <label for=""><b style="color: white; font-size: 17px;">+</b></label>
                                                        </button>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>

                                    {{-- plant materials section --}}
                                    <ul id="plantMaterials" class="d-none">
                                        @foreach($plant_materials as $plantMaterial)
                                            <li class="menu-accordian mt-4">
                                                <div class="row">
                                                    <div class="col-1" style="margin-top: 2px !important;">
                                                        <img id="plant_materialsFavoriteItem-{{$plantMaterial->id}}" @class(['d-none' => $plantMaterial->is_favorite == 1, 'cursor-pointer addItemToFavorite' => true]) data-item-id="{{ $plantMaterial->id }}" data-item-type="plant_materials"
                                                             src="{{ asset('asset/assets/images/not-favorite.svg') }}" alt="non favorite item icon">
                                                        <img id="plant_materialsNotFavoriteItem-{{$plantMaterial->id}}"  @class(['d-none' => $plantMaterial->is_favorite == 0, 'cursor-pointer removeItemFromFavorite' => true]) data-item-id="{{ $plantMaterial->id }}" data-item-type="plant_materials"
                                                             src="{{ asset('asset/assets/images/favorite.svg') }}" alt="favorite item icon">
                                                    </div>
                                                    <div class="col-7 text-left">
                                                        <span style="text-transform: capitalize;">{{ $plantMaterial->name }}</span>
                                                    </div>
                                                    <div class="col-3 text-end" style="padding-right: 0px;">
                                                        <button id="plantMaterialAddBtn-{{ $plantMaterial->id }}" style="width: 24px;
              height: 24px;
              border-radius: 50%;
              text-align: center;
              margin-left: auto;
              color: white;
              border: none;
              font-size: 18px;
              background-color: #2FCC40;
              ">
                                                            <label for=""><b style="color: white; font-size: 17px;">+</b></label>
                                                        </button>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                            {{-- materials section end --}}

                            {{-- other cost section start --}}
                            <div class="tab">
                                <input type="checkbox" name="accordion-1" id="cb4" class="accordion-checkbox">
                                <label for="cb4" class="tab__label">Other Cost</label>
                                <div class="tab__content">
                                    <ul id="othercost" style="">
                                        @foreach($other_job_costs as $item)
                                            <li class="menu-accordian mt-4">
                                                <div class="row">
                                                    <div class="col-1" style="margin-top: 2px !important;">
                                                        <img id="other_costsFavoriteItem-{{$item->id}}" @class(['d-none' => $item->is_favorite == 1, 'cursor-pointer addItemToFavorite' => true]) data-item-id="{{ $item->id }}" data-item-type="other_costs"
                                                             src="{{ asset('asset/assets/images/not-favorite.svg') }}" alt="non favorite item icon">
                                                        <img id="other_costsNotFavoriteItem-{{$item->id}}"  @class(['d-none' => $item->is_favorite == 0, 'cursor-pointer removeItemFromFavorite' => true]) data-item-id="{{ $item->id }}" data-item-type="other_costs"
                                                             src="{{ asset('asset/assets/images/favorite.svg') }}" alt="favorite item icon">
                                                    </div>
                                                    <div class="col-7 text-left">
                                                        <span style="text-transform: capitalize;">{{ $item->name }}</span>
                                                    </div>
                                                    <div class="col-3 text-end" style="padding-right: 0px;">
                                                        <button id="otherCostAddB-{{ $item->id }}" style="width: 24px;
              height: 24px;
              border-radius: 50%;
              text-align: center;
              margin-left: auto;
              color: white;
              border: none;
              font-size: 18px;
              background-color: #2FCC40;
              ">
                                                            <label for=""><b style="color: white; font-size: 17px;">+</b></label>
                                                        </button>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                            {{-- other cost section end --}}

                            {{-- subcontractors section start --}}
                            @foreach($sub_contractor as $item)
                                <li class="menu-accordian mt-4">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="col-7 text-left px-0">
                                            <span style="text-transform: capitalize;">{{ $item->name }}</span>
                                        </div>
                                        <div class="col-3 text-end" style="padding-right: 0px;">
                                            <button id="contractorAddBtn-{{ $item->id }}" style="width: 24px;
              height: 24px;
              border-radius: 50%;
              text-align: center;
              /*margin-left: 6rem;*/
              color: white;
              border: none;
              font-size: 18px;
              background-color: #2FCC40;
              cursor: pointer;    font-weight: 600;">+</button>
                                        </div>
                                    </div>
                                </li>
                            @endforeach
                            {{-- subcontractors section end --}}
                        </section>
                    </div>
                </div>
            </div>
            <!-- items sections end -->

            <div class="col-lg-9">
                <div class="accordion panel mt-4" style="padding: 0.5rem !important;" id="accordionExample">
                    <div class="accordion-item form-control" style="border: none;">
                        <h2 class="accordion-header" style="line-height: 0rem !important; margin-left: -1% !important;" id="headingOne">
                            <button class="accordion-button form-control accbtn text-left d-flex justify-content-between" style="border: none;" type="button" data-toggle="collapse" data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                <h2>
                                    {{ $opportunity->opportunity_name }}
                                    <span style="color: #7C8091; font-size: 12px;">&nbsp; &nbsp;Opp#{{$opportunity->opportunity_count}}</span> {{-- opportunity number --}}
                                </h2>

                                <label for="" style="width: 30px;height: 30px;padding: 7px 0 0 0; gap: 0;border-radius: 5px;text-align: center;border: 1px;opacity: 0; angle: -180 deg; background: #F6F6F6;">
                                    <svg style="" width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 1L5 5L1 1" stroke="#667085" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </label>

                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne"
                             style="margin-top: -7px !important;" data-parent="#accordionExample">
                            <div class="accordion-body">
                                <div class="">

                                    <div class="row d-flex justify-content-between">
                                        <div class="col-lg-2 col-md-4 col-sm-6 pt-1">
                                            <div class="detail_info">
                                                <label class="label">Account</label>
                                                <p class="text">{{ $opportunity?->account?->company_name ?? '-'}}</p>
                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-md-4 col-sm-6 pt-1">
                                            <div class="detail_info">
                                                <label class="label">Property Name</label>
                                                <p class="text">{{ $opportunity?->propertyInformation?->name ?? '' }}</p>
                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-md-4 col-sm-6 pt-1">
                                            <div class="detail_info">
                                                <label class="label">Opportunity Owner</label>
                                                <p class="text">{{ $opportunity?->opportunityOwner?->name ?? '-' }}</p>
                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-md-4 col-sm-6 pt-1">
                                            <div class="detail_info">
                                                <label class="label">Opportunity Type</label>
                                                <p class="text">{{ $opportunity?->opportunity_type ?? '-' }}</p>
                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-md-4 col-sm-6 pt-1">
                                            <div class="detail_info">
                                                <label class="label">Salesman</label>
                                                <p class="text">{{ $opportunity?->salesman?->name ?? '-' }}</p>
                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-md-4 col-sm-6 pt-1">
                                            <div class="detail_info">
                                                <label class="label">Estimator Name</label>
                                                <p class="text">{{ $opportunity?->estimator?->name }}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <input type="hidden" name="" value="" id="opportunity-id">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-6">
                        <h3>Estimate</h3>
                    </div>
                    <div class="col-6">
                        <div class="d-flex" style="margin-left: auto; justify-content: end; ">
                            <a href="{{ route('organization.estimation.download-pdf', ['opportunityId' => $opportunity->id]) }}" class="btn px-3 dnload d-flex mx-3">
                                <img src="{{ asset('asset/assets/images/opportunity-pdf-download.svg') }}" alt="opportunity pdf download icon">
                                <b class="mt-1">&nbsp;&nbsp;Download</b>
                            </a>

                            <a href="{{ URL::route(getRouteAlias() . '.organization.preview', ['opportunityId' => encodeId($opportunity->id)]) }}"
                               class="btn px-4" style="background-color:#E6F1FB; color: #117BDD; font-weight: 800;align-items: center;justify-content: center;display: flex;">Preview</a>

                            <a href="javascript:void(0)" class="btn btn-primary px-4 mx-3 d-flex addNewSection" style="font-weight: 800; padding-top: 7px;">+ Section</a>

                            <a href="javascript:void(0)" class="btn btn-primary px-4 mx-3 d-flex add-scope-btn-modal" style="font-weight: 800; padding-top: 7px;" data-toggle="modal" data-target="#definescope">Define Scope</a>

                            <a href="{{ URL::route(getRouteAlias() . '.organization.previewEstimation', ['opportunityId' => encodeId($opportunity->id)]) }}"
                               class="btn btn-large d-flex mx-3 btn-primary px-4" style="font-weight: 800;">
                                <img src="{{ asset('asset/assets/images/send-to-client-icon.svg') }}" alt="send to client button icon">
                                <b style="margin-top: 4px; margin-left: 5px; color: white;">Send To Client</b></a>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="w-100" style="padding: 1.4rem !important;">
                        <div class="table-responsive">
                            <div class="panel" style="padding: 0 0 30px 0 !important">
                                <table class="table  maintable main-table-dropable" style="border-radius: 8px !important;">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 4%;" colspan="">Items</th>
                                        <th scope="col" style="width: 20%;"></th>
                                        <th class="border" style="width:13%">Dimension</th>
                                        <th class="border" scope="col" style="width: 12%;">Quantity</th>
                                        <th class="border" scope="col" style="width: 9%;">UoM</th>
                                        <th class="border" scope="col" style="width: 9%;">Unit Cost ($)</th>
                                        <th class="border" scope="col" style="width: 9%;">Total Cost ($)</th>
                                        <th class="border" scope="col" style="width: 12%;">Gross Margin</th>
                                        <th class="border" scope="col" style="width: 9%;">Total Price ($)</th>
                                        <th colspan="2" scope="col" style="width: 9%;">Unit Price ($)</th>
                                        <th scope="col"></th>
                                    </tr>
                                    </thead>
                                    <tbody id="sortable-table-body" class="">
                                    {{-- new section tr --}}
                                    <tr class="tablerows-1 lastforsectionrow" style="background: #f5faff">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <input type="checkbox" class="dynamic-checkbox d-flex align-items-center" id="checkbox-1" data-id="1" title="Add Item" style="cursor: pointer;">
                                            </div>
                                        </td>
                                        <td class="align-middle">
                                            <div class="d-flex align-items-center">
                                                <div id="section1Heading-1" class="me-2 sectionNames">Section 01</div>
                                                <input type="hidden" style="text-align: left;" class="editinputid" id="editinputid-1" data-sec-id="1" value="1">
                                                <input type="text" style="text-align: left;" value="Section 01" class="editinput" id="editinput-1">
                                                <a href="javascript:void(0)" id="editbutn-1" class="text-decoration-none mt-1 mx-3 editbutn">
                                                    <svg class="" width="15" height="15" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M1.87604 17.1156C1.92198 16.7021 1.94496 16.4954 2.00751 16.3022C2.06301 16.1307 2.14143 15.9676 2.24064 15.8171C2.35246 15.6475 2.49955 15.5005 2.79373 15.2063L16 2C17.1046 0.895427 18.8955 0.895428 20 2C21.1046 3.10457 21.1046 4.89543 20 6L6.79373 19.2063C6.49955 19.5005 6.35245 19.6475 6.18289 19.7594C6.03245 19.8586 5.86929 19.937 5.69785 19.9925C5.5046 20.055 5.29786 20.078 4.88437 20.124L1.5 20.5L1.87604 17.1156Z" stroke="#7C8091" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </a>
                                                <a href="javascript:void(0)" id="okbutn-1" style="padding-right: 25px;" class="btn btn-sm btn-primary text-decoration-none mt-1 mx-3 okbutn">
                                                    Save
                                                </a>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td class="d-flex" style="gap: 15px">
                                            <div class="dropdown">
                                                <button class="btn btn-sm bg-transparent dropdown-toggle" type="button" data-toggle="dropdown" aria-expanded="false">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots" viewBox="0 0 16 16">
                                                        <path d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"/>
                                                    </svg>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="javascript:void(0)">Edit</a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="javascript:void(0)">Delete</a>
                                                    </li>
                                                </ul>
                                            </div>

                                            <button class="btn btn-sm" id="mainbtnnow-1" style="background: #f6f6f6; border: 1px solid #dfdfdf;" type="button"
                                                    data-toggle="collapse"
                                                    data-target="#section-1"
                                                    aria-expanded="false"
                                                    aria-controls="section-initial">
                                                <label for="" class="changeiconall changeicon-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" class="bi bi-chevron-down arrow-icon " viewBox="0 0 16 16">
                                                        <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/>
                                                    </svg>
                                                </label>
                                            </button>
                                        </td>
                                    </tr>

                                    {{-- items for sections --}}
                                    <tr id="section-2" class="collapse tavy tablerows-2">
                                        <td style="padding: 0 !important" colspan="11">
                                            <table class="table">
                                                <tbody class="sortable-content dropable-content-here accord-bodys accord-bodys-2" data-section-id="2">
                                                {{--                                                    @foreach($gitems as $data)--}}
                                                <tr class="border datarowsequ22-2" data-item-id="1">
                                                    <td class="drag-handle" style="width: 4%">
                                                        <svg width="17" height="22" viewBox="0 0  17 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                                d="M5.29167 11.9166C5.79793 11.9166 6.20833 11.5062 6.20833 11C6.20833 10.4937 5.79793 10.0833 5.29167 10.0833C4.78541 10.0833 4.375 10.4937 4.375 11C4.375 11.5062 4.78541 11.9166 5.29167 11.9166Z"
                                                                stroke="#D3D6E0"
                                                                stroke-width="1.83333"
                                                                stroke-linecap="round"
                                                                stroke-linejoin="round"
                                                            />
                                                            <path
                                                                d="M5.29167 5.49996C5.79793 5.49996 6.20833 5.08955 6.20833 4.58329C6.20833 4.07703 5.79793 3.66663 5.29167 3.66663C4.78541 3.66663 4.375 4.07703 4.375 4.58329C4.375 5.08955 4.78541 5.49996 5.29167 5.49996Z"
                                                                stroke="#D3D6E0"
                                                                stroke-width="1.83333"
                                                                stroke-linecap="round"
                                                                stroke-linejoin="round"
                                                            />
                                                            <path
                                                                d="M5.29167 18.3333C5.79793 18.3333 6.20833 17.9229 6.20833 17.4166C6.20833 16.9104 5.79793 16.5 5.29167 16.5C4.78541 16.5 4.375 16.9104 4.375 17.4166C4.375 17.9229 4.78541 18.3333 5.29167 18.3333Z"
                                                                stroke="#D3D6E0"
                                                                stroke-width="1.83333"
                                                                stroke-linecap="round"
                                                                stroke-linejoin="round"
                                                            />
                                                            <path
                                                                d="M11.7096 11.9166C12.2159 11.9166 12.6263 11.5062 12.6263 11C12.6263 10.4937 12.2159 10.0833 11.7096 10.0833C11.2034 10.0833 10.793 10.4937 10.793 11C10.793 11.5062 11.2034 11.9166 11.7096 11.9166Z"
                                                                stroke="#D3D6E0"
                                                                stroke-width="1.83333"
                                                                stroke-linecap="round"
                                                                stroke-linejoin="round"
                                                            />
                                                            <path
                                                                d="M11.7096 5.49996C12.2159 5.49996 12.6263 5.08955 12.6263 4.58329C12.6263 4.07703 12.2159 3.66663 11.7096 3.66663C11.2034 3.66663 10.793 4.07703 10.793 4.58329C10.793 5.08955 11.2034 5.49996 11.7096 5.49996Z"
                                                                stroke="#D3D6E0"
                                                                stroke-width="1.83333"
                                                                stroke-linecap="round"
                                                                stroke-linejoin="round"
                                                            />
                                                            <path
                                                                d="M11.7096 18.3333C12.2159 18.3333 12.6263 17.9229 12.6263 17.4166C12.6263 16.9104 12.2159 16.5 11.7096 16.5C11.2034 16.5 10.793 16.9104 10.793 17.4166C10.793 17.9229 11.2034 18.3333 11.7096 18.3333Z"
                                                                stroke="#D3D6E0"
                                                                stroke-width="1.83333"
                                                                stroke-linecap="round"
                                                                stroke-linejoin="round"
                                                            />
                                                        </svg>
                                                    </td>
                                                    <td class="border" style="width: 20% !important;">
                                                        <label for="">
                                                            <input type="text" id="laboritemname-2" value="Labor item 1"/>
                                                        </label>
                                                        <input type="text" style="display: none;" class="clas" value="1">
                                                    </td>
                                                    <td></td>
                                                    <td class="border" colspan="1" style="width: 12%;">
                                                        <input type="number" value="1" min="1" style="width: 100%;" placeholder="01"/></td>
                                                    <td class="border" colspan="1" style="width: 9% !important">UOM</td>
                                                    <td class="border" style="width: 9% !important" id="" colspan="1">
                                                        <label id="">20</label>
                                                    </td>
                                                    <td class="border" style="width: 9% !important" id="" colspan="1">60</td>

                                                    <td class="border " style="width: 12% !important" colspan="1">
                                                        <input type="text" style="display: none" id="sumofmarginequ-1" value="" class="sumofmargin"/>
                                                        <input class="" type="hidden" id="" style="width: 100% !important" value="10" placeholder="01"/>
                                                        20
                                                    </td>
                                                    <td class="border" style="width: 9% !important" id="" colspan="1">120</td>
                                                    <td class="border" style="width: 9% !important" id="">80</td>

                                                    <td class="border"
                                                        style="padding-top: 12px; text-align: center;">
                                                        <a href="javascript:void(0)">
                                                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M5.5 0.5H10.5M0.5 3H15.5M13.8333 3L13.2489 11.7661C13.1612 13.0813 13.1174 13.7389 12.8333 14.2375C12.5833 14.6765 12.206 15.0294 11.7514 15.2497C11.235 15.5 10.5759 15.5 9.25779 15.5H6.74221C5.42409 15.5 4.76503 15.5 4.24861 15.2497C3.79396 15.0294 3.41674 14.6765 3.16665 14.2375C2.88259 13.7389 2.83875 13.0813 2.75107 11.7661L2.16667 3" stroke="red" stroke-linecap="round" stroke-linejoin="round"/>
                                                            </svg>
                                                        </a>
                                                    </td>
                                                </tr>
                                                {{--                                                    @endforeach--}}
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>

                                    {{-- items without sections --}}
                                    <tr class="border datarowsequ-1" data-item-id="1">
                                        <td class="drag-handle" style="width: 4%">
                                            <svg width="17" height="22" viewBox="0 0  17 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M5.29167 11.9166C5.79793 11.9166 6.20833 11.5062 6.20833 11C6.20833 10.4937 5.79793 10.0833 5.29167 10.0833C4.78541 10.0833 4.375 10.4937 4.375 11C4.375 11.5062 4.78541 11.9166 5.29167 11.9166Z" stroke="#D3D6E0" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M5.29167 5.49996C5.79793 5.49996 6.20833 5.08955 6.20833 4.58329C6.20833 4.07703 5.79793 3.66663 5.29167 3.66663C4.78541 3.66663 4.375 4.07703 4.375 4.58329C4.375 5.08955 4.78541 5.49996 5.29167 5.49996Z" stroke="#D3D6E0" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path
                                                    d="M5.29167 18.3333C5.79793 18.3333 6.20833 17.9229 6.20833 17.4166C6.20833 16.9104 5.79793 16.5 5.29167 16.5C4.78541 16.5 4.375 16.9104 4.375 17.4166C4.375 17.9229 4.78541 18.3333 5.29167 18.3333Z"
                                                    stroke="#D3D6E0"
                                                    stroke-width="1.83333"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                />
                                                <path
                                                    d="M11.7096 11.9166C12.2159 11.9166 12.6263 11.5062 12.6263 11C12.6263 10.4937 12.2159 10.0833 11.7096 10.0833C11.2034 10.0833 10.793 10.4937 10.793 11C10.793 11.5062 11.2034 11.9166 11.7096 11.9166Z"
                                                    stroke="#D3D6E0"
                                                    stroke-width="1.83333"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                />
                                                <path
                                                    d="M11.7096 5.49996C12.2159 5.49996 12.6263 5.08955 12.6263 4.58329C12.6263 4.07703 12.2159 3.66663 11.7096 3.66663C11.2034 3.66663 10.793 4.07703 10.793 4.58329C10.793 5.08955 11.2034 5.49996 11.7096 5.49996Z"
                                                    stroke="#D3D6E0"
                                                    stroke-width="1.83333"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                />
                                                <path
                                                    d="M11.7096 18.3333C12.2159 18.3333 12.6263 17.9229 12.6263 17.4166C12.6263 16.9104 12.2159 16.5 11.7096 16.5C11.2034 16.5 10.793 16.9104 10.793 17.4166C10.793 17.9229 11.2034 18.3333 11.7096 18.3333Z"
                                                    stroke="#D3D6E0"
                                                    stroke-width="1.83333"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                />
                                            </svg>
                                        </td>
                                        <td class="border" style="width: 20% !important;">
                                            <label for="">
                                                <input type="text" id="contractoritemname-1" value="Subcontractor Name"/>
                                            </label>
                                            <input type="text" style="display: none;" class="" value="">
                                        </td>
                                        <td></td>
                                        <td class="border" colspan="1" style="width: 12%;">
                                            <input type="number" value="1" min="1" style="width: 100%;" id="" placeholder="01"/>
                                        </td>
                                        <td class="border" colspan="1" style="width: 9% !important">UOM</td>
                                        <td class="border" style="width: 9% !important" colspan="1">
                                            <input type="number" value="25" min="1" style="width: 100%;" id="" placeholder="01"/>
                                        </td>
                                        <td class="border" style="width: 9% !important" id="" colspan="1">140</td>

                                        <td class="border " style="width: 12% !important" colspan="1">
                                            <input type="text" style="display: none" id="sumofmarginequ-2" value="" class="sumofmargin"/>
                                            <input class="" type="hidden" id="" style="width: 100% !important" value="13" placeholder="01"/>
                                            13
                                        </td>
                                        <td class="border " style="width: 9% !important" id="" colspan="1">200</td>
                                        <td class="border" style="width: 9% !important" id="">90</td>

                                        <td class="border" style="padding-top: 12px; text-align: center;">
                                            <a href="javascript:void(0)">
                                                <svg
                                                    width="16"
                                                    height="16"
                                                    viewBox="0 0 16 16"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    <path
                                                        d="M5.5 0.5H10.5M0.5 3H15.5M13.8333 3L13.2489 11.7661C13.1612 13.0813 13.1174 13.7389 12.8333 14.2375C12.5833 14.6765 12.206 15.0294 11.7514 15.2497C11.235 15.5 10.5759 15.5 9.25779 15.5H6.74221C5.42409 15.5 4.76503 15.5 4.24861 15.2497C3.79396 15.0294 3.41674 14.6765 3.16665 14.2375C2.88259 13.7389 2.83875 13.0813 2.75107 11.7661L2.16667 3"
                                                        stroke="red"
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                    />
                                                </svg>
                                            </a>
                                        </td>
                                    </tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <tr id="section-initial" class="collapse tavy tablerows">
                                        <td style="padding: 0px !important" colspan="10">
                                            <table class="table sortable-content">
                                                <tbody class="sortable-content accord-bodys"></tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!-- onclick="addNewItem()" -->
                            <a href="javascript:void(0)" type="button" class="btn btn-success mt-3" data-toggle="modal" data-target="#addItemModal">Add Item</a>

                        </div>

                    </div>

                </div>

                <div class="row" style="margin-top: 0px;">
                    <div class="col">
                        {{--<a href="javascript:void(0)" class="btn btn-success mt-5 text-white" data-toggle="modal" data-target="#addItemModal" >Add Item</a>--}}
                        <b class="mx-3" id="totalaveragegrossmargen" style="display: none;">0</b>
                        <div class="d-flex justify-content-end" style="margin-top: -16px !important;">
                            <label for="" class="textcolorprimary">Selling price at standard margins:</label><b class="mx-3" id="totalSellingPrice">$ 2500</b>
                            <b class="mx-3" id="totalSellingCost" style="display: none;">$2000</b>
                            <label for="" class="textcolorprimary">Gross Margin %:</label>
                            <b class="mx-3" id="totalaveragegrossmargenadded">0.00 %</b>
                        </div>
                    </div>
                </div>
                <div class="row mt-4" style="margin-bottom: 60px !important;">
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end mt-3">
                            <b for="">Adjust selling price by dollar amount</b>
                            <input type="number" id="forceselling" class="mx-3 px-2" placeholder="Enter here" style="border: 1px solid #D0D5DD; text-align: left; color: black; border-radius: 5px;">
                        </div>
                        <div class="d-flex justify-content-end mt-3">
                            <b for="">Adjust selling price by margin amount</b>
                            <input type="number" id="forcegm" class="mx-3 px-2" placeholder="Enter here" style="border: 1px solid #D0D5DD; text-align: left; color: black; border-radius: 5px;">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end mt-3">
                            <label for="" class="textcolorprimary">Final selling price </label>
                            <input type="number" id="forcefinalselling" class="mx-3 px-2" placeholder="$" style="border: 1px solid #D0D5DD; color: black; text-align: left; border-radius: 5px;">
                        </div>

                        <div class="d-flex justify-content-end mt-3">
                            <label for="" class="textcolorprimary">Final Gross Margin % </label>
                            <input type="number" id="forcefinalgm" class="mx-3 px-2" placeholder="60%" style="border: 1px solid #D0D5DD; color: black; text-align: left; border-radius: 5px;">
                        </div>
                    </div>
                </div>

                <div class="row w-100 " style="margin-bottom: 60px; display: none;">
                    <div class="bottombox">
                        <label>Equipment</label>
                        <p id="totalcountequip">$90</p>

                    </div>
                    <div class="bottombox">
                        <label>Labor</label>
                        <p id="totalcountlabor">$30</p>
                    </div>
                    <div class="bottombox">
                        <label>Labor Burden</label>
                        <p id="totalcountburden">$40</p>
                    </div>
                    <div class="bottombox">
                        <label>Material</label>
                        <p id="totalcountmaterial">$40</p>
                    </div>
                    <div class="bottombox">
                        <label>Hours</label>
                        <p id="">0.00</p>
                    </div>
                    <div class="bottombox">
                        <label>Other</label>
                        <p id="totalcountother">$20</p>
                    </div>
                    <div class="bottombox">
                        <label>Sub Contractor</label>
                        <p id="totalSubContractorsValu">$30</p>
                    </div>
                    <div class="bottombox">
                        <label>Total</label>
                        <p id="totalPriceCount">$100</p>
                    </div>
                    <div class="bottombox2">
                        <label>Estimated sales tax</label>
                        <p id="totalmaterialsaletex">$0.00</p>
                    </div>
                </div>
            </div>

            <div class="row panel2 w-100">
                <div class="bottombox">
                    <label>Equipment</label>
                    <p id="totalcountequipPrice">$200</p>
                </div>
                <div class="bottombox">
                    <label>Labor</label>
                    <p id="totalcountlaborPrice">$40</p>
                </div>
                <div class="bottombox">
                    <label>Labor Burden</label>
                    <p id="totalcountburdenPrice">$10</p>
                </div>
                <div class="bottombox">
                    <label>Labor Hours</label>
                    <p id="totalhourslabors">15</p>
                </div>
                <div class="bottombox">
                    <label>Supervisor Hours</label>
                    <p id="totalhourssupervision">2</p>
                </div>
                <div class="bottombox">
                    <label>Material</label>
                    <p id="totalcountmaterialPrice">$100</p>
                </div>

                <div class="bottombox">
                    <label>Other</label>
                    <p id="totalcountotherPrice">$20</p>
                </div>
                <div class="bottombox">
                    <label>Sub Contractor</label>
                    <p id="totalSubContractorsValuPrice">$29</p>
                    <!-- <p id="totalSubContractorsValu">$0.00</p> -->
                </div>
                <div class="bottombox">
                    <label>Sales tax</label>
                    <p id="totalmaterialsaletexPrice">$30</p>
                </div>
                <div class="bottombox2">
                    <label>Total</label>
                    <p id="totalPriceCount">$300</p>
                    <p id="totalallcount" style="display: none;">$300</p>
                </div>

            </div>
            <input type="hidden" id="salestaxuser" value="16">

            <input type="hidden" id="laborburdenval" value="10">

            {{-- add new item modal --}}
            <div class="modal fade" id="addItemModal" tabindex="-1" aria-labelledby="addItemModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header" style="background: #E1F4FF;">
                            <h4 class="modal-title" id="addItemModalLabel" style="color: #0074D9 !important">Add new items</h4>
                            <button type="button" class="btn-close hidemodelbtn px-3" data-dismiss="modal" aria-label="Close" style="border: none; background-color: transparent">
                                <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label for=""><b>Select Category</b></label>
                                <select name="" style="height: 30px !important;" class="form-control" id="category_list">
                                    <option value="equipment">Equipment</option>
                                    <!-- <option value="labors">Labor</option> -->
                                    <option value="hard_materials">Hard Materials</option>
                                    <option value="plant_materials">Plant Materials</option>
                                    <option value="other_costs">Other Job Cost</option>
                                    <!-- <option value="contractors">Sub Contractor</option> -->
                                </select>
                            </div>
                            <div class="form-group">
                                <b for=""> Name</b>
                                <input type="text" id="item_name" style="height: 30px !important; text-align: left;" class="form-control " placeholder=" Name">
                            </div>
                            <div class="form-group">
                                <b for=""> Quantity</b>
                                <input type="number" id="item_quantity" style="height: 30px !important; text-align: left;" class="form-control " placeholder="Item Quantity">
                            </div>
                            <div class="form-group">
                                <b for=""> Uom</b>
                                <!-- <input type="text" id="item_uom" style="height: 30px !important; text-align: left;" class="form-control " placeholder="Item Uom"> -->
                                <select name="" id="item_uom" class="form-control">
                                    <option disabled selected>Choose Uom</option>
                                    <option value="Daily">Daily</option>
                                    <option value="Weekly">Weekly</option>
                                    <option value="Kathleen">Kathleen</option>
                                    <option value="Hour">Hour</option>
                                    <option value="klj">klj</option>
                                    <option value="Hours">Hours</option>
                                    <option value="Quart">Quart</option>
                                    <option value="Nereida">Nereida</option>
                                    <!-- <option value="werw">werw</option> -->
                                </select>
                            </div>
                            <div class="form-group" id="depth_fields">
                                <b for="">Depth</b>
                                <input type="number" style="height: 30px !important; text-align: left;" class="form-control" id="depth" min="0" placeholder="Enter Depth (In)">
                            </div>
                            <div class="form-group" id="sqft_fields">
                                <b for="">Sqft</b>
                                <input type="number" style="height: 30px !important; text-align: left;" class="form-control" id="sqft" min="0" placeholder="Enter Sqft">
                            </div>
                            <div class="form-group">
                                <b for="">Unit Cost</b>
                                <input type="number" id="item_unitcost" style="height: 30px !important; text-align: left;" class="form-control " placeholder="Item Unit Cost">
                            </div>
                            <button type="button" class="btn btn-primary form-control">Add Item</button>
                        </div>
                    </div>
                </div>
            </div>

            {{-- dimensions modal for hard material: square feet & depth (inch) --}}
            <div class="modal fade" id="inputModal" tabindex="-1" role="dialog" aria-labelledby="inputModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content" style="width:70%">
                        <div class="modal-header" style="background-color:#dcf2ff">
                            <h5 class="modal-title" id="inputModalLabel" style="color:#0074d9;font-weight:bold">Select Dimensions</h5>
                            <input type="hidden" id="hard_material_id" value="">
                            <input type="hidden" id="hard_material_uom">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="inputForm">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="sqft">Sqft</label><span class="text-danger">*</span>
                                            <input type="number" class="form-control" id="sqftVal" min="0" placeholder="Enter Sqft" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="depth">Depth (In)</label><span class="text-danger">*</span>
                                            <input type="number" class="form-control" id="depthVal" min="0" placeholder="Enter Depth (In)" required>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" id="deptSqftModalBtn" onclick="submitInput()">Add</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- define scope modal -->
            <div class="modal fade" id="definescope" tabindex="-1" aria-labelledby="addItemModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header" style="background: #E1F4FF;">
                            <h4 class="modal-title" id="addItemModalLabel" style="color: #0074D9 !important; font-weight: bold;">Define Scope</h4>
                            <button type="button" class="btn-close hidemodelbtn px-3" data-dismiss="modal" aria-label="Close" style="border: none; background-color: transparent">
                                <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <b for="">Scope Title</b>
                                <input type="text" name="scope_title" value="Scope Title" style="height: 30px !important; text-align: left; font-size: 16px;" class="form-control scope_title" id="scope_title" placeholder="Title">
                            </div>
                            <div class="form-group">
                                <label for=""><b>Scope Description</b></label>
                                <textarea id="textEditor" class="wycwyg_editorb describe-project addNoteSummer2 scope_description" name="scope_description" id="addNoteSummer" style="height:200px">Description</textarea>
                            </div>
                            <button type="button" class="btn btn-primary doneBtnDefinescope" style="font-size: 16px; float: inline-end;" id="doneBtnDefinescope">Add Scope</button>
                        </div>

                    </div>
                </div>
            </div>


            <!--Success Modal -->
            <div class="modal-small success-modal modal fade" id="grossmarginmodel" data-keyboard="false"
                 tabindex="-1"
                 aria-labelledby="successModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-body">
                            <div class="text-center mb-4">
                                <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/check-icon.png') }}" alt="check icon">
                            </div>

                            <h2 class="title text-center">Company Margin Setup</h2>
                            <p class="para mt-3 text-center">First you have to set company margin in settings</p>
                            <button type="button" class="btn primaryblue w-100 mt-5">Set Margin</button> {{-- onclick="window.location.href='{{ route('organization.margin_setup') }}'" --}}
                        </div>

                    </div>
                </div>
            </div>
            <!--Success Modal -->

            <!-- edit item modal -->
            <div class="modal fade" id="editItemModal" tabindex="-1" aria-labelledby="editItemModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header" style="background: #E1F4FF;">
                            <h4 class="modal-title" id="addItemModalLabel" style="color: #0074D9 !important">Edit Item</h4>
                            <button type="button" class="btn-close hidemodelbtn px-3" data-dismiss="modal" aria-label="Close" style="border: none; background-color: transparent">
                                <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label for=""><b>Select Category</b></label>
                                <select name="" style="height: 30px !important;" class="form-control" id="edit_category_list" @disabled(true)>
                                    <option value="equipment">Equipment</option>
                                    <!-- <option value="labors">Labor</option> -->
                                    <option value="hard_materials">Hard Materials</option>
                                    <option value="plant_materials">Plant Materials</option>
                                    <option value="other_costs">Other Job Cost</option>
                                    <!-- <option value="contractors">Sub Contractor</option> -->
                                </select>
                            </div>
                            <div class="form-group">
                                <b for=""> Name</b>
                                <input type="text" id="edit_item_name" style="height: 30px !important; text-align: left;" class="form-control " placeholder=" Name" @readonly(true)>
                            </div>
                            <div class="form-group">
                                <b for=""> Quantity</b>
                                <input type="number" id="edit_item_quantity" style="height: 30px !important; text-align: left;" class="form-control " placeholder="Item Quantity" @readonly(true)>
                            </div>
                            <div class="form-group">
                                <b for=""> Uom</b>
                                <!-- <input type="text" id="item_uom" style="height: 30px !important; text-align: left;" class="form-control " placeholder="Item Uom"> -->
                                <select name="" id="edit_item_uom" class="form-control" @disabled(true)>
                                    <option disabled selected>Choose Uom</option>
                                    <option value="Daily">Daily</option>
                                    <option value="Weekly">Weekly</option>
                                    <option value="Kathleen">Kathleen</option>
                                    <option value="Hour">Hour</option>
                                    <option value="klj">klj</option>
                                    <option value="Hours">Hours</option>
                                    <option value="Quart">Quart</option>
                                    <option value="Nereida">Nereida</option>
                                    <!-- <option value="werw">werw</option> -->
                                </select>
                            </div>
                            <div class="form-group" id="depth_fields">
                                <b for="">Depth</b>
                                <input type="number" style="height: 30px !important; text-align: left;" class="form-control" id="edit_depth" min="0" placeholder="Enter Depth (In)">
                            </div>
                            <div class="form-group" id="sqft_fields">
                                <b for="">Sqft</b>
                                <input type="number" style="height: 30px !important; text-align: left;" class="form-control" id="edit_sqft" min="0" placeholder="Enter Sqft">
                            </div>
                            <div class="form-group">
                                <b for="">Unit Cost</b>
                                <input type="number" id="edit_item_unitcost" style="height: 30px !important; text-align: left;" class="form-control " placeholder="Item Unit Cost" @readonly(true)>
                            </div>
                            <input type="hidden" id="edit_item_id">
                            <button type="button" class="btn btn-primary form-control" id="updateEstimateItem">Update Item</button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('extra-scripts')
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <script src="{{ asset('assets/tinymce/js/tinymce/tinymce.min.js') }}"></script>
    @include('organization.opportunity.opportunity-estimation-scripts')
@endsection
