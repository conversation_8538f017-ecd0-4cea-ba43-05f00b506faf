<script>
    $(document).ready(function(e) {
        transactions_table = $('.yajra-datatable').DataTable({
            processing: true,
            responsive: true,
            scrollX: true,
            serverSide: true,
            ajax: {
                url: "{{ route(getRouteAlias() . '.transactions') }}",

            },
            columns: [{
                    data: 'invoice_number',
                    name: 'invoice_number',
                    orderable: false
                }, {
                    data: 'invoice_subject',
                    name: 'invoice_subject',
                    orderable: false
                }, {
                    data: 'invoice_issue_date',
                    name: 'invoice_issue_date',
                    orderable: false
                }, {
                    data: 'transaction_date',
                    name: 'transaction_date',
                    orderable: false
                },
                {
                    data: 'payment_method',
                    name: 'payment_method',
                    orderable: false
                },
                {
                    data: 'amount',
                    name: 'amount',
                    orderable: false
                }, {
                    data: 'status',
                    name: 'status',
                    orderable: false
                }


            ],
            language: {
                zeroRecords: "Sorry we could not find any results",
                paginate: {
                    "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                    "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
                },


            },
            dom: '<"top">rt<"bottom"lip><"clear">',
        });

        // Custome Search
        $('.transactionsList #transaction_search').on('keyup', function() {
            transactions_table.search(this.value).draw();
        });

    })
</script>
