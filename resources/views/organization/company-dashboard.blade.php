@extends('layouts.admin.master')
@section('title', 'Dashboard')
@section('styles')
    <style>
        /* Default styling for the placeholder-like effect */
        input[type="date"].placeholder-shown,
        input[type="time"].placeholder-shown {
            /* color: #e8e8e8 !important; */
            font-style: italic;
        }

        select.modal-field {
            /* color: #e8e8e8 !important; Placeholder color */
        }

        /* Valid selection color */
        select.modal-field.validss {
            /* color: #e8e8e8 !important; Black for valid options */
        }

        /* Target the time input field */
        input[type="time"] {
            font-size: 16px;
            /* Adjust text size */
            color: #333;
            /* Text color */
            padding: 10px;
            /* Add padding */
            border: 1px solid #ccc;
            /* Border styling */
            border-radius: 4px;
            /* Rounded corners */
            background-color: #f9f9f9;
            /* Background color */
        }

        /* Target the icon inside the time input */
        /* input[type="time"]::-webkit-calendar-picker-indicator {
                  font-size: 20px !important;
                  color: red !important;
                  cursor: pointer;
                } */
        input[type="time"]::-webkit-calendar-picker-indicator {
            filter: invert(70%) sepia(20%) saturate(200%) hue-rotate(180deg) brightness(90%) !important;
            font-size: 20px !important;
            cursor: pointer;
        }

        .dropdown-toggle:focus {
            outline: none !important;
            box-shadow: none !important;
        }
    </style>
    <style>
        .modal-large {
            width: 80% !important;
            max-width: 100% !important;
        }

        .table thead th {
            vertical-align: bottom;
            border-bottom: none !important;
            border: none !important;
        }

        .btn-close:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        .btn-close {
            cursor: pointer;
        }

        .dashboard_stat_cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(255px, 1fr)) !important;
            gap: 16px;
        }

        input::placeholder {
            color: #e8e8e8 !important
        }

        .hidemodelbtn {
            font-size: 18px !important;

            color: #7e8a9d !important;
        }

        .hidemodelbtn:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        .hidemodelbtn:hover {
            cursor: pointer !important;
        }

        @media screen and (max-width: 580px) {
            .large-modal {
                max-width: 95% !important;
                width: 95% !important;
            }

            .hidemodelbtn {
                font-size: 15px !important;
                color: #7e8a9d !important;
            }
        }

        .input.datepicker-icon {
            background-image: url("{{ asset('admin_assets/images/icons/calander.svg') }}") !important;
            background-position: center right 10px !important;
            background-repeat: no-repeat !important;
        }

        .datepicker.dropdown-menu .datepicker-days .table-condensed .day {
            text-align: center;
            font-family: "Cabin";
            font-style: normal;
            font-weight: 400;
            font-size: 12px !important;
            line-height: 24px;
            color: #192a3e;
            padding: 10px 10px;
            border-radius: 3px;
        }

        .datepicker.dropdown-menu .table-condensed .datepicker-switch {
            font-family: "Cabin";
            font-style: normal;
            font-weight: 600;
            font-size: 16px !important;
            line-height: 33px;
            color: #003366;
            text-align: center;
        }

        .datepicker.dropdown-menu .datepicker-days .table-condensed .dow {
            padding: 15px 10px;
            font-family: "Cabin";
            font-style: normal;
            font-weight: 700;
            font-size: 12px !important;
            line-height: 21px;
            color: #192a3e;
        }

        .task_details {
            display: flex !important;
            align-items: center !important;
            margin-left: 0px !important;
        }

        .task_info {
            display: flex;
            flex-direction: column;
            margin-left: 0px !important;
        }

        .btn-completted {
            background-color: green;
            border: none;
            color: #fff;
            height: 40px;
            border-radius: 6px;
            width: 120px;
            cursor: pointer;
        }

        .showMessage {
            display: none;
            padding: 0px 10px 7px 10px;
            background: #c3e6cb;
            color: #155724;
            text-align: left;
        }

        .showMessage p {
            color: #155724;
        }
    </style>
@endsection
@section('section')

    <section class="dashboard_main">
        <div class="showMessage"></div>
        <div class="d-flex">
            <img loading="lazy" class="rounded mt-1 mr-3" height="48px" width="48px"
                src="{{ isset($brandings->profile_photo_path) ? Storage::url('user_images/' . $brandings->profile_photo_path) : asset('admin_assets/images/account.png') }}"
                alt="total clients">
            <div class="info">
                <p class="compnay_name"
                    style="font-size: 14px; color: rgba(124, 128, 145, 1) ; font-weight: 300">
                    {{ auth()->user()->isOrganization() ? auth()->user()->company_name : auth()->user()->name }}
                </p>
                <p class="profile_name">
                    Good Afternoon, <span style=" font-weight: 800"> {{ auth()->user()->name }}</span>
                </p>
            </div>
        </div>
        <div class="dashboard_stat_cards mt-4 pt-2">

            @can('dashboard_clients')
                {{-- <div class="state_card">
                    <div class="info">
                        <h2 class="card_title">Total Clients</h2>
                        <!-- if super admin -->
                        <!-- <h2 class="card_title">Total Companies</h2> -->
                        <p class="card_count mt-4">{{ custom_number_format($totalClients->count() ?? 0) }}</p>
                    </div>
                    <div>
                        <img loading="lazy" class="rounded" height="48px" width="48px"
                            src="{{ asset('admin_assets/images/icons/total-clients.png') }}" alt="total clients">
                    </div>
                </div> --}}
                <div class="state_card">
                    <div class="info">
                        <h2 class="card_title">Opportunity Requests</h2>
                        <!-- if super admin -->
                        <!-- <h2 class="card_title">Total Companies</h2> -->
                        <p class="card_count mt-4">
                            {{ custom_number_format($totalOpportunities->count() ?? 0) }}</p>
                    </div>
                    <div>
                        <img loading="lazy" class="rounded" height="48px" width="48px"
                            src="{{ asset('admin_assets/images/icons/estimate-requests.png') }}"
                            alt="total clients">
                    </div>
                </div>
            @endcan

            @can('dashboard_requests')
                <div class="state_card">
                    <div class="info">
                        <h2 class="card_title">Lost Jobs YTD</h2>
                        <!-- if super admin -->
                        <!-- <h2 class="card_title">Active Companies</h2> -->
                        <p class="card_count text-danger mt-4">
                            {{ custom_number_format($requestsCount ?? 0) }}</p>
                    </div>
                    <div>
                        <img loading="lazy" class="rounded" height="48px" width="48px"
                            src="{{ asset('admin_assets/images/jobs.png') }}" alt="total clients">
                    </div>
                </div>
            @endcan

            @can('dashboard_won')
                <div class="state_card">
                    <div class="info">
                        {{--                        <h2 class="card_title">Conversion Rate </h2> --}}
                        <h2 class="card_title">Close Rate </h2>
                        <!-- if super admin -->
                        <!-- <h2 class="card_title">Total Earning</h2> -->
                        <p class="card_count text-danger mt-4">
                            {{ custom_number_format($estimatesWonCount ?? 0) }}</p>
                    </div>
                    <div>
                        <img loading="lazy" class="rounded" height="48px" width="48px"
                            src="{{ asset('admin_assets/images/Group 1000004322.svg') }}"
                            alt="total clients">
                    </div>
                </div>
            @endcan


            @can('dashboard_won')
                <div class="state_card">
                    <div class="info">
                        <h2 class="card_title">Change Requests</h2>
                        <!-- if super admin -->
                        <!-- <h2 class="card_title">Total Earning</h2> -->
                        <p class="card_count mt-4">{{ custom_number_format($changeRequestCount ?? 0) }}</p>
                    </div>
                    <div>
                        <img loading="lazy" class="rounded" height="48px" width="48px"
                            src="{{ asset('admin_assets/images/check-pen.png') }}" alt="change requests">
                    </div>
                </div>
            @endcan


        </div>

        <div class="dashboard_sales_graphs">
            @can('dashboard_sales')
                <div class="card_wrapper chart_wrapper"
                    @cannot(['dashboard_estimate_losts', 'dashboard_sales']) style="grid-column:1/3" @endcannot>
                    <div class="chart_header">
                        <div class="info">
                            <!-- <h2 class="status_title">Total Sales</h2> -->
                            <p class="value mt-2">Total Sales</p>
                            <p class="value mt-2">${{ number_format($totalWonPrice, 2) }} <span
                                    class="indicator"><i
                                        class="fa-solid fa-arrow-trend-up"></i>{{ number_format($wonPercentage, 2) }}%</span>
                            </p>
                        </div>

                        <select class="selectBox form-control arrow no-border" name=""
                            onchange="getSelectedValue(this);">
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="year" selected>This Year</option>
                        </select>
                    </div>

                    <canvas id="totalSalesChart"
                        style="width:100% !important;object-fit:contain;max-height:300px;height:100%"></canvas>
                </div>
            @endcan
            <div class="card_wrapper chart_wrapper"
                @cannot(['dashboard_estimate_losts', 'dashboard_sales']) style="grid-column:1/3" @endcannot>
                <div class="task_header">
                    <div class="info">
                        <h2 class="status_title">Task</h2>
                    </div>
                    <button class="add_task_button task-btn emptytaskModalField" data-toggle="modal"
                        data-target="#taskModal" approve-btn>Add Task</button>
                </div>
                <div class="task_container">

                    @foreach ($tasks as $task)
                        <div class="task mb-3" style="padding-right: 11px; cursor: pointer"
                            data-task-id="{{ $task->id }}" data-toggle="modal"
                            data-target="#taskModalEdit"
                            onclick="updatetasksNow({{ $task->id }}, '{{ $task->assign_to_type }}')">
                            <div class="task_header">
                                <p class="task_description">{{ $task->subject }}</p>
                                <span class="task_date" style="color: #51566C">
                                    @if ($task->isDue())
                                        <b style="color: #A8ABB5">Due:</b>
                                        {{ \Carbon\Carbon::parse($task->end_date)->format('F j, Y') }}
                                        {{ \Carbon\Carbon::createFromFormat('H:i:s', $task->end_time)->format('h:i A') }}
                                    @else
                                        <b style="color: #A8ABB5">Due:</b> <label for=""
                                            style="color: #E91010;">
                                            {{ \Carbon\Carbon::parse($task->end_date)->format('F j, Y') }}
                                            {{ \Carbon\Carbon::createFromFormat('H:i:s', $task->end_time)->format('h:i A') }}</label>
                                    @endif
                                </span>
                            </div>
                            <div class="task_details">
                                <!-- <img class="rounded-circle" height="30" width="30"
                                        src="{{ optional($task->client)->image ? asset('storage/user_images/' . $task->client->image) : asset('admin_assets/images/dummy_image.webp') }}"
                                        alt="{{ optional($task->client)->first_name }}" class="task_image"> -->
                                <div class="task_info">
                                    @if ($task->assign_to_type == 'users')
                                        <p class="task_name">
                                            {{ optional($task->clientUser)->first_name }}
                                            {{ optional($task->clientUser)->last_name }}</p>
                                    @else
                                        <p class="task_name">{{ optional($task->client)->first_name }}
                                            {{ optional($task->client)->last_name }}</p>
                                    @endif
                                </div>
                            </div>
                            <hr>
                        </div>
                    @endforeach
                </div>
            </div>

            <div class="card_wrapper chart_wrapper"
                @cannot(['dashboard_estimate_losts', 'dashboard_sales']) style="grid-column:1/4" @endcannot>
                <div class="chart_header">
                    <div class="info">
                        <p class="value mt-2">Property Health</p>
                    </div>

                    <!-- <p class="value mt-2" style="color: #192A3E;">View All</p> -->

                    <!-- <select class="selectBox form-control arrow no-border" onchange="getval(this);" name=""
                                id="salesLost">
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                                <option value="year" selected>This Year</option>
                            </select> -->
                </div>
                <p style="text-align: center !important; color: #51566C;">Book of Business</p>

                <canvas id="doughnut"
                    style="width:100%;object-fit:contain;max-height:300px;height:100%"></canvas>

            </div>
            @can('dashboard_estimate_losts')
                <div class="card_wrapper chart_wrapper"
                    @cannot(['dashboard_estimate_losts', 'dashboard_sales']) style="grid-column:1/3" @endcannot>
                    <div class="chart_header">
                        <div class="info">
                            <h2 class="status_title">Statistics</h2>
                            <p class="value mt-2">Estimate Lost</p>
                        </div>

                        <select class="selectBox form-control arrow no-border" onchange="getval(this);"
                            name="" id="salesLost">
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="year" selected>This Year</option>
                        </select>
                    </div>

                    <canvas id="lostChart"
                        style="width:100%;object-fit:contain;max-height:300px;height:100%"></canvas>

                </div>
            @endcan
            @can('dashboard_wip')
                <div class="card_wrapper chart_wrapper">
                    <div class="chart_header">
                        <div class="info">
                            <h2 class="status_title">WIP</h2>
                            <p class="value mt-2">
                                ${{ custom_number_format($operationCompletedPrice ?? 0) }} <span
                                    class="indicator"><i
                                        class="fa-solid fa-caret-up"></i>{{ convertToShortFormat($operationPendingPrice) }}</span>
                            </p>
                        </div>
                        <ul class="legends aligned">
                            <li class="legend-list">
                                <span class="legend-span" id="accepted"></span>
                                <span class="legend-button" id="acceptLine">Completed</span>
                            </li>

                            <li class="legend-list">
                                <span class="legend-span" style="background-color:#2ECC40;"
                                    id="rejected"></span>
                                <span class="legend-button" id="rejectLine">Pending</span>
                            </li>
                        </ul>
                        <select class="selectBox form-control arrow no-border"
                            onchange="getSelectedValueWIPP(this);" name=""
                            id="double-chart-filter">
                            <option value="year" selected>This Year </option>
                            <option value="month">This Month</option>
                            <option value="week">This Week</option>
                        </select>
                    </div>

                    <canvas id="wipChart"
                        style="width:100%;object-fit:contain;max-height:300px;height:100%"></canvas>

                </div>

                <!-- <div class="card_wrapper chart_wrapper">
                            <div class="chart_header">
                                <div class="info">
                                    <h2 class="status_title">WIP</h2>
                                    <p class="value mt-2">${{ custom_number_format($operationCompletedPrice ?? 0) }} <span
                                            class="indicator"><i
                                                class="fa-solid fa-caret-up"></i>{{ convertToShortFormat($operationPendingPrice) }}</span>
                                    </p>
                                </div>
                                <ul class="legends aligned">
                                    <li class="legend-list">
                                        <span class="legend-span" id="accepted"></span>
                                        <span class="legend-button" id="acceptLine">Completed</span>
                                    </li>

                                    <li class="legend-list">
                                        <span class="legend-span" style="background-color:#2ECC40;" id="rejected"></span>
                                        <span class="legend-button" id="rejectLine">Pending</span>
                                    </li>
                                </ul>
                                <select class="selectBox form-control arrow no-border" name="" id="double-chart-filter">
                                    <option value="year" selected>This Year </option>
                                    <option value="month">This Month</option>
                                    <option value="week">This Week</option>
                                </select>
                            </div>

                            <canvas id="wipChart" style="width:100%;object-fit:contain;max-height:300px;height:100%"></canvas>

                        </div> -->
            @endcan
            <div class="card_wrapper chart_wrapper"
                style="display: grid; grid-template-columns: 1fr 2fr; gap: 20px; width: 100%; max-width: 1200px; margin: 0px 0 0 auto; height: 100%; background-color: white; padding: 20px; border-radius: 8px;">

                <!-- Static Number Boxes (1/3 of the screen) -->
                <div class="static_numbers"
                    style="display: flex; flex-direction: column; gap: 15px; justify-content: center; height: 100%;">
                    <p class="detail-heading">Customer Issues</p>

                    <div class="box" data-toggle="modal" data-target="#exampleModalOpen"
                        style="cursor: pointer; background-color: #ffe8c5a8; padding: 20px; border-radius: 10px; text-align: center; color: #F5A623;">
                        <h2 style="color: #3B4159; margin: 0; font-size: 32px; color: #F5A623">
                            {{ count($openissues) }}</h2>
                        <p style="color: #3B4159; margin: 5px 0 0; font-size: 16px; color: #F5A623">
                            Open Issue</p>
                    </div>
                    <div class="box" data-toggle="modal" data-target="#exampleModalPending"
                        style="cursor: pointer; background-color: #FFF7CF; padding: 20px; border-radius: 10px; text-align: center;">
                        <h2 style="color: #F1B602; margin: 0; font-size: 32px; color: #FCD024">
                            {{ count($pendingissues) }}</h2>
                        <p style="color: #F1B602; margin: 5px 0 0; font-size: 16px; color: #FCD024;">
                            Pending</p>
                    </div>
                    <div class="box" data-toggle="modal"
                        data-target="#exampleModalCompleted"
                        style="cursor: pointer; background-color: #E9F7E7; padding: 20px; border-radius: 10px; text-align: center;">
                        <h2 style="color: #1BB6D5; margin: 0; font-size: 32px; color: #22B14C;">
                            {{ count($completedissues) }}</h2>
                        <p style="color: #1BB6D5; margin: 5px 0 0; font-size: 16px; color: #22B14C;">
                            Completed</p>
                    </div>
                </div>
                <!-- Chart Section (2/3 of the screen) -->
                <div class="chart_section"
                    style="display: flex; justify-content: center; align-items: center; height: 100%;">
                    <canvas id="customerIssuesChart"
                        style="width:100%; height:100%; padding-top: 35px !important"></canvas>
                </div>
            </div>
        </div>


        <!-- <div class="card_wrapper chart_wrapper" style="display: grid; grid-template-columns: 1fr 2fr; gap: 20px; width: 50%; max-width: 1200px; margin: 10px 0 0 auto; height: 100%; background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">


            <div class="static_numbers" style="display: flex; flex-direction: column; gap: 15px; justify-content: center; height: 100%;">
            <p class="detail-heading">Customer Issues</p>

                <div class="box" style="background-color: #FFF2E2; padding: 20px; border-radius: 10px; text-align: center;">
                    <h2 style="color: #FF9900; margin: 0; font-size: 32px;">{{ $customerIssuesCount['Open Issue'] }}</h2>
                    <p style="color: #FF9900; margin: 5px 0 0; font-size: 16px;">Open Issue</p>
                </div>
                <div class="box" style="background-color: #FFF9D6; padding: 20px; border-radius: 10px; text-align: center;">
                    <h2 style="color: #FFDD00; margin: 0; font-size: 32px;">{{ $customerIssuesCount['Pending'] }}</h2>
                    <p style="color: #FFDD00; margin: 5px 0 0; font-size: 16px;">Pending</p>
                </div>
                <div class="box" style="background-color: #E9F8EB; padding: 20px; border-radius: 10px; text-align: center;">
                    <h2 style="color: #2ECC40; margin: 0; font-size: 32px;">{{ $customerIssuesCount['Completed'] }}</h2>
                    <p style="color: #2ECC40; margin: 5px 0 0; font-size: 16px;">Completed</p>
                </div>
            </div>

            <div class="chart_section" style="display: flex; justify-content: center; align-items: center; height: 100%;">
                <canvas id="customerIssuesChart" style="width:100%; height:100%; padding-top: 35px !important"></canvas>
            </div>
        </div> -->
        <!-- open issue modal -->
        <div class="modal fade select-client" id="exampleModalOpen" data-backdrop="static"
            data-keyboard="false" tabindex="-1" aria-labelledby="selectClientModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-large">
                <div class="modal-content">
                    <div class="modal-header" style="">
                        <h4 class="modal-title" id="addItemModalLabel"
                            style="color: black !important">
                            <b>Open Issue</b>
                        </h4>
                        <button type="button" class="btn-close" data-dismiss="modal"
                            aria-label="Close"
                            style="border: none; background-color: transparent; font-size: 23px !important;">
                            x
                        </button>
                    </div>

                    <div class="modal-body">
                        <div class="container-fluid">
                            <div class="row">
                                <div class="table-responsive" style="border-radius: 15px !important;">
                                    <table class="table table-stripped">
                                        <thead style="background-color: #DCF2FF; ">
                                            <tr>
                                                <th>{{ __('ISSUE #') }}</th>
                                                <th>{{ __('Subject') }}</th>
                                                <th>{{ __('Property Name') }}</th>
                                                <th>{{ __('Account') }}</th>
                                                <th>{{ __('Category') }}</th>

                                                <th>{{ __('Created By') }}</th>
                                                <th>{{ __('Date & Time') }}</th>

                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($openissues as $key => $issue)
                                                <tr>
                                                    <td>{{ $key + 1 }}</td>
                                                    <td><a href="{{ URL::route(getRouteAlias() . '.customer-issue.detail', ['id' => encodeId($issue?->id)]) }}"
                                                            style="text-decoration: underline; color: black;">{{ Str::limit($issue->subject, 30, '...') }}
                                                        </a>
                                                    </td>
                                                    <td>{{ $issue->prop_id ? DB::table('property_information')->where('id', $issue->prop_id)->value('name') : '---' }}
                                                    </td>
                                                    <td>{{ $issue->acc_id ? DB::table('accounts')->where('id', $issue->acc_id)->value('company_name') : '---' }}
                                                    </td>
                                                    <td>{{ $issue->category }}</td>
                                                    <td>{{ $issue->creator_full_name }}</td>
                                                    <td>{{ \Carbon\Carbon::parse($issue->created_at)->format('F d, Y') }}
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- pending  issue modal -->
        <div class="modal fade select-client" id="exampleModalPending" data-backdrop="static"
            data-keyboard="false" tabindex="-1" aria-labelledby="selectClientModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-large">
                <div class="modal-content">
                    <div class="modal-header" style="">
                        <h4 class="modal-title" id="addItemModalLabel"
                            style="color: black !important">
                            <b>Pending Issue</b>
                        </h4>
                        <button type="button" class="btn-close" data-dismiss="modal"
                            aria-label="Close"
                            style="border: none; background-color: transparent; font-size: 23px !important;">
                            x
                        </button>
                    </div>

                    <div class="modal-body">
                        <div class="container-fluid">
                            <div class="row">
                                <div class="table-responsive" style="border-radius: 15px !important;">
                                    <table class="table table-stripped">
                                        <thead style="background-color: #DCF2FF; ">
                                            <tr>
                                                <th>{{ __('ISSUE #') }}</th>
                                                <th>{{ __('Subject') }}</th>
                                                <th>{{ __('Property Name') }}</th>
                                                <th>{{ __('Account') }}</th>
                                                <th>{{ __('Category') }}</th>

                                                <th>{{ __('Created By') }}</th>
                                                <th>{{ __('Date & Time') }}</th>

                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($pendingissues as $key => $issue)
                                                <tr>
                                                    <td>{{ $key + 1 }}</td>
                                                    <td><a href="{{ URL::route(getRouteAlias() . '.customer-issue.detail', ['id' => encodeId($issue?->id)]) }}"
                                                            style="text-decoration: underline; color: black;">{{ Str::limit($issue->subject, 30, '...') }}</a>
                                                    </td>
                                                    <td>{{ $issue->prop_id ? DB::table('property_information')->where('id', $issue->prop_id)->value('name') : '---' }}
                                                    </td>
                                                    <td>{{ $issue->acc_id ? DB::table('accounts')->where('id', $issue->acc_id)->value('company_name') : '---' }}
                                                    </td>
                                                    <td>{{ $issue->category }}</td>
                                                    <td>{{ $issue->creator_full_name }}</td>
                                                    <td>{{ \Carbon\Carbon::parse($issue->created_at)->format('F d, Y') }}
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- completed  issue modal -->
        <div class="modal fade select-client" id="exampleModalCompleted" data-backdrop="static"
            data-keyboard="false" tabindex="-1" aria-labelledby="selectClientModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-large">
                <div class="modal-content">
                    <div class="modal-header" style="">
                        <h4 class="modal-title" id="addItemModalLabel"
                            style="color: black !important">
                            <b>Completed Issue</b>
                        </h4>
                        <button type="button" class="btn-close" data-dismiss="modal"
                            aria-label="Close"
                            style="border: none; background-color: transparent; font-size: 23px !important;">
                            x
                        </button>
                    </div>

                    <div class="modal-body">
                        <div class="container-fluid">
                            <div class="row">
                                <div class="table-responsive" style="border-radius: 15px !important;">
                                    <table class="table table-stripped">
                                        <thead style="background-color: #DCF2FF; ">
                                            <tr>
                                                <th>{{ __('ISSUE #') }}</th>
                                                <th>{{ __('Subject') }}</th>
                                                <th>{{ __('Property Name') }}</th>
                                                <th>{{ __('Account') }}</th>
                                                <th>{{ __('Category') }}</th>

                                                <th>{{ __('Created By') }}</th>
                                                <th>{{ __('Date & Time') }}</th>

                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($completedissues as $key => $issue)
                                                <tr>
                                                    <td>{{ $key + 1 }}</td>
                                                    <td><a href="{{ URL::route(getRouteAlias() . '.customer-issue.detail', ['id' => encodeId($issue?->id)]) }}"
                                                            style="text-decoration: underline; color: black;">{{ Str::limit($issue->subject, 30, '...') }}
                                                            </>
                                                    </td>

                                                    <td>{{ $issue->prop_id ? DB::table('property_information')->where('id', $issue->prop_id)->value('name') : '---' }}
                                                    </td>
                                                    <td>{{ $issue->acc_id ? DB::table('accounts')->where('id', $issue->acc_id)->value('company_name') : '---' }}
                                                    </td>
                                                    <td>{{ $issue->category }}</td>
                                                    <td>{{ $issue->creator_full_name }}</td>
                                                    <td>{{ \Carbon\Carbon::parse($issue->created_at)->format('F d, Y') }}
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>




        <!-- Green Health Property modal -->
        <div class="modal fade select-client" id="exampleModalGreen" data-backdrop="static"
            data-keyboard="false" tabindex="-1" aria-labelledby="selectClientModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-large">
                <div class="modal-content">
                    <div class="modal-header" style="">
                        <div>
                            <h4 class="modal-title" id="addItemModalLabel"
                                style="color: black !important">
                                <b>Green Property health</b>
                            </h4>
                        </div>

                        <div style="display: flex; gap: 20px;">
                            <svg width="78" height="25" viewBox="0 0 78 35" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <rect y="0.5" width="78" height="34" rx="8"
                                    fill="green" />
                            </svg>
                            <!-- <div style="width: 35px;
                            height: 35px;
                            top: 8px;
                            left: 16px;
                            gap: 0px;
                            opacity: 0px;
                            ">70%</div> -->
                            <button type="button" class="btn-close" data-dismiss="modal"
                                aria-label="Close"
                                style="border: none; background-color: transparent; font-size: 23px !important;">
                                x
                            </button>
                        </div>
                    </div>

                    <div class="modal-body">
                        <div class="container-fluid">
                            <div class="row">
                                <div class="table-responsive" style="border-radius: 15px !important;">
                                    <table class="table table-stripped">
                                        <thead style="background-color: #DCF2FF; ">
                                            <tr>

                                                <th>{{ __('Property Name') }}</th>
                                                <th>{{ __('Account') }}</th>
                                                <th>{{ __('Account Owner') }}</th>
                                                <th>{{ __('Property Type') }}</th>

                                                <th class="text-center">{{ __('Property Health') }}
                                                </th>



                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($greenhealthdata as $key => $issue)
                                                <tr>
                                                    <!-- <td>{{ $key + 1 }}</td> -->
                                                    <td><a href="{{ URL::route(getRouteAlias() . '.property.detail', ['id' => encodeId($issue?->id)]) }}"
                                                            style="text-decoration: underline; color: black;">{{ Str::limit($issue->property_name, 30, '...') }}
                                                        </a>
                                                    </td>

                                                    <td>{{ $issue->company_name }}
                                                    </td>
                                                    <td>{{ $issue->account_owner_name }}</td>
                                                    <td>Enhancements</td>
                                                    <td class="text-center">
                                                        <div class="dropdown">
                                                            <button
                                                                class="btn bg-transparent dropdown-toggle"
                                                                type="button"
                                                                id="colorDropdown_{{ $key }}"
                                                                data-toggle="dropdown"
                                                                aria-expanded="false">
                                                                <span class="dropdown-selected-color"
                                                                    style="display: inline-block; width: 20px; height: 20px; border-radius: 5px; background-color: green;"></span>
                                                            </button>
                                                            <ul class="dropdown-menu"
                                                                aria-labelledby="colorDropdown_{{ $key }}">
                                                                <li>
                                                                    <a class="dropdown-item d-flex align-items-center"
                                                                        href="#"
                                                                        onclick="selectColor({{ $key }}, 'green', {{ $issue->id }})"
                                                                        style="gap: 10px;">
                                                                        <span class="color-indicator"
                                                                            style="width: 20px; height: 20px; border-radius: 5px; background-color: green;"></span>
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item d-flex align-items-center"
                                                                        href="#"
                                                                        onclick="selectColor({{ $key }}, 'red', {{ $issue->id }})"
                                                                        style="gap: 10px;">
                                                                        <span class="color-indicator"
                                                                            style="width: 20px; height: 20px; border-radius: 5px; background-color: red;"></span>
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item d-flex align-items-center"
                                                                        href="#"
                                                                        onclick="selectColor({{ $key }}, 'yellow', {{ $issue->id }})"
                                                                        style="gap: 10px;">
                                                                        <span class="color-indicator"
                                                                            style="width: 20px; height: 20px; border-radius: 5px; background-color: yellow;"></span>
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>

                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- Yellow Health Property modal -->
        <div class="modal fade select-client" id="exampleModalYellow" data-backdrop="static"
            data-keyboard="false" tabindex="-1" aria-labelledby="selectClientModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-large">
                <div class="modal-content">
                    <div class="modal-header" style="">
                        <div>
                            <h4 class="modal-title" id="addItemModalLabel"
                                style="color: black !important">
                                <b>Yellow Property health</b>
                            </h4>
                        </div>

                        <div style="display: flex;
    gap: 20px;">
                            <svg width="78" height="25" viewBox="0 0 78 35" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <rect y="0.5" width="78" height="34" rx="8"
                                    fill="yellow" />
                            </svg>
                            <!-- <div style="width: 35px;
                            height: 35px;
                            top: 8px;
                            left: 16px;
                            gap: 0px;
                            opacity: 0px;
                            ">70%</div> -->
                            <button type="button" class="btn-close" data-dismiss="modal"
                                aria-label="Close"
                                style="border: none; background-color: transparent; font-size: 23px !important;">
                                x
                            </button>
                        </div>
                    </div>

                    <div class="modal-body">
                        <div class="container-fluid">
                            <div class="row">
                                <div class="table-responsive" style="border-radius: 15px !important;">
                                    <table class="table table-stripped">
                                        <thead style="background-color: #DCF2FF; ">
                                            <tr>

                                                <th>{{ __('Property Name') }}</th>
                                                <th>{{ __('Account') }}</th>
                                                <th>{{ __('Account Owner') }}</th>
                                                <th>{{ __('Property Type') }}</th>

                                                <th class="text-center">{{ __('Property Health') }}
                                                </th>



                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($yellowhealthdata as $key => $issue)
                                                <tr>
                                                    <!-- <td>{{ $key + 1 }}</td> -->
                                                    <td><a href="{{ URL::route(getRouteAlias() . '.property.detail', ['id' => encodeId($issue?->id)]) }}"
                                                            style="text-decoration: underline; color: black;">{{ Str::limit($issue->property_name, 30, '...') }}</a>
                                                    </td>

                                                    <td>{{ $issue->company_name }}
                                                    </td>
                                                    <td>{{ $issue->account_owner_name }}</td>
                                                    <td>Enhancements</td>
                                                    <td class="text-center">
                                                        <div class="dropdown">
                                                            <button
                                                                class="btn bg-transparent dropdown-toggle"
                                                                type="button"
                                                                id="colorDropdown_{{ $key }}"
                                                                data-toggle="dropdown"
                                                                aria-expanded="false">
                                                                <span class="dropdown-selected-color"
                                                                    style="display: inline-block; width: 20px; height: 20px; border-radius: 5px; background-color: yellow;"></span>
                                                            </button>
                                                            <ul class="dropdown-menu"
                                                                aria-labelledby="colorDropdown_{{ $key }}">
                                                                <li>
                                                                    <a class="dropdown-item d-flex align-items-center"
                                                                        href="#"
                                                                        onclick="selectColor({{ $key }}, 'green', {{ $issue->id }})"
                                                                        style="gap: 10px;">
                                                                        <span class="color-indicator"
                                                                            style="width: 20px; height: 20px; border-radius: 5px; background-color: green;"></span>
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item d-flex align-items-center"
                                                                        href="#"
                                                                        onclick="selectColor({{ $key }}, 'red', {{ $issue->id }})"
                                                                        style="gap: 10px;">
                                                                        <span class="color-indicator"
                                                                            style="width: 20px; height: 20px; border-radius: 5px; background-color: red;"></span>
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item d-flex align-items-center"
                                                                        href="#"
                                                                        onclick="selectColor({{ $key }}, 'yellow', {{ $issue->id }})"
                                                                        style="gap: 10px;">
                                                                        <span class="color-indicator"
                                                                            style="width: 20px; height: 20px; border-radius: 5px; background-color: yellow;"></span>
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>

                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>





        <!-- Red Health Property modal -->
        <div class="modal fade select-client" id="exampleModalRed" data-backdrop="static"
            data-keyboard="false" tabindex="-1" aria-labelledby="selectClientModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-large">
                <div class="modal-content">
                    <div class="modal-header" style="">
                        <div>
                            <h4 class="modal-title" id="addItemModalLabel"
                                style="color: black !important">
                                <b>Red Property health</b>
                            </h4>
                        </div>

                        <div style="display: flex;
    gap: 20px;">
                            <svg width="78" height="25" viewBox="0 0 78 35" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <rect y="0.5" width="78" height="34" rx="8"
                                    fill="red" />
                            </svg>
                            <!-- <div style="width: 35px;
                            height: 35px;
                            top: 8px;
                            left: 16px;
                            gap: 0px;
                            opacity: 0px;
                            ">70%</div> -->
                            <button type="button" class="btn-close" data-dismiss="modal"
                                aria-label="Close"
                                style="border: none; background-color: transparent; font-size: 23px !important;">
                                x
                            </button>
                        </div>
                    </div>

                    <div class="modal-body">
                        <div class="container-fluid">
                            <div class="row">
                                <div class="table-responsive" style="border-radius: 15px !important;">
                                    <table class="table table-stripped">
                                        <thead style="background-color: #DCF2FF; ">
                                            <tr>

                                                <th>{{ __('Property Name') }}</th>
                                                <th>{{ __('Account') }}</th>
                                                <th>{{ __('Account Owner') }}</th>
                                                <th>{{ __('Property Type') }}</th>

                                                <th class="text-center">{{ __('Property Health') }}
                                                </th>



                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($redhealthdata as $key => $issue)
                                                <tr>
                                                    <!-- <td>{{ $key + 1 }}</td> -->
                                                    <td><a href="{{ URL::route(getRouteAlias() . '.property.detail', ['id' => encodeId($issue?->id)]) }}"
                                                            style="text-decoration: underline; color: black;">{{ Str::limit($issue->property_name, 30, '...') }}</a>
                                                    </td>

                                                    <td>{{ $issue->company_name }}
                                                    </td>
                                                    <td>{{ $issue->account_owner_name }}</td>
                                                    <td>Enhancements</td>
                                                    <td class="text-center">
                                                        <div class="dropdown">
                                                            <button
                                                                class="btn bg-transparent dropdown-toggle"
                                                                type="button"
                                                                id="colorDropdown_{{ $key }}"
                                                                data-toggle="dropdown"
                                                                aria-expanded="false">
                                                                <span class="dropdown-selected-color"
                                                                    style="display: inline-block; width: 20px; height: 20px; border-radius: 5px; background-color: red;"></span>
                                                            </button>
                                                            <ul class="dropdown-menu"
                                                                aria-labelledby="colorDropdown_{{ $key }}">
                                                                <li>
                                                                    <a class="dropdown-item d-flex align-items-center"
                                                                        href="#"
                                                                        onclick="selectColor({{ $key }}, 'green', {{ $issue->id }})"
                                                                        style="gap: 10px;">
                                                                        <span class="color-indicator"
                                                                            style="width: 20px; height: 20px; border-radius: 5px; background-color: green;"></span>
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item d-flex align-items-center"
                                                                        href="#"
                                                                        onclick="selectColor({{ $key }}, 'red', {{ $issue->id }})"
                                                                        style="gap: 10px;">
                                                                        <span class="color-indicator"
                                                                            style="width: 20px; height: 20px; border-radius: 5px; background-color: red;"></span>
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item d-flex align-items-center"
                                                                        href="#"
                                                                        onclick="selectColor({{ $key }}, 'yellow', {{ $issue->id }})"
                                                                        style="gap: 10px;">
                                                                        <span class="color-indicator"
                                                                            style="width: 20px; height: 20px; border-radius: 5px; background-color: yellow;"></span>
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>

                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>



    </section>

    {{-- Task Modal --}}
    <div class="modal fade propert-modal" id="taskModalEdit" data-backdrop="static"
        data-keyboard="false" tabindex="-1" aria-labelledby="taskModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" style="max-width: 700px !important">
            <div class="modal-content" style="padding: 0px !important">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="taskModalLabel">Edit Task</h1>
                    <button type="button" class="btn-close hidemodelbtn px-3"
                        data-dismiss="modal" aria-label="Close"
                        style="border: none; background-color: transparent; margin-top: 1.5%;">
                        <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="body-wraped">
                        <form class="" action="{{ route('organization.tasks.update') }}"
                            method="POST">
                            @csrf
                            @method('POST')


                            <!-- Input fields in rows -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="filed-wraper">
                                        <label for="subject" class="modal-label">Subject</label>
                                        <input
                                            class="@error('subject') is-invalid @enderror modal-field subjectupdate"
                                            type="text" id="subject" name="subject"
                                            placeholder="Enter Subject" value="{{ old('subject') }}"
                                            required>
                                        @error('subject')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" class="tasts_id_get" name="tasks_id">

                            <div class="row mb-3">

                                <div class="col-md-6 mt-3">
                                    <div class="filed-wraper">
                                        <label for="end_date" class="modal-label">Due Date</label>
                                        <input
                                            class="@error('end_date') is-invalid @enderror modal-field input form-control datepicker-icon duedateupdate"
                                            required type="text" id="end_date" name="end_date"
                                            placeholder="Choose Date" readonly
                                            value="{{ old('end_date') }}">
                                        @error('end_date')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6 mt-3">
                                    <div class="filed-wraper">
                                        <label for="end_time" class="modal-label">Due Time</label>
                                        <input
                                            class="@error('end_time') is-invalid @enderror modal-field duetimeupdate"
                                            type="time" required id="end_time" name="end_time"
                                            placeholder="Choose Time" value="{{ old('end_time') }}">
                                        @error('end_time')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <?php
                            
                            ?>
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="filed-wraper">
                                        <label for="assign_to" class="modal-label">Assign To</label>
                                        <select
                                            class="@error('assign_to') is-invalid @enderror modal-field select_placeholder assigntoupdate"
                                            id="assign_to" name="assign_to">
                                            <option value="" class="select-placeholder"
                                                selected>Select</option>

                                            <!-- System Users Section -->
                                            <optgroup label="System Users">
                                                @forelse ($totalUsers as $client)
                                                    <option value="user-{{ $client->id }}">
                                                        {{ $client->first_name }}
                                                        {{ $client->last_name }}</option>
                                                @empty
                                                    <option value="">No System Users Available
                                                    </option>
                                                @endforelse
                                            </optgroup>

                                            <!-- Contacts Section -->
                                            {{-- <optgroup label="Contacts">
                                                @forelse ($totalClients as $client)
                                                    <option value="contact-{{ $client->id }}">{{ $client->first_name }} {{ $client->last_name }}</option>
                                                @empty
                                                    <option value="">No Contacts Available</option>
                                                @endforelse
                                            </optgroup> --}}
                                        </select>

                                        <!-- <select class="@error('assign_to') is-invalid @enderror modal-field select_placeholder assigntoupdate"
                                                id="assign_to" name="assign_to" >
                                                <option value="" class="select-placeholder" selected>Select</option>
                                                @forelse ($totalUsers as $client)
    <option value="{{ $client->id }}">{{ $client->first_name }}</option>

                                        @empty
                                                    <option value="1">Test Client</option>
    @endforelse
                                                <label for="">Contacts</label>
                                                @forelse ($totalClients as $client)
    <option value="{{ $client->id }}">{{ $client->first_name }}</option>

                                        @empty
                                                    <option value="1">Test Client</option>
    @endforelse
                                                </select> -->
                                        @error('assign_to')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Buttons -->
                            <div class="row mt-3"
                                style="display: flex; justify-content: space-between;">
                                <button type="button" onclick="taskscompleted()"
                                    class="btn-completted" data-dismiss="modal"
                                    style="margin-top: 8px;">Complete</button>
                                <div class="text-end mt-3">

                                    <button type="button" class="cancel-button"
                                        data-dismiss="modal">Cancel</button>
                                    <button type="submit" class="create-button">Update</button>
                                </div>
                            </div>


                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Task Modal --}}
    <div class="modal fade propert-modal" id="taskModal" data-backdrop="static"
        data-keyboard="false" tabindex="-1" aria-labelledby="taskModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" style="max-width: 700px !important">
            <div class="modal-content" style="padding: 0px !important">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="taskModalLabel">New Task</h1>
                    <button type="button" class="btn-close hidemodelbtn px-3"
                        data-dismiss="modal" aria-label="Close"
                        style="border: none; background-color: transparent; margin-top: 1.5%;">
                        <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="body-wraped">
                        <form class="formtaskModal" action="{{ route('organization.tasks.store') }}"
                            method="POST">
                            @csrf
                            @method('POST')


                            <!-- Input fields in rows -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="filed-wraper">
                                        <label for="subject" class="modal-label">Subject</label>
                                        <input
                                            class="@error('subject') is-invalid @enderror modal-field"
                                            type="text" id="subject" name="subject"
                                            placeholder="Enter Subject" value="{{ old('subject') }}"
                                            required>
                                        @error('subject')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">

                                <div class="col-md-6 mt-3">
                                    <div class="filed-wraper">
                                        <label for="end_date" class="modal-label">Due Date</label>
                                        <input
                                            class="@error('end_date') is-invalid @enderror modal-field input form-control datepicker-icon"
                                            type="text" id="end_date_create" name="end_date"
                                            placeholder="Choose Date" required readonly
                                            value="{{ old('end_date') }}">
                                        <span class="show_date_first" style="color: #dc3545"></span>
                                        @error('end_date')
                                            <span
                                                class="invalid-feedback show_date_first">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6 mt-3">
                                    <div class="filed-wraper">
                                        <label for="end_time" class="modal-label">Due Time</label>
                                        <input
                                            class="@error('end_time') is-invalid @enderror modal-field"
                                            type="time" id="end_time" name="end_time"
                                            placeholder="Choose Time" required
                                            value="{{ old('end_time') }}">
                                        @error('end_time')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <?php
                            
                            ?>
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="filed-wraper">
                                        <label for="assign_to" class="modal-label">Assign To</label>

                                        <select
                                            class="@error('assign_to') is-invalid @enderror modal-field select_placeholder "
                                            id="assign_to" name="assign_to">
                                            <option value="" class="select-placeholder"
                                                selected>Select</option>

                                            <!-- System Users Section -->
                                            <optgroup label="System Users">
                                                @forelse ($totalUsers as $client)
                                                    <option value="user-{{ $client->id }}">
                                                        {{ $client->first_name }}
                                                        {{ $client->last_name }}</option>
                                                @empty
                                                    <option value="">No System Users Available
                                                    </option>
                                                @endforelse
                                            </optgroup>

                                            <!-- Contacts Section -->
                                            {{-- <optgroup label="Contacts">
                                                @forelse ($totalClients as $client)
                                                    <option value="contact-{{ $client->id }}">{{ $client->first_name }} {{ $client->last_name }}</option>
                                                @empty
                                                    <option value="">No Contacts Available</option>
                                                @endforelse
                                            </optgroup> --}}
                                        </select>
                                        <!-- <select class="@error('assign_to') is-invalid @enderror modal-field select_placeholder"
                                                id="assign_to" name="assign_to" >
                                                <option value="" class="select-placeholder" selected>Select</option>
                                                @forelse ($totalClients as $client)
    <option value="{{ $client->id }}">{{ $client->first_name }}</option>

                                        @empty
                                                    <option value="1">Test Client</option>
    @endforelse
                                                </select> -->
                                        @error('assign_to')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Buttons -->
                            <div class="row mt-3">
                                <div class="col-12 text-end mt-3">
                                    <button type="button" class="cancel-button"
                                        data-dismiss="modal">Cancel</button>
                                    <button type="submit" class="create-button">Create</button>
                                </div>
                            </div>


                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>


    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            // jQuery function to clear form fields
            $('.emptytaskModalField').on('click', function() {
                $('.formtaskModal')[0].reset(); // Reset all fields in the form
            });
        </script>
        <script>
            function taskscompleted() {
                var id = $('.tasts_id_get').val();
                $.ajax({
                    type: 'GET',
                    url: '{{ route('organization.tasks.completed') }}',
                    data: {
                        id: id
                    },
                    success: function(data) {
                        $('.showMessage').css('display', 'flex');
                        $(`.task[data-task-id="${id}"]`).remove();
                        // After 3 seconds (3000ms), fade it out
                        setTimeout(function() {
                            $('.showMessage').fadeOut('slow', function() {
                                // Optional: Reset the display property to 'none' after fading out
                                $(this).css('display', 'none');
                            });
                        }, 3000);
                        $('.showMessage').html('');
                        $('.showMessage').append(
                            `<p class="para mt-3 text-center">Task Completed Successfully!</p>`
                        );

                        console.info(data);
                    }
                });


            }

            function updatetasksNow(id, type) {
                $.ajax({
                    url: "{{ route('organization.getTaskNow') }}",
                    method: "GET",
                    data: {
                        id: id,
                        type: type,
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(data) {
                        console.info(data);
                        // location.reload();
                        $('.subjectupdate').val(data.subject);
                        //$('.duedateupdate').val(data.end_date);
                        // data.end_date format example: '2024-11-12' (ISO format)
                        let rawDate = data.end_date;

                        // Convert the date string to a JavaScript Date object
                        let dateObject = new Date(rawDate);

                        let formattedDate = dateObject.toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                        });

                        $('.duedateupdate').val(formattedDate);

                        $('.duetimeupdate').val(data.end_time);
                        $('.tasts_id_get').val(data.id);
                        // $('.assigntoupdate').val(data.assign_to).change();
                        if (data.assign_to_type == "users") {
                            $('.assigntoupdate').val(`user-${data.assign_to}`).change();
                        } else if (data.assign_to_type == "contacts") {
                            $('.assigntoupdate').val(`contact-${data.assign_to}`).change();
                        }
                    }
                });

            }
        </script>
        <script>
            $("#start_date").datepicker({
                startDate: "today",
                autoclose: true, // Set the minimum date to today
                format: "MM d, yyyy"
            });
            $("#end_date").datepicker({
                startDate: "today",
                autoclose: true, // Set the minimum date to today
                format: "MM d, yyyy"
            });
            //                 $("#end_date_create").datepicker({
            //     startDate: "today", // Prevent past dates
            //     autoclose: true,    // Close datepicker after selecting a date
            //     format: "MM d, yyyy" // Display format
            // }).on("changeDate", function(e) {
            //     // Ensure the input's value is updated for validation
            //     $(this).val(e.format("MM d, yyyy")).prop("required", true);
            // });

            $("#end_date_create").datepicker({
                startDate: "today",
                autoclose: true,
                format: "MM d, yyyy"
            }).on("changeDate", function(e) {
                $(this).val(e.format("MM d, yyyy"));
            });

            // Custom form validation
            $(".formtaskModal").on("submit", function(e) {
                const endDate = $("#end_date_create").val();

                if (!endDate) {
                    e.preventDefault(); // Prevent form submission

                    // alert("Please select a due date.");
                    $(".show_date_first").text('The end date field is required.');
                }
            });



            // $("#end_date_create").datepicker({
            //     startDate: "today",
            //     autoclose: true, // Set the minimum date to today
            //     format: "MM d, yyyy"
            // });
        </script>
        <script>
            function selectColor(rowKey, color, id) {
                // Find the specific dropdown button in the row
                const dropdownButton = document.getElementById(`colorDropdown_${rowKey}`);
                const selectedIndicator = dropdownButton.querySelector('.dropdown-selected-color');

                // Update the color of the indicator
                selectedIndicator.style.backgroundColor = color;

                // Optional: Log the selected color for debugging
                console.log(`Row ${rowKey} color selected: ${color}`);

                $.ajax({
                    url: '{{ route('organization.update.propertyHealth') }}', // Your route to handle the update
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}', // CSRF token for security
                        property_health: color, // The selected value from the dropdown
                        id: id
                    },
                    success: function(response) {
                        if (response.success) {
                            //   alert('Property Health updated successfully!');
                            location.reload();
                        } else {
                            alert('Error updating property health.');
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('AJAX request failed: ' + error);
                    }
                });
            }
        </script>
        <script>
            const selectFields = document.querySelectorAll('.select_placeholder');

            // Loop through all the select elements with this class
            selectFields.forEach(selectField => {
                selectField.addEventListener('change', function() {
                    if (this.value === "") {
                        // Placeholder style
                        // console.info(this.value);
                        this.classList.add('validss');
                    } else {
                        // Valid style
                        console.info(this.value);
                        this.classList.remove('validss');

                    }
                });
                if (selectField.value === "") {
                    selectField.classList.add('validss');
                } else {
                    selectField.classList.remove('validss');

                }
            });
        </script>

        <script>
            const inputs = document.querySelectorAll('input[type="date"], input[type="time"]');

            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    if (this.value) {
                        this.classList.remove(
                            'placeholder-shown'); // Remove placeholder style
                    } else {
                        this.classList.add('placeholder-shown'); // Add placeholder style
                    }
                });

                // Initialize placeholder state on page load
                if (!input.value) {
                    input.classList.add('placeholder-shown');
                } else {
                    input.classList.remove('placeholder-shown');
                }
            });
        </script>
        <script type="text/javascript">
            var salesLostFilter = 'year';
            var barColors = ["#109CF1"];
            var chart3 = null;
            var chart4 = null;
            var chart5 = null;
            var waveformChart = null;




            function ajaxCAll(url, filter) {

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {
                        filter: salesLostFilter,
                        "_token": "{{ csrf_token() }}",
                    },


                    success: function(chartData) {

                        if (chartData.labels && chartData.data) {
                            if (url === '{{ route(getRouteAlias() . '.lostChartData') }}') {
                                renderLostChart(chartData.labels, chartData.data);
                                console.info('chartData1');
                            }
                            if (url ===
                                '{{ route(getRouteAlias() . '.totalSalesChartData') }}') {
                                totalSalesChart(chartData.labels, chartData.data);
                                console.info('chartData2');
                            }

                        }

                        if (chartData.labels && chartData.datasets) {
                            renderWipChart(chartData.labels, chartData.datasets);
                            // console.info('chartData3');

                        }
                    },
                    error: function(response) {

                        $.each(response.responseJSON.errors, function(field_name, error) {

                        });
                    }
                })

            }

            @can('dashboard_estimate_losts')
                ajaxCAll('{{ route(getRouteAlias() . '.lostChartData') }}', salesLostFilter);
            @endcan

            @can('dashboard_sales')
                ajaxCAll('{{ route(getRouteAlias() . '.totalSalesChartData') }}', salesLostFilter);
            @endcan

            @can('dashboard_wip')
                ajaxCAll('{{ route(getRouteAlias() . '.wipChartData') }}', salesLostFilter);
            @endcan

            function convertValueToShortForm(value) {
                const suffixes = ["", "K", "M", "B", "T", "Q"];
                const suffixNum = Math.floor((("" + value).length - 1) / 3);
                const divisor = Math.pow(1000, suffixNum);
                const shortValue = value / divisor;

                let formattedValue = shortValue.toFixed(1);
                if (shortValue >= 1000) {
                    formattedValue = shortValue.toFixed(
                        0); // Remove decimal for values greater than or equal to 1000
                }

                return '$' + formattedValue + suffixes[suffixNum];
            }




            function renderLostChart(labels, data) {
                if (chart3) {
                    chart3.destroy(); // Destroy the existing bar chart instance if it exists
                }

                chart3 = new Chart("lostChart", {
                    type: "line",
                    data: {
                        labels: labels,
                        datasets: [{
                            // data: yValues,

                            borderRadius: 10 // set border radius to 10
                        }, {
                            type: "bar",
                            data: data,
                            borderRadius: 6,
                            barThickness: 25,
                            borderColor: "#1C7FD7", // change border color to a darker shade of blue
                            borderWidth: 0,
                            fill: true,
                            pointRadius: 0,
                            backgroundColor: barColors,

                        }]
                    },
                    options: {
                        layout: {
                            padding: {
                                top: 0 // add a margin of 25 pixels to the top of the chart
                            },

                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false, // remove x-axis line
                                    clip: false
                                }

                            },
                            y: {
                                ticks: {
                                    beginAtZero: false,
                                    stepSize: 100,
                                    // min: 100,
                                    // max: 300,

                                    callback: function(value, index, values) {
                                        return convertValueToShortForm(value);
                                    },
                                    font: {
                                        size: 12,
                                        color: '#90A0B7',
                                        family: 'Poppins',
                                    }
                                },
                                grid: {
                                    display: true,
                                },
                                borderColor: 'rgba(0, 0, 0, 0)', // Remove y-axis lines
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';

                                        if (label) {
                                            label += ': ';
                                        }
                                        if (context.parsed.y !== null) {
                                            label += new Intl.NumberFormat('en-US', {
                                                style: 'currency',
                                                currency: 'USD'
                                            }).format(context.parsed.y);
                                        }
                                        return label;
                                    }
                                }
                            },
                            legend: {
                                display: false
                            },
                            title: {
                                display: false,
                                text: "World Wine Production 2018"
                            },
                        }
                    }
                });
            }

            function totalSalesChart(labels, data) {
                if (waveformChart) {
                    waveformChart.destroy(); // Destroy the existing waveform chart instance if it exists
                }
                waveformChart = new Chart('totalSalesChart', {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: data,
                            borderColor: "#109CF1",
                            fill: false,
                            tension: 0.4,
                            pointBackgroundColor: "white",
                            pointBorderColor: "#2ECC40",
                            pointBorderWidth: 2,
                            pointRadius: [0, 3, 3, 3, 3, 0, 0],
                            spanGaps: true, // add gap for last data point
                        }]
                    },
                    options: {
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';

                                        if (label) {
                                            label += ': ';
                                        }
                                        if (context.parsed.y !== null) {
                                            label += new Intl.NumberFormat('en-US', {
                                                style: 'currency',
                                                currency: 'USD'
                                            }).format(context.parsed.y);
                                        }
                                        return label;
                                    }
                                }
                            },
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                ticks: {
                                    beginAtZero: false,
                                    stepSize: 100,
                                    // min: 100,
                                    // max: 300,
                                    callback: function(value, index, values) {
                                        return convertValueToShortForm(value);
                                    },
                                    font: {
                                        size: 12,
                                        color: '#90A0B7',
                                        family: 'Poppins',
                                    }
                                },
                                grid: {
                                    display: true,
                                },
                                borderColor: 'rgba(0, 0, 0, 0)', // Remove y-axis lines
                            },
                            x: {
                                grid: {
                                    display: false,
                                    //   borderDash: [10, 5],
                                    //   borderWidth: 1.02,
                                    borderColor: '#E0E0E0'
                                },
                            }
                        }
                    },
                });
            }

            // Function to render the double bar chart
            function renderWipChart(labels, datasets) {
                if (chart4) {
                    // alert('yesss');
                    chart4.destroy(); // Destroy the existing bar chart instance if it exists
                }
                chart4 = new Chart("wipChart", {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: datasets
                    },

                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        height: 340,
                        layout: {
                            padding: {
                                left: 0,
                                right: 10,
                                top: 30,
                                bottom: 4
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';

                                        if (label) {
                                            label += ': ';
                                        }
                                        if (context.parsed.y !== null) {
                                            label += new Intl.NumberFormat('en-US', {
                                                style: 'currency',
                                                currency: 'USD'
                                            }).format(context.parsed.y);
                                        }
                                        return label;
                                    }
                                }
                            },
                            legend: {
                                display: false,
                            },
                        },
                        scales: {
                            x: {
                                stacked: false,
                                grid: {
                                    display: false
                                },
                            },
                            y: {
                                ticks: {
                                    beginAtZero: false,
                                    stepSize: 100,
                                    min: 100,
                                    max: 300,
                                    callback: function(value, index, values) {
                                        return convertValueToShortForm(value);
                                    },
                                    font: {
                                        size: 12,
                                        color: '#90A0B7',
                                        family: 'Poppins',
                                    }
                                },
                                grid: {
                                    display: true,
                                },
                                borderColor: 'rgba(0, 0, 0, 0)', // Remove y-axis lines
                            },

                        },
                        barThickness: 8,
                    }

                });

                if (chart5) {
                    // alert('yesss');
                    chart5.destroy(); // Destroy the existing bar chart instance if it exists
                }
                const dataValues = [{{ $redhealth }}, {{ $yellowhealth }}, {{ $greenhealth }}];

                // Check if all values are zero
                const totalValues = dataValues.reduce((a, b) => a + b, 0);

                const chartData = totalValues > 0 ? dataValues : [
                    1
                ]; // If all values are zero, use [1] for a full grey segment
                const chartColors = totalValues > 0 ? ['rgba(255, 75, 85, 1)', 'rgba(254, 220, 102, 1)',
                    'rgba(70, 210, 85, 1)'
                ] : ['rgba(200, 200, 200, 1)']; // Grey color for no data

                chart5 = new Chart("doughnut", {
                    type: 'doughnut',
                    data: {
                        // labels: totalValues > 0 ? labels : [''], // Label for grey segment
                        datasets: [{
                            data: chartData,
                            backgroundColor: chartColors, // Dynamic color based on data
                            borderColor: '#fff',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        cutout: '60%',
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        let label = context.label || '';
                                        let value = context.raw;

                                        if (label) {
                                            label += ': ';
                                        }
                                        if (value !== null) {
                                            label += new Intl.NumberFormat('en-US', {
                                                style: 'currency',
                                                currency: 'USD'
                                            }).format(value);
                                        }
                                        return label;
                                    }
                                }
                            },
                            legend: {
                                display: true,
                                labels: {
                                    font: {
                                        size: 12,
                                        color: '#90A0B7',
                                        family: 'Poppins',
                                    }
                                }
                            },
                        },
                        onClick: (event, elements) => {
                            if (totalValues > 0 && elements.length > 0) {
                                // Get the clicked segment index
                                const segmentIndex = elements[0].index;

                                // Check which area was clicked
                                if (segmentIndex === 0) {
                                    openPopup('Red');
                                } else if (segmentIndex === 1) {
                                    openPopup('Yellow');
                                } else if (segmentIndex === 2) {
                                    openPopup('Green');
                                }
                            } else {
                                console.log('No data to interact with');
                            }
                        }
                    }
                });


                //                     const dataValues = [{{ $redhealth }}, {{ $yellowhealth }}, {{ $greenhealth }}];


                // chart5 = new Chart("doughnut", {
                //     type: 'doughnut',
                //     data: {
                //         labels: labels,
                //         datasets: [{
                //             data: dataValues,
                //             backgroundColor: ['rgba(255, 75, 85, 1)', 'rgba(254, 220, 102, 1)', 'rgba(70, 210, 85, 1)'], // Colors
                //             borderColor: '#fff',
                //             borderWidth: 1
                //         }]
                //     },
                //     options: {
                //         responsive: true,
                //         maintainAspectRatio: true,
                //         cutout: '60%',
                //         plugins: {
                //             tooltip: {
                //                 callbacks: {
                //                     label: function(context) {
                //                         let label = context.label || '';
                //                         let value = context.raw;

                //                         if (label) {
                //                             label += ': ';
                //                         }
                //                         if (value !== null) {
                //                             label += new Intl.NumberFormat('en-US', {
                //                                 style: 'currency',
                //                                 currency: 'USD'
                //                             }).format(value);
                //                         }
                //                         return label;
                //                     }
                //                 }
                //             },
                //             legend: {
                //                 display: true,
                //                 labels: {
                //                     font: {
                //                         size: 12,
                //                         color: '#90A0B7',
                //                         family: 'Poppins',
                //                     }
                //                 }
                //             },
                //         },
                //         onClick: (event, elements) => {
                //             if (elements.length > 0) {
                //                 // Get the clicked segment index
                //                 const segmentIndex = elements[0].index;

                //                 // Check which area was clicked
                //                 if (segmentIndex === 0) {
                //                     // Red area clicked
                //                     openPopup('Red');
                //                 } else if (segmentIndex === 1) {
                //                     // Yellow area clicked
                //                     openPopup('Yellow');
                //                 } else if (segmentIndex === 2) {
                //                     // Green area clicked
                //                     openPopup('Green');
                //                 }
                //             }
                //         }
                //     }
                // });



                // chart5 = new Chart("doughnut", {
                // type: 'doughnut',
                // data: {
                //     labels: labels,
                //     datasets: [{
                //         data: dataValues,
                //         backgroundColor: ['rgba(255, 75, 85, 1)', 'rgba(254, 220, 102, 1)', 'rgba(70, 210, 85, 1)'], // Three different colors
                //         borderColor: '#fff',
                //         borderWidth: 1
                //     }]
                // },
                // options: {
                //     responsive: true,
                //     maintainAspectRatio: true,
                //     cutout: '60%', // Adjust the cutout size if needed
                //     plugins: {
                //         tooltip: {
                //             callbacks: {
                //                 label: function(context) {
                //                     let label = context.label || '';
                //                     let value = context.raw;

                //                     if (label) {
                //                         label += ': ';
                //                     }
                //                     if (value !== null) {
                //                         label += new Intl.NumberFormat('en-US', {
                //                             style: 'currency',
                //                             currency: 'USD'
                //                         }).format(value);
                //                     }
                //                     return label;
                //                 }
                //             }
                //         },
                //         legend: {
                //             display: true,
                //             labels: {
                //                 font: {
                //                     size: 12,
                //                     color: '#90A0B7',
                //                     family: 'Poppins',
                //                 }
                //             }
                //         },
                //         // Custom plugin to display total value in the center
                //         afterDraw: function(chart) {
                //             const ctx = chart.ctx;
                //             const width = chart.width;
                //             const height = chart.height;
                //             const totalValue = chart.data.datasets[0].data.reduce((a, b) => a + b, 0);

                //             ctx.restore();
                //             const fontSize = (height / 114).toFixed(2);
                //             ctx.font = fontSize + "em Poppins";
                //             ctx.textBaseline = "middle";

                //             const text = new Intl.NumberFormat('en-US', {
                //                 style: 'currency',
                //                 currency: 'USD'
                //             }).format(totalValue);

                //             const textX = Math.round((width - ctx.measureText(text).width) / 2);
                //             const textY = height / 2;

                //             ctx.fillText(text, textX, textY);
                //             ctx.save();
                //         }
                //     }
                // },

                // });

            }

            // Function to open popup based on the segment
            function openPopup(color) {
                // alert(color + " area clicked!");
                if (color == 'Green') {
                    $('#exampleModalGreen').modal('show');
                } else if (color == 'Yellow') {
                    $('#exampleModalYellow').modal('show');
                } else if (color == 'Red') {
                    $('#exampleModalRed').modal('show');
                }

            }




            function getval(object) {
                salesLostFilter = object.value;
                console.info(salesLostFilter);
                ajaxCAll('{{ route(getRouteAlias() . '.lostChartData') }}', salesLostFilter);
            }

            function getSelectedValue(object) {
                salesLostFilter = object.value;
                ajaxCAll('{{ route(getRouteAlias() . '.totalSalesChartData') }}', salesLostFilter);
            }
            // function getSelectedValueWIPP(object) {
            //     salesLostFilter = object.value;
            //     console.info(salesLostFilter);
            //     ajaxCAll('{{ route(getRouteAlias() . '.wipChartData') }}', salesLostFilter);
            // }
            $('#double-chart-filter').change(function() {
                salesLostFilter = $(this).val();
                ajaxCAll('{{ route(getRouteAlias() . '.wipChartData') }}', salesLostFilter);
            });


            // Sample dynamic data, can be replaced by backend data
            const dynamicData = @json($customerIssuesCount);
            console.log(dynamicData); // [Open Issues, Pending, Completed]

            // Chart.js setup
            const ctx = document.getElementById('customerIssuesChart').getContext('2d');
            const customerIssuesChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Open Issue', 'Pending', 'Completed'],
                    datasets: [{
                        label: 'Customer Issues',
                        data: [{{ count($openissues) }}, {{ count($pendingissues) }},
                            {{ count($completedissues) }}
                        ], // Dynamic data array
                        // backgroundColor: ['#3B4159', '#F1B602', '#1BB6D5'], // Colors for each bar
                        backgroundColor: ['#FFE8C5 ', '#FFF7CF',
                            '#FCD024'
                        ], // Colors for each bar
                        borderWidth: 1,
                        fill: false

                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 50 // Adjust this based on the range of your data
                        }
                    },
                    plugins: {
                        legend: {
                            display: false // Hide the default legend
                        }
                    }
                }
            });
        </script>
    @endpush
@endsection
