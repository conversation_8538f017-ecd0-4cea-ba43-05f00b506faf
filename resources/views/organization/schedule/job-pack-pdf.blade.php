<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <title>Job Pack PDF</title>
</head>
<style>
    @font-face {
        font-family: 'Poppins';
        src: url({{ storage_path('fonts/Poppins/Poppins-Regular.ttf') }}) format("truetype");
        font-weight: 400;
        font-style: normal;
    }

    @font-face {
        font-family: 'Poppins';
        src: url({{ storage_path('fonts/Poppins/Poppins-Bold.ttf') }}) format("truetype");
        font-weight: 700;
        font-style: normal;
    }

    @font-face {
        font-family: 'Poppins';
        src: url({{ storage_path('fonts/Poppins/Poppins-SemiBold.ttf') }}) format("truetype");
        font-weight: 600;
        font-style: normal;
    }

    @font-face {
        font-family: 'Poppins';
        src: url({{ storage_path('fonts/Poppins/Poppins-Medium.ttf') }}) format("truetype");
        font-weight: 500;
        font-style: normal;
    }

    /* @page :first {
        margin: 0px 0px;
    } */

    /* Define the margins of your page for all other pages */
    @page {
        margin: 0px 0px;
        margin-top: 50px
    }

    header {
        position: fixed;
        top: -50px;
        left: 0px;
        right: 0px;
        height: 50px;
        font-size: 20px !important;
        color: white;
        text-align: center;
        line-height: 35px;
    }

    footer {
        position: fixed;
        bottom: 0px;
        left: 0px;
        right: 0px;
        height: 50px;
        font-size: 20px !important;
        color: white;
        text-align: center;
        line-height: 35px;
    }

    .custom_datatable .email{
        text-transform: none !important;
    }


    .w-100 {
        width: 100%;
    }

    .w-70 {
        width: 70%;
    }

    .w-50 {
        width: 50%;
    }

    .w-30 {
        width: 30%;
    }

    .float-left {
        float: left;
    }

    .float-right {
        float: right;
    }

    .mt-28 {
        margin-top: 28px;
    }

    .text-left {
        text-align: left;
    }

    .text-right {
        text-align: right;
    }

    .text-center {
        text-align: center;
    }

    body {
        margin: 0;
        padding: 0;
        /* overflow: hidden; */
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
    }

    .bottom_bar {
        position: fixed;
        width: 100%;
        bottom: 0px;
        left: 0px;
        z-index: 20;
    }

    .top_bar .primary_header,
    .bottom_bar .bar {
        height: 16px;
        width: 100%;
        background: {{ $primary_color }};
        position: relative;
    }

    .top_bar .primary_header_notch {
        transform: rotate(41.59deg);
        position: absolute;
        right: -59px;
        top: -17px;
    }

    .top_bar .primary_header_notch .notch_1 {
        height: 86px;
        width: 188px;
        background: {{ $secondary_color }};
        /* background: #003366; */
    }

    .top_bar .primary_header_notch .notch_2 {
        height: 12px;
        width: 188px;
        background: {{ $primary_color }};
        /* background: #FE9B6A; */
    }

    .header {
        margin-top: -50px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 153px;
    }

    .header .proposal_no {
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;
        text-transform: capitalize;
        color: #707683;
    }

    .header .proposal_no .value {
        color: #192a3e;
    }

    .placeholder-text {
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;
        text-transform: capitalize;
        color: #707683;
    }

    .address,
    .zip_code {
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        text-transform: capitalize;
        color: #192a3e;
    }

    .main_heading {
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        text-transform: capitalize;
        color: #192a3e;
    }

    .spacing-0 {
        padding: 0px !important;
        margin: 0px !important;
    }

    .mt-4 {
        margin-top: 4px;
    }

    .mb-12 {
        margin-bottom: 12px;
    }

    .mt-16 {
        margin-top: 16px;
    }

    .mt-24 {
        margin-top: 24px;
    }

    .mt-49 {
        margin-top: 49px;
    }

    .vertical-top {
        vertical-align: top;
    }

    .vertical-middle {
        vertical-align: middle;
    }

    /* table style */

    .custom_datatable {
        box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px,
            rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
        background: #ffffff;
        border-radius: 8px;
    }

    .table_wrapper {
        border: 1px solid #e7e7e7;
        border-radius: 8px;
        overflow: hidden;
    }

    .custom_datatable thead th {
        /* padding: 16px !important; */
        vertical-align: middle;
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 22px;
        color: #192a3e;

        background: #dcf2ff;
        border-bottom: none !important;
        white-space: nowrap;
        user-select: none;
        margin: 0px;
        padding-left: 12px;
        padding-bottom: 12px;
        padding-right: 12px;
    }

    .custom_datatable thead th:first-child {
        border-top-left-radius: 8px;
    }

    .custom_datatable thead th:last-child {
        border-top-right-radius: 8px;
    }

    .custom_datatable tbody td {
        padding: 14px !important;
        vertical-align: middle;
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 10px;
        line-height: 10px;
        color: #192a3e;
        border: none !important;
        word-wrap: break-word;
        word-break: break-word;

        margin: 0px;
        /* padding-left: 12px;
            padding-bottom: 12px;
            padding-right: 12px; */
    }

    .custom_datatable tbody tr:nth-child(even) td {
        background-color: #f9f9f9 !important;
    }

    .panel {
        background: #ffffff;
        border: 1px solid #e7e7e7;
        border-radius: 8px;
        padding: 16px;
    }

    .vertical_line {
        border: none;
        border-bottom: 1px solid #e7e7e7;
    }

    .leading-16 {
        line-height: 16px;
    }

    .mb-24 {
        margin-bottom: 24px;
    }

    .mb-16 {
        margin-bottom: 16px;
    }

    .mb-8 {
        margin-bottom: 8px;
    }

    .page-break {
        page-break-before: always;
    }

    .mt-32 {
        margin-top: 32px;
    }

    .content_wrapper {
        padding: 44px 40px
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    hr {
        margin: 10px 0;
        color: inherit;
        background-color: #D7D7D7;
        border: 0;
        opacity: .25;
    }

    hr:not([size]) {
        height: 1px;
    }

    /* table style end */
</style>

<style>
    p {
        margin: 0;
        font-weight: 400;
        font-size: 16px;
        line-height: 26px;
    }

    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
        appearance: none;
    }

    label.label {
        color: var(--Additional, #90A0B7);
        font-family: Cabin;
        font-size: 10px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        /* 150% */
    }

    .border-line {
        border: 2px solid #D7D7D7;
        margin-top: 20px
    }

    .fields-container {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        margin-top: 30px;
    }

    .fields-container .time-field {
        width: fit-content;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px #EBEBEB solid;
        position: relative;
    }

    .fields-container .time-field .remove-time-field {
        position: absolute;
        top: -7px;
        right: -3px;
        background: transparent;
        border: none;
        width: fit-content;
        height: fit-content;
    }

    .fields-container .time-field .fa-xmark {
        color: red;
        font-size: 14px;
    }

    .fields-container .time-field .partial-field {
        /* width: 103px; */
        padding: 6px 16px 15px 16px;
    }

    .fields-container .time-field .partial-field .time-field-label,
    .fields-container .time-field .partial-field .time-field-input,
    .fields-container .time-field .field-header {
        font-family: "Cabin";
        font-size: 14px;
        font-weight: 400;
        font-style: normal;
        line-height: 14px;
        color: #868686;
    }

    .fields-container .time-field .field-header {
        padding: 8px;
        background-color: #EBF2F6;
        text-align: center;
        color: #202020;
    }

    .fields-container .time-field .partial-field .time-field-input {
        width: 100%;
        text-align: center;
        color: #192A3E;
        padding: 8px;
        border-top: none;
        border-left: none;
        border-right: none;
        border-bottom: none;
    }

    .fields-container .time-field .partial-field .time-field-input::placeholder {
        color: #C2C2C2;
    }

    .add-time-field {
        background-color: transparent;
        width: 40px !important;
        height: 40px !important;
        /* margin: auto 0; */
        border: none;
        outline: none;
        padding: 0 !important;
    }

    .add-time-field .fa-solid {
        color: #ffffff;
        font-size: 20px;
    }

    .notes,
    .sub_heading.job-pack,
    .panel_title.panel_sub_title {
        font-family: "Cabin";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        text-transform: capitalize;
        color: #192A3E;
    }

    .sub_heading.job-pack {
        font-size: 30px;
        line-height: normal;
        color: #036;
    }

    .panel_title.panel_sub_title {
        font-size: 20px;
        color: #192A3E;
    }

    .note-detail,
    .special-notes {
        font-family: "Cabin";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
        word-wrap: break-word;
        color: #192A3E;
    }

    .special-notes {
        border-radius: 12px;
        resize: none;
        height: calc(100% - 30px);
        width: 100%;
        padding: 13px 14px;
    }

    .special-notes::placeholder {
        color: #E9E9E9;
    }

    @media (min-width: 576px) {
        #scheduleDetailModal .modal-dialog {
            max-width: calc(100% - 100px) !important;
        }
    }

    .div-content-scrollable {
        max-height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .div-content-scrollable .vendor-input {
        margin-top: 8px;
        border: none;
        font-size: 12px;
    }

    .input {
        background: var(--white);
        border: 1px solid var(--bordercolor);
        border-radius: 6px;
        width: 100%;
        padding: 8px 12px;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        height: 40px;
    }

    .form-control {
        display: block;
        width: 100%;
        padding: 3.75px 7.5px;
        font-size: 10px;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border-radius: 2.5px;
        transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    button,
    input,
    optgroup,
    select,
    textarea {
        margin: 0;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
    }

    .panel {
        background: #ffffff;
        box-shadow: 0px 0px 12px rgba(36, 185, 236, 0.08);
        border-radius: 8px;
        padding: 24px;
        /* height: 100%; */
    }

    .panel .panel_header,
    .panel_top .panel_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        flex-wrap: wrap;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .gap-3 {
        gap: 10px !important;
    }

    .panel_title.panel_sub_title {
        font-size: 20px;
        color: #192A3E;
    }

    .notes,
    .sub_heading.job-pack,
    .panel_title.panel_sub_title {
        font-family: "Cabin";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        text-transform: capitalize;
        color: #192A3E;
    }

    .panel_title {
        font-family: "Cabin";
        font-style: normal;
        font-weight: 600;
        font-size: 24px;
        line-height: 36px;
        text-transform: capitalize;
        color: #003366;
        word-break: break-all;
    }

    .d-flex {
        display: flex !important;
    }

    .gap-2 {
        gap: 5px !important;
    }

    .label {
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: block;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        user-select: none;
    }

    .border-0 {
        border: 0 !important;
    }

    .border-line {
        border: 2px solid #D7D7D7;
        margin-top: 20px;
    }

    .row {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
    }

    .row-2 {
        display: grid;
        grid-template-columns: 1fr 1fr;
    }


    .row-4 {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
    }

    .row-5 {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    }

    .align-items-center {
        align-items: center !important;
    }

    .mt-30 {
        margin-top: 30px;
    }

    h2 {
        margin-top: 0;
    }

    .gap-5 {
        gap: 30px !important;
    }


    .w-100 {
        width: 100%;
    }

    .w-70 {
        width: 70%;
    }

    .w-50 {
        width: 50%;
    }

    .w-30 {
        width: 30%;
    }

    .float-left {
        float: left;
    }

    .float-right {
        float: right;
    }

    .mt-28 {
        margin-top: 28px;
    }

    .text-left {
        text-align: left;
    }

    .text-right {
        text-align: right;
    }

    .text-center {
        text-align: center;
    }

    body {
        margin: 0;
        padding: 0;
        /* overflow: hidden; */
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
    }

    .bottom_bar {
        position: fixed;
        width: 100%;
        bottom: 0px;
        left: 0px;
        z-index: 20;
    }

    .top_bar .primary_header,
    .bottom_bar .bar {
        height: 16px;
        width: 100%;
        background: {{ $primary_color }};
        position: relative;
    }

    .top_bar .primary_header_notch {
        transform: rotate(41.59deg);
        position: absolute;
        right: -59px;
        top: -17px;
        background: {{ $secondary_color }};
    }

    .top_bar .primary_header_notch .notch_1 {
        height: 86px;
        width: 188px;
        background: {{ $secondary_color }};
        /* background: #003366; */
    }

    .top_bar .primary_header_notch .notch_2 {
        height: 12px;
        width: 188px;
        background: {{ $primary_color }};
        /* background: #FE9B6A; */
    }

    .header {
        /* margin-top: 28px; */
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 153px;
    }

    .header .proposal_no {
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;
        text-transform: capitalize;
        color: #707683;
    }

    .header .proposal_no .value {
        color: #192a3e;
    }

    .placeholder-text {
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;
        text-transform: capitalize;
        color: #707683;
    }

    .title_name {
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 24px;
        text-transform: capitalize;
        color: #003366;
    }

    .address,
    .zip_code {
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        text-transform: capitalize;
        color: #192a3e;
    }

    .main_heading {
        font-family: "Poppins", sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        text-transform: capitalize;
        color: #192a3e;
    }

    .spacing-0 {
        padding: 0px !important;
        margin: 0px !important;
    }

    .mt-4 {
        margin-top: 4px;
    }

    .mb-12 {
        margin-bottom: 12px;
    }

    .mt-16 {
        margin-top: 16px;
    }

    .mt-24 {
        margin-top: 24px;
    }

    .mt-49 {
        margin-top: 49px;
    }

    .vertical-top {
        vertical-align: top;
    }

    .vertical-middle {
        vertical-align: middle;
    }

    /* table style */

    .custom_datatable {
        box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px,
            rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
        background: #ffffff;
        border-radius: 8px;
    }

    .table_wrapper {
        border: 1px solid #e7e7e7;
        border-radius: 8px;
        overflow: hidden;
    }

    .custom_datatable thead th {
        /* padding: 16px !important; */
        vertical-align: middle;
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 22px;
        color: #192a3e;

        background: #dcf2ff;
        border-bottom: none !important;
        white-space: nowrap;
        user-select: none;
        margin: 0px;
        padding-left: 12px;
        padding-bottom: 12px;
        padding-right: 12px;
    }

    .custom_datatable thead th:first-child {
        border-top-left-radius: 8px;
    }

    .custom_datatable thead th:last-child {
        border-top-right-radius: 8px;
    }

    .custom_datatable tbody td {
        padding: 14px !important;
        vertical-align: middle;
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 10px;
        line-height: 10px;
        color: #192a3e;
        border: none !important;
        word-wrap: break-word;
        word-break: break-word;

        margin: 0px;
        /* padding-left: 12px;
            padding-bottom: 12px;
            padding-right: 12px; */
    }

    .custom_datatable tbody tr:nth-child(even) td {
        background-color: #f9f9f9 !important;
    }

    .panel {
        background: #ffffff;
        border: 1px solid #e7e7e7;
        border-radius: 8px;
        padding: 16px;
    }

    .vertical_line {
        border: none;
        border-bottom: 1px solid #e7e7e7;
    }

    .leading-16 {
        line-height: 16px;
    }

    .mb-24 {
        margin-bottom: 24px;
    }

    .mb-16 {
        margin-bottom: 16px;
    }

    .mb-8 {
        margin-bottom: 8px;
    }

    .page-break {
        page-break-before: always;
    }

    .total_payable .title {
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        color: #192a3e;
    }

    .total_payable .value {
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 24px;
        line-height: 40px;
        color: #003366;
    }

    .approval_title {
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;
        text-transform: capitalize;
        color: #192a3e;
    }

    .approval_items .sign {
        margin-right: 16px;
    }

    .approval_items .signature {
        width: 100%;
        border-top: 1px dashed #90a0b7;
    }

    .approval_items .sign_title {
        font-family: "Poppins", sans-serif;
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;
        text-transform: capitalize;
        color: #707683;
    }

    .mt-32 {
        margin-top: 32px;
    }

    .content_wrapper {
        padding: 44px 40px
    }

    .mt-10 {
        margin-top: 7px !important;
    }
</style>

<body>
    <header>
        <div class="top_bar c_header">
            <div class="primary_header"></div>

            <div class="primary_header_notch">
                <div class="notch_1"></div>
                <div class="notch_2"></div>
            </div>
        </div>
    </header>

    <footer class="bottom_bar mt-24 ">
        <table class="table" style="width:100%;">
            <tr>
                <td
                    style="
                    width:50%;
                    text-align:left;
                    font-family: 'Poppins';
                    font-style: normal;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 22px;
                    color: #90A0B7;
                    padding-left:44px;">
                    Powered by <span style="color: #003366;text-transform: uppercase;">{{ config('app.name') }}</span>
                </td>
                <td
                    style="
                    width:50%;
                    text-align:right;
                    font-family: 'Poppins';
                    font-style: normal;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 22px;
                    text-transform: uppercase;
                    color: #90A0B7;
                    padding-right:44px;">
                    Confidential</td>
            </tr>
        </table>
        <div class="bar"></div>
    </footer>

    <div class="content_wrapper div-content-scrollable">

        <section class="dashboard_main pb-5">
            <div class="header w-100">
                <table class="table w-100">
                    <tr>
                        <th class="w-50 float-left text-left">
                        <img loading="lazy" class="rounded mt-1 mr-3" height="47px" width="47px"
                        src="{{ isset($brandings->profile_photo_path) ? Storage::url('user_images/' . $brandings->profile_photo_path) : asset('admin_assets/images/account.png') }}" alt="total clients">
                            <!-- <img loading="lazy" height="47px" src="{{ $logo }}" alt="site logo" /> -->
                        </th>
                        <th class="w-50 float-left">
                            <p class="proposal_no w-100">
                                PO# <span class="value">{{ $costSummary->po_number ?? '--------' }}</span>
                            </p>
                        </th>
                    </tr>
                </table>
            </div>

            <div style="clear: both"></div>

            <div class="table_filter_header mb-4">
                <div class="d-flex align-items-center gap-3">
                    <h2 class="sub_heading job-pack">Job Pack</h2>
                </div>
            </div>
            <div style="clear: both"></div>
            <div class="panel mt-4">
                <div class="panel_header justify-content-between gap-3">
                    <h2 class="panel_title panel_sub_title">Job Information</h2>
                </div>

                <div class="w-100 mt-16">
                    <table class="table w-100">
                        <tr>
                            <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                <p class="placeholder-text text-left spacing-0">
                                    Job Name
                                </p>
                                <h2 class="title_name text-left spacing-0 mt-4">
                                    {{ $generate_estimate?->request?->job_name ?: '---' }}
                                </h2>
                            </th>
                            <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                <p class="placeholder-text text-left spacing-0 mt-4">
                                    Job Number
                                </p>
                                <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                                    {{ $generate_estimate?->request?->job_number ?: '---' }}
                                </p>
                            </th>
                            <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                <p class="placeholder-text text-left spacing-0">
                                    Client Contact
                                </p>
                                <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                                    {{ $generate_estimate->client?->mobile_no ?? '---' }}
                                </p>
                            </th>
                        </tr>
                    </table>
                </div>

                <div style="clear: both"></div>

                <div class="w-100 mt-16">
                    <table class="table w-100">
                        <tr>
                            <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                <p class="placeholder-text text-left spacing-0">
                                    Ops Manager
                                </p>
                                <h2 class="address text-left spacing-0 mt-4">
                                    {{ $generate_estimate?->manager?->name ?? '---' }}
                                </h2>
                            </th>
                            <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                <p class="placeholder-text text-left spacing-0 mt-4">
                                    Salesperson Name
                                </p>
                                <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                                    {{ $generate_estimate?->saleMan?->name ?? '---' }}
                                </p>
                            </th>
                            <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                <p class="placeholder-text text-left spacing-0">
                                    Estimator Name
                                </p>
                                <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                                    {{ $generate_estimate?->estimator?->name ?? '---' }}
                                </p>
                            </th>
                        </tr>
                    </table>
                </div>

                <div style="clear: both"></div>


                <div class="w-100 mt-16">
                    <table class="table w-100">
                        <tr>
                            <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                <p class="placeholder-text text-left spacing-0">
                                    Job Sold Date
                                </p>
                                <h2 class="address text-left spacing-0 mt-4">
                                    {{ $generate_estimate?->won_date ? customDateFormat(\Carbon\Carbon::parse($generate_estimate?->won_date ?? now())) : '----' }}
                                </h2>
                            </th>
                            <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                <p class="placeholder-text text-left spacing-0 mt-4">
                                    Start Date
                                </p>
                                <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                                    {{ $generate_estimate?->created_at ? customDateFormat(\Carbon\Carbon::parse($generate_estimate?->created_at ?? now())) : '----' }}
                                </p>
                            </th>
                            <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                <p class="placeholder-text text-left spacing-0">
                                    Completion Date
                                </p>
                                <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                                    ---
                                </p>
                            </th>
                        </tr>
                    </table>
                </div>

                <div style="clear: both"></div>

                <hr class="border-line">

                <div class="w-100 mt-16">
                    <table class="table w-100">
                        <tr>
                            <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                <p class="placeholder-text text-left spacing-0">
                                    Site Address
                                </p>
                            </th>
                            <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                <p class="placeholder-text text-left spacing-0 mt-4">
                                    {{ $propertyAddress?->address1 ?: '----' }}
                                </p>
                            </th>
                        </tr>
                    </table>
                </div>
                <hr class="border-line">

                <div style="clear: both"></div>


                <div class="row mt-30">
                    <div class="col-6 panel_header">
                        <h2 class="panel_title panel_sub_title">Labor details</h2>
                    </div>
                    <div class="w-100 mt-16">
                        <table class="table w-100">
                            <tr>
                                <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                    <p class="placeholder-text text-left spacing-0">
                                        Estimated Hours
                                    </p>
                                    <p class="address text-left spacing-0 mt-4">

                                        @php
                                            $laborHours =
                                                ($estimatePlantMaterial->count() > 0
                                                    ? $estimatePlantMaterial
                                                        ->map(function ($item) {
                                                            // Calculate the total_cost by adding quantity and material install
                                                            return $item->quantity + ($item->material ? $item->material->install : 0);
                                                        })
                                                        ->sum()
                                                    : 0) +
                                                ($estimateHardMaterial->count() > 0
                                                    ? $estimateHardMaterial
                                                        ->map(function ($item) {
                                                            // Calculate the total_cost by adding quantity and material labor
                                                            return $item->quantity + ($item->material ? $item->material->labor : 0);
                                                        })
                                                        ->sum()
                                                    : 0);
                                        @endphp
                                        {{ custom_number_format($laborHours) }} hours
                                        @php
                                            $scheduledHours = $estimateLabor->where('labor.name', 'Laborers')->sum(function ($item) {
                                                $scheduledHours = data_get($item, 'labor_hours_info.scheduled_hours');
                                                return is_array($scheduledHours) ? array_sum(array_filter($scheduledHours, fn($value) => $value !== null)) : 0;
                                            });

                                            $actualHours = $estimateLabor->where('labor.name', 'Laborers')->sum(function ($item) {
                                                $actualHours = data_get($item, 'labor_hours_info.actual_hours');
                                                return is_array($actualHours) ? array_sum(array_filter($actualHours, fn($value) => $value !== null)) : 0;
                                            });

                                        @endphp
                                    </p>
                                </th>
                                <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                    <p class="placeholder-text text-left spacing-0 mt-4">
                                        Schedule labor Hours
                                    </p>
                                    <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                                        {{ $scheduledHours }}
                                        hours
                                    </p>
                                </th>
                                <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                    <p class="placeholder-text text-left spacing-0">
                                        Acutal Man Hours
                                    </p>
                                    <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                                        {{ $actualHours }} hours
                                    </p>
                                </th>
                            </tr>
                        </table>
                    </div>

                    <div style="clear: both"></div>

                </div>
                <hr class="border-line">
                <div style="clear: both"></div>
            </div>
            @if ($estimateLabor->where('labor.name', 'Laborers')->count() > 0)

                @forelse ($estimateLabor->where('labor.name','Laborers') as $key=>$row)
                    <div class="w-100 mt-16 {{ $key != 0 ? 'mt-30' : '' }}">
                        <table class="table w-100">
                            <tr>
                                <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                    <p class="placeholder-text text-left spacing-0">
                                        Quantity
                                    </p>
                                    <h2 class="address text-left spacing-0 mt-4">
                                        {{ $row->quantity ?? '---' }}
                                    </h2>
                                </th>
                                <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                    <p class="placeholder-text text-left spacing-0 mt-4">
                                        UOM
                                    </p>
                                    <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                                        {{ $row->uom ?? ($row?->labor?->uom ?? '---') }}
                                    </p>
                                </th>
                                <th class="float-left spacing-0 vertical-top" style="width: 33%">
                                    <p class="placeholder-text text-left spacing-0">
                                        Description
                                    </p>
                                    <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                                        {{ $row->description ?? '---' }}
                                    </p>
                                </th>
                            </tr>
                        </table>
                    </div>

                    <div style="clear: both"></div>

                    <div class="fields-container" style="transform: scale(0.7);margin-left:-125px">
                        @if ($row->labor_hours_info)
                            @for ($i = 0; $i < count($row?->labor_hours_info?->scheduled_hours); $i++)
                                @if ($row?->labor_hours_info?->scheduled_hours[$i] || $row?->labor_hours_info?->actual_hours[$i])
                                    <div class="time-field" style=" display:inline-block"
                                        data-target-id="{{ encodeID($row->id) }}">
                                        <h2 class="field-header">Day {{ $i + 1 }}</h2>
                                        <div style="display: inline-block;width:fit-content">
                                            <div class="partial-field" style="display: inline-block;">
                                                <label class="time-field-label">Scheduled</label>
                                                <p class="placeholder-text text-left spacing-0 mt-10 text-center">
                                                    {{ $row?->labor_hours_info?->scheduled_hours[$i] ?? '---' }}
                                                </p>
                                            </div>
                                            <div class="partial-field" style="display: inline-block;">
                                                <label class="time-field-label">Actual</label>
                                                <p class="placeholder-text text-left spacing-0 mt-10 text-center">
                                                    {{ $row?->labor_hours_info?->actual_hours[$i] ?? '---' }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @endfor
                        @else
                        @endif
                    </div>
                @empty
                @endforelse
            @endif

            <div class="row-2">
                <div class="col-6">
                    <h2 class="notes">Notes</h2>
                    <p class="note-detail mt-3"> {!! $generate_estimate->notes ?? '---' !!} </p>
                </div>
                <div class="col-6">
                    <h2 class="notes">Special Notes</h2>
                    <p class="note-detail mt-3"> {{ $generate_estimate->special_notes ?? '---' }} </p>
                </div>
            </div>

            @if ($estimateMaterial->count() > 0)
                <div style="page-break-inside: avoid;">
                    <div class="panel_header justify-content-between gap-3 mt-30">
                        <h2 class="panel_title panel_sub_title">Equipments</h2>
                    </div>
                    <div style="clear: both"></div>
                    <div class="table_wrapper mt-24">
                        <table class="table custom_datatable display" style="width: 100%" cellpadding="0"
                            cellspacing="0">
                            <thead>
                                <tr>
                                    <th class="text-left" width="30%">Name</th>
                                    <th class="text-left">Quantity</th>
                                    <th class="text-left">UOM</th>
                                    <th class="text-left" width="30%"></th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($estimateMaterial as $key=>$item)
                                    <tr>
                                        <td> {{ $item->equipment->name }}
                                            <hr>
                                            <label class="label">Vendor Name</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->equipmentVName ?? '---' }}
                                            </p>
                                        </td>
                                        <td>{{ $item->quantity }}
                                            <hr>
                                            <label class="label">Vendor Contact</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->equipmentCName ?? '---' }}
                                            </p>
                                        </td>
                                        <td>{{ $item->uom ?? $item->equipment->uom }}
                                            <hr>
                                            <label class="label">Vendor Phone</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->equipmentPhone ?? '---' }}
                                            </p>

                                        </td>
                                        <td>
                                            <span style="visibility: hidden">+</span>
                                            <hr>
                                            <label class="label">Vendor Email</label>
                                            <p class="email placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->equipmentEmail ?? '---' }}
                                            </p>
                                        </td>
                                    </tr>
                                @empty
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>

            @endif

            @if ($estimateLabor->count() > 0)
                <div style="page-break-inside: avoid;">
                    <div class="panel_header justify-content-between gap-3 mt-30">
                        <h2 class="panel_title panel_sub_title">Labors</h2>
                    </div>
                    <div style="clear: both"></div>
                    <div class="table_wrapper mt-24">
                        <table class="table custom_datatable display" style="width: 100%" cellpadding="0"
                            cellspacing="0">
                            <thead>
                                <tr>
                                    <th class="text-left" width="30%">Name</th>
                                    <th class="text-left">Quantity</th>
                                    <th class="text-left">UOM</th>
                                    <th class="text-left" width="30%"></th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($estimateLabor as $key=>$item)
                                    <tr>
                                        <td> {{ $item->labor?->name }}
                                            <hr>
                                            <label class="label">Vendor Name</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->laborVName ?? '---' }}
                                            </p>
                                        </td>
                                        <td>{{ $item->quantity }}
                                            <hr>
                                            <label class="label">Vendor Contact</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->laborCName ?? '---' }}
                                            </p>

                                        </td>
                                        <td>{{ $item->labor?->uom }}
                                            <hr>
                                            <label class="label">Vendor Phone</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->laborPhone ?? '---' }}
                                            </p>

                                        </td>
                                        <td>
                                            <span style="visibility: hidden">+</span>
                                            <hr>
                                            <label class="label">Vendor Email</label>
                                            <p class="email placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->laborEmail ?? '---' }}
                                            </p>
                                        </td>
                                    </tr>
                                @empty
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>


            @endif

            @if ($estimatePlantMaterial->count() > 0)
                <div style="page-break-inside: avoid;">
                    <div class="panel_header justify-content-between gap-3 mt-30">
                        <h2 class="panel_title panel_sub_title">Plant Material</h2>
                    </div>
                    <div style="clear: both"></div>
                    <div class="table_wrapper mt-24">
                        <table class="table custom_datatable display" style="width: 100%" cellpadding="0"
                            cellspacing="0">
                            <thead>
                                <tr>
                                    <th class="text-left" width="30%">Name</th>
                                    <th class="text-left">Quantity</th>
                                    <th class="text-left">UOM</th>
                                    <th class="text-left" width="30%"></th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($estimatePlantMaterial as $key=>$item)
                                    <tr>
                                        <td> {{ $item->material->name }}
                                            <hr>
                                            <label class="label">Vendor Name</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->plantMaterialVName ?? '---' }}
                                            </p>
                                        </td>
                                        <td>{{ $item->quantity }}
                                            <hr>
                                            <label class="label">Vendor Contact</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->plantMaterialCName ?? '---' }}
                                            </p>
                                        </td>
                                        <td>{{ $item->uom ?? $item->material->uom }}
                                            <hr>
                                            <label class="label">Vendor Phone</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->plantMaterialPhone ?? '---' }}
                                            </p>
                                        </td>
                                        <td>
                                            <span style="visibility: hidden">+</span>
                                            <hr>
                                            <label class="label">Vendor Email</label>
                                            <p class="email placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->plantMaterialEmail ?? '---' }}
                                            </p>
                                        </td>
                                    </tr>
                                @empty
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif

            @if ($estimateHardMaterial->count() > 0)
                <div style="page-break-inside: avoid;">
                    <div class="panel_header justify-content-between gap-3 mt-30">
                        <h2 class="panel_title panel_sub_title">Hard Material</h2>
                    </div>
                    <div style="clear: both"></div>
                    <div class="table_wrapper mt-24">
                        <table class="table custom_datatable display" style="width: 100%" cellpadding="0"
                            cellspacing="0">
                            <thead>
                                <tr>
                                    <th class="text-left" width="30%">Name</th>
                                    <th class="text-left">Quantity</th>
                                    <th class="text-left">UOM</th>
                                    <th class="text-left" width="30%"></th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($estimateHardMaterial as $key=>$item)
                                    <tr>
                                        <td> {{ $item->material->name }}
                                            <hr>
                                            <label class="label">Vendor Name</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->hardMaterialVName ?? '---' }}
                                            </p>

                                        </td>
                                        <td>{{ $item->quantity }}
                                            <hr>
                                            <label class="label">Vendor Contact</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->hardMaterialCName ?? '---' }}
                                            </p>
                                        </td>
                                        <td>{{ $item->uom ?? $item->material->uom }}
                                            <hr>
                                            <label class="label">Vendor Phone</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->hardMaterialPhone ?? '---' }}
                                            </p>

                                        </td>
                                        <td> <span style="visibility: hidden">+</span>
                                            <hr>
                                            <label class="label">Vendor Email</label>
                                            <p class="email placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->hardMaterialEmail ?? '---' }}
                                            </p>
                                        </td>
                                    </tr>
                                @empty
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif

            @if ($estimateOtherCost->count() > 0)
                <div style="page-break-inside: avoid;">
                    <div class="panel_header justify-content-between gap-3 mt-30">
                        <h2 class="panel_title panel_sub_title">Other Cost</h2>
                    </div>
                    <div style="clear: both"></div>
                    <div class="table_wrapper mt-24">
                        <table class="table custom_datatable display" style="width: 100%" cellpadding="0"
                            cellspacing="0">
                            <thead>
                                <tr>
                                    <th class="text-left" width="30%">Name</th>
                                    <th class="text-left">Quantity</th>
                                    <th class="text-left">UOM</th>
                                    <th class="text-left" width="30%"></th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($estimateOtherCost as $key=>$item)
                                    <tr>
                                        <td> {{ $item->otherCost->name }}
                                            <hr>
                                            <label class="label">Vendor Name</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->otherCostVName ?? '---' }}
                                            </p>

                                        </td>
                                        <td>{{ $item->quantity }}
                                            <hr>
                                            <label class="label">Vendor Contact</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->otherCostCName ?? '---' }}
                                            </p>

                                        </td>
                                        <td>{{ $item->uom }}
                                            <hr>
                                            <label class="label">Vendor Phone</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->otherCostPhone ?? '---' }}
                                            </p>

                                        </td>
                                        <td>
                                            <span style="visibility: hidden">+</span>
                                            <hr>
                                            <label class="label">Vendor Email</label>
                                            <p class="email placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->otherCostEmail ?? '---' }}
                                            </p>

                                        </td>
                                    </tr>
                                @empty
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif

            @if ($estimateSubContractor->count() > 0)
                <div style="page-break-inside: avoid;">
                    <div class="panel_header justify-content-between gap-3 mt-30">
                        <h2 class="panel_title panel_sub_title">Sub Contractor</h2>
                    </div>
                    <div style="clear: both"></div>
                    <div class="table_wrapper mt-24">
                        <table class="table custom_datatable display" style="width: 100%" cellpadding="0"
                            cellspacing="0">
                            <thead>
                                <tr>
                                    <th class="text-left" width="30%">Name</th>
                                    <th class="text-left">Quantity</th>
                                    <th class="text-left">UOM</th>
                                    <th class="text-left" width="30%"></th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($estimateSubContractor as $key=>$item)
                                    <tr>
                                        <td> {{ $item->name }}
                                            <hr>
                                            <label class="label">Vendor Name</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->subcontactorVName ?? '---' }}
                                            </p>
                                        </td>
                                        <td>{{ $item->quantity }}
                                            <hr>
                                            <label class="label">Vendor Contact</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->subcontactorCName ?? '---' }}
                                            </p>

                                        </td>
                                        <td>{{ $item->uom }}
                                            <hr>
                                            <label class="label">Vendor Phone</label>
                                            <p class="placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->subcontactorPhone ?? '---' }}
                                            </p>

                                        </td>
                                        <td>
                                            <span style="visibility: hidden">+</span>
                                            <hr>
                                            <label class="label">Vendor Email</label>
                                            <p class="email placeholder-text text-left spacing-0 mt-4">
                                                {{ $item->vendor_info?->subcontactorEmail ?? '---' }}
                                            </p>

                                        </td>
                                    </tr>
                                @empty
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif

        </section>
    </div>

</body>

</html>
