<script>
    var modal = new bootstrap.Modal(document.getElementById('selectCalenderItems'));
    var scheduleDatesModel = new bootstrap.Modal(document.getElementById('multiSelectDate'));
    var scheduleDate;
    var search;
    var validRange = true;
    var scheduleEstimateIdArray = [];
    var unScheduleEstimateIdArray = [];
    var draggedId = null;
    var timeInterval;
    var status = 'all';
    var calendar;
    toastr.options = {
        "closeButton": false,
        "debug": false,
        "newestOnTop": false,
        "progressBar": false,
        "positionClass": "toast-top-right",
        "preventDuplicates": false,
        "onclick": null,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": "5000",
        "extendedTimeOut": "1000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut"
    }
    document.addEventListener('DOMContentLoaded', function() {

        //// the individual way to do it
        var containerEl = document.getElementById('external-events-list');
        var eventEls = Array.prototype.slice.call(
            containerEl.querySelectorAll('.fc-event')
        );
        eventEls.forEach(function(eventEl) {
            new FullCalendar.Draggable(eventEl, {
                eventData: {
                    title: eventEl.innerHTML.trim()
                }
            });
        });

        /* initialize the calendar
        -----------------------------------------------------------------*/

        var calendarEl = document.getElementById('calendar');

        calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            editable: true,
            droppable: true,
            events: "{{ route(getRouteAlias() . '.scheduled-estimates') }}",

            selectAllow: function(selectInfo) {
                var today = new Date().toISOString().slice(0, 10);
                return selectInfo.startStr >= today;
            },
            eventAllow: function(dropInfo) {
                var today = new Date().setHours(0, 0, 0, 0);
                var dropDate = dropInfo.start.setHours(0, 0, 0, 0);
                return dropDate >= today;
            },
            selectable: true,
            select: function(info) {
                scheduleDate = info.startStr;
                $('#searchEstimate').val('');
                $('.filterSchedule').val(null).trigger('change');
                getSchedule();
            },
            headerToolbar: {
                left: 'title prev,next',
                // center: 'title',
                right: ''
            },
            eventDrop: function(info) {
                console.log("event drop");
                draggedId = info.event.id;
                scheduleDate = info.event.startStr;
                // addSchedule();
            },
            drop: function(arg) {
                console.log("drop", arg);
                scheduleDatesModel.show();
                if (document.getElementById('drop-remove').checked) {
                    arg.draggedEl.parentNode.removeChild(arg.draggedEl);
                }
                draggedId = arg.draggedEl.children[1].dataset.id;
                scheduleDate = arg.dateStr;
                // addSchedule(false);
            },
            eventReceive: function(info) {
                info.revert();
                openDateModel(scheduleDate);

            },
            dayMaxEvents: 1,
            eventContent: function(arg) {
                // create a new element to hold the event content
                var contentEl = document.createElement('div');
                // set the HTML content of the new element
                contentEl.innerHTML = arg.event.title;
                // return the new element
                return {
                    domNodes: [contentEl]
                };
            },
            eventClick: function(info) {
                // Handle event click here
                showEventModal(info.event);
            }
            // dateClick: function(info) {
            //     // Show the Bootstrap modal when a cell is double-clicked
            //     scheduleDate = info.dateStr;
            //     getSchedule();

            // },

        });
        calendar.render();


        function showEventModal(event) {

            let eventID=event.id;
            let url="{{ route(getRouteAlias() . '.get-scheduled-estimate-detail',[':eventID']) }}";
            url = url.replace(':eventID', eventID);
            $.ajax({
                url: url,
                type: 'GET',
                success: function(response) {
                    console.log("etsting 1",response);
                    if (response) {
                        $('.div-content-scrollable').html(response.html)
                        $('#scheduleDetailModal').modal('show');
                    }
                },
                error: function(response) {
                    toastr.error('Something went wrong!');
                }
            })
        }

    });
    $(document).on('click', '#schedule', function() {
        scheduleEstimateIdArray = [];
        unScheduleEstimateIdArray = [];

        $('.selected-estimates').each(function() {
            if ($(this).is(':checked')) {
                scheduleEstimateIdArray.push($(this).val())
            } else {
                unScheduleEstimateIdArray.push($(this).val())
            }
        })
        addSchedule();
    })

    $('.filter_search').on('keyup', function() {
        search = $(this).val();
        manipulateTable();
    })
    $('.allEstimateSearch').on('keyup', function() {
        search = $(this).val();
        getSchedule()
    })
    $('.filterSchedule').on('change', function() {
        status = $(this).val();
        getSchedule()
    })
    $('#months').on('change', function() {
        calendar.gotoDate($(this).val());
    })
    $('.today').on('click', function() {
        window.location.reload();
    })

    function manipulateTable() {
        $.ajax({
            method: "Get",
            url: "{{ URL::route(getRouteAlias() . '.unschedule-list') }}",
            data: {
                search,
                timeInterval
            }
        }).done(function(response) {
            if (response)
                $('.unscheduleList').html(JSON.parse(response))
        })
    }

    function getSchedule() {
        $.ajax({
            url: '{{ route(getRouteAlias() . '.get-schedule') }}',
            type: 'POST',
            data: {
                scheduleDate,
                search,
                status,
                validRange,
                "_token": "{{ csrf_token() }}",
            },

            success: function(response) {
                if (response) {
                    $('#allEstimate').html(JSON.parse(response))
                    modal.show();
                }
            },
            error: function(response) {
                toastr.error('Something went wrong!');
            }
        })
    }

    function addSchedule(refetch = true) {
        $('#attentionModal .dynamic-content-data').html('');
        $('#attentionModal .dynamic-content-data').append(
            `<h2 class="title text-center">Attention</h2>
         <p class="para mt-3 text-center">Are you sure you want to take this action? </p>`
        );
        $('#attentionModal').modal('show');
        $('#attentionModal').css('background', '#87808063');
    }

    $(".attentionConfirmation").click(function() {
        // if (confirm("Are you want to sure to this action!") == true) {
        $.ajax({
            url: '{{ route(getRouteAlias() . '.add.schedule') }}',
            type: 'POST',
            data: {
                scheduleDate,
                draggedId,
                scheduleEstimateIdArray,
                unScheduleEstimateIdArray,
                "_token": "{{ csrf_token() }}",
            },

            success: function(response) {
                // if (!draggedId) {
                modal.hide();
                // }
                manipulateTable();
                // if (refetch) {
                // calendar.refetchEvents();
                refetchEvents();
                // }
                toastr.success('Data updated Successfully!');
                $('#attentionModal').modal('hide');
                // setTimeout(function() {
                //     window.location.reload();
                // }, 1000);

            },
            error: function(response) {
                $('#attentionModal').modal('hide');
                toastr.error('Something went wrong!');
            }
        })
        // }
    });

    function refetchEvents() {
        calendar.refetchEvents();
        setTimeout(() => {
            var containerEl = document.getElementById('external-events-list');
            var eventEls = Array.prototype.slice.call(
                containerEl.querySelectorAll('.fc-event')
            );
            eventEls.forEach(function(eventEl) {
                new FullCalendar.Draggable(eventEl, {
                    eventData: {
                        title: eventEl.innerHTML.trim()
                    }
                });
            });
        }, 1000);

    }


    $(document).on('click', '#scheduleMultiDates', function() {
        // Get Dates from datepicker and convert into string
        let scheduleDates = $('.multiDate').datepicker('getUTCDates').map(function(date) {
            return date.toISOString().slice(0, 10);
        });

        $.ajax({
            url: '{{ route(getRouteAlias() . '.add.schedule') }}',
            type: 'POST',
            data: {
                scheduleDates,
                draggedId,
                "_token": "{{ csrf_token() }}",
            },

            success: function(response) {
                if (!draggedId) {
                    modal.hide();
                }
                manipulateTable();
                refetchEvents();
                toastr.success('Data updated Successfully!');
                scheduleDatesModel.hide();
            },
            error: function(response) {
                toastr.error('Something went wrong!');
            }
        })
    })

    function openDateModel(selecetdDate) {
        let datePicker = $('.multiDate').datepicker({
            multidate: true,
            startDate: new Date(),
            format: 'dd-mm-yyyy',
        });
        $('.multiDate').datepicker('setDate', new Date(selecetdDate)).datepicker('fill')
        scheduleDatesModel.show();
    }
</script>
