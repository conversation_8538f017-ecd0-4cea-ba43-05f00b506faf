@forelse ($unscheduleEstimates as $estimate)
    <div class='fc-event fc-event-main fc-h-event fc-daygrid-event fc-daygrid-block-event'>
        <div class="request_id">Sales Order <span>#{{ $estimate?->opportunityid?->sales_order_number }}</span></div>
        <div data-id="{{ encodeID($estimate->id) }}" class='request_name'>{{ $estimate?->opportunityid?->account?->company_name }}
        </div>
    </div>
@empty
    <p class="text-center">No Record Found</p>
@endforelse
