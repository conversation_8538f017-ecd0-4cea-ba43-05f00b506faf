<style>
    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
        appearance: none;
    }

    .row label.label {
        color: var(--Additional, #90A0B7);
        font-family: Cabin;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        /* 150% */
    }

    .border-line {
        border: 2px solid #D7D7D7;
        margin-top: 20px
    }

    .fields-container {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        margin-top: 30px;
    }

    .fields-container .time-field {
        width: 206px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px #EBEBEB solid;
        position: relative;
    }

    .fields-container .time-field .remove-time-field {
        position: absolute;
        top: -7px;
        right: -3px;
        background: transparent;
        border: none;
        width: fit-content;
        height: fit-content;
    }

    .fields-container .time-field .fa-xmark {
        color: red;
        font-size: 14px;
    }

    .fields-container .time-field .partial-field {
        width: 103px;
        padding: 6px 16px 15px 16px;
    }

    .fields-container .time-field .partial-field:first-child {
        border-right: 1px #EBEBEB solid;
    }

    .fields-container .time-field .partial-field .time-field-label,
    .fields-container .time-field .partial-field .time-field-input,
    .fields-container .time-field .field-header {
        font-family: "Cabin";
        font-size: 14px;
        font-weight: 400;
        font-style: normal;
        line-height: 14px;
        color: #868686;
    }

    .fields-container .time-field .field-header {
        padding: 8px;
        background-color: #EBF2F6;
        text-align: center;
        color: #202020;
    }

    .fields-container .time-field .partial-field .time-field-input {
        width: 100%;
        text-align: center;
        color: #192A3E;
        padding: 8px;
        border-top: none;
        border-left: none;
        border-right: none;
        border-bottom: 1px dotted #DCDCDC;
    }

    .fields-container .time-field .partial-field .time-field-input::placeholder {
        color: #C2C2C2;
    }

    .add-time-field {
        background-color: transparent;
        width: 40px !important;
        height: 40px !important;
        /* margin: auto 0; */
        border: none;
        outline: none;
        padding: 0 !important;
    }

    .add-time-field .fa-solid {
        color: #ffffff;
        font-size: 20px;
    }

    .notes,
    .sub_heading.job-pack,
    .panel_title.panel_sub_title {
        font-family: "Cabin";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        text-transform: capitalize;
        color: #192A3E;
    }

    .sub_heading.job-pack {
        font-size: 30px;
        line-height: normal;
        color: #036;
    }

    .panel_title.panel_sub_title {
        font-size: 20px;
        color: #192A3E;
    }

    .note-detail,
    .special-notes {
        font-family: "Cabin";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 28px;
        color: #192A3E;
    }

    .special-notes {
        border-radius: 12px;
        border: 1px solid #E9E9E9;
        resize: none;
        height: calc(100% - 30px);
        width: 100%;
        padding: 13px 14px;
    }

    .special-notes::placeholder {
        color: #E9E9E9;
    }

    @media (min-width: 576px) {
        #scheduleDetailModal .modal-dialog {
            max-width: calc(100% - 100px) !important;
        }
    }

    .div-content-scrollable {
        max-height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .div-content-scrollable .vendor-input {
        margin-top: 8px;
    }

    #job-pack-form .accordion-button {
        font-family: "Cabin";
        font-style: normal;
        font-weight: 500;
        font-size: 1.6rem;
        line-height: 2.4rem;
        text-transform: capitalize;
        color: #192a3e;
        padding: 1.6rem;
        background-color: #ffffff;
        box-shadow: none;
        box-shadow: none;
        border-bottom: 1px solid #e7e7e7;
    }

</style>

<section class="dashboard_main pb-5" id="printJobPack">
    <form id="job-pack-form" method="POST"
        action="{{ route(getRouteAlias() . '.save-schedule-estimate-job-pack', [encodeID($generate_estimate->id)]) }}">
        <div class="table_filter_header mb-4">
            <div class="d-flex align-items-center gap-3" style="gap: 10px">
                <img src="{{ $org->profile_photo_path ? asset('storage/user_images/' . $org->profile_photo_path) : asset('admin_assets/images/account.png') }}"
                    width="60" height="60" alt="logo">

                <h2 class="sub_heading job-pack">Job Pack</h2>
            </div>
            <div class="panel_header justify-content-between gap-3" style="gap: 13px">
                <h2 class="panel_title panel_sub_title"></h2>
                <div class="d-flex gap-2" style="gap: 10px">
                    <a href="{{ route(getRouteAlias() . '.job-pack.pdf-download', ['id' => encodeId($generate_estimate->id)]) }}"
                        data-url="{{ route(getRouteAlias() . '.job-pack.pdf-download', ['id' => encodeId($generate_estimate->id)]) }}"
                        class="btn  transparent bg-transparent downloadPDF">Download PDF</a>
                    <button type="submit" class="btn primaryblue ">Save</button>
                </div>

            </div>
        </div>

        <div class="panel mt-4">
            <div class="panel_header justify-content-between gap-3">
                <h2 class="panel_title panel_sub_title">Job Information</h2>
                <div class="d-flex gap-2">
                    <div class="position-relative d-flex">
                        <label class="label">PO:</label>
                        <div class="">
                            <input class="text border-0" type="number" value="{{ $costSummary->po_number ?? '' }}"
                                name="po_number" id="po-number" placeholder="________">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row ">
                <?php
$opportunityId=$generate_estimate->opportunity_id;
$esitems = App\Models\EstimateItem::where('opportunity_id', $opportunityId)->get();
$totalPriceSumlaborhourcount = DB::table('estimate_items')
->where('opportunity_id', $opportunityId)
->where('category_type', 'labors')
->where('labor_type', 'Laborers')
->sum('quantity');
                ?>

                <div class="col-md-4 col-sm-6 mt-5">
                    <div class="d-flex gap-2 align-items-center" style="gap: 10px">
                        <div class="">
                            <label class="label">Job Name:</label>
                            <label class="label">Job Number:</label>
                            <label class="label">Scheduled Man Hours :</label>
                        </div>
                        <div class="">
                            <p class="text">{{ $generate_estimate?->opportunityid?->opportunity_name ?: '---' }}</p>
                            <p class="text">{{ $generate_estimate?->opportunityid?->job_no ?: '---' }}</p>
                            <p class="text">{{ $totalPriceSumlaborhourcount }}</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 col-sm-6 mt-5">
                    <div class="d-flex gap-2 align-items-center" style="gap: 10px">
                        <div class="">
                            <label class="label">Ops Manager:</label>
                            <label class="label">Account Owner:</label>
                            <label class="label">Estimator Name:</label>
                        </div>
                        <div class="">
                            <p class="text">{{ $generate_estimate?->manager?->name ?? '---' }}</p>
                            <p class="text">{{ $generate_estimate?->opportunityid?->account?->accountowner?->first_name ?? '---' }} {{ $generate_estimate?->opportunityid?->account?->accountowner?->last_name ?? '---' }}</p>
                            <p class="text">{{ $generate_estimate?->estimator?->name ?? '---' }}</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 col-sm-6 mt-5">
                    <div class="d-flex gap-2 align-items-center" style="gap: 10px">
                        <div class="">
                            <label class="label">Job Sold Date:</label>
                            <label class="label">Start Date:</label>
                            <label class="label">Completion Date:</label>
                        </div>
                        <div class="">
                            <p class="text">
                            {{ $generate_estimate?->won_date ? \Carbon\Carbon::parse($generate_estimate?->won_date)->format('F d, Y') : '----' }}

                            </p>
                            <p class="text">
                            {{ $generate_estimate?->created_at ? \Carbon\Carbon::parse($generate_estimate?->created_at)->format('F d, Y') : '----' }}

                            </p>
                            <p class="text"> {{ $generate_estimate?->completed_at ? \Carbon\Carbon::parse($generate_estimate?->completed_at)->format('F d, Y') : '----' }} </p>
                        </div>
                    </div>
                </div>

            </div>

            <hr class="border-line">

            <div class="row mt-4">
                <div class="col-12">
                    <div class="d-flex gap-5 align-items-center" style="gap: 15px">
                        <label class="label">Site Address:</label>
                        <p class="text"> {{ $generate_estimate?->opportunityid?->propertyInformation?->address1 ?: '----' }} </p>
                    </div>
                </div>
            </div>



        </div>

        <!-- <div class="panel mt-4">

            @if ($estimateLabor->where('labor.name', 'Laborers')->count() > 0)
                <div class="row">
                    <div class="col-6 panel_header">
                        <h2 class="panel_title panel_sub_title">Labor details</h2>
                    </div>
                    <div class="col-6 d-flex align-items-center gap-5">
                        <div class="d-flex align-items-center gap-3" style="display: none !important">
                            <label class="label">Scheduled Hours:</label>
                            <p id="scheduledHOurs" class="text ">
                                @php
                                    $scheduledHours = $estimateLabor->where('labor.name', 'Laborers')->sum(function ($item) {
                                        $scheduledHours = data_get($item, 'labor_hours_info.scheduled_hours');
                                        return is_array($scheduledHours) ? array_sum(array_filter($scheduledHours, fn($value) => $value !== null)) : 0;
                                    });

                                    $actualHours = $estimateLabor->where('labor.name', 'Laborers')->sum(function ($item) {
                                        $actualHours = data_get($item, 'labor_hours_info.actual_hours');
                                        return is_array($actualHours) ? array_sum(array_filter($actualHours, fn($value) => $value !== null)) : 0;
                                    });

                                @endphp
                                {{ $scheduledHours + $actualHours }} hours</p>
                        </div>

                        <div class="d-flex align-items-center gap-3">
                            <strong><label class="label">Estimates
                                    Hours:</label></strong>
                            <p id="estimatedHours" class="text">
                                @php
                                    $laborHours =
                                        ($estimatePlantMaterial->count() > 0
                                            ? $estimatePlantMaterial
                                                ->map(function ($item) {
                                                    // Calculate the total_cost by adding quantity and material install
                                                    return $item->quantity + ($item->material ? $item->material->install : 0);
                                                })
                                                ->sum()
                                            : 0) +
                                        ($estimateHardMaterial->count() > 0
                                            ? $estimateHardMaterial
                                                ->map(function ($item) {
                                                    // Calculate the total_cost by adding quantity and material labor
                                                    return $item->quantity + ($item->material ? $item->material->labor : 0);
                                                })
                                                ->sum()
                                            : 0);
                                @endphp
                                {{ custom_number_format($laborHours) }} hours
                            </p>
                        </div>

                        <div class="d-flex align-items-center gap-3">
                            <label class="label">Schedule labor Hours:</label>
                            <p id="scheduleLaborHOurs" class="text ">
                                {{ $scheduledHours }}
                                hours</p>
                        </div>

                        <div class="d-flex align-items-center gap-3">
                            <strong><label class="label" style="color: #000;font-weight:500;">Acutal labor
                                    Hours:</label></strong>
                            <p id="actualLaborHOurs" class="text">
                                {{ $actualHours }}
                                hours</p>
                        </div>

                    </div>
                </div>
                @forelse ($estimateLabor->where('labor.name','Laborers') as $row)
                    <div class="row ">
                        <div class="col-sm-2 mt-5">
                            <label class="label">Quantity</label>
                            <p class="text">
                                {{ $row->quantity ?? '---' }}
                            </p>
                        </div>
                        <div class="col-sm-2 mt-5">
                            <label class="label">UOM</label>
                            <p class="text">
                                {{ $row->uom ?? ($row?->labor?->uom ?? '---') }}
                            </p>
                        </div>
                        {{-- <div class="col-sm-2 mt-5">
                            <label class="label">Estimate Hours</label>
                            <p class="text" id="laborDetailScheduleHours-{{ encodeID($row->id) }}">
                                @php
                                    $scheduledHoursData = data_get($row, 'labor_hours_info.scheduled_hours');
                                    $individualScheduledHours = is_array($scheduledHoursData) ? array_sum(array_filter($scheduledHoursData, fn($value) => $value !== null)) : 0;

                                    $actualHours = data_get($row, 'labor_hours_info.actual_hours');
                                    $individualActualHours = is_array($actualHours) ? array_sum(array_filter($actualHours, fn($value) => $value !== null)) : 0;
                                @endphp

                                {{ custom_number_format($individualScheduledHours + $individualActualHours) }} hours
                            </p>
                        </div> --}}

                        <div class="col-sm-6 mt-5">
                            <label class="label">Description</label>
                            <p class="text">{{ $row->description ?? '---' }}</p>
                        </div>

                    </div>
                    <div class="fields-container">
                        @if ($row->labor_hours_info)
                            @for ($i = 0; $i < count($row?->labor_hours_info?->scheduled_hours); $i++)
                                <div class="time-field" data-target-id="{{ encodeID($row->id) }}">
                                    @if ($i != 0)
                                        <button type="button" class="remove-time-field"
                                            data-target-id="{{ encodeID($row->id) }}"><i
                                                class="fa-solid fa-xmark"></i></button>
                                    @endif
                                    <h2 class="field-header">Day {{ $i + 1 }}</h2>
                                    <div class="d-flex">
                                        <div class="partial-field">
                                            <label class="time-field-label">Scheduled</label>
                                            <input class="time-field-input" type="number"
                                                name="scheduled_hours[{{ encodeID($row->id) }}][]"
                                                data-target-id="{{ encodeID($row->id) }}"
                                                id="scheduled-{{ $i + 1 }}"
                                                value="{{ $row?->labor_hours_info?->scheduled_hours[$i] ?? null }}"
                                                placeholder="Type">
                                        </div>
                                        <div class="partial-field">
                                            <label class="time-field-label">Actual</label>
                                            <input class="time-field-input" type="number"
                                                name="actual_hours[{{ encodeID($row->id) }}][]"
                                                data-target-id="{{ encodeID($row->id) }}"
                                                id="actual-{{ $i + 1 }}"
                                                value="{{ $row?->labor_hours_info?->actual_hours[$i] ?? null }}"
                                                placeholder="Type">
                                        </div>
                                    </div>
                                </div>
                            @endfor
                        @else
                            <div class="time-field" data-target-id="{{ encodeID($row->id) }}">
                                <button type="button" class="remove-time-field"
                                    data-target-id="{{ encodeID($row->id) }}"><i
                                        class="fa-solid fa-xmark"></i></button>
                                <h2 class="field-header">Day 1</h2>
                                <div class="d-flex">
                                    <div class="partial-field">
                                        <label class="time-field-label">Scheduled</label>
                                        <input class="time-field-input" type="number"
                                            data-target-id="{{ encodeID($row->id) }}"
                                            name="scheduled_hours[{{ encodeID($row->id) }}][]"
                                            id="scheduled-[{{ encodeID($row->id) }}]" placeholder="Type">
                                    </div>
                                    <div class="partial-field">
                                        <label class="time-field-label">Actual</label>
                                        <input class="time-field-input" type="number"
                                            data-target-id="{{ encodeID($row->id) }}"
                                            name="actual_hours[{{ encodeID($row->id) }}][]"
                                            id="actual-[{{ encodeID($row->id) }}]" placeholder="Type">
                                    </div>
                                </div>
                            </div>
                        @endif
                        <button type="button" class="btn primaryblue add-time-field"
                            data-target-id="{{ encodeID($row->id) }}">
                            <i class="fa-solid fa-plus"></i>
                        </button>
                    </div>
                @empty
                @endforelse
            @endif


        </div> -->

        <div class="panel mt-4">
            <div class="row">
                <div class="col-6">
                    <h2 class="notes">Scope of Work</h2>
                    <table class="table table-striped mt-3">
                               <thead style="background: #DCF2FF; border: 1px solid transparent;
">
                               <tr style="border: none;">
                                <th>Item / Description</th>
                                <th>Uom</th>
                                <th>Quantity</th>
                                <th>Unit Price</th>
                                <th>Total Price</th>
                               </tr>
                               </thead>
                               <tbody>
                                @foreach ($esitems as $item)
                                <tr>
                                    <td>{{ $item->item_name }}</td>
                                    <td>{{ $item->uom }}</td>
                                    <td>{{ $item->quantity }}</td>
                                    <td>${{ $item->unit_price }}</td>
                                    <td>${{ $item->total_price }}</td>
                                </tr>
                                @endforeach
                               </tbody>
                              </table>
                    <!-- <p class="note-detail mt-3"> {!! $generate_estimate->notes ?? '---' !!} </p> -->
                </div>
                <div class="col-6">
                    <h2 class="notes">Special Notes</h2>
                    <textarea class="special-notes mt-3" style="height: auto !important" rows="4" name="special_notes" id="special_notes" placeholder="Write any note here">{{ $generate_estimate->special_notes ?? '' }}</textarea>

                    <h2 class="notes">Equipment / Tools
                    </h2>
                    <textarea class="special-notes mt-3" style="height: auto !important" rows="4"  name="equipment_tools" id="equipment_tools" placeholder="Write any note here">{{ $generate_estimate->equipment_tools ?? '' }}</textarea>
                    <h2 class="notes">Rental Information
                    </h2>
                    <textarea class="special-notes mt-3" style="height: auto !important" rows="4"  name="rental_information" id="rental_information" placeholder="Write any note here">{{ $generate_estimate->rental_information ?? '' }}</textarea>
                </div>


            </div>
        </div>

        <div class="panel mt-4">
            @if ($estimateMaterial->count() > 0)
                <!-- Equipment Starts-->
                <div class="accordion-item mt-4">
                    <h2 class="accordion-header" id="EquipmentData">
                        <button class="accordion-button" type="button" data-toggle="collapse"
                            data-target="#EquipmentData-collapseOne" aria-expanded="true"
                            aria-controls="EquipmentData-collapseOne">
                            Equipment
                        </button>
                    </h2>
                    <div id="EquipmentData-collapseOne" class="accordion-collapse collapse show"
                        aria-labelledby="EquipmentData">
                        <div class="accordion-body">

                            <div class="table-responsive">
                                <table class="table table-striped shadow-none custom_datatable display"
                                    style="width:100%">
                                    <thead>
                                        <tr>
                                            {{-- <th>Sr #</th> --}}
                                            <th>Name</th>
                                            <th>Quantity</th>
                                            <th>UoM</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($estimateMaterial as $key=>$item)
                                            <tr>
                                                <td> {{ $item->equipment->name }}
                                                    <hr>
                                                    <label class="label">Vendor Name</label>
                                                    <input type="text" placeholder="Enter Vendor Name"
                                                        name="equipmentVName[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->equipmentVName }}">
                                                </td>
                                                <td>{{ $item->quantity }}
                                                    <hr>
                                                    <label class="label">Vendor Contact Name</label> <input
                                                        type="text" placeholder="Enter Contact Name"
                                                        name="equipmentCName[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->equipmentCName }}">
                                                </td>
                                                <td>{{ $item->uom ?? $item->equipment->uom }}
                                                    <hr>
                                                    <label class="label">Vendor Phone</label> <input type="text"
                                                        minlength="12" maxlength="12"
                                                        oninput="maskPhoneNumber(event)"
                                                        placeholder="Enter Vendor Phone "
                                                        name="equipmentPhone[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->equipmentPhone }}">
                                                </td>
                                                <td>
                                                    <label class="label" style="margin-top: 40px">Vendor
                                                        Email</label>
                                                    <input type="email" placeholder="Enter Vendor Email"
                                                        name="equipmentEmail[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->equipmentEmail }}">
                                                </td>
                                            </tr>
                                        @empty
                                        @endforelse

                                </table>
                            </div>

                        </div>
                    </div>
                </div>
                <!-- Equipment Starts-->
            @endif


            @if ($estimateLabor->count() > 0)
                <!-- Equipment Starts-->
                <div class="accordion-item mt-4">
                    <h2 class="accordion-header" id="EquipmentData">
                        <button class="accordion-button" type="button" data-toggle="collapse"
                            data-target="#EquipmentData-collapseOne" aria-expanded="true"
                            aria-controls="EquipmentData-collapseOne">
                            Labor
                        </button>
                    </h2>
                    <div id="EquipmentData-collapseOne" class="accordion-collapse collapse show"
                        aria-labelledby="EquipmentData">
                        <div class="accordion-body">

                            <div class="table-responsive">
                                <table class="table table-striped shadow-none custom_datatable display"
                                    style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Quantity</th>
                                            <th>UoM</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($estimateLabor as $key=>$item)
                                            <tr>
                                                <td>{{ $item->labor?->name }}
                                                    <hr>
                                                    <label class="label">Vendor Name</label>
                                                    <input type="text" placeholder="Enter Vendor Name"
                                                        name="laborVName[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->laborVName }}">
                                                </td>
                                                <td>{{ $item->quantity }}
                                                    <hr>
                                                    <label class="label">Vendor Contact Name</label>
                                                    <input type="text" placeholder="Enter Contact Name"
                                                        name="laborCName[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->laborCName }}">
                                                </td>
                                                <td>{{ $item->labor?->uom }}
                                                    <hr>
                                                    <label class="label">Vendor Phone</label>
                                                    <input type="text" minlength="12" maxlength="12"
                                                        oninput="maskPhoneNumber(event)"
                                                        placeholder="Enter Vendor Phone "
                                                        name="laborPhone[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input"
                                                        value="{{ $item->vendor_info?->laborPhone }}">
                                                </td>
                                                <td>
                                                    <label class="label" style="margin-top: 40px">Vendor
                                                        Email</label>
                                                    <input type="email" placeholder="Enter Vendor Email"
                                                        name="laborEmail[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->laborEmail }}">
                                                </td>
                                            </tr>
                                        @empty
                                        @endforelse

                                </table>
                            </div>

                        </div>
                    </div>
                </div>
                <!-- Equipment Starts-->
            @endif

            @if ($estimatePlantMaterial->count() > 0)
                <!-- Equipment Starts-->
                <div class="accordion-item mt-4">
                    <h2 class="accordion-header" id="EquipmentData">
                        <button class="accordion-button" type="button" data-toggle="collapse"
                            data-target="#EquipmentData-collapseOne" aria-expanded="true"
                            aria-controls="EquipmentData-collapseOne">
                            Plant Material
                        </button>
                    </h2>
                    <div id="EquipmentData-collapseOne" class="accordion-collapse collapse show"
                        aria-labelledby="EquipmentData">
                        <div class="accordion-body">

                            <div class="table-responsive">
                                <table class="table table-striped shadow-none custom_datatable display"
                                    style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Quantity</th>
                                            <th>UoM</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($estimatePlantMaterial as $key=>$item)
                                            <tr>
                                                <td> {{ $item->material->name }}
                                                    <hr>
                                                    <label class="label">Vendor Name</label>
                                                    <input type="text" placeholder="Enter Vendor Name"
                                                        name="plantMaterialVName[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->plantMaterialVName }}">
                                                </td>
                                                <td>{{ $item->quantity }}
                                                    <hr>
                                                    <label class="label">Vendor Contact Name</label>
                                                    <input type="text" placeholder="Enter Contact Name"
                                                        name="plantMaterialCName[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->plantMaterialCName }}">
                                                </td>
                                                <td>{{ $item->uom ?? $item->material->uom }}
                                                    <hr>
                                                    <label class="label">Vendor Phone</label>
                                                    <input type="text" minlength="12" maxlength="12"
                                                        oninput="maskPhoneNumber(event)"
                                                        placeholder="Enter Vendor Phone "
                                                        name="plantMaterialPhone[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input"
                                                        value="{{ $item->vendor_info?->plantMaterialPhone }}">
                                                </td>
                                                <td>
                                                    <label class="label" style="margin-top: 40px">Vendor
                                                        Email</label>
                                                    <input type="email" placeholder="Enter Vendor Email"
                                                        name="plantMaterialEmail[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->plantMaterialEmail }}">
                                                </td>
                                            </tr>
                                        @empty
                                        @endforelse

                                </table>
                            </div>

                        </div>
                    </div>
                </div>
                <!-- Equipment Starts-->
            @endif

            @if ($estimateHardMaterial->count() > 0)
                <!-- Equipment Starts-->
                <div class="accordion-item mt-4">
                    <h2 class="accordion-header" id="EquipmentData">
                        <button class="accordion-button" type="button" data-toggle="collapse"
                            data-target="#EquipmentData-collapseOne" aria-expanded="true"
                            aria-controls="EquipmentData-collapseOne">
                            Hard Material
                        </button>
                    </h2>
                    <div id="EquipmentData-collapseOne" class="accordion-collapse collapse show"
                        aria-labelledby="EquipmentData">
                        <div class="accordion-body">

                            <div class="table-responsive">
                                <table class="table table-striped shadow-none custom_datatable display"
                                    style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Quantity</th>
                                            <th>UoM</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($estimateHardMaterial as $key=>$item)
                                            <tr>
                                                <td>{{ $item->material->name }}
                                                    <hr>
                                                    <label class="label">Vendor Name</label>
                                                    <input type="text" placeholder="Enter Vendor Name"
                                                        name="hardMaterialVName[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->hardMaterialVName }}">
                                                </td>
                                                <td>{{ $item->quantity }}
                                                    <hr>
                                                    <label class="label">Vendor Contact Name</label>
                                                    <input type="text" placeholder="Enter Contact Name"
                                                        name="hardMaterialCName[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->hardMaterialCName }}">
                                                </td>
                                                <td>{{ $item->uom ?? $item->material->uom }}
                                                    <hr>
                                                    <label class="label">Vendor Phone</label>
                                                    <input type="text" minlength="12" maxlength="12"
                                                        oninput="maskPhoneNumber(event)"
                                                        placeholder="Enter Vendor Phone "
                                                        name="hardMaterialPhone[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input"
                                                        value="{{ $item->vendor_info?->hardMaterialPhone }}">
                                                </td>
                                                <td>
                                                    <label class="label" style="margin-top: 40px">Vendor
                                                        Email</label>
                                                    <input type="email" placeholder="Enter Vendor Email"
                                                        name="hardMaterialEmail[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->hardMaterialEmail }}">
                                                </td>
                                            </tr>
                                        @empty
                                        @endforelse

                                </table>
                            </div>

                        </div>
                    </div>
                </div>
                <!-- Equipment Starts-->
            @endif

            @if ($estimateOtherCost->count() > 0)
                <!-- Equipment Starts-->
                <div class="accordion-item mt-4">
                    <h2 class="accordion-header" id="EquipmentData">
                        <button class="accordion-button" type="button" data-toggle="collapse"
                            data-target="#EquipmentData-collapseOne" aria-expanded="true"
                            aria-controls="EquipmentData-collapseOne">
                            Other Cost
                        </button>
                    </h2>
                    <div id="EquipmentData-collapseOne" class="accordion-collapse collapse show"
                        aria-labelledby="EquipmentData">
                        <div class="accordion-body">

                            <div class="table-responsive">
                                <table class="table table-striped shadow-none custom_datatable display"
                                    style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Quantity</th>
                                            <th>UoM</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($estimateOtherCost as $key=>$item)
                                            <tr>
                                                <td>{{ $item->otherCost->name }}
                                                    <hr>
                                                    <label class="label">Vendor Name</label>
                                                    <input type="text" placeholder="Enter Vendor Name"
                                                        name="otherCostVName[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->otherCostVName }}">
                                                </td>
                                                <td>{{ $item->quantity }}
                                                    <hr>
                                                    <label class="label">Vendor Contact Name</label> <input
                                                        type="text" placeholder="Enter Contact Name"
                                                        name="otherCostCName[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->otherCostCName }}">
                                                </td>
                                                <td>{{ $item->uom }}
                                                    <hr>
                                                    <label class="label">Vendor Phone</label>
                                                    <input type="text" minlength="12" maxlength="12"
                                                        oninput="maskPhoneNumber(event)"
                                                        placeholder="Enter Vendor Phone "
                                                        name="otherCostPhone[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input"
                                                        value="{{ $item->vendor_info?->otherCostPhone }}">
                                                </td>
                                                <td>
                                                    <label class="label" style="margin-top: 40px">Vendor
                                                        Email</label>
                                                    <input type="email" placeholder="Enter Vendor Email"
                                                        name="otherCostEmail[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->otherCostEmail }}">
                                                </td>
                                            </tr>
                                        @empty
                                        @endforelse

                                </table>
                            </div>

                        </div>
                    </div>
                </div>
                <!-- Equipment Starts-->
            @endif

            @if ($estimateSubContractor->count() > 0)
                <!-- Equipment Starts-->
                <div class="accordion-item mt-4">
                    <h2 class="accordion-header" id="EquipmentData">
                        <button class="accordion-button" type="button" data-toggle="collapse"
                            data-target="#EquipmentData-collapseOne" aria-expanded="true"
                            aria-controls="EquipmentData-collapseOne">
                            Sub Contractor
                        </button>
                    </h2>
                    <div id="EquipmentData-collapseOne" class="accordion-collapse collapse show"
                        aria-labelledby="EquipmentData">
                        <div class="accordion-body">

                            <div class="table-responsive">
                                <table class="table table-striped shadow-none custom_datatable display"
                                    style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Quantity</th>
                                            <th>UoM</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($estimateSubContractor as $key=>$item)
                                            <tr>
                                                <td> {{ $item->name }}
                                                    <hr>
                                                    <label class="label">Vendor Name</label>
                                                    <input type="text" placeholder="Enter Vendor Name"
                                                        name="subcontactorVName[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->subcontactorVName }}">
                                                </td>
                                                <td>{{ $item->quantity }}
                                                    <hr>
                                                    <label class="label">Vendor Contact Name</label>
                                                    <input type="text" placeholder="Enter Contact Name"
                                                        name="subcontactorCName[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->subcontactorCName }}">
                                                </td>
                                                <td>{{ $item->uom }}
                                                    <hr>
                                                    <label class="label">Vendor Phone</label>
                                                    <input type="text" minlength="12" maxlength="12"
                                                        oninput="maskPhoneNumber(event)"
                                                        placeholder="Enter Vendor Phone "
                                                        name="subcontactorPhone[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input"
                                                        value="{{ $item->vendor_info?->subcontactorPhone }}">
                                                </td>
                                                <td>
                                                    <label class="label" style="margin-top: 40px">Vendor
                                                        Email</label>
                                                    <input type="email" placeholder="Enter Vendor Email"
                                                        name="subcontactorEmail[{{ encodeID($item->id) }}]"
                                                        class="input form-control vendor-input" minlength="2"
                                                        value="{{ $item->vendor_info?->subcontactorEmail }}">
                                                </td>
                                            </tr>
                                        @empty
                                        @endforelse

                                </table>
                            </div>

                        </div>
                    </div>
                </div>
                <!-- Equipment Starts-->
            @endif

        </div>
    </form>
</section>

</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
    // $(document).ready(function () {
    //     let debounceTimer;

    //     $('#special_notes').on('input', function () {
    //         const specialNotes = $(this).val();
    //         const generate_id = {{$generate_estimate->id}};

    //         // Clear previous timer
    //         clearTimeout(debounceTimer);

    //         // Set a new debounce timer (500ms delay)
    //         debounceTimer = setTimeout(function () {
    //             saveSpecialNotes(specialNotes, generate_id);
    //         }, 500);
    //     });

    //     function saveSpecialNotes(specialNotes, generate_id) {
    //         $.ajax({
    //             url: "{{ route('organization.save.special.notes') }}", // Define your route
    //             type: "POST",
    //             data: {
    //                 _token: "{{ csrf_token() }}", // Laravel CSRF token
    //                 special_notes: specialNotes,
    //                 generate_id: generate_id,
    //             },
    //             success: function (response) {
    //                 console.info("Notes saved successfully:", response);
    //             },
    //             error: function (xhr, status, error) {
    //                 console.error("Error saving notes:", error);
    //             },
    //         });
    //     }
    // });
</script>

<script>
    console.log('This is partial view-specific JavaScript code.');
    $(document).ready(function() {

        $(document).off("submit", "#job-pack-form").on("submit", "#job-pack-form", function(event) {
            event.preventDefault(); // Prevent the default form submission

            var form = $(this); // Cache the form element
            var emailFormData = new FormData(form[0]); // Create FormData object from the form

            $('.laravel_error').remove();

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            $.ajax({
                url: form.attr("action"),
                type: "POST",
                data: emailFormData, // Use the FormData object you created
                processData: false, // Prevent jQuery from processing the data
                contentType: false, // Prevent jQuery from setting content type
                success: function(response, status) {
                    if (response.success) {
                        toastr.success(response.success);
                        $("#scheduleDetailModal").modal('hide')
                    }
                    // Handle success here
                },
                error: function(request, error) {
                    var errorResponse = JSON.parse(request.responseText);
                    if (request.status === 400) {
                        toastr.error(errorResponse.error); // Corrected from response.error
                    }

                    if (request.status === 422) {
                        toastr.error(
                        'Job Pack not saved successfully!'); // Corrected from response.error
                    }

                    if (request.status === 422) {
                        if (errorResponse.hasOwnProperty('errors')) {
                            $.each(errorResponse.errors, function(field_name, error) {
                                if (field_name.indexOf('.') !== -1) {
                                    // Convert dot notation to square brackets
                                    field_name = field_name.replace(/\./g, '[') +
                                        ']';
                                }
                                form.find('[name="' + field_name + '"]').after(
                                    '<div class="invalid-feedback laravel_error" >' +
                                    error[0] + '</div>'
                                );
                                $(".invalid-feedback").css("display", "block");
                            });
                        }
                    } else {
                        // Handle other errors here
                    }
                }
            });
        });



        $('#job-pack-form').validate({
            errorPlacement: function(error, element) {
                if (element.hasClass("select2-hidden-accessible")) {
                    error.insertAfter(element.next(".select2-container"));
                } else if (element.attr("name") === "format_file") {
                    // Show the error message below the format_radio_fields class
                    error.insertAfter(".format_radio_fields");
                } else {
                    error.insertAfter(element);
                }
            },
            ignore: "#address2 , #property_address2, #billing_address2",

            rules: {
                // Equipments validation
                equipmentVName: {
                    maxlength: 255,
                },
                equipmentCName: {
                    maxlength: 255,
                },
                equipmentPhone: {
                    minlength: 10,
                    maxlength: 24,
                    phoneNumber: true,
                },
                equipmentEmail: {
                    // checkmail: true,
                    maxlength: 255,
                },

                // Labor validation
                laborVName: {
                    maxlength: 255,
                },
                laborCName: {
                    maxlength: 255,
                },
                laborPhone: {
                    minlength: 10,
                    maxlength: 24,
                    phoneNumber: true,
                },
                laborEmail: {
                    // checkmail: true,
                    maxlength: 255,
                },

                // Plant validation
                plantMaterialVName: {
                    maxlength: 255,
                },
                plantMaterialCName: {
                    maxlength: 255,
                },
                plantMaterialPhone: {
                    minlength: 10,
                    maxlength: 24,
                    phoneNumber: true,
                },
                plantMaterialEmail: {
                    // checkmail: true,
                    maxlength: 255,
                },

                // Hard Material validation
                hardMaterialVName: {
                    maxlength: 255,
                },
                hardMaterialCName: {
                    maxlength: 255,
                },
                hardMaterialPhone: {
                    minlength: 10,
                    maxlength: 24,
                    phoneNumber: true,
                },
                hardMaterialEmail: {
                    // checkmail: true,
                    maxlength: 255,
                },

                // OtherJob Cost validation
                otherCostVName: {
                    maxlength: 255,
                },
                eotherCostCName: {
                    maxlength: 255,
                },
                otherCostEmail: {
                    minlength: 10,
                    maxlength: 24,
                    phoneNumber: true,
                },
                otherCostEmail: {
                    // checkmail: true,
                    maxlength: 255,
                },

                // Sub COntractors validation
                subcontactorVName: {
                    maxlength: 255,
                },
                subcontactorCName: {
                    maxlength: 255,
                },
                subcontactorPhone: {
                    minlength: 10,
                    maxlength: 24,
                    phoneNumber: true,
                },
                subcontactorEmail: {
                    // checkmail: true,
                    maxlength: 255,
                },
            },
            messages: {

            },
        });


        $(".downloadPDF").click(function(e) {
            e.preventDefault();
            var downloadUrl = e.target.getAttribute('data-url');
            $.ajax({
                type: 'GET',
                url: downloadUrl,
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(response) {
                    // Check if the response is not empty
                    if (response.size > 0) {
                        // File download success, use FileSaver.js to trigger the download
                        saveAs(response, 'job_pack.pdf');
                    } else {
                        toastr.error("File not found!");
                    }
                },
                error: function(xhr, status, error) {
                    console.error(error);
                    toastr.error("Error occurred while downloading the file.");
                }
            });
        });




        // Function to calculate and update the total hours
        function updateTotalHours(id) {
            let dataTargetId = id
            let scheduledTotal = 0;
            let actualTotal = 0;
            let individualHoursTotal = 0;

            // Loop through each time-field
            $(".time-field").each(function() {

                const scheduledInput = $(this).find("input[name^='scheduled_hours']");
                const actualInput = $(this).find("input[name^='actual_hours']");

                const scheduledIndividualInput = $(this).find("input[name^='scheduled_hours[" +
                    dataTargetId + "][]']");
                const actualIndividualInput = $(this).find("input[name^='actual_hours[" + dataTargetId +
                    "][]']");

                // Parse input values to integers and add to totals
                scheduledTotal += parseFloat(scheduledInput.val()) || 0;
                actualTotal += parseFloat(actualInput.val()) || 0;
                individualHoursTotal += parseFloat(scheduledIndividualInput.val()) || 0;
                individualHoursTotal += parseFloat(actualIndividualInput.val()) || 0;
            });

            // Update the total hours displayed on the page
            $("#scheduleLaborHOurs").text(scheduledTotal + ' hours');
            $("#actualLaborHOurs").text(actualTotal + ' hours');
            $("#scheduledHOurs").text(scheduledTotal + actualTotal + ' hours');

            $("#laborDetailScheduleHours-" + dataTargetId).text(individualHoursTotal + ' hours');
        }

        // Attach the input event listener to update the total on input
        $(".fields-container").on("input", "input[name^='scheduled_hours'], input[name^='actual_hours']",
            function(e) {
                console.log("change")
                var dataTargetId = $(this).attr("data-target-id");
                updateTotalHours(dataTargetId);
            });

        $(".fields-container").on("click", ".remove-time-field", function() {
            var dataTargetId = $(this).attr("data-target-id");
            $(this).closest(".time-field").remove();

            // Update day numbers after deletion
            $(".time-field[data-target-id=" + dataTargetId + "]").each(function(index) {
                $(this).find(".field-header").text("Day " + (index + 1));
            });
            updateTotalHours();
        });

        $(".add-time-field").click(function(e) {
            var dataTargetId = $(this).attr("data-target-id");
            let counter = $(".time-field[data-target-id=" + dataTargetId + "]").length + 1;

            var timeField = $(
                '<div class="time-field" data-target-id="' + dataTargetId + '">' +
                '<button class="remove-time-field" data-target-id="' + dataTargetId +
                '"><i class="fa-solid fa-xmark"></i></button>' +
                '    <h2 class="field-header">Day ' + counter + '</h2>' +
                '    <div class="d-flex">' +
                '        <div class="partial-field">' +
                '            <label class="time-field-label">Scheduled</label>' +
                '            <input class="time-field-input" type="number" name="scheduled_hours[' +
                dataTargetId + '][]" id="scheduled-' +
                dataTargetId + '"  data-target-id="' + dataTargetId + '" placeholder="Type">' +
                '        </div>' +
                '        <div class="partial-field">' +
                '            <label class="time-field-label">Actual</label>' +
                '            <input class="time-field-input" data-target-id="' + dataTargetId +
                '" type="number" name="actual_hours[' + dataTargetId + '][]" id="actual-' +
                dataTargetId + '" placeholder="Type">' +
                '        </div>' +
                '    </div>' +
                '</div>'
            );

            $(this).before(timeField);
            updateTotalHours();
        });
    });
</script>
