@forelse ($Estimates as $estimate)
    <div class='fc-event'>
        <div class="check_item">
            <div class="request_id">Sales Order <span>#{{ $estimate?->opportunityid?->sales_order_number }}</span>
            </div>
            <input type="checkbox" value="{{ encodeID($estimate->id) }}" class="check_box selected-estimates" name=""
                {{ $estimate->schedules->where('date', $scheduleDate)->count() > 0 ? 'checked' : '' }}>
        </div>
        <div class='request_name'>{{ $estimate?->opportunityid?->account?->company_name }}</div>
    </div>
@empty
    <b>No estimate found</span>
@endforelse
