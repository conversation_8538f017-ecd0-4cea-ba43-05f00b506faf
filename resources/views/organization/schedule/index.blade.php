@extends('layouts.admin.master')
@section('title', 'Schedule')
@section('styles')
    <link id="bsdp-css" href="https://unpkg.com/bootstrap-datepicker@1.9.0/dist/css/bootstrap-datepicker3.min.css"
        rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('asset/assets/css/customDateSelector.css') }}">
    <style>
        .fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end,
        .fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start {
            cursor: pointer !important;
        }
    </style>
@endsection
@section('section')
<style>
    #months{
        margin-bottom: 14px !important;
        padding-bottom: 2px !important;
    }
    .hidemodelbtn {
        font-size: 18px !important;
        color: #7e8a9d !important;
    }
    .hidemodelbtn:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .hidemodelbtn:hover {
        cursor: pointer !important;
    }
    @media screen and (max-width: 580px) {

        .hidemodelbtn {
            font-size: 15px !important;
            color: #7e8a9d !important;
        }
    }
</style>
    <section class="dashboard_main">

        <div class="modal fade schedule-detail" id="scheduleDetailModal" tabindex="-1"
            aria-labelledby="scheduleDetailModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0">
                    <div class="modal-header">
                        <h5 class="modal-title" id="scheduleDetailModalLabel" style="font-size: 16px">Job Pack</h5>
                        <button
                    type="button"
                    class="btn-close hidemodelbtn px-3"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                </button>
                    </div>
                    <div class="div-content-scrollable">
                    </div>
                </div>
            </div>
        </div>

        <div class="panel event_calender_main">
            <div class='event_calender_wrapper'>

                <div id='calendar-wrap'>
                    <div id='calendar'></div>
                </div>

                <div id='external-events'>
                    <div class="external-events-filter">
                        <button class="today-btn today">Today</button>
                        <div class="select_field">
                            <label for="months">Sort By:</label>
                            @php
                                 $currentYear = date('Y');
                            @endphp
                            <select name="" id="months" class="select form-control arrow">
                                <option value="" selected disabled>Month</option>
                                <option value="{{$currentYear}}-01-01">Jan</option>
                                <option value="{{$currentYear}}-02-01">Feb</option>
                                <option value="{{$currentYear}}-03-01">Mar</option>
                                <option value="{{$currentYear}}-04-01">Apr</option>
                                <option value="{{$currentYear}}-05-01">May</option>
                                <option value="{{$currentYear}}-06-01">Jun</option>
                                <option value="{{$currentYear}}-07-01">Jul</option>
                                <option value="{{$currentYear}}-08-01">Aug</option>
                                <option value="{{$currentYear}}-09-01">Sep</option>
                                <option value="{{$currentYear}}-10-01">Oct</option>
                                <option value="{{$currentYear}}-11-01">Nov</option>
                                <option value="{{$currentYear}}-12-01">Dec</option>
                            </select>
                        </div>
                    </div>

                    <div class="external-events-wrapper">
                        <h2 class="title_status mb-3">Unscheduled</h2>
                        <input type="search" name="" class="search_field mb-4 filter_search" placeholder="Search">
                        <div class="btn-group drop_modify mb-3" role="group"
                            aria-label="Basic checkbox toggle button group">
                            <input type="checkbox" class="btn-check" id="drop-remove" autocomplete="off">
                            <!-- <label class="btn drop_btn" for="drop-remove">Remove After Drop</label> -->
                        </div>
                        <div class="external-events-list unscheduleList" id='external-events-list'>
                            @forelse ($unscheduleEstimates as $estimate)
                                <div class='fc-event fc-event-main fc-h-event fc-daygrid-event fc-daygrid-block-event'>
                                    <div class="request_id">Sales Order
                                        <span>#{{ $estimate?->opportunityid?->sales_order_number }}</span>
                                    </div>
                                    <div data-id="{{ encodeID($estimate->id) }}" class='request_name'>
                                    {{ \Illuminate\Support\Str::limit($estimate?->opportunityid?->opportunity_name, 23) }}
                                    </div>
                                </div>
                            @empty
                                <p class="text-center">No Record Found</p>
                            @endforelse

                        </div>
                    </div>


                </div>

            </div>
        </div>
        <div class="operation_modal modal fade calender_items_modal" id="selectCalenderItems" tabindex="-1"
            aria-labelledby="selectCalenderItemsLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="selectCalenderItemsLabel">All Estimates</h5>
                        <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">


                        <div class="p-4 pb-0">
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="search" name="" id="searchEstimate"
                                        class="search_field mb-4 allEstimateSearch" placeholder="Search">
                                </div>
                                <div class="col-md-6">
                                    <div class="field">
                                        <select name="" id=""
                                            class="input custom_selectBox basic-single-select filterSchedule">
                                            <option value="" id="all">All</option>
                                            <option id="schedule" value="Scheduled">Scheduled</option>
                                            <option id="unschedule" value="Unscheduled">Unscheduled</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="external-events-list p-4 pt-0" id="allEstimate">


                        </div>


                    </div>
                    <div class="modal-footer mt-4 p-4">
                        <button class="btn primaryblue transparent px-5" data-dismiss="modal"
                            aria-label="Close">Cancel</button>
                        <button type="button" class="btn primaryblue px-5" id="schedule">Update</button>
                    </div>
                </div>
            </div>
        </div>


        <div class="operation_modal modal fade multiSelectDate" id="multiSelectDate" tabindex="-1"
            aria-labelledby="multiSelectDateLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="multiSelectDateLabel">Select Dates you want to schedule</h5>
                        <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">


                        <div class="p-4 pb-0">
                            <div class="row">
                                <div class="col-md-12">
                                    {{-- <input type="text" class="form-control date multiDate" placeholder="Pick the multiple dates"> --}}
                                    <div class="multiDate" data-date-format="MM/DD/YYYY"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer mt-4 p-4">
                        <button class="btn primaryblue transparent px-5" data-dismiss="modal"
                            aria-label="Close">Cancel</button>
                        <button type="button" class="btn primaryblue px-5" id="scheduleMultiDates">Schedule</button>
                    </div>
                </div>
            </div>
        </div>

    </section>
    @include('layouts.admin.attention-modal')
    @push('scripts')
        <script src="https://unpkg.com/bootstrap-datepicker@1.9.0/dist/js/bootstrap-datepicker.min.js"></script>
        @include('organization.schedule.script')
    @endpush
@endsection
