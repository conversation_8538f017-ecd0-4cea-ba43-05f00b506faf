@forelse ($managers as $manager)
    <tbody>
        <tr>
            <td class="select_om">
                <input type="radio" {{ $operation->manager_id == $manager->id ? 'checked' : '' }}
                    name="selectOM" data-id="{{ encodeId($manager->id) }}" class="managerID">
                <label for="result1">{{ $manager->name }}</label>
            </td>
        </tr>
    </tbody>
@empty
    {{-- <p>No Record Found</p> --}}
@endforelse



