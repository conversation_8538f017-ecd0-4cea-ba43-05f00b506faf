@extends('layouts.admin.master')
@section('title', 'Operations')

@section('section')
    <style>
        .select2-container--default {
            margin-top: 21px !important;
        }

        .table_filter_dropdown {
            margin-top: -45px !important;
        }

        .dropdown-toggle::after {
            display: none !important;

        }

        .showMessage {
            display: none;
            padding: 0px 10px 7px 10px;
            background: #c3e6cb;
            color: #155724;
            text-align: left;
        }

        .showMessage p {
            color: #155724;
        }

        .hidemodelbtn {
            font-size: 18px !important;
            color: #7e8a9d !important;
        }

        .hidemodelbtn:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        .hidemodelbtn:hover {
            cursor: pointer !important;
        }

        @media screen and (max-width: 580px) {

            .hidemodelbtn {
                font-size: 15px !important;
                color: #7e8a9d !important;
            }
        }

        .operation-completed {
            background-color: #CFFBFF !important;
            color: #1BB6D5 !important;

        }

        .operation-progress {
            background-color: #E6F1FB !important;
            color: #0074D9 !important;

        }

        .operation-warning {
            background-color: #FFFAE8 !important;
            color: #F1B602 !important;

        }
    </style>
    <section class="dashboard_main pb-5 operations_table_filters">
        <div class="showMessage"></div>
        <div class="table_filter_header mb-4">
            <h2 class="sub_heading">Operations</h2>

            <div class="filters">
                {{-- <div class="download align-items-center">
                    <button type="button" onclick=""
                        class="btn primaryblue transparent px-5 downloadBtn"
                        style="border: 2px solid #2688ea; color: #2688ea !important; border-radius: 6px; height: 38px; width:115px">
                        <img src="{{ asset('admin_assets/images/download-icon1.png') }}" alt="">
                        <span style="font-size:12px;color:#2688ea!important">&nbsp;Download</span>
                    </button>
                </div> --}}
                <form method="POST" id="export-opportunity"
                    action="{{ route(getRouteAlias() . '.export.operations') }}">
                    @csrf
                    <button type="submit" onclick="document.getElementById('excelInput').click()"
                        class="btn primaryblue transparent px-5 downloadBtn"
                        style="border: 2px solid #2688ea; color: #2688ea !important; border-radius: 6px; height: 38px; width:115px">
                        <img src="{{ asset('admin_assets/images/download-icon1.png') }}" alt="">
                        <span style="font-size:12px;color:#2688ea!important">&nbsp;Download</span>
                    </button>
                </form>
                <input type="search" placeholder="Search" name="" id="filter_search"
                    class="clients_Detail_Search filter_search">
                <select name="" id="select_filter" class="select-small basic-single-select">
                    <option value="" selected disabled>Filter</option>
                    <option value="Clear">Clear</option>
                    <option value="Pending">Pending</option>
                    <option value="In Progress">In Progress</option>
                    <option value="Completed">Completed</option>
                </select>
            </div>
        </div>

        <div class="table-responsive">

            <table id="clients_Detail"
                class="table table-striped yajra-datatable custom_datatable display" style="width:100%">
                <thead>
                    <tr>
                        <th>Job#</th>
                        <th>Job Name</th>
                        <th>Property Name</th>
                        <th>Account</th>
                        <th>Account Owner</th>
                        <th>Operation Manager</th>
                        <th>Schedule Date</th>
                        <th>Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>



            </table>
        </div>


    </section>

    <!--Change Status -->
    <div class="operation_modal modal fade" id="changeStatus" tabindex="-1"
        aria-labelledby="changeStatusLabel" aria-hidden="true">
        <div class="modal-dialog m400">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="changeStatusLabel">Edit</h5>
                    <button type="button" class="btn-close hidemodelbtn px-3" data-dismiss="modal"
                        aria-label="Close" style="border: none; background-color: transparent">
                        <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <h2 class="text-md text-primary mb-3">Change Status</h2>
                    <p class="text-placeholder text-sm">You can change organization status</p>

                    <div class="row">
                        <div class="col-6">
                            <div class="radio_group mt-5">
                                <input class="" type="radio" name="project_status"
                                    id="Pending">
                                <label class="label" for="Pending">Pending</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="radio_group mt-5">
                                <input class="" type="radio" name="project_status"
                                    id="InProgress">
                                <label class="label" for="In Progress">In Progress</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="radio_group mt-5">
                                <input class="" type="radio" name="project_status"
                                    id="Completed" checked>
                                <label class="label" for="Completed">Completed</label>
                            </div>
                        </div>
                    </div>


                </div>
                <div class="modal-footer pt-5">
                    <button type="button" class="btn primaryblue w-100 updateStatus">Update</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Assign OM -->
    <div class="operation_modal modal fade" id="assignOm" tabindex="-1"
        aria-labelledby="assignOmLabel" aria-hidden="true">
        <div class="modal-dialog m400">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignOmLabel">Assign OM</h5>
                    <button type="button" class="btn-close hidemodelbtn px-3"
                        data-dismiss="modal" aria-label="Close"
                        style="border: none; background-color: transparent">
                        <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                    </button>
                </div>
                <div class="modal-body">

                    <input type="search" placeholder="Search" name="search" id="searchManager"
                        class="clients_Detail_Search filter_search w-100">

                    <div class="table-responsive assign_om_table mt-4">
                        <table
                            class="table table-striped custom_datatable shadow-none display m-0 p-0 assignOM"
                            style="width:100%">


                        </table>
                    </div>

                </div>
                <div class="modal-footer pt-5">
                    <button type="button" class="btn primaryblue w-100 selectManager">Select</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Generate invoice confirmations -->
    <div class="modal fade" id="generateInvoiceConfirm" tabindex="-1"
        aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered delete-modal">
            <div class="modal-content">
                <div class="modal-body">

                </div>
            </div>
        </div>
    </div>
    @include('layouts.partials.success-modal')

    @push('scripts')
        @include('organization.operation.script')
    @endpush
@endsection
