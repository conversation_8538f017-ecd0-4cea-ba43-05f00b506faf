@extends('layouts.admin.master')
@section('title', 'Operation Detail')
@section('section')
    @include('organization.operation.style')
    <style>
        .download {
            float: right;
            margin-left: 799px;
        }

        .downloadBtn {
            height: 29px;
            width: 96px;
            font-size: 13px;
        }
    </style>
    <section class="dashboard_main">
        <div class="panel">
            <div class="bg-card rounded-lg p-4 p-md-2">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="">Here Bis The Job Name</h3>
                    <div class="d-flex align-items-center" style="gap: 1rem;">
                        <button type="button" class="btn primaryblue transparent px-5 downloadBtn"
                            style="border: 2px solid #2688ea; color: #2688ea !important; border-radius: 6px; height: 38px; width:115px"
                            onclick="downloadDashboardAsPDF()">
                            <img src="{{ asset('admin_assets/images/download-icon1.png') }}"
                                alt="">
                            <span style="font-size:12px;color:#2688ea!important">&nbsp;Download
                                PDF</span>
                        </button>
                        <span class="badge bg-dashboard-warning text-white badge-rounded">
                            <div class="dropdown">
                                <span class="badge bg-dashboard-warning badge-rounded"
                                    style="color: #c0853c">
                                    {{ $generate_estimate->operation_status ?? 'Pending' }}
                                </span>
                            </div>
                        </span>
                    </div>

                    <style>
                        @media print {
                            body * {
                                visibility: hidden;
                            }

                            .dashboard_main,
                            .dashboard_main * {
                                visibility: visible;
                            }

                            .dashboard_main {
                                position: absolute;
                                left: 0;
                                top: 0;
                                width: 100% !important;
                                max-width: 100% !important;
                                margin: 0 !important;
                                padding: 10px !important;
                            }

                            .no-print,
                            .download {
                                display: none !important;
                            }
                        }

                        /* Fix for PDF width issues */
                        .pdf-content {
                            width: 100% !important;
                            max-width: 100% !important;
                        }
                    </style>

                    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js">
                    </script>

                    <script>
                        function downloadDashboardAsPDF() {
                            // Create a clone of your dashboard to preserve original styling
                            const element = document.querySelector('.dashboard_main');
                            const clone = element.cloneNode(true);

                            clone.classList.add('pdf-content');

                            const elementsToHide = clone.querySelectorAll('.download, .no-print');
                            elementsToHide.forEach(el => el.style.display = 'none');

                            document.body.appendChild(clone);

                            const opt = {
                                margin: 10,
                                filename: 'dashboard_report.pdf',
                                image: {
                                    type: 'jpeg',
                                    quality: 0.98
                                },
                                html2canvas: {
                                    scale: 2,
                                    scrollY: 0,
                                    width: clone.scrollWidth,
                                    windowWidth: clone.scrollWidth
                                },
                                jsPDF: {
                                    unit: 'mm',
                                    format: 'a4',
                                    orientation: 'portrait',
                                    compress: true
                                }
                            };

                            // Generate PDF
                            html2pdf().from(clone).set(opt).save().then(() => {
                                document.body.removeChild(clone);
                            });
                        }
                    </script>

                </div>
                <div class="row gy-3 gx-4 header1row">

                    <div class="col-md-3">
                        <div class="text-sm text-muted-foreground">Job no#:
                            <span>{{ $generate_estimate->opportunityid->job_no ?? '' }}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-sm text-muted-foreground">Operation Manager:
                            <span>{{ $generate_estimate->manager->first_name ?? '' }}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-sm text-muted-foreground">Division:
                            <span>{{ $generate_estimate->opportunityid->serviceLine->division->name ?? '' }}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-sm text-muted-foreground">Service Line:
                            <span>{{ $generate_estimate->opportunityid->serviceLine->name ?? '' }}</span>
                        </div>
                    </div>

                </div>
                &nbsp;
                <div class="row ty-3 gx-4 header2row">
                    <div class="col-md-2">
                        <div class="text-sm text-muted-foreground">Property Name:</div>
                        <p class="font-semibold text-foreground mb-0">
                            {{ $generate_estimate->opportunityid->opportunity_name ?? '' }}
                        </p>
                    </div>
                    <div class="col-md-2">
                        <div class="text-sm text-muted-foreground">Account Name:</div>
                        <p class="font-semibold text-foreground mb-0">
                            {{ $generate_estimate->opportunityid->account->company_name ?? '' }}
                        </p>
                    </div>
                    <div class="col-md-2">
                        <div class="text-sm text-muted-foreground">Scheduled Date:</div>
                        <p class="font-semibold text-foreground mb-0">
                            {{ $generate_estimate->latestScheduleDate->date ?? 'Not Schedule' }}
                        </p>
                    </div>
                    <div class="col-md-2">
                        <div class="text-sm text-muted-foreground">Completed Date:</div>
                        <p class="font-semibold text-foreground mb-0">Amendment</p>
                    </div>
                    @php
                        use App\Models\User;
                        $estimator = User::find($generate_estimate->opportunityid->sale_person_id);
                        $estimator = User::find($generate_estimate->opportunityid->estimator_id);
                    @endphp
                    <div class="col-md-2">
                        <div class="text-sm text-muted-foreground">Sales Man:</div>
                        <p class="font-semibold text-foreground mb-0">
                            {{ $estimator->first_name ?? 'N/A' }} {{ $estimator->last_name ?? 'N/A' }}
                        </p>
                    </div>
                    <div class="col-md-2">
                        <div class="text-sm text-muted-foreground">Estimator Name:</div>
                        <p class="font-semibold text-foreground mb-0">
                            {{ $estimator->first_name ?? 'N/A' }} {{ $estimator->last_name ?? 'N/A' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4 header mt-4">
            <!-- Revenue Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card shadow-sm metric-card h-100">
                    <div class="card-body">
                        <h6 class="card-title">Revenue</h6>
                        <div class="value text-dark fs-4 fw-bold">$ {{ number_format($dynamicMetrics['revenue'], 1) }}</div>
                        <div class="estimate text-muted small">Est: ${{ number_format($totalEstimated, 0) }}</div>
                    </div>
                </div>
            </div>

            <!-- Selling Price Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card shadow-sm metric-card h-100">
                    <div class="card-body">
                        <h6 class="card-title">Selling Price</h6>
                        <div class="value text-dark fs-4 fw-bold">{{ number_format($dynamicMetrics['sellingPrice'], 2) }}</div>
                        <div class="estimate text-muted small">Estimated Total</div>
                    </div>
                </div>
            </div>

            <!-- GP Card (Combined GP% and GP$) -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card shadow-sm gp-card h-100">
                    <div class="card-body d-flex align-items-stretch p-3">
                        <div class="flex-fill d-flex flex-column justify-content-center text-center">
                            <div class="label-badge mb-2">GP %</div>
                            <div class="gp-value">{{ $dynamicMetrics['gpPercent'] }}%</div>
                            <div class="est-label">Est: {{ round(($totalEstimated > 0 && $dynamicMetrics['sellingPrice'] > 0) ? (($dynamicMetrics['sellingPrice'] - $totalEstimated) / $dynamicMetrics['sellingPrice']) * 100 : 15, 0) }}%</div>
                        </div>

                        <div class="divider mx-3"></div>

                        <div class="flex-fill d-flex flex-column justify-content-center text-center">
                            <div class="label-badge mb-2">GP$</div>
                            <div class="gp-value">${{ number_format($dynamicMetrics['gpDollar'], 2) }}</div>
                            <div class="est-label">Est: ${{ number_format($dynamicMetrics['sellingPrice'] - $totalEstimated, 0) }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rev/mh Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card shadow-sm metric-card h-100">
                    <div class="card-body">
                        <div style="text-align: center;">
                            <div class="label-badge">Rev/mh</div>
                            <div class="gp-value">${{ number_format($dynamicMetrics['revPerMh'], 2) }}</div>
                            <div class="est-label">{{ $dynamicMetrics['laborHours'] }} labor hours</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-8">
                <div class="card shadow-sm" style="border-radius: 10px;">
                    <div class="card-body">
                        <h3 class="card-title mb-4">Estimated Vs. Actual Cost By Category</h3>
                        @if ($totalActual <= 0.01)
                            <div class="alert alert-info mb-4">
                                No actual cost data available. The chart will show estimated costs only.
                            </div>
                        @endif
                        <div class="chart-container" style="position: relative; height: 240px;">
                            <canvas id="costChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card shadow-sm" style="border-radius: 10px;">
                    <div class="card-body">
                        <h5 class="card-title mb-3">Cost Ratios</h5>
                        @if ($totalActual <= 0.01)
                            <div class="alert alert-info mb-3">
                                No actual cost data available. Percentages will show as 0%.
                            </div>
                        @endif
                        <div class="d-flex align-items-center justify-content-between flex-wrap">
                            <div class="me-3" style="min-width: 160px;">
                                @foreach ($groupedData as $category => $data)
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <div class="d-flex align-items-center">
                                            <span class="color-indicator"
                                                style="background-color: {{ $categoryColors[$category] ?? '#007bff' }};"></span>
                                            <span class="ms-2">{{ ucfirst($category) }}</span>
                                        </div>
                                        <strong>
                                            @if ($totalActual > 0.01)
                                                {{ number_format(($data['actual'] / $totalActual) * 100, 1) }}%
                                            @else
                                                0.0%
                                            @endif
                                        </strong>
                                    </div>
                                @endforeach
                            </div>
                            <div style="width: 150px; height: 253px;">
                                <canvas id="costRatioChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="col-12 d-flex justify-content-between align-items-center">
                        <h3>Estimate Vs Actual Break Down</h3>
                    </div>
                    <div class="card mt-4" style="border-radius: 10px;">
                        <div class="card-body p-0">
                            <div class="">
                                @php
                                    // Group existing EstimateItems by category
                                    $existingGroupedItems = collect($estimatesItems)->groupBy('category_type');

                                                                        // Define all possible categories using enum
                                    $allCategories = \App\Enums\BillCategoryType::values();

                                    // Create complete grouped items including empty categories
                                    $groupedItems = collect($allCategories)->mapWithKeys(function($category) use ($existingGroupedItems) {
                                        return [$category => $existingGroupedItems->get($category, collect())];
                                    });

                                    // Get category metadata from enum
                                    $categoryIcons = [];
                                    $categoryLabels = [];
                                    foreach (\App\Enums\BillCategoryType::cases() as $categoryEnum) {
                                        $categoryIcons[$categoryEnum->value] = $categoryEnum->icon();
                                        $categoryLabels[$categoryEnum->value] = $categoryEnum->label();
                                    }
                                @endphp

                                <table class="table">
                                    <thead>
                                        <tr style="font-size: 12px;">
                                            <th width="18%">Expense</th>
                                            <th class="text-center">Estimated Qty</th>
                                            <th class="text-center">Actual Qty</th>
                                            <th class="text-center">Estimate Cost</th>
                                            <th class="text-center">Actual Cost</th>
                                            <th class="text-center">Variance</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody style="border-radius: 10px;">
                                        @foreach ($groupedItems as $category => $items)
                                            @php
                                                $totalQty = $items->sum('quantity');
                                                $actualQty = $items->flatMap->bills->sum(
                                                    'actual_qty',
                                                ); // ✅ changed from qty
                                                $estimatedCost = $items->sum('total_price');
                                                $actualCost = $items->flatMap->bills->sum(
                                                    'invoice_amount',
                                                );
                                                $variance = $estimatedCost - $actualCost;

                                                $categoryLabel = $categoryLabels[$category] ?? ucfirst(str_replace('_', ' ', $category));
                                                $icon = $categoryIcons[$category] ?? 'operationIcon/other.png';
                                            @endphp

                                            {{-- Summary Row --}}
                                            <tr style="background: #EBF5FE;border-radius: 10px;"
                                                class="{{ $category }}-main-row">
                                                <td>
                                                    <img src="{{ asset($icon) }}"
                                                        alt="{{ $categoryLabel }}" />
                                                    <strong>{{ $categoryLabel }}</strong>
                                                </td>
                                                <td class="text-center">{{ $totalQty }}</td>
                                                <td class="text-center">{{ $actualQty }}</td>
                                                <td class="text-center">
                                                    ${{ number_format($estimatedCost, 2) }}</td>
                                                <td class="text-center">
                                                    ${{ number_format($actualCost, 2) }}</td>
                                                <td class="text-center">
                                                    {{ $variance >= 0 ? '+' : '-' }}${{ number_format(abs($variance), 2) }}
                                                </td>
                                                <td class="text-end toggle-{{ $category }}"
                                                    style="cursor:pointer;">
                                                    <i class="fas fa-chevron-right"></i>
                                                </td>
                                            </tr>

                                            {{-- Detail Items --}}
                                            @foreach ($items as $item)
                                                @php
                                                    $itemActualQty = $item->bills->sum(
                                                        'actual_qty',
                                                    ); // ✅ updated
                                                    $itemActualCost = $item->bills->sum(
                                                        'invoice_amount',
                                                    );
                                                    $itemVariance =
                                                        $item->total_price - $itemActualCost;
                                                @endphp
                                                <tr class="{{ $category }}-details"
                                                    style="display:none;background: #F4F7FA;">
                                                    <td class="ps-4">{{ $item->item_name }}</td>

                                                    <td class="text-center">{{ $item->quantity }}</td>

                                                    <td class="text-center">
                                                        @if ($category === 'labors')
                                                            <input type="number"
                                                                class="form-control form-control-sm labor-qty-input"
                                                                data-item-id="{{ $item->id }}"
                                                                value="{{ $itemActualQty }}"
                                                                style="width: 60px; margin: auto;">
                                                        @else
                                                            {{ $itemActualQty }}
                                                        @endif
                                                    </td>

                                                    <td class="text-center">
                                                        ${{ number_format($item->total_price, 2) }}
                                                    </td>

                                                    <td class="text-center">
                                                        @if ($category === 'labors')
                                                            <input type="number"
                                                                class="form-control form-control-sm labor-cost-input"
                                                                data-item-id="{{ $item->id }}"
                                                                value="{{ $itemActualCost }}"
                                                                style="width: 80px; margin: auto;">
                                                        @else
                                                            ${{ number_format($itemActualCost, 2) }}
                                                        @endif
                                                    </td>

                                                    <td class="text-center">
                                                        {{ $itemVariance >= 0 ? '+' : '-' }}${{ number_format(abs($itemVariance), 2) }}
                                                    </td>

                                                    <td class="text-end">
                                                        @if ($category !== 'labors')
                                                            <button
                                                                class="btn btn-sm open-bill-modal mr-1"
                                                                data-toggle="modal"
                                                                data-target="#addVendorBillModal"
                                                                data-item-id="{{ $item->id }}"
                                                                style="background: #E6F1FB; color: #0074D9; font-size: 14px;">
                                                                <i class="fa fa-plus"
                                                                    style="color: #0074D9;"></i>
                                                                Expense
                                                            </button>
                                                            <i class="fa fa-chevron-right toggle-sub ms-2 mr-4"
                                                                style="cursor:pointer;"></i>
                                                        @endif
                                                    </td>
                                                </tr>

                                                {{-- Item Bill Sub Details --}}
                                                @if ($category !== 'labors')
                                                    <tr class="sub-details"
                                                        style="display:none; border-radius: 10px;">
                                                        <td colspan="7" class="p-0">
                                                            <div
                                                                style="margin: 12px auto; width: 95%;">
                                                                <table class="table mb-0 table-sm"
                                                                    style="border-radius: 8px; border: 1px solid #e1e3e4;">
                                                                    <thead
                                                                        style="background: #EBF5FE;">
                                                                        <tr>
                                                                            <th>Vendor Name</th>
                                                                            <th>Invoice Number</th>
                                                                            <th>Qty</th>
                                                                            <th>Due Date</th>
                                                                            <th>Invoice Amount</th>
                                                                            <th></th>
                                                                            <th></th>
                                                                            <th></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        @forelse ($item->bills as $bill)
                                                                            <tr>
                                                                                <td>{{ $bill->vendor_name }}
                                                                                </td>
                                                                                <td>{{ $bill->invoice_number }}
                                                                                </td>
                                                                                <td>{{ $bill->actual_qty }}
                                                                                </td>
                                                                                {{-- ✅ updated --}}
                                                                                <td>{{ \Carbon\Carbon::parse($bill->due_date)->format('d-m-Y') }}
                                                                                </td>
                                                                                <td>${{ number_format($bill->invoice_amount, 2) }}
                                                                                </td>
                                                                                <td
                                                                                    class="text-center">
                                                                                    @if ($bill->invoice_image)
                                                                                        <img src="{{ asset('operationIcon/opImage.png') }}"
                                                                                            alt="View"
                                                                                            class="img-fluid view-image"
                                                                                            style="cursor:pointer; width: 24px;"
                                                                                            data-url="{{ asset('storage/' . $bill->invoice_image) }}">
                                                                                    @endif
                                                                                </td>

                                                                                <!-- Edit Icon in table row -->
                                                                                <td class="text-end">
                                                                                    <img src="{{ asset('operationIcon/opEdit.png') }}"
                                                                                        class="edit-bill"
                                                                                        data-bill-id="{{ $bill->id }}"
                                                                                        style="cursor:pointer;" />
                                                                                    &nbsp;
                                                                                    <img src="{{ asset('operationIcon/opDelete.png') }}"
                                                                                        style="cursor:pointer;"
                                                                                        class="delete-bill"
                                                                                        data-id="{{ $bill->id }}">
                                                                                </td>

                                                                            </tr>
                                                                        @empty
                                                                            <tr>
                                                                                <td colspan="8"
                                                                                    class="text-center text-muted">
                                                                                    No bills added yet.
                                                                                </td>
                                                                            </tr>
                                                                        @endforelse
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endif
                                            @endforeach
                                        @endforeach
                                    </tbody>
                                </table>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        const allCategories = [...document.querySelectorAll('[class$="-main-row"]')];

                                        allCategories.forEach(mainRow => {
                                            const classList = Array.from(mainRow.classList);
                                            const catClass = classList.find(c => c.endsWith('-main-row'));
                                            if (!catClass) return;
                                            const category = catClass.replace('-main-row', '');

                                            const toggleBtn = document.querySelector(`.toggle-${category}`);
                                            const detailRows = document.querySelectorAll(`.${category}-details`);

                                            toggleBtn?.addEventListener('click', () => {
                                                const icon = toggleBtn.querySelector('i');
                                                const showing = detailRows[0]?.style.display !== 'none';
                                                detailRows.forEach(r => r.style.display = showing ? 'none' :
                                                    'table-row');
                                                icon?.classList.toggle('fa-chevron-down', !showing);
                                                icon?.classList.toggle('fa-chevron-right', showing);

                                                document.querySelectorAll('.sub-details').forEach(
                                                    sub => sub.style.display = 'none'
                                                );
                                            });

                                            document.querySelectorAll(`.${category}-details .toggle-sub`).forEach(
                                                subIcon => {
                                                    subIcon.addEventListener('click', e => {
                                                        e.stopPropagation();
                                                        const parent = subIcon.closest(
                                                            `.${category}-details`);
                                                        const subRow = parent?.nextElementSibling;
                                                        if (subRow && subRow.classList.contains(
                                                                'sub-details')) {
                                                            const open = subRow.style.display !==
                                                                'none';
                                                            subRow.style.display = open ? 'none' :
                                                                'table-row';
                                                            subIcon.classList.toggle('fa-chevron-down',
                                                                !open);
                                                            subIcon.classList.toggle('fa-chevron-right',
                                                                open);
                                                        }
                                                    });
                                                });
                                        });
                                    });
                                </script>


                                <style>
                                    .table td,
                                    .table th {
                                        vertical-align: middle;
                                    }

                                    .toggle-main i,
                                    .toggle-sub {
                                        font-size: 14px;
                                    }
                                </style>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @include('organization.operation.billmodal')
    <script>
        // Get the data from backend
        const groupedData = @json($groupedData);
        const categoryColors = @json($categoryColors);
        const estimatesItems = @json($estimatesItems);
        const phpTotalActual = {{ $totalActual }};

        // Define category mappings using enum
        const categoryNames = @json(collect(\App\Enums\BillCategoryType::cases())->mapWithKeys(function($case) {
            return [$case->value => $case->displayName()];
        }));

        // Get categories from enum
        const rawCategories = @json(\App\Enums\BillCategoryType::values());

        // Use the actual data from groupedData (calculated in PHP)
        const labels = rawCategories.map(key => categoryNames[key]);
        const actualData = rawCategories.map(key => groupedData[key]?.actual || 0);
        const estimatedData = rawCategories.map(key => groupedData[key]?.estimated || 0);
        const pieColors = rawCategories.map(key => categoryColors[key] || '#007bff');

        // Debug logging (remove in production)
        // console.log('Chart Data:', { groupedData, actualData, estimatedData, totalActual: phpTotalActual, labels });

                // Function to update charts with new data
        function updateCharts(newGroupedData) {
            const newActualData = rawCategories.map(key => newGroupedData[key]?.actual || 0);
            const newEstimatedData = rawCategories.map(key => newGroupedData[key]?.estimated || 0);
            const newTotalActual = Math.max(newActualData.reduce((sum, val) => sum + val, 0), 0.01);

            // Update bar chart
            barChart.data.datasets[0].data = newActualData;
            barChart.data.datasets[1].data = newEstimatedData;
            barChart.update('active');

            // Update pie chart with filtered data
            const newPieData = newActualData.map(val => (val / newTotalActual) * 100);
            const filteredPieData = [];
            const filteredLabels = [];
            const filteredColors = [];

            newPieData.forEach((value, index) => {
                if (value > 0) {
                    filteredPieData.push(value);
                    filteredLabels.push(labels[index]);
                    filteredColors.push(pieColors[index]);
                }
            });

            pieChart.data.labels = filteredLabels;
            pieChart.data.datasets[0].data = filteredPieData;
            pieChart.data.datasets[0].backgroundColor = filteredColors;
            pieChart.update('active');

            // Update cost ratios percentages in the sidebar
            updateCostRatiosDisplay(newGroupedData, newTotalActual);
        }

                // Function to update cost ratios display
        function updateCostRatiosDisplay(newGroupedData, newTotalActual) {
            rawCategories.forEach(category => {
                const actualCost = newGroupedData[category]?.actual || 0;
                const percentage = newTotalActual > 0 ? ((actualCost / newTotalActual) * 100) : 0;

                // Find and update the percentage display for this category
                const categoryName = categoryNames[category];

                // More robust selector to find the percentage span
                document.querySelectorAll('.d-flex').forEach(flex => {
                    const spanText = flex.querySelector('span')?.textContent;
                    if (spanText && spanText.includes(categoryName)) {
                        const strongElement = flex.querySelector('strong');
                        if (strongElement) {
                            strongElement.textContent = percentage.toFixed(1) + '%';
                        }
                    }
                });
            });
        }

                // Function to recalculate grouped data from current DOM state
        function recalculateGroupedData() {
            const newGroupedData = {
                equipment: { actual: 0, estimated: 0 },
                labors: { actual: 0, estimated: 0 },
                hard_materials: { actual: 0, estimated: 0 },
                other_costs: { actual: 0, estimated: 0 },
                contractors: { actual: 0, estimated: 0 }
            };

            // Category mapping from table display to internal categories
            const categoryMapping = {
                'Equipment': 'equipment',
                'Labor Cost': 'labors',
                'Material': 'hard_materials',
                'Other Job Cost': 'other_costs',
                'Sub-contractors': 'contractors'
            };

            // Parse each category section in the table
            document.querySelectorAll('[class$="-main-row"]').forEach(categoryRow => {
                const categoryClass = Array.from(categoryRow.classList).find(c => c.endsWith('-main-row'));
                if (!categoryClass) return;

                const category = categoryClass.replace('-main-row', '');

                // Get the category name from the first cell (with icon and text)
                const categoryCell = categoryRow.querySelector('td:first-child');
                if (!categoryCell) return;

                const categoryText = categoryCell.textContent.trim();
                const mappedCategory = Object.keys(categoryMapping).find(key =>
                    categoryText.includes(key)) ?
                    categoryMapping[Object.keys(categoryMapping).find(key => categoryText.includes(key))] :
                    category;

                if (newGroupedData.hasOwnProperty(mappedCategory)) {
                    const actualCostText = categoryRow.querySelector('td:nth-child(5)')?.textContent.replace(/[^\d.-]/g, '') || '0';
                    const estimatedCostText = categoryRow.querySelector('td:nth-child(4)')?.textContent.replace(/[^\d.-]/g, '') || '0';

                    newGroupedData[mappedCategory] = {
                        actual: parseFloat(actualCostText) || 0,
                        estimated: parseFloat(estimatedCostText) || 0
                    };
                }
            });

            return newGroupedData;
        }

        // Bar Chart: Estimated vs Actual
        const ctx1 = document.getElementById('costChart').getContext('2d');
        const barChart = new Chart(ctx1, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                        label: 'Actual Cost',
                        data: actualData,
                        backgroundColor: '#EFA037',
                        borderColor: '#EFA037',
                        borderWidth: 1,
                        borderRadius: 15
                    },
                    {
                        label: 'Estimated Cost',
                        data: estimatedData,
                        backgroundColor: '#FDD032',
                        borderColor: '#FDD032',
                        borderWidth: 1,
                        borderRadius: 15
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                if (value >= 1000000) {
                                    return '$' + (value / 1000000).toFixed(1) + 'M';
                                } else if (value >= 1000) {
                                    return '$' + (value / 1000).toFixed(1) + 'K';
                                }
                                return '$' + value.toLocaleString();
                            }
                        },
                        grid: {
                            drawBorder: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 12,
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': $' + context.raw.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                            }
                        }
                    }
                },
                barPercentage: 0.6,
                categoryPercentage: 0.8,
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // Pie Chart: Cost Ratios (using dynamic actual costs)
        const totalActual = Math.max(phpTotalActual, 0.01);
        const pieData = actualData.map(val => (val / totalActual) * 100);

        // Filter out categories with 0 values for pie chart
        const filteredPieData = [];
        const filteredLabels = [];
        const filteredColors = [];
        const filteredActualData = [];

        pieData.forEach((value, index) => {
            if (value > 0) {
                filteredPieData.push(value);
                filteredLabels.push(labels[index]);
                filteredColors.push(pieColors[index]);
                filteredActualData.push(actualData[index]);
            }
        });

        const ctxPie = document.getElementById('costRatioChart').getContext('2d');
        const pieChart = new Chart(ctxPie, {
            type: 'pie',
            data: {
                labels: filteredLabels,
                datasets: [{
                    data: filteredPieData,
                    backgroundColor: filteredColors,
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw || 0;
                                const amount = filteredActualData[context.dataIndex] || 0;
                                return `${context.label}: ${value.toFixed(1)}% ($${amount.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                })})`;
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
                });

                // Simple function to redirect to operation detail page for fresh data
        window.refreshOperationData = function() {
            const encodedId = '{{ encodeId($generate_estimate->id) }}';
            const currentUrl = `{{ route('organization.operation.detail', encodeId($generate_estimate->id)) }}`;
            window.location.href = currentUrl;
        };
    </script>
@endsection
