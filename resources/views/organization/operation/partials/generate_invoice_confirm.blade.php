<div class="text-center">
    <svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="35" cy="35" r="35" fill="#27AE60" />
        <path d="M22.3369 36.81L29.5732 44.0463L47.6639 25.9556" stroke="white" stroke-width="6" stroke-linecap="round"
            stroke-linejoin="round" />
    </svg>
</div>

<h2 class="delete-request">Operation Completed </h2>
<p class="are-sure">Do you want to generate an invoice?</p>
<div class="buttons-wraper">
    <button type="button" class="cancel-btn" data-dismiss="modal">Cancel</button>

    @if (empty($operation->invoice))
        @if (optional($Organization->stripeAccount)?->acc_connected == 1 &&
                optional($Organization->stripeAccount)?->acc_connected_id != null)
            <a class="conform-btn text-decoration-none"
                href="{{ route(getRouteAlias() . '.invoices.operation', ['operationId' => encodeID($operation->id)]) }}">Yes</a>
        @else
            <a class="conform-btn text-decoration-none"
                href="{{ route(getRouteAlias() . '.invoices.operation', ['operationId' => encodeID($operation->id)]) }}">Yes</a>

                <!-- <a class="conform-btn text-decoration-none"
                href="{{ route(getRouteAlias() . '.account.settings', 'acc_connect=null') }}">Yes</a> -->
        @endif
    @else
        <a class="conform-btn text-decoration-none"
            href="{{ route(getRouteAlias() . '.invoices.index') }}">Yes</a>
    @endif

</div>
