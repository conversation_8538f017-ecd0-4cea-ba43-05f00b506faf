<style>
    .header1row {
        border-radius: 6px;
        background: #EAF5FB;
        padding: 10px;
    }

    .header2row {
        background: none;
        border-radius: 4px;
    }

    .dropdown-toggle {
        color: #F09A37;
    }

    .aditable {
        width: 113px;
        font-size: 12px;
        align-items: center;
        height: 29px;
        float: right;
        margin-right: 27px;
    }

    .chart-labels {
        font-size: 14px;
        gap: 10px;
    }

    .color-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 3px;
    }

    #costRatioChart {
        max-width: 210px;
        max-height: 500px;
    }

    .ms-2 {
        margin-left: 4px !important;
    }

    .text-primary {
        color: #0074d9 !important;
    }

    td i {
        margin-right: 5px;
    }

    td {
        vertical-align: middle;
    }

    .fas {
        border: 1px solid #817a7a;
        margin-right: 8px;
        padding: 3px;
        background: #e6e3e3;
        border-radius: 5px;
    }

    td:first-child {
        text-align: left;
    }

    .font {
        font-size: 13px;
        font-weight: 700;
    }

    .bill {
        height: 37px;
        border-radius: 6px;
    }

    input[type="date"]::-webkit-calendar-picker-indicator {
        display: none;
    }

    /* azhar desing code */
    .bg-card {
        background-color: #ffffff;
    }

    .text-muted-foreground {
        color: #6c757d;
    }

    .text-foreground {
        color: #212529;
    }

    .bg-dashboard-warning {
        background-color: #FFECD7;
    }

    .badge-rounded {
        border-radius: 999px;
        padding: 0.5rem 1rem;
        font-size: 14px;
        ;
    }

    .font-semibold {
        font-weight: 600;
    }

    .card-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 11px;
        padding: 2px 8px;
        border-radius: 10px;
        background-color: #0d6efd;
        /* Bootstrap primary */
        color: #fff;
    }

    .card-content {
        position: relative;
        padding: 1rem;
        text-align: center
    }

    .card h6 {
        font-size: 14px;
        font-weight: 500;
        color: #000000;
        margin-bottom: 0.5rem;
    }

    .card .value {
        font-size: 16px;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .card .estimate {
        font-size: 12px;
        color: #6c757d;
    }

    .metric-card {
        border-radius: 8px;
        border: none;
        height: 100px;
        margin-bottom: 20px;
        background-color: #ffffff;
    }

    .metric-card .card-body {
        padding: 16px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        height: 100%;
    }

    .metric-card .card-title {
        font-size: 14px;
        font-weight: 600;
        color: #6c757d;
        margin-bottom: 8px;
    }

    .metric-card .value {
        font-size: 20px;
        font-weight: 700;
        color: #000;
        margin-bottom: 2px;
        line-height: 1.1;
    }

    .metric-card .estimate {
        font-size: 11px;
        color: #6c757d;
        margin: 0;
        line-height: 1.2;
    }

    /* GP Card Specific Styling */
    .gp-card {
        border-radius: 8px;
        border: none;
        height: 100px;
        margin-bottom: 20px;
        background-color: #ffffff;
    }

    .gp-card .card-body {
        padding: 16px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Universal label badge styling for all cards */
    .label-badge {
        background-color: #E8F4FD;
        color: #0066CC;
        padding: 2px 8px;
        border-radius: 8px;
        font-size: 10px;
        font-weight: 500;
        display: inline-block;
        margin-bottom: 4px;
        width: fit-content;
    }

    .gp-value {
        font-size: 20px;
        font-weight: 700;
        color: #000;
        margin-bottom: 1px;
        line-height: 1.1;
    }

    .est-label {
        font-size: 11px;
        color: #6c757d;
        margin: 0;
        line-height: 1.2;
    }

    .divider {
        width: 1px;
        background-color: #e9ecef;
        margin: 0 10px;
        height: 60px;
        align-self: center;
    }

    .gp-card .flex-fill {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
    }

    /* Rev/mh Card Specific Styling */
    .metric-card .label-badge {
        background-color: #E8F4FD;
        color: #0066CC;
        padding: 2px 8px;
        border-radius: 8px;
        font-size: 10px;
        font-weight: 500;
        display: inline-block;
        margin-bottom: 4px;
        width: fit-content;
    }

    /* Additional styling for metric cards to ensure consistency */
    .metric-card h6,
    .metric-card .card-title {
        font-size: 12px !important;
        font-weight: 500 !important;
        color: #6c757d !important;
        margin-bottom: 4px !important;
    }

    .metric-card .value,
    .metric-card .fs-4,
    .metric-card .fw-bold {
        font-size: 20px !important;
        font-weight: 700 !important;
        color: #000 !important;
        margin-bottom: 2px !important;
        line-height: 1.1 !important;
    }

    .metric-card .estimate,
    .metric-card .small,
    .metric-card .text-muted {
        font-size: 11px !important;
        color: #6c757d !important;
        margin: 0 !important;
        line-height: 1.2 !important;
    }

    .shadow-sm {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08) !important;
    }

    /* Header row styling */
    .header {
        margin-top: 24px !important;
        margin-bottom: 24px !important;
    }

    /* Ensure proper spacing between cards */
    .row.header .col-xl-3,
    .row.header .col-lg-6,
    .row.header .col-md-6,
    .row.header .col-sm-12 {
        padding-left: 10px;
        padding-right: 10px;
    }
</style>
