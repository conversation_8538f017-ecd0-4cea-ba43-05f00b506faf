<script type="text/javascript">
    var status;
    var search;
    var filterStatus;
    var updateId;
    var managerId;
    var operations_table

    $(document).ready(function() {

        // operations_table = $('.yajra-datatable').DataTable({
        //     processing: true,
        //     responsive: true,
        //     scrollX: true,
        //     serverSide: true,
        //     ajax: {
        //         url: "{{ route(getRouteAlias() . '.operation-list') }}",
        //         data: function(d) {
        //             d.operation_status = filterStatus
        //         }
        //     },
        //     columns: [{
        //             data: 'opportunityid.sale_no',
        //             name: 'request.sale_no',
        //             orderable: false
        //         }, {
        //             data: 'opportunityid.opportunity_name',
        //             name: 'opportunityid.opportunity_name',
        //             orderable: false
        //         },
        //         {
        //             data: 'property',
        //             name: 'property',
        //             orderable: false
        //         },
        //         {
        //             data: 'client.name',
        //             name: 'client.name',
        //             orderable: false
        //         },
        //         {
        //             data: 'account_owner',
        //             name: 'account_owner',
        //             orderable: false
        //         },
        //         {
        //             data: 'manager.name',
        //             name: 'manager.name',
        //             orderable: false
        //         },
        //         {
        //             data: 'schedule_date',
        //             name: 'schedule_date',
        //             orderable: false
        //         },
        //          {
        //             data: 'operation_status',
        //             name: 'operation_status',
        //             orderable: false
        //         }, {
        //             data: 'action',
        //             name: 'action',
        //             orderable: false
        //         },


        //     ],
        //     language: {
        //         zeroRecords: "Sorry we could not find any results",
        //         paginate: {
        //             "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
        //             "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
        //         },


        //     },
        //     dom: '<"top">rt<"bottom"lip><"clear">',
        // });
        operations_table = $('.yajra-datatable').DataTable({
            processing: true,
            responsive: true,
            scrollX: true,
            serverSide: true,
            ajax: {
                url: "{{ route(getRouteAlias() . '.operation-list') }}",
                data: function(d) {
                    d.operation_status = filterStatus
                }
            },
            columns: [{
                    data: 'opportunityid.sale_no',
                    name: 'request.sale_no',
                    orderable: false
                },
                {
                    data: 'opportunityid.opportunity_name',
                    name: 'opportunityid.opportunity_name',
                    orderable: false
                },
                {
                    data: 'property',
                    name: 'property',
                    orderable: false
                },
                {
                    data: 'account.company_name', // Company name from accounts table
                    name: 'account.company_name',
                    orderable: false
                },
                {
                    data: 'account_owner_name', // Account owner's full name
                    name: 'account_owner_name',
                    orderable: false
                },
                {
                    data: 'manager.name',
                    name: 'manager.name',
                    orderable: false
                },
                {
                    data: 'schedule_date',
                    name: 'schedule_date',
                    orderable: false
                },
                {
                    data: 'operation_status',
                    name: 'operation_status',
                    orderable: false
                },
                {
                    data: 'action',
                    name: 'action',
                    orderable: false
                }
            ],
            language: {
                zeroRecords: "Sorry we could not find any results",
                paginate: {
                    "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                    "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
                },
            },
            dom: '<"top">rt<"bottom"lip><"clear">',
        });
        // Custome Search Filter
        $('.operations_table_filters #filter_search').on('keyup', function() {
            // alert(this.value);
            operations_table.search(this.value).draw();
        });
        // Custom Select Filter
        $('.operations_table_filters #select_filter').on('change', function() {
            filterStatus = $(this).val();
            operations_table.columns().search('').draw();
        });
    });

    $(document).on('click', '#changeOperationStatus', function() {
        var dataStatus = $(this).attr("data-value");
        console.log("dataStatus", dataStatus);
        if (dataStatus == 'In Progress') {
            $("#InProgress").prop("checked", true);
        } else {
            $("#" + dataStatus).prop("checked", true);
        }

        updateId = $(this).attr("data-id");
    });
    $(document).on('click', '#Pending,#Completed,#InProgress', function() {
        status = $(this).attr('id');
    })
    $(document).on('click', '.updateStatus', function() {
        var url =
            '{{ URL::route(getRouteAlias() . '.update-operation-status') }}';

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                "_token": "{{ csrf_token() }}",
                status: status,
                id: updateId,
            },

            success: function(response) {
                status = '';
                $('#changeStatus').modal('hide');
                operations_table.draw();
                if (response.status == 'Completed') {
                    $('#generateInvoiceConfirm .modal-body').html(response
                        .view);
                    $('#generateInvoiceConfirm').modal('show');

                } else {
                    $('.showMessage').css('display', 'flex');

                    // After 3 seconds (3000ms), fade it out
                    setTimeout(function() {
                        $('.showMessage').fadeOut('slow', function() {
                            // Optional: Reset the display property to 'none' after fading out
                            $(this).css('display', 'none');
                        });
                    }, 3000);
                    $('.showMessage').html('');
                    $('.showMessage').append(
                        `<p class="para mt-3 text-center">Status Changed Successfully!</p>`
                    );
                }
            },
            error: function(response) {
                status = '';
            }
        })
    })

    function manipulateTable() {

        var url =
            '{{ URL::route(getRouteAlias() . '.getManagers') }}';
        $.ajax({
            url: url,
            type: 'POST',
            data: {
                "_token": "{{ csrf_token() }}",
                updateId,
                search
            },
            success: function(response) {
                $('.assign_om_table > table.assignOM').html('');
                if (JSON.parse(response)) {
                    // $("#searchManager").prop('readonly', false).removeClass('disabled');
                    $('.assign_om_table > table.assignOM').html(JSON.parse(response))
                } else {
                    // $("#searchManager").prop('readonly', true).addClass('disabled');
                    $('.assign_om_table > table.assignOM').html('<p>No data found!</p>')
                }
                // $('.assignOM').html(JSON.parse(response))
            },
            error: function(response) {}
        })
    }
    $(document).on('click', '#esstimateAssignOm', function() {
        updateId = $(this).attr('data-id');
        managerId = $(this).attr('data-managerID');
        $("#searchManager").val('');
        search = null;
        manipulateTable();
    })
    $(document).on('click', '.managerID', function() {
        $('.invalid-feedback').html('');
        managerId = $(this).attr('data-id');
    })

    $(document).on('click', '.selectManager', function() {
        $('.invalid-feedback').html('');
        if (!managerId) {
            $(document).find('[name=search]').after(
                '<div class="invalid-feedback laravel_error" >' +
                'Please select any option to assign manager.' +
                '</div>');
            $(".invalid-feedback").css("display", "block");
            return;
        }

        var url =
            '{{ URL::route(getRouteAlias() . '.assignManager') }}';

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                "_token": "{{ csrf_token() }}",
                managerId: managerId,
                id: updateId,
            },

            success: function(response) {
                $('#assignOm').modal('hide');
                // Show the element
                $('.showMessage').css('display', 'flex');

                // After 3 seconds (3000ms), fade it out
                setTimeout(function() {
                    $('.showMessage').fadeOut('slow', function() {
                        // Optional: Reset the display property to 'none' after fading out
                        $(this).css('display', 'none');
                    });
                }, 3000);

                $('.showMessage').html('');
                $('.showMessage').append(
                    `<p class="para mt-3 text-center">Manager Assigned Successfully!</p>`
                );
                operations_table.draw();
            },
            error: function(response) {
                $('#assignOm').modal('hide');
                operations_table.draw();
            }
        })
    })
    $(document).on('keyup', '#searchManager', function() {
        search = $(this).val();
        manipulateTable();
    })
</script>

{{-- <script>
        var ctx1 = document.getElementById('costChart').getContext('2d');
        var costChart = new Chart(ctx1, {
            type: 'bar',
            data: {
                labels: ['Equipment', 'Labor Cost', 'Material', 'Other Job Cost',
                    'Sub-Contractor'
                ],
                datasets: [{
                    label: 'Actual Cost',
                    data: [600, 400, 300, 500, 700],
                    backgroundColor: '#EFA037',
                    borderColor: '#EFA037',
                    borderWidth: 1,
                    borderRadius: 15
                }, {
                    label: 'Estimated Cost',
                    data: [500, 450, 350, 600, 750],
                    backgroundColor: '#FDD032', // Blue
                    borderColor: '#FDD032',
                    borderWidth: 1,
                    borderRadius: 15, 
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 200,
                            callback: function(value) {
                                return '$' + value;
                            }
                        },
                        grid: {
                            drawBorder: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 12,
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle',
                            marginLeft: 10
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': $' + context.raw;
                            }
                        }
                    }
                },
                barPercentage: 0.6,
                categoryPercentage: 0.8
            }
        });

        const ctxPie = document.getElementById('costRatioChart').getContext('2d');
        const costRatioChart = new Chart(ctxPie, {
            type: 'pie', // <- PIE chart, not doughnut
            data: {
                labels: ['Equipment', 'Labor', 'Material', 'Other Job', 'Subcontractors'],
                datasets: [{
                    data: [25.2, 7.5, 27.5, 30.5, 13.5],
                    backgroundColor: [
                        '#28a745', // Equipment - green
                        '#ffc107', // Labor - yellow
                        '#ff8c33', // Material - orange
                        '#ff5e5e', // Other Job - red
                        '#3b8beb' // Subcontractors - blue
                    ],
                    borderWidth: 0 // no border lines
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false 
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.raw}%`;
                            }
                        }
                    }
                }
            }
        });
    </script> --}}




