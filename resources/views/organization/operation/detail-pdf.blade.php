<!DOCTYPE html>
<html>
<head>
    <title>Estimate #{{ $generate_estimate->id }}</title>
    <style>
        /* PDF-Specific Styling */
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
        }
        .container {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
        }
        .card {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }
        .card-body {
            padding: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
        }
        .text-center {
            text-align: center;
        }
        .color-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
            vertical-align: middle;
        }
        .page-break {
            page-break-after: always;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            color: #2688ea;
            margin-bottom: 5px;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 10px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Estimate Details</h1>
        <p>Generated on: {{ now()->format('d M Y h:i A') }}</p>
    </div>

    <div class="container">
        <!-- Your existing page content here -->
        <!-- Example: -->
        <div class="card">
            <div class="card-body">
                <h3>Estimate Information</h3>
                <table>
                    <tr>
                        <th>Estimate ID</th>
                        <td>{{ $generate_estimate->id }}</td>
                    </tr>
                    <tr>
                        <th>Total Price</th>
                        <td>{{ number_format($totalPrice, 2) }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Cost Categories -->
        <div class="card">
            <div class="card-body">
                <h3>Cost Breakdown</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Estimated</th>
                            <th>Actual</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($groupedData as $category => $data)
                        <tr>
                            <td>
                                <span class="color-indicator" style="background-color: {{ $categoryColors[$category] }}"></span>
                                {{ ucfirst($category) }}
                            </td>
                            <td>{{ number_format($data['estimated'], 2) }}</td>
                            <td>{{ number_format($data['actual'], 2) }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Items List -->
        <div class="card">
            <div class="card-body">
                <h3>Estimate Items</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Category</th>
                            <th>Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($estimatesItems as $item)
                        <tr>
                            <td>{{ $item->name }}</td>
                            <td>{{ $itemCategoryMap[$item->id] ?? 'N/A' }}</td>
                            <td>{{ number_format($item->total_price, 2) }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>© {{ date('Y') }} Your Company Name. All rights reserved.</p>
    </div>
</body>
</html>