@extends('layouts.admin.master')
@section('title', 'Settings')
@section('section')
<style>
    .select2-container{
        height: 50px !important;
    }
</style>
    <section class="dashboard_main">

        <div class="settings_tab_grid">

            <x-settings_component.settings_tab />

            <div class="settings_content">

                <form id="general_settings_form" method="POST"
                    action={{ route(getRouteAlias() . '.general.settings.update', encodeID($company->id)) }}
                    enctype="multipart/form-data">
                    @csrf
                    <div class="panel">
                        <div class="panel_header">
                            <h2 class="panel_title">Company Details</h2>
                        </div>

                        <div class="row gy-4 mt-4 max-766">

                        <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">First Name <span class="steric">*</span></label>
                                    <input type="text" placeholder="First Name" name="first_name"
                                        value="{{ old('first_name', $company->first_name) }}"
                                        class="input form-control">
                                    @if ($errors->has('first_name'))
                                        <div class="laravel_error">{{ $errors->first('first_name') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Last Name <span class="steric">*</span></label>
                                    <input type="text" placeholder="Last Name"
                                        value="{{ old('last_name', $company->last_name) }}" name="last_name" class="input form-control">
                                    @if ($errors->has('last_name'))
                                        <div class="laravel_error">{{ $errors->first('last_name') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Company Name <span class="steric">*</span></label>
                                    <input type="text" placeholder="Company Name" name="company_name"
                                        value="{{ old('company_name', $company->company_name) }}"
                                        class="input form-control">
                                    @if ($errors->has('company_name'))
                                        <div class="laravel_error">{{ $errors->first('company_name') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Email <span class="steric">*</span></label>
                                    <input type="email" placeholder="Email" readOnly="true"
                                        value="{{ old('email', $company->email) }}" class="input form-control">
                                    @if ($errors->has('email'))
                                        <div class="laravel_error">{{ $errors->first('email') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Phone Number <span class="steric">*</span></label>
                                    <input type="text" name="phone_no"
                                        value="{{ $address?->phone_no }}" placeholder="Phone number"
                                        class="input form-control" minlength="12" maxlength="12"
                                        oninput="maskPhoneNumber(event)">
                                    @if ($errors->has('phone_no'))
                                        <div class="laravel_error">{{ $errors->first('phone_no') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Website URL</label>
                                    <input type="text" name="website_url" placeholder="Website URL"
                                        value="{{ old('website_url', $address->website_url ?? '') }}"
                                        class="input form-control">
                                    <!-- @if ($errors->has('website_url'))
                                        <div class="laravel_error">{{ $errors->first('website_url') }}</div>
                                    @endif -->
                                </div>
                            </div>

                        </div>


                    </div>

                    <div class="panel mt-4">
                        <div class="panel_header">
                            <h2 class="panel_title">Company Address</h2>
                        </div>

                        <div class="row gy-4 mt-4 max-766">

                            <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Address 1 <span class="steric">*</span></label>
                                    <input type="text" placeholder="Address" name="address1" id="address1"
                                        value="{{ old('address1', $address->address1 ?? '') }}" class="input form-control">
                                    @if ($errors->has('address1'))
                                        <div class="laravel_error">{{ $errors->first('address1') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Address 2 <span
                                            class="placeholder-text">(Optional)</span></label>
                                    <input type="text" name="address2" id="address2" placeholder="Address"
                                        value="{{ old('address2', $address->address2 ?? '') }}" class="input form-control">
                                    @if ($errors->has('address2'))
                                        <div class="laravel_error">{{ $errors->first('address2') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">City <span class="steric">*</span></label>
                                    <input type="text" name="city" value="{{ old('city', $address->city ?? '') }}"
                                        placeholder="City" class="input form-control">
                                    @if ($errors->has('city'))
                                        <div class="laravel_error">{{ $errors->first('city') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">State <span class="steric">*</span></label>
                                    <input type="text" name="state" value="{{ old('state', $address->state ?? '') }}"
                                        placeholder="State" class="input form-control">
                                    @if ($errors->has('state'))
                                        <div class="laravel_error">{{ $errors->first('state') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Zip Code <span class="steric">*</span></label>
                                    <input type="text" name="zip" value="{{ old('zip', $address->zip ?? '') }}"
                                        placeholder="Zip Code" class="input form-control">
                                    @if ($errors->has('zip'))
                                        <div class="laravel_error">{{ $errors->first('zip') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Time-Zone <span class="steric">*</span></label>
                                    <div class="absolute-error">
                                        <select name="timezone" id=""
                                            class="input custom_selectBox basic-single-select-search">
                                            <option value="" selected disabled>Select Timezone</option>
                                            @foreach (timezone_identifiers_list() as $timezone)
                                                <option value="{{ $timezone }}"
                                                    {{ old('timezone', $address->timezone ?? '') == $timezone ? 'selected' : '' }}>
                                                    {{ $timezone }}</option>
                                            @endforeach
                                        </select>
                                        @if ($errors->has('timezone'))
                                            <div class="laravel_error">{{ $errors->first('timezone') }}</div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Date Format <span class="steric">*</span></label>
                                    <div class="absolute-error">
                                        <select name="date_format" id=""
                                            class="input custom_selectBox basic-single-select">
                                            <option value="" selected disabled>Select Format</option>
                                            @foreach (['Y/m/d', 'm/d/Y', 'd/m/Y'] as $format)
                                                <option value="{{ $format }}"
                                                    {{ old('date_format', $address->date_format ?? '') == $format ? 'selected' : '' }}>
                                                    {{ $format }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @if ($errors->has('date_format'))
                                        <div class="laravel_error">{{ $errors->first('date_format') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Time Format <span class="steric">*</span></label>
                                    <div class="absolute-error">
                                        <select name="time_format" class="input custom_selectBox basic-single-select">
                                            <option value="" selected disabled>Select Format</option>
                                            @foreach (['H:i A' => '24 hours format', 'h:i A' => '12 hours format'] as $key => $value)
                                                <option value="{{ $key }}"
                                                    {{ old('time_format', $address->time_format ?? '') == $key ? 'selected' : '' }}>
                                                    {{ $value }}</option>
                                            @endforeach
                                        </select>
                                        @if ($errors->has('time_format'))
                                            <div class="laravel_error">{{ $errors->first('time_format') }}</div>
                                        @endif
                                    </div>
                                </div>
                            </div> -->

                        </div>

                    </div>

                    <div class="d-flex align-items-center justify-content-end gap-3 flex-wrap mt-4">
                        <button type="submit" class="btn primaryblue px-5 min-w-174">Update</button>
                    </div>

                </form>


            </div>

        </div>


    </section>
@endsection
