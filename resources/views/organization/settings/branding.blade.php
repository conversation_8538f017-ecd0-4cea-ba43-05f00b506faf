@extends('layouts.admin.master')
@section('title', 'Settings')

@section('section')
    @auth
        <section class="dashboard_main">

            <div class="settings_tab_grid">

                <x-settings_component.settings_tab />

                <div class="settings_content">

                    <form id="brandingForm" action="{{ route(getRouteAlias() . '.save-branding') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="panel">
                            <div class="panel_header">
                                <h2 class="panel_title">Estimate Branding setting</h2>
                            </div>

                            <div class="row gx-5 mt-4">
                                <div class="col-lg-6">

                                    <label class="label mb-2">Company Logo</label>
                                    <div class="drop_zone_wrapper branding_drop_wrapper">
                                        <label for="file_upload" class="upload_documents_wrapper">

                                            <img style="max-height:100px;" height="100%"
                                                src="{{ $organization?->profile_photo_path ? asset('storage/user_images/' . $organization->profile_photo_path) : '' }}"
                                                id="image-preview" src="" alt="">

                                            <div class="drop-zone__prompt text-center">
                                                <svg width="34" height="34" viewBox="0 0 34 34" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                        d="M9.36275 2.85175C11.4866 1.02029 14.1955 0.00879434 17 0C22.7162 0 27.4614 4.25 27.9778 9.73038C31.3608 10.2085 34 13.0411 34 16.5176C34 20.3341 30.8168 23.375 26.9599 23.375H21.25C20.9682 23.375 20.698 23.2631 20.4987 23.0638C20.2994 22.8645 20.1875 22.5943 20.1875 22.3125C20.1875 22.0307 20.2994 21.7605 20.4987 21.5612C20.698 21.3619 20.9682 21.25 21.25 21.25H26.962C29.7054 21.25 31.875 19.0995 31.875 16.5176C31.875 13.9336 29.7075 11.7831 26.9599 11.7831H25.8974V10.7206C25.8995 6.00313 21.947 2.125 17 2.125C14.7047 2.13417 12.4883 2.96317 10.7504 4.4625C9.14175 5.848 8.30025 7.51825 8.30025 8.82938V9.78137L7.35462 9.8855C4.386 10.2106 2.125 12.648 2.125 15.5507C2.125 18.6681 4.73875 21.25 8.03463 21.25H12.75C13.0318 21.25 13.302 21.3619 13.5013 21.5612C13.7006 21.7605 13.8125 22.0307 13.8125 22.3125C13.8125 22.5943 13.7006 22.8645 13.5013 23.0638C13.302 23.2631 13.0318 23.375 12.75 23.375H8.03463C3.6295 23.375 0 19.9028 0 15.5507C0 11.8044 2.69025 8.70187 6.25175 7.91562C6.55562 6.08175 7.735 4.25425 9.36275 2.85175Z"
                                                        fill="#90A0B7" />
                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                        d="M16.2471 8.8102C16.3458 8.71125 16.4631 8.63275 16.5922 8.57918C16.7213 8.52562 16.8596 8.49805 16.9994 8.49805C17.1391 8.49805 17.2775 8.52562 17.4066 8.57918C17.5357 8.63275 17.6529 8.71125 17.7516 8.8102L24.1266 15.1852C24.3261 15.3847 24.4382 15.6553 24.4382 15.9374C24.4382 16.2196 24.3261 16.4902 24.1266 16.6897C23.9271 16.8892 23.6565 17.0013 23.3744 17.0013C23.0922 17.0013 22.8216 16.8892 22.6221 16.6897L18.0619 12.1273V30.8124C18.0619 31.0942 17.9499 31.3645 17.7507 31.5637C17.5514 31.763 17.2812 31.8749 16.9994 31.8749C16.7176 31.8749 16.4473 31.763 16.2481 31.5637C16.0488 31.3645 15.9369 31.0942 15.9369 30.8124V12.1273L11.3766 16.6897C11.1771 16.8892 10.9065 17.0013 10.6244 17.0013C10.3422 17.0013 10.0716 16.8892 9.87214 16.6897C9.67263 16.4902 9.56055 16.2196 9.56055 15.9374C9.56055 15.6553 9.67263 15.3847 9.87214 15.1852L16.2471 8.8102Z"
                                                        fill="#90A0B7" />
                                                </svg>
                                                <p class="placeholder-text font-14">Drop your logo here, or
                                                    <span>browse</span>
                                                </p>
                                                <p class="placeholder-text font-14">PNG, JPEG, JPG, SVG, Max size: 2MB</p>
                                            </div>

                                            <input type="file" data-max_length="1" class="input_file drop-zone__input"
                                                name="image" id="file_upload" onchange="fileValue(this)" accept="image/*">
                                        </label>
                                        <div class="branding_error text-danger"></div>
                                        @if ($errors->has('image'))
                                            <div class="laravel_error">{{ $errors->first('image') }}</div>
                                        @endif
                                    </div>

                                </div>
                                <div class="col-lg-6">
                                    <div class="field">
                                        <label for="" class="label mb-2">Primary Color</label>
                                        <div class="d-flex align-items-center gap-4 flex-wrap" style="gap: 10px !important;">

                                            <label for="Green" class="branding_color_btn" style="background-color:#32923c;">
                                                Green
                                                <input type="radio" name="primary_color" value="#32923c" id="Green"
                                                    {{ $organization->primary_color == '#32923c' ? 'checked' : '' }}>
                                            </label>
                                            <label for="Red" class="branding_color_btn" style="background-color:#e53b3b;">
                                                Red
                                                <input type="radio" name="primary_color" value="#e53b3b" id="Red"
                                                    {{ $organization->primary_color == '#e53b3b' ? 'checked' : '' }}>
                                            </label>

                                            <label for="Orange" class="branding_color_btn" style="background-color:#fb901e;">
                                                Orange
                                                <input type="radio" name="primary_color" value="#fb901e" id="Orange"
                                                    {{ $organization->primary_color == '#fb901e' ? 'checked' : '' }}>
                                            </label>

                                            <label for="Yellow" class="branding_color_btn" style="background-color:#fdf33a;">
                                                Yellow
                                                <input type="radio" name="primary_color" value="#fdf33a" id="Yellow"
                                                    {{ $organization->primary_color == '#fdf33a' ? 'checked' : '' }}>
                                            </label>

                                            <label for="Blue" class="branding_color_btn" style="background-color:#2fb6ef;">
                                                Blue
                                                <input type="radio" name="primary_color" value="#2fb6ef" id="Blue"
                                                    {{ $organization->primary_color == '#2fb6ef' ? 'checked' : '' }}>
                                            </label>

                                            <label for="Purple" class="branding_color_btn" style="background-color:#c540f9;">
                                                Purple
                                                <input type="radio" name="primary_color" value="#c540f9" id="Purple"
                                                    {{ $organization->primary_color == '#c540f9' ? 'checked' : '' }}>
                                            </label>

                                            <label for="Pink" class="branding_color_btn" style="background-color:#f082e0;">
                                                Pink
                                                <input type="radio" name="primary_color" value="#f082e0" id="Pink"
                                                    {{ $organization->primary_color == '#f082e0' ? 'checked' : '' }}>
                                            </label>

                                            <label for="Brown" class="branding_color_btn"
                                                style="background-color:#eb903b;">
                                                Brown
                                                <input type="radio" name="primary_color" value="#eb903b" id="Brown"
                                                    {{ $organization->primary_color == '#eb903b' ? 'checked' : '' }}>
                                            </label>

                                            <label for="Gray" class="branding_color_btn"
                                                style="background-color:#b7bab7;">
                                                Gray
                                                <input type="radio" name="primary_color" value="#b7bab7" id="Gray"
                                                    {{ $organization->primary_color == '#b7bab7' ? 'checked' : '' }}>
                                            </label>

                                            <label for="Black" class="branding_color_btn"
                                                style="background-color:#0d0d0d;">
                                                Black
                                                <input type="radio" name="primary_color" value="#0d0d0d" id="Black"
                                                    {{ $organization->primary_color == '#0d0d0d' ? 'checked' : '' }}>
                                            </label>

                                            <label for="White" class="branding_color_btn"
                                                style="background-color:#ffffff;color:black;border:1px solid #0d0d0d">
                                                White
                                                <input type="radio" name="primary_color" value="#ffffff" id="White"
                                                    {{ $organization->primary_color == '#ffffff' ? 'checked' : '' }}>
                                            </label>
                                            @if ($errors->has('primary_color'))
                                                <div class="laravel_error">{{ $errors->first('primary_color') }}</div>
                                            @endif
                                        </div>

                                        {{-- <label for="" class="label mb-2 mt-5">Secondary Color</label> --}}
                                        {{-- <div class="d-flex align-items-center gap-4 flex-wrap">
                                            <label for="default" class="branding_color_btn" style="background-color:#003366;">
                                                Default
                                                <input type="radio" name="secondary_color" value="#003366" id="default"
                                                    {{ $organization->secondary_color == '#003366' ? 'checked' : '' }}>
                                            </label>

                                            <label for="Red" class="branding_color_btn" style="background-color:#FC0B0B;">
                                                Red
                                                <input type="radio" name="secondary_color" value="#FC0B0B" id="Red"
                                                    {{ $organization->secondary_color == '#FC0B0B' ? 'checked' : '' }}>
                                            </label>

                                            <label for="Purple" class="branding_color_btn" style="background-color:#8C04F6;">
                                                Purple
                                                <input type="radio" name="secondary_color" value="#8C04F6" id="Purple"
                                                    {{ $organization->secondary_color == '#8C04F6' ? 'checked' : '' }}>
                                            </label>
                                            @if ($errors->has('secondary_color'))
                                                <div class="laravel_error">{{ $errors->first('secondary_color') }}</div>
                                            @endif
                                        </div> --}}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center justify-content-end gap-3 flex-wrap mt-5">
                            <!-- <button type="reset" class="btn primaryblue transparent px-5 min-w-174">Cancel</button> -->
                            <button type="submit" id="submitButton" class="btn primaryblue px-5 min-w-174">Save & Continue</button>
                        </div>
                    </form>

                </div>

            </div>

            <div class="branding_image_modal modal fade" id="branding_image_modal" tabindex="-1"
            aria-labelledby="branding_image_modalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="branding_image_modalLabel">Branding Image</h5>
                        <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <div class="p-4 pb-0">
                            <div class="row">
                                <div class="col-md-12">
                                    <p id="invalidImageMessage">Resize the image to 84x44 pixels or click "Yes" for automatic resizing to make it compatible.</p>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="modal-footer mt-4 p-4">
                        <button class="btn primaryblue transparent px-5" data-dismiss="modal"
                            aria-label="Close">Cancel</button>
                        <button type="button" class="btn primaryblue px-5"  onclick="resizeImageButton()" id="resizeImageButton">Yes</button>
                    </div>
                </div>
            </div>
        </div>

        </section>
    @endauth
@endsection
