@extends('layouts.admin.master')
@section('title', 'Settings')
@section('section')
    <style>
        .pricing {
            display: flex;
            margin-left: 10px;
            justify-content: center;
            align-items: center;
            text-align: center;
            width: auto !important;
            height: 40px;
        }

        .pricing span {
            margin-left: 10px !important;
        }

        .text-placeholder {
            margin-left: 10px !important;
        }

        .field {
            margin-top: 16px !important;
        }
    </style>
    <section class="dashboard_main">
        <?php
        $date = $organization->expire_untill;
        
        if ($organization->payment_mode != 'trial') {
            $date = $organization?->subscription?->ends_at;
        }
        
        // Ensure $date is a Carbon instance
        if (is_string($date)) {
            $date = \Carbon\Carbon::parse($date); // Convert string to Carbon instance
        }
        
        $isExpired = now()->setTime(0, 0, 0)->gt($date);
        
        ?>

        @if ($isExpired)
            <div class="alert alert-danger" role="alert">
                {{ 'Your Package has expired. Please purchase a package to continue using ' . config('app.name') . ' services.' }}
            </div>
        @endif

        <div class="settings_tab_grid">

            <x-settings_component.settings_tab />

            <div class="settings_content h-fit">


                <div class="panel">
                    <div class="panel_header">
                        <h2 class="panel_title">Current Plan</h2>
                    </div>

                    <div class="row mt-4 pb-5">
                        <div class="col-lg-6">
                            <h2 class="text-md text-primary mb-3 mt-5">
                                {{ $organization?->subscription?->plan?->name == config('custom.plans.premium.name') ? '1 Month Premium Package' : config('custom.plans.free.days') . ' days Free trail' }}
                            </h2>
                            <p class="text-placeholder text-sm leading-5">Create powerful proposals and
                                estimate your jobs
                                accurately. Try {{ config('app.name') }} for Free.</p>
                            <div class="d-flex align-items-center gap-3 mt-5">
                                <span class="text-placeholder text-sm">Expire</span>
                                <span
                                    class="text-md  {{ \Carbon\Carbon::parse($date)->isToday() ? 'text-danger' : 'text-primary' }}">{{ customdateformat(Carbon\Carbon::parse($date)) }}
                                </span>
                            </div>

                            <div class="item_list mt-5 d-flex align-items-center gap-3">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <rect x="23.9102" y="24" width="23.0811" height="24"
                                        rx="11.5405" transform="rotate(-180 23.9102 24)"
                                        fill="#003366" />
                                    <path
                                        d="M10.9009 16C10.6083 16 10.3312 15.884 10.1536 15.684L7.75055 12.986C7.67625 12.9027 7.62201 12.8077 7.59092 12.7066C7.55984 12.6055 7.55252 12.5001 7.56939 12.3965C7.58625 12.293 7.62697 12.1933 7.68921 12.1031C7.75146 12.0129 7.83401 11.9341 7.93214 11.871C8.03024 11.8077 8.14208 11.7615 8.26125 11.735C8.38041 11.7085 8.50457 11.7022 8.62659 11.7165C8.74862 11.7309 8.86611 11.7655 8.97235 11.8185C9.07858 11.8715 9.17146 11.9418 9.24567 12.0253L10.8269 13.7992L14.8024 8.37472C14.9343 8.19561 15.1443 8.06826 15.3865 8.02058C15.6288 7.97291 15.8833 8.00881 16.0944 8.12041C16.5336 8.35243 16.6688 8.8439 16.3944 9.21765L11.6972 15.624C11.617 15.7338 11.5065 15.8254 11.3752 15.8907C11.2439 15.956 11.0959 15.9932 10.9439 15.9989C10.9291 16 10.9157 16 10.9009 16Z"
                                        fill="white" />
                                </svg>

                                <span class="text-placeholder text-sm">
                                    {{ $organization?->subscription?->plan?->name == config('custom.plans.premium.name') ? '1 ' . config('custom.plans.premium.interval') . ' Premium Package' : 'Free ' . config('custom.plans.free.days') . ' days trial' }}
                                </span>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="plan_cards">

                                <!-- <div class="para mb-4 text-danger text-right">Your trial period has expired, Please upgrade your account.</div> -->

                                <div class="plan_card basic">
                                    <div class="plan_header">
                                        <h2 class="heading">{{ config('custom.plans.free.name') }}</h2>
                                        @if ($organization->subscription?->plan?->name != config('custom.plans.premium.name'))
                                            <div class="status warning w-fit">
                                                {{ now()->setTime(0, 0, 0)->gt($date) ? 'Expired' : 'Active' }}
                                            </div>
                                        @endif

                                    </div>

                                    <div class="para mt-3">{{ config('custom.plans.free.name') }} plan
                                        gives you full access
                                        to {{ config('app.name') }} software for
                                        {{ config('custom.plans.free.days') }}
                                        days.</div>
                                    {{-- <div class="pricing mt-4">7 Days <span>/ Free</span></div> --}}
                                    <div
                                        class="d-flex align-items-center flex-wrap justify-content-between mt-4">
                                        <div class="pricing">{{ config('custom.plans.free.days') }}
                                            Days <span>Free
                                                Trial</span></div>
                                        {{-- @if (now()->gt($date) && $organization->subscription?->plan?->name == 'Basic')
                                            <a href="{{ route(getRouteAlias() . '.upgradeSubscription') }}"
                                    class="btn btn-warning">Upgrade</a>
                                    @endif --}}
                                    </div>
                                </div>

                                <div class="plan_card pro mt-4">
                                    <div class="plan_header">

                                        <h2 class="heading">Premium</h2>
                                        @if (!empty($organization->subscription->canceled_at))
                                            <div class="status warning w-fit">
                                                Canceled
                                            </div>
                                        @endif
                                    </div>
                                    <div class="para mt-3">Premium plan gives you full access to
                                        {{ config('app.name') }}
                                        software for an
                                        month.
                                    </div>
                                    <div
                                        class="d-flex align-items-center flex-wrap justify-content-between mt-4">
                                        <div class="pricing">
                                            ${{ config('custom.plans.premium.price') }} <span>/
                                                Month</span>
                                        </div>
                                        @if ($organization->subscription?->plan?->name == 'Premium')

                                            @if (now()->setTime(0, 0, 0)->gt($date))
                                                <a href="{{ route(getRouteAlias() . '.upgradeSubscription') }}"
                                                    class="btn btn-warning">Purchase</a>
                                            @else
                                                <div class="status warning w-fit">
                                                    Active
                                                </div>
                                            @endif
                                        @else
                                            <a href="{{ route(getRouteAlias() . '.upgradeSubscription') }}"
                                                class="btn btn-warning">Purchase</a>
                                        @endif

                                        {{-- @if (now()->gt($date))
                                            <a href="{{ route(getRouteAlias() . '.upgradeSubscription') }}"
                                    class="btn btn-warning">Upgrade</a>
                                    @else
                                    <div class="status warning w-fit">
                                        Active
                                    </div>
                                    @endif --}}

                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                </div>

                @if (!$isExpired)
                    <!-- Stripe express accounts -->
                    @if (request()->query('acc_connect') && optional($organization->stripeAccount)?->acc_connected == 0)
                        <div class="alert alert-danger  mt-4" role="alert">
                            {{ 'Please connnect your stripe account to create invoices and payments' }}
                        </div>
                    @endif
                    <div class="panel mt-4">
                        <div class="panel_header">
                            <h2 class="panel_title">Stripe Account</h2>
                            @if (optional($organization->stripeAccount)?->acc_connected == 1 &&
                                    optional($organization->stripeAccount)?->acc_connected_id)
                                <a href="javascript::void(0)" class="btn primaryblue">Account
                                    Connected</a>
                            @else
                                <a href="{{ route(getRouteAlias() . '.stripe.account.create') }}"
                                    class="btn btn-warning">Connect
                                    Account</a>
                            @endif
                        </div>

                    </div>
                @endif

                <div class="panel mt-4">
                    <div class="panel_header">
                        <h2 class="panel_title">Billing Address</h2>
                    </div>
                    <form method="POST" id="billing_address_form"
                        action={{ route(getRouteAlias() . '.update.company.address') }}>
                        @csrf
                        <div class="row mt-4 gy-4">

                            <div class="col-lg-6 col-md-4">
                                <div class="field">
                                    <label class="label mb-2">Address 1 <span
                                            class="steric">*</span></label>
                                    <input type="text" name="address1" id="address1"
                                        value="{{ old('address1', $address->address1 ?? '') }}"
                                        placeholder="Address 1" class="input form-control"
                                        {{ $isExpired ? 'readonly' : '' }}>
                                    @if ($errors->has('address1'))
                                        <div class="laravel_error">{{ $errors->first('address1') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-4">
                                <div class="field">
                                    <label class="label mb-2">Address 2 <span
                                            class="placeholder-text">(Optional)</span></label>
                                    <input type="text" name="address2" id="address2"
                                        value="{{ old('address2', $address->address2 ?? '') }}"
                                        placeholder="Address 2" class="input form-control"
                                        {{ $isExpired ? 'readonly' : '' }}>
                                    @if ($errors->has('address2'))
                                        <div class="laravel_error">{{ $errors->first('address2') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-4">
                                <div class="field">
                                    <label class="label mb-2">City <span class="steric">*</span></label>
                                    <input type="text" name="city"
                                        value="{{ old('city', $address->city ?? '') }}"
                                        placeholder="City" class="input form-control"
                                        {{ $isExpired ? 'readonly' : '' }}>
                                    @if ($errors->has('city'))
                                        <div class="laravel_error">{{ $errors->first('city') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-4">
                                <div class="field">
                                    <label class="label mb-2">State <span
                                            class="steric">*</span></label>
                                    <input type="text" name="state"
                                        value="{{ old('state', $address->state ?? '') }}"
                                        placeholder="State" class="input form-control "
                                        {{ $isExpired ? 'readonly' : '' }}>
                                    @if ($errors->has('state'))
                                        <div class="laravel_error">{{ $errors->first('state') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-4">
                                <div class="field">
                                    <label class="label mb-2">Zip Code <span
                                            class="steric">*</span></label>
                                    <input type="text" name="zip"
                                        value="{{ old('zip', $address->zip ?? '') }}"
                                        placeholder="Zip Code" class="input form-control"
                                        {{ $isExpired ? 'readonly' : '' }}>
                                    @if ($errors->has('zip'))
                                        <div class="laravel_error">{{ $errors->first('zip') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-4">
                                <div class="field">
                                    <label class="label mb-2">Phone Number <span
                                            class="steric">*</span></label>
                                    <input type="text" name="phone_no"
                                        value="{{ old('phone_no', $address->phone_no ?? '') }}"
                                        placeholder="Phone number" class="input form-control"
                                        minlength="12" maxlength="12"
                                        oninput="maskPhoneNumber(event)"
                                        {{ $isExpired ? 'readonly' : '' }}>
                                    @if ($errors->has('phone_no'))
                                        <div class="laravel_error">{{ $errors->first('phone_no') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                </div>

                <div
                    class="{{ $isExpired ? 'd-none' : '' }}  d-flex align-items-center gap-4 flex-wrap justify-content-end mt-5">
                    {{-- <a href="" class="btn primaryblue transparent min-w-174">Cancel</a> --}}
                    <button type="submit" class="btn primaryblue min-w-174">Update</button>
                </div>
                </form>


            </div>
        </div>

        <x-settings_component.help_modal />


    </section>
@endsection
