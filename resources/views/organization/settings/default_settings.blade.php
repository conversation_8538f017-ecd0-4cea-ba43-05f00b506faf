@extends('layouts.admin.master')

@push('tinymce-scripts')
<!-- TinyMCE -->
<script src="{{ asset('assets/tinymce/js/tinymce/tinymce.min.js') }}"></script>
@endpush
@section('title', 'Default Settings Preview')
@section('section')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">

    <style>
        .text-blue {
            color: #0094d6;
        }
        .downloadbtnnn{
            width: 100% !important;
            background: #E6F1FB;
            padding: 6px 14px 6px 14px;
            display: block !important;
            border-radius: 12px;
            /* font-size: 10px; */
            color: #117BDD;
        }

        .radio-inline {
            display: inline;
        }

        .radio-inline-input {
            clip: rect(1px, 1px, 1px, 1px);
            position: absolute !important;
            text-align: center;
        }

        .radio-inline-label {
            display: inline-block;
            width: 47%;
            padding: 5px 28px;
            margin-right: 5px;
            border-radius: 12px;
            font-size: 12px;
            /* border: 1px solid #0094d6; */
            background: #E6F1FB;

            color: #0094d6;
            position: relative;
        }

        .radio-inline-input:checked + .radio-inline-label {
            /* background: #e4e4e4; */
            color: #0094d6;
            border: 1px solid #0094d6;
            text-align: center;
        }

        /* Adding tick icon */
        .radio-inline-input:checked + .radio-inline-label::after {
            content: "\2713"; /* Unicode for the check mark */
            font-size: 14px;
            color: #0094d6;
            text-align: center;
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
        }

    </style>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Nunito:wght@200;300;400;600;700;800;900&display=swap');

        button:focus,
        #multiplefileupload:focus{
            outline: none;
            box-shadow: none;

        }
        #multiplefileupload2:focus{
            outline: none;
            box-shadow: none;

        }
        #multiplefileupload3:focus{
            outline: none;
            box-shadow: none;

        }
        #multiplefileupload4:focus{
            outline: none;
            box-shadow: none;

        }
        #multiplefileupload:focus{
            outline: none;
            box-shadow: none;

        }
        #multiplefileupload{
            display: none;
        }
        #multiplefileupload2{
            display: none;
        }
        #multiplefileuploadgallery{
            display: none;
        }
        #multiplefileupload3{
            display: none;
        }
        #multiplefileupload4{
            display: none;
        }
        .fileinput-cancel{
            display: none;
        }
        textarea:focus{
            outline: none;
            box-shadow: none;
        }
        a,
        a:hover{
            text-decoration: none;
        }
        .upload-area{
            text-align: center !important;
        }

        /*----------multiple-file-upload-----------*/
        .file-upload-contain{
            position: relative;
            width: 100%;
            margin-bottom: 40px;
        }
        .file-upload-contain .file-input,
        .file-upload-contain .file-preview{
            position: initial;
        }
        .file-upload-contain .file-drop-zone{
            border: 1px dashed #D6D6D6;
            width: 100%;
            transition: 0.3s;
            margin: 0;
            padding: 0;
            border-radius: 20px;
            background-color: white;
            min-height: auto;
        }
        .file-upload-contain .file-drop-zone.clickable:hover,
        .file-upload-contain .file-drop-zone.clickable:focus,
        .file-upload-contain .file-highlighted{
            /* border: 2px dashed #1e80e8 !important; */
            background-color: white;
        }
        .upload-area i {
            color: #1e80e8;
            font-size: 50px;
        }
        .upload-area p {
            margin-bottom: 30px;
            /* margin-top: 30px; */
            font-size: 14px;
            font-weight: 600;
            color: #2580e8;
        }
        .upload-area p b {
            color: #1e80e8;
        }
        .upload-area button {
            padding: 8px 16px;
            min-width: 150px;
            font-size: 16px;
            font-weight: 600;
            color: #fff;
            background-color: #1e80e8;
            border: 2px solid #1e80e8;
            border-radius: 50px;
            transition: 0.3s;
        }
        .upload-area button:hover{
            background-color: #1e80e8;
            box-shadow: 0px 4px 8px rgb(37 128 232 / 48%);
        }
        .file-preview{
            padding: 0;
            border: none;
            margin-bottom: 30px;
        }
        .file-preview .fileinput-remove{
            display: none;
        }
        .file-drop-zone-title{
            padding: 30px 10px;
        }
        .file-drop-zone .file-preview-thumbnails{
            cursor: pointer;
        }
        .file-preview-frame{
            cursor: default;
            display: flex;
            align-items: center;
            border: none;
            background-color: #2580e8;
            box-shadow: none;
            border-radius: 8px;
            width: 100%;
            padding: 15px;
            margin: 8px 0px;
        }
        .file-preview-frame:not(.file-preview-error):hover{
            border: none;
            box-shadow: 0 0 10px 0 rgb(0 0 0 / 20%);
        }
        .file-preview-frame .kv-file-content{
            min-width: 45px;
            min-height: 45px;
            width: 45px;
            height: 45px;
            border-radius: 4px;
            margin-right: 10px;
            background-color: #fff;
            padding: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .file-preview-image {
            border-radius: 4px;
        }
        .file-preview-frame .file-footer-caption{
            padding-top: 0;
        }
        .file-preview-frame .file-footer-caption{
            text-align: left;
            margin-bottom: 0;
        }
        .file-detail{
            font-size: 14px;
            height: auto;
            width: 100%;
            line-height: initial;
        }
        .file-detail .file-caption-name{
            color: #fff;
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 6px;
        }
        .file-detail .file-size{
            color: #f1f8fe;
            font-size: 12px;
        }
        .kv-zoom-cache {
            display: none;
        }
        .file-preview-frame .file-thumbnail-footer{
            height: auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }
        .file-preview-frame .file-drag-handle,
        .file-preview-frame .file-upload-indicator{
            float: none;
        }
        .file-preview-frame .file-footer-buttons{
            float: none;
            display: flex;
            align-items: center;
        }
        .file-preview-status.text-center {
            display: none;
        }
        .kv-file-remove.file-remove {
            border: none;
            background-color: #ef2f2f;
            color: #fff;
            width: 25px;
            height: 25px;
            font-size: 12px;
            border-radius: 4px;
            margin: 0px 4px;
        }
        .file-drag-handle.file-drag {
            border: none;
            background-color: #fff;
            color: #2580e8;
            width: 25px;
            height: 25px;
            font-size: 12px;
            border-radius: 4px;
            margin: 0px 4px;
        }
        .kv-file-upload.file-upload{
            border: none;
            background-color: #48bd22;
            color: #fff;
            width: 25px;
            height: 25px;
            font-size: 12px;
            border-radius: 4px;
            margin: 0px 4px;
        }
        .file-thumb-loading{
            background: none !important;
        }
        .file-preview-frame.sortable-chosen {
            background-color: #64a5ef;
            border-color: #64a5ef;
            box-shadow: none!important;
        }

    </style>
    <style>
        .btn-toggle {

            padding: 0;
            position: relative;
            border: none;
            height: 1.6rem;
            width: 3.2rem;
            border-radius: 1.5rem;
            color: #6b7381;
            background: #bdc1c8;
        }
        .btn-toggle:focus, .btn-toggle:focus.active, .btn-toggle.focus, .btn-toggle.focus.active {
            outline: none;
        }
        .btn-toggle:before, .btn-toggle:after {
            line-height: 1.5rem;
            width: 4rem;
            text-align: center;
            font-weight: 600;
            font-size: .75rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            position: absolute;
            bottom: 0;
            transition: opacity .25s;
        }
        .btn-toggle:before {
            content: '';
            left: -8rem;
            font-weight: 900;
            font-size: 10px;
            color: #7C8091;
            fill-opacity: #7C8091;
        }
        .btn-toggle:after {
            content: '';
            left: -8rem;
            font-weight: 900;
            font-size: 10px;

            opacity: 1;
        }
        .active:after {
            color: #0074D9; /* New color */
        }
        .btn-toggle > .handle {
            position: absolute;
            margin-top: 0.7px !important;
            top: 0.1875rem;
            left: 0.1875rem;
            width: 1.125rem;
            height: 1.125rem;
            border-radius: 1.125rem;
            background: #fff;
            transition: left .25s;
        }
        .btn-toggle.active {
            transition: background-color .25s;
        }
        .btn-toggle.active {
            background-color: #117BDD;
        }
        .btn-toggle.active > .handle {
            left: 1.6875rem;
            transition: left .25s;
        }
        .btn-toggle.active:before {
            /* opacity: .5; */
            display: none;
        }
        .btn-toggle.active:after {
            opacity: 1;
        }
        .sidemenuul li{
            padding: 10px 5px;
            border: 1px solid #E2E4EA;
            border-radius: 8px;
        }
        .sidemenuul li p{
            color: #3B4159;
        }
        .dnbtn{
            width: 100%;
            /* height: Hug (37px)px; */
            padding: 6px 14px 6px 14px;
            gap: 12px;
            border-radius: 12px;
            background: #E6F1FB;
            opacity: 0px;
            border: none;
            font-size: 16px !important;
            color: #117BDD;

        }
        .sendcbtn p{
            font-size: 16px;
        }
        .sendcbtn{
            width: 100%;
            background: #0074D9;
            padding: 6px 14px 6px 14px;
            gap: 12px;
            border-radius: 12px;
            opacity: 0px;
            font-size: 16px;
            color: white;
            border: none;

        }
        .leftimg{
            position: absolute;
            top: 17px;
            left: 6.6%;
        }
        .proposal {
            width: fit-content;
            height: 50px;
            margin-top: 25px;
            /* margin-left: -21px; */
            /* padding: 13px 47px 19px 105px; */
            padding: 12px 60px;
            text-align: center;
            font-size: 15px;
            gap: 10px;
            border-radius: 0px 100px 100px 0px;
            color: white;
            opacity: 0px;
            background: #0074D9;
        }
        .proposal_personal {
            width: fit-content;
            height: 50px;
            margin-top: 25px;
            margin-left: -25px !important;
            /* padding: 13px 0px 19px 54px; */
            padding: 12px 60px;
            text-align: center;
            font-size: 15px;
            gap: 10px;
            border-radius: 0px 100px 100px 0px;
            color: white;
            opacity: 0px;
            background: #0074D9;
        }

        .proposal b{
            color: white;
        }
        .proposal_personal b{
            color: white;
        }

        .panel {
            background: #ffffff;
            box-shadow: 0px 0px 12px rgba(36, 185, 236, 0.08);
            border-radius: 8px;

        }
        .panel23{
            padding: 0px !important;
        }
        .table td, .table th {
            border-bottom: none;
            border-top: none;
        }
        .table thead th {

            border-bottom: none;
        }
        .main-div-sec{
            overflow-y: scroll;
            height: 793px;
            width: 100%;
        }
        .main-div-sec::-webkit-scrollbar {
            display: none; /* Webkit browsers ke liye scroll bar ko hide karna */
        }

        /* Firefox ke liye */
        .main-div-sec {
            scrollbar-width: none; /* Firefox ke liye scroll bar hide karna */
        }
        .sidenavsec{
            height: 793px;
        }
        .custombtn{

            background-color: transparent;
            border: none;
            text-align: justify;
        }
        .bluetext b{
            color: #0074D9 !important;
        }
        .browntext b{
            color: #7C8091 !important;
        }
        .edit_cover_section{
            display: none;
        }

        .edit_gallery_section{
            display: none;
        }
        .edit_about_section{
            display: none;
        }
        .edit_personal_section{
            display: none;
        }
        .edit_sample_section{
            display: none;
        }
        .edit_terms_section{
            display: none;
        }
        .edit_payment_section{
            display: none;
        }
        .input-group-append {
            cursor: pointer;
        }
        button{
            cursor: pointer;
        }
        div, p, span, label, h1, h2, h3, h4, h5, h6 {
            overflow-wrap: anywhere !important;
            font-size: 16px;
            white-space: normal !important; /* Ensure wrapping */
            word-break: break-word; /* Fallback for older browsers */

        }
        input, textarea, select{
            font-size: 16px;
        }

    </style>
    <style>
        .image-container {
            position: relative;
            width: 100%;
            margin-top: 100px;
            max-width: 100%; /* Adjust to your needs */
            overflow: hidden;
        }

        .image-container img {
            width: 100%;
            height: auto;
            /* Applying a mask to create the curved effect */
            /* mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><path fill="white" d="M100,0 C80,0 20,10 0,40 L0,100 L100,100 Z"/></svg>'); */
            mask-size: 100% 100%;
            -webkit-mask-size: 100% 100%; /* For Safari support */
        }

        .centered-text {
            position: absolute;
            top: 60%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
            font-weight: bold;
        }
        .centered-text strong{
            color: #ffffff;
            font-weight: 800;
            font-size: 9.83mm;
            font-weight: 600;
            text-transform: uppercase;
            line-height: 15.83mm;
            --tw-text-opacity: 1;
        }

        .image-container::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 190px; /* Adjust height to control shadow size */
            background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent); /* Creates the fading shadow */
            pointer-events: none; /* Ensures the shadow doesn't interfere with any interactions */
        }
        .subtitle-text-main {
            background: #0074D9;
            width: fit-content;
            /* display: none; */
            height: auto;
            padding: 13px 61px;

            border-bottom-left-radius: 0.5rem;
            color: white;
        }
        .subtitle-text-main p{
            color: white;
        }
        input{
            height: 32px;
        }
        .pspdfkit-1thsp3e {
            position: relative;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            /* display: none; */
            margin-top: 90px;
            max-height: 97mm;
            overflow: hidden;
            --tw-bg-opacity: 1;
            background-color: rgb(203 213 225 / var(--tw-bg-opacity));
            margin-top: 3rem;
        }
        .pspdfkit-1eoji5i {
            position: absolute;
            left: 0;
            top: 0;
            height: 80%;
            width: 100%;
            /* background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(0, 0, 0, 0) 100%); */
            content: '';
        }

        .pspdfkit-1q9q9cv {
            width: 100%;
            -webkit-flex: 1 1 0%;
            -ms-flex: 1 1 0%;
            flex: 1 1 0%;
            /* background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); */
            --tw-gradient-from: rgb(245,246,252,0.52) var(--tw-gradient-from-position);
            --tw-gradient-to: rgb(117,19,93,0.73) var(--tw-gradient-to-position);
            --tw-gradient-stops: var(--tw-gradient-from),var(--tw-gradient-to);
            object-fit: cover;
        }
        .panel22{
            height: auto !important;
            min-height: 683px !important;
            position: relative;
        }
        .bottom_row{
            position: absolute;
            bottom: 0;
            width: 100%;
            left: 15px;
        }
        #gtermstext{
            /* display: none; */
        }
        #multiplefileupload5{
            display: none;
        }
        .around-padding-all{
            padding: 0px 23px !important;
        }
        .around-padding-all2{
            padding: 0px 36px !important;
        }

        @media (min-width: 576px) {
            body .modal-logo {
                max-width: 496px !important;
            }
        }
        @media (max-width: 576px) {
            body .modal-logo {
                max-width: 96% !important;
            }
        }
        .gallery-image {
            position: relative;
            display: block;
        }

        .delete-icon {
            position: absolute;
            top: 10px;
            right: 26px;
            display: none;
            z-index: 10;
        }

        .col-md-6:hover .delete-icon {
            display: inline-block;
        }
        #multiplefileuploadtemplate{
            display: none;
        }
        .bottom_row .col-6 b{
            font-size: 16px;
        }
        .sendcbtn:hover{
            background-color: #0056b3;
            color: white;
        }
        .note-btn {
            display: inline-block;
            font-weight: 400;
            margin-bottom: 0;
            text-align: center;
            vertical-align: middle;
            touch-action: manipulation;
            cursor: pointer;
            background-image: none;
            white-space: nowrap;
            outline: 0;
            color: #333;
            background-color: #fff;
            border: 1px solid #dae0e5;
            padding: 0px 7px !important;
            font-size: 14px;
            line-height: 1.4;
            border-radius: 3px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        .note-editor.note-airframe .note-editing-area .note-editable, .note-editor.note-frame .note-editing-area .note-editable {
            padding: 25px !important;
            overflow: auto;
            word-wrap: break-word;
        }
        .signature {
            font-family: "Reenie Beanie";
            font-size: 29px;
            font-style: normal;
            font-weight: 400;
            line-height: 13px;
            color: #192a3e;
        }
        input, textarea {
            font-size: 16px !important;
        }

        .sidemenuul li {
            padding: 10px 13px !important;
            border: 1px solid #E2E4EA;
            border-radius: 8px;
        }
    </style>

    <section class="dashboard_main">
        <div class="row justify-content-around">
            <div class="col-lg-4 sidenavsec">
                <div class="panel mt-4 mb-5">
                    <div class="main_side_menu">

                        <div class="row px-1">
                            <p style="color: #0074D9;">Select Proposal Pages

                                <br>
                                <label for="" style="color: #90A0B7; font-size: 16px;">Customize and send your proposal  for  e-sign</label>
                            </p>
                            <!-- <button style="background-color: #E1F4FF; border: none; height: 31px;
        padding: 0px 12px; border-radius: 5px; margin-left: auto;"  data-toggle="modal" data-target="#logoModel"><b style="color: #0074D9;">Company Logo</b></button> -->

                        </div>
                        <!-- side nav for proposal settings page -->
                        <div class="row">
                            <ul class="w-100 px-2 sidemenuul">
                                <!-- Proposal section -->
                                <li class="d-flex mt-3 justify-content-between align-items-center w-100">
                                    <p class="mb-0">Proposal</p>
                                    <div class="d-flex editcover" style="margin-left: auto !important; gap: 10px;">
                                        <a href="#main_cover" class="custombtn cover_custom bluetext" onclick="showeditcover()">
                                            <b>Customize</b>
                                        </a>
                                        <!-- <button type="button" style="margin-top: 4px;" class="btn btn-toggle active coverdbtn" onclick="coverSection()" data-toggle="button" aria-pressed="false" autocomplete="off">
                                            <div class="handle"></div>
                                        </button> -->
                                    </div>
                                </li>

                                <!-- about us section -->
                                <li class="d-flex mt-3 justify-content-between align-items-center w-100">
                                    <p class="mb-0">About Us</p>

                                    <div class="d-flex editcover" style="margin-left: auto !important; gap: 10px;">
                                        <a href="#main_about" class="custombtn about_btn bluetext" onclick="showeditabout()">
                                            <b>Customize</b>
                                        </a>
                                        <!-- <button type="button" onclick="aboutSection()" style="margin-top: 4px;" class="btn btn-toggle active abutbtn" data-toggle="button" aria-pressed="false" autocomplete="off">
                                            <div class="handle"></div>
                                        </button> -->
                                    </div>


                                </li>

                                <!-- personal introduction section -->
                                <li class="d-flex mt-3 justify-content-between align-items-center w-100">
                                    <p class="mb-0">Personal Introduction</p>

                                    <div class="d-flex editcover" style="margin-left: auto !important; gap: 10px;">
                                        <a href="#main_personal" class="custombtn intro_btn bluetext" onclick="showeditpersonal()">
                                            <b>Customize</b>
                                        </a>
                                        <!-- <button type="button" onclick="introSection()" style="margin-top: 4px;" class="btn btn-toggle active introbtn" data-toggle="button" aria-pressed="false" autocomplete="off">
                                            <div class="handle"></div>
                                        </button> -->
                                    </div>
                                </li>

                                <!-- payment schedule section -->
                                <li class="d-flex mt-3 justify-content-between align-items-center w-100">
                                    <p class="mb-0">Payment Schedule</p>

                                    <div class="d-flex editcover" style="margin-left: auto !important; gap: 10px;">
                                        <a href="#main_pmnt" class="custombtn payment_btn bluetext" onclick="showeditpayment()">
                                            <b>Customize</b>
                                        </a>
                                        <!-- <button type="button" onclick="paymentSection()" style="margin-top: 4px;" class="btn btn-toggle active paymntbtn" data-toggle="button" aria-pressed="false" autocomplete="off">
                                            <div class="handle"></div>
                                        </button> -->
                                    </div>

                                </li>

                                <!-- terms & conditions section -->
                                <li class="d-flex mt-3 justify-content-between align-items-center w-100">
                                    <p class="mb-0">Terms & Conditions</p>

                                    <div class="d-flex editcover bluetext" style="margin-left: auto !important; gap: 10px;">
                                        <a href="#main_trms" class="custombtn terms_btn" onclick="showeditterms()">
                                            <b>Customize</b>
                                        </a>
                                        <!-- <button type="button" onclick="termsSection()" style="margin-top: 4px;" class="btn btn-toggle active termbtn" data-toggle="button" aria-pressed="false" autocomplete="off">
                                            <div class="handle"></div>
                                        </button> -->
                                    </div>

                                </li>
                            </ul>
                        </div>
                        <form action="{{URL::route(getRouteAlias() . '.send-mail.client')}}" method="post" enctype="multipart/form-data">
                            @csrf
                            <!-- <div class="row px-2">
                                <p class="mt-1" style="font-size: 14px; color: #263944;">
                                    Upload other files
                                </p>
                                <div class="file-upload-contain">
                                    <input id="multiplefileupload" type="file" name="select_file[]" accept=".jpg,.gif,.png" multiple />
                                </div>
                            </div> -->
                            <div class="row" style="margin-top: 31px;">
                                <div class="col-12">
                                    <a href="{{ url()->previous() }}" class="sendcbtn" style="margin-top: 51px !important; color: white; padding: 7px 30px; cursor: pointer">
                                        <i class="fa fa-long-arrow-left" style="color: white" aria-hidden="true"></i>
                                        &nbsp;&nbsp;Back
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>





                    <div class="edit_cover_section px-3" style="height: auto; padding: 0px 0px;">
                        <div class="row px-1">
                            <p style="color: #0074D9;">Customize Cover Page</p>
                        </div>
                        @if(!isset($template))
                            <div class="row">
                                <div class="form-group w-100 mt-3">
                                    <span for="coverpage">Cover Title</span>
                                    <input type="text" value="{{ $coverorg->cov_title ?? '' }}" id="cov_title" class="form-control" style="background-color : #FCFCFC; border: 1px solid #D6D6D6; " placeholder="Enter cover title">
                                </div>
                                <div class="form-group w-100">
                                    <span for="coverpage">Subtitle Text</span>
                                    <input type="text" value="{{ $coverorg->cov_sub_title ?? ''}}" id="sub_tit" class="form-control" style="background-color : #FCFCFC;
; border: 1px solid #D6D6D6" placeholder="e.g Project name">
                                </div>
                                <div class="form-group w-100">
                                    <span for="">Upload Cover Image</span>

                                    <div class="file-upload-contain">
                                        <input id="multiplefileupload2" type="file" accept=".jpg,.gif,.png"  />
                                    </div>
                                </div>
                                {{--<div class="form-group w-100 px-1" style="margin-top: 67px;">
                                    <input type="checkbox" id="remem_settings_cover" style="width: 12px;
                                    height: 12px;
                                    gap: 0px;
                                    border-radius: 8px;

                                    opacity: 0px;
                                    border: 1px solid #B0D4F3
                                    "><label for="remem_settings_cover"> &nbsp;&nbsp;Remember these settings for Future Projects</label>
                                </div>--}}
                                <div class="row w-100" style="">
                                    <div class="col-6">
                                        <button class="dnbtn" id="cancelBtn" onclick="hideeditcover()">&nbsp;&nbspCancel</button>
                                    </div>
                                    <div class="col-6">
                                        <button class="sendcbtn d-flex justify-content-center" id="doneBtn">
                                            <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            <p style="color: white; font-size: 16px;">Save</p>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endif
                        <!-- <div class="text-center mt-5"><h4>OR</h4></div>

                    <div class="form-group w-100">
                            <span for="">Upload Your Own Template</span>

                            <div class="file-upload-contain">
                                <input id="multiplefileuploadtemplate" type="file" accept=".jpg,.gif,.png,.jpeg"  onchange="uploadFile(this)"/>
                            </div>

                            @if(isset($template))
                            <div class="file-preview-thumbnails hidetemplate" bis_skin_checked="1">
 <div class="file-preview-frame file-sortable  kv-preview-thumb" id="thumb-multiplefileuploadtemplate-611087_bg-two.jpg" data-fileindex="-1" data-fileid="611087_bg-two.jpg" data-template="image" data-zoom="" bis_skin_checked="1"><div class="kv-file-content" bis_skin_checked="1">
<img src="{{isset($template->project_image) ? Storage::url($template->project_image) : asset('admin_assets/images/12.png')}}" class="file-preview-image kv-preview-data" title="bg-two.jpg" alt="bg-two.jpg" style="width: auto; height: auto; max-width: 100%; max-height: 100%; image-orientation: from-image;">
</div><div class="file-thumbnail-footer" bis_skin_checked="1">
<div class="file-detail" bis_skin_checked="1"><div class="file-caption-name" bis_skin_checked="1">bg-two.jpg</div>
    <div class="file-size" bis_skin_checked="1"> <samp>(596.76 KB)</samp></div>
</div>   <div class="file-actions" bis_skin_checked="1">
    <div class="file-footer-buttons" bis_skin_checked="1">
          <button type="button" class="kv-file-remove file-remove" onclick="window.location.href='{{ route('organization.deleteTemplate', encodeId($template->id)) }}'"
          title="Remove file"><i class="fa fa-times"></i></button>
      </div>
</div>

<div class="clearfix" bis_skin_checked="1"></div>
</div>

<div class="kv-zoom-cache" bis_skin_checked="1"></div></div>
<button class="dnbtn mt-5" id="cancelBtn" onclick="hideeditcover()">

&nbsp;&nbsp;Cancel
</button>
</div>
@endif






                        </div> -->
                    </div>


                    <div class="edit_about_section px-3" style="height: auto">
                        <div class="row px-1">
                            <b style="color: #0074D9;">Customize About Us Page</b>
                        </div>
                        <div class="row">
                            <div class="form-group w-100 mt-3">
                                <span for="coverpage">Intro Text</span>

                                <textarea id="textEditor" class="wycwyg_editorb describe-project addNoteSummer2 scope_description"
                                          name="scope_description" style="height:200px">
                {!! $aboutorg?->cov_sub_title !!}
            </textarea>
                            </div>

                            <div class="form-group w-100">
                                <span for="">Upload Cover Image</span>

                                <div class="file-upload-contain">
                                    <input id="multiplefileupload3" type="file" accept=".jpg,.gif,.png"/>
                                </div>
                            </div>
                            <!-- <div class="form-group w-100 px-1" style="margin-top: 87px;">
                                <input type="checkbox" id="remem_settings_about" style="width: 12px;
                                height: 12px;
                                gap: 0px;
                                border-radius: 8px;

                                opacity: 0px;
                                border: 1px solid #B0D4F3
                                "><label for="remem_settings_about"> &nbsp;&nbsp;Remember these settings for Future Projects</label>

                            </div> -->
                            <div class="row w-100" style="">
                                <div class="col-6">
                                    <button class="dnbtn" onclick="hideeditabout()" id="cancelBtnabout">
                                        &nbsp;&nbsp;Cancel
                                    </button>

                                </div>
                                <div class="col-6">
                                    <button class="sendcbtn d-flex justify-content-center" id="doneBtnabout">
                                        <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <p style="color: white; font-size: 16px;">Save</p>

                                    </button>
                                </div>

                            </div>

                        </div>
                    </div>

                    <div class="edit_personal_section px-3" style="height: auto">
                        <div class="row px-1">
                            <b style="color: #0074D9;">Customize Personal Introduction Page</b>
                        </div>
                        <div class="row">
                            <div class="form-group w-100 mt-3">
                                <span for="coverpage">Intro Text</span>
                                <textarea rows="10" id="intro_letter" class="form-control" style="background-color : #FCFCFC;
; border: 1px solid #D6D6D6" placeholder="Intro text here.....">{!! $introorg->cov_sub_title ?? ''  !!}</textarea>
                            </div>

                            <!-- <div class="form-group w-100 px-1" style="margin-top: 254px;">
                                <input type="checkbox" id="remem_settings_intro" style="width: 12px;
                                height: 12px;
                                gap: 0px;
                                border-radius: 8px;

                                opacity: 0px;
                                border: 1px solid #B0D4F3
                                "><label for="remem_settings_intro"> &nbsp;&nbsp;Remember these settings for Future Projects</label>

                            </div> -->
                            <div class="row w-100" style="">
                                <div class="col-6">
                                    <button class="dnbtn" id="cancelBtnintro" onclick="hideeditpersonal()">&nbsp;&nbsp;Cancel</button>
                                </div>
                                <div class="col-6">
                                    <button class="sendcbtn d-flex justify-content-center" id="doneBtnintro">
                                        <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <p style="color: white; font-size: 16px;">Save</p>

                                    </button>
                                </div>

                            </div>

                        </div>
                    </div>





                    <div class="edit_terms_section px-3" style="height: auto;">
                        <div class="row px-1">
                            <b style="color: #0074D9;">Terms & Conditions</b>
                        </div>
                        <div class="row">
                            <div class="form-group w-100 mt-3">
                                <span for="coverpage">General Terms & Conditions</span>
                                <textarea rows="10" id="gterms" class="form-control" style="background-color : #FCFCFC;
; border: 1px solid #D6D6D6" placeholder="Enter the term and conditions here">{!! $termsorg?->cov_sub_title ?? ''!!}</textarea>
                            </div>

                            <!-- <div class="form-group w-100 px-1" style="margin-top: 254px;">
                                <input type="checkbox" id="remem_settings_terms" style="width: 12px;
                                height: 12px;
                                gap: 0px;
                                border-radius: 8px;

                                opacity: 0px;
                                border: 1px solid #B0D4F3
                                "><label for="remem_settings_terms"> &nbsp;&nbsp;Remember these settings for Future Projects</label>

                            </div> -->
                            <div class="row w-100" style="margin-top: 25px;">
                                <div class="col-6">
                                    <button class="dnbtn" id="cancelgterms" onclick="hideeditterms()">
                                        &nbsp;&nbsp;Cancel
                                    </button>

                                </div>
                                <div class="col-6">
                                    <button class="sendcbtn d-flex justify-content-center" id="donebtngterms">
                                        <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <p style="color: white; font-size: 16px;">Save</p>

                                    </button>
                                </div>

                            </div>

                        </div>
                    </div>

                    <div class="edit_payment_section px-3" style="height: auto">
                        <div class="row px-1">
                            <b style="color: #0074D9;" style="font-size: 16px">Set Payment Schedule & Terms
                            </b>
                        </div>
                        <div class="row">
                            <div class="article-question w-100">
                                <span class="text-black" style="font-size: 16px">Does this Proposal Expire?</span><br>
                                <div class="radio-inline">
                                    <input class="radio-inline-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="yes"
                                        @checked($paymentorg?->expiry)>

                                    <label class="radio-inline-label" for="inlineRadio1" style="font-size: 16px">Yes</label>
                                </div>
                                <div class="radio-inline">
                                    <input class="radio-inline-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="no"
                                        @checked($paymentorg?->expiry === null)>

                                    <label class="radio-inline-label" for="inlineRadio2" style="font-size: 16px">No</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group w-100 mt-3" id="proposalexpiry" style="display: {{ $paymentorg?->expiry ? 'block' : 'none' }};">
                                <span for="coverpage" style="font-size: 16px">Proposal Expiry Date</span>

                                <div class="input-group w-100 date" id="datepicker">
                                    <input type="date" value="{{ $paymentorg?->expiry ?? ''}}" style="background: #FCFCFC; height: 27px;" class="form-control" placeholder="Pick a Date" id="date"/>



                                </div>

                            </div>
                            <div class="form-group w-100">
                                <span for="coverpage" style="font-size: 16px">Payment Schedule</span>
                                <textarea rows="8" id="paymentschedle" class="form-control" style="background-color : #FCFCFC;
; border: 1px solid #D6D6D6; " placeholder="Enter payment schedule here">{{ $paymentorg?->cov_sub_title ?? ''}}</textarea>
                            </div>
                            <div class="form-group w-100 mt-3">
                                <b for="coverpage" style="font-size: 16px">Total</b>
                                <p style="color: #7C8091; font-size: 12px;" id="gettotalpaym"></p>
                            </div>
                            <div class="form-group w-100 mt-1">
                                @php
                                    $paymentSchedule = $paymentorg?->payment_schedule ? json_decode($paymentorg->payment_schedule) : null;
                                @endphp
                                <label for="coverpage" style="font-size: 16px">Down Payment</label>
                                <div class="d-flex" style="gap: 17px;">
                                    <input type="number" value="{{ $paymentSchedule && isset($paymentSchedule->down_payment) ? $paymentSchedule->down_payment : 0 }}" oninput="getdown1()" min="0" max="" id="getdown1" class="px-2" style="border: 1px solid #D6D6D6; background-color: #FCFCFC; width: 50%;" placeholder="$0.00"> <p style="overflow-wrap: normal !important;
    white-space: normal !important;
    word-break: normal !important;">or</p> <input type="number" value="{{ $paymentSchedule && isset($paymentSchedule->down_payment_percent) ? $paymentSchedule->down_payment_percent : 0 }}" max="100" min="0" oninput="getdown2()" id="getdown2" class="px-2" style="border: 1px solid #D6D6D6; background-color: #FCFCFC; width: 50%;" placeholder="20%">
                                </div>
                            </div>
                            <!-- <div class="form-group w-100 mt-3">
                                <b for="coverpage">Balance</b>
                               <p style="color: #7C8091; font-size: 12px;" id="totalbalance">$43,987</p>
                            </div> -->
                            <!-- <div class="form-group w-100 px-1" style="margin-top: 80px;">
                                <input type="checkbox" id="remem_settings_payments" style="width: 12px;
                                height: 12px;
                                gap: 0px;
                                border-radius: 8px;

                                opacity: 0px;
                                border: 1px solid #B0D4F3
                                "><label for="remem_settings" style="font-size: 16px"> &nbsp;&nbsp;Remember these settings for Future Projects</label>

                            </div> -->
                            <div class="row w-100" style="">
                                <div class="col-6">
                                    <button class="dnbtn" onclick="hideeditpayment()">&nbsp;&nbsp;Cancel</button>

                                </div>
                                <div class="col-6">
                                    <button class="sendcbtn d-flex justify-content-center" id="donepaymentsave">
                                        <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <p style="color: white; font-size: 16px;">Save</p>

                                    </button>
                                </div>

                            </div>

                        </div>
                    </div>




                </div>

            </div>

            <div class="col-lg-8 main-div-sec">

                <!-- @if(isset($template))
                    <div class="panel panel23 coverd mt-4" id="main_cover" style="width: 90%; margin-left: 5%; overflow: hidden;">

                            <img src="{{ isset($template->project_image) ? Storage::url($template->project_image) : asset('admin_assets/images/12.png') }}" style="min-width: 100% !important" alt="">

    </div>


@else -->
                    <div class="panel panel23 coverd mt-4" id="main_cover" style="width: 90%; margin-left: 5%; overflow: hidden; position: relative">
                        <div class="">
                            <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                                <div class="col-6">

                                    <div class="proposal" style="margin-left: -21px;">
                                        <b>
                                            Proposal
                                        </b>
                                    </div>

                                </div>
                                <div class="col-6 d-flex px-3">
                                    <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                        <img id="add_logo_image" class="add_logo_image" src="{{ Storage::url('user_images/' . $organization->profile_photo_path) }}" style="
                                                                          height: 65px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                    </div>
                                </div>
                            </div>


                            <div class="row">
                                <div class="logo" style="margin-top: 43px;
    margin-left: 7.5%;">
                                    <h3 id="add_cov_tit" style="word-wrap: break-word !important; overflow-wrap: break-word !important;">{{ $coverorg->cov_title ?? ''}}</h3>
                                </div>


                            </div>
                            <div class="row2">
                                <div class="imge" style="margin-top: -107px;">



                                    <div class="image-container">
                                        <img id="preview_image" src="{{ $coverorg?->cov_image ? Storage::url($coverorg->cov_image) : asset('admin_assets/images/12.png') }}
" style="width: 100%;" alt="">
                                        <div class="centered-text">

                                        </div>
                                    </div>


                                </div>
                            </div>
                            <div class="row justify-content-end">
                                <div class="col-11" style="margin-left: auto;">
                                    <div class="subtitle-text-main text-center" style="margin-left: auto;">
                                        <p id="add_sub_tit" style=" overflow-wrap: anywhere !important;
    white-space: normal !important;
    word-break: break-word;">{{ $coverorg->cov_sub_title ?? ''}}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="around-padding-all">
                                <div class="container mt-4" style="padding-bottom: 80px">
                                    <div class="row ">
                                        <div class="col-md-4">
                                            <label for="" style="color: #7C8091;">
                                                Prepared for:
                                            </label>
                                            <p>
                                                <b> </b>
                                            </p>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="" style="color: #7C8091;">
                                                Submitted by:
                                            </label>
                                            <p>
                                                <b></b>
                                            </p>
                                            <label for="">
                                                <!-- {{$organization->company_name}} -->
                                            </label>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="" style="color: #7C8091;">
                                                Date Submitted:
                                            </label>
                                            <p>
                                                <b>{{ \Carbon\Carbon::now()->format('F jS, Y') }}</b>
                                            </p>


                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="row mt-4" style="margin-left: 0px !important;">
                                <svg width="100%" height="180" style="margin-bottom: -10px !important;" viewBox="0 0 800 180" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="-14" y="14" width="188" height="188" rx="23" transform="matrix(1 0 0 -1 5 300)" fill="white" stroke="#D9EAF9" stroke-width="28"/>
                                    <rect x="802" y="186" width="614" height="34" transform="rotate(180 802 186)" fill="#D9EAF9" stroke="#D9EAF9" stroke-width="4"/>
                                    <rect x="-14" y="14" width="184" height="188" rx="23" transform="matrix(1 0 0 -1 -95 216)" fill="white" stroke="#D9EAF9" stroke-width="28"/>
                                    </svg>



                            </div> -->





                        </div>
                        <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">
                                    {{$organization->companyPropertyAddress?->website_url}}
                                </b>
                            </div>

                        </div>


                    </div>
                    <!-- @endif -->
                <div class="panel panel22 about mt-4" id="main_about" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                            <!-- <div class="leftimg2">
                                <svg style="position: absolute;
        top: 0; left: 0;" width="4" height="244" viewBox="0 0 4 244" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect width="4" height="163" fill="#0074D9"/>
                                    <rect y="175" width="4" height="27" fill="#0074D9"/>
                                    <rect y="214" width="4" height="9" fill="#0074D9"/>
                                    <rect y="235" width="4" height="9" fill="#0074D9"/>
                                    </svg>
                            </div> -->


                        </div>

                        <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                            <div class="col-6">

                                <div class="proposal" style="margin-left: -24px;">
                                    <b>
                                        About Us
                                    </b>
                                </div>

                            </div>
                            <div class="col-6 d-flex px-3">
                                <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                    <img id="add_logo_image" class="add_logo_image" src="{{ Storage::url('user_images/' . $organization->profile_photo_path) }}" style="
                                                                          height: 65px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                </div>
                            </div>
                        </div>
                        <div class="around-padding-all2">
                            <div class="container mt-5 pb-5">
                                <div class="row px-1">
                                    <br>
                                    <p class="" id="add_about_text" style="color: #3B4159; font-size: 14px;">
                                        {!! $aboutorg->cov_sub_title ?? '' !!}
                                    </p>
                                </div>
                            </div>

                            <div class="pspdfkit-1thsp3e e1h2lg7j2 mt-5" style="display: {{ ($aboutorg?->cov_image !== null && $aboutorg?->cov_image !== '') ? 'block' : 'none' }}"

                            >

                                <div class="pspdfkit-1eoji5i e1h2lg7j0"></div>
                                <img src="{{ $aboutorg?->cov_image ? Storage::url($aboutorg?->cov_image) : '' }}" alt="cover" id="preview_about_image" class="pspdfkit-1q9q9cv e1h2lg7j1">





                            </div>
                        </div>


                        <div class="row px-5 justify-content-between bottom_row py-2" style="background-color: #E6F1FB; margin-top: 0px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">
                                    {{$organization->companyPropertyAddress?->website_url}}
                                </b>
                            </div>

                        </div>

                    </div>

                </div>

                <div class="panel panel22 personal mt-4" id="main_personal" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                            <!-- <div class="leftimg2">
                                <svg style="position: absolute;
        top: 0; left: 0;" width="4" height="244" viewBox="0 0 4 244" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect width="4" height="163" fill="#0074D9"/>
                                    <rect y="175" width="4" height="27" fill="#0074D9"/>
                                    <rect y="214" width="4" height="9" fill="#0074D9"/>
                                    <rect y="235" width="4" height="9" fill="#0074D9"/>
                                    </svg>
                            </div> -->


                        </div>

                        <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                            <div class="col-6">

                                <div class="proposal_personal" style="">
                                    <b>
                                        Personal Introduction
                                    </b>
                                </div>

                            </div>
                            <div class="col-6 d-flex px-3">
                                <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                    <img id="add_logo_image" class="add_logo_image" src="{{ Storage::url('user_images/' . $organization->profile_photo_path) }}" style="
                                                                          height: 65px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                </div>
                            </div>
                        </div>

                        <div class="around-padding-all2">
                            <div class="container mt-5">
                                <div class="row px-1">
                                    <br>
                                    <p class="" id="add_intro_letter" style="color: #3B4159; font-size: 14px;">
                                        {!! $introorg?->cov_sub_title ?? '' !!}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">
                                    {{$organization->companyPropertyAddress?->website_url}}
                                </b>
                            </div>

                        </div>

                    </div>

                </div>


                <div class="panel panel22 terms_conditions mt-4" id="main_trms" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                            <!-- <div class="leftimg2">
                                <svg style="position: absolute;
        top: 0; left: 0;" width="4" height="244" viewBox="0 0 4 244" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect width="4" height="163" fill="#0074D9"/>
                                    <rect y="175" width="4" height="27" fill="#0074D9"/>
                                    <rect y="214" width="4" height="9" fill="#0074D9"/>
                                    <rect y="235" width="4" height="9" fill="#0074D9"/>
                                    </svg>
                            </div> -->


                        </div>

                        <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                            <div class="col-6">

                                <div class="proposal_personal" style="">
                                    <b>
                                        Term & Conditions
                                    </b>
                                </div>

                            </div>
                            <div class="col-6 d-flex px-3">
                                <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                    <img id="add_logo_image" class="add_logo_image" src="{{ Storage::url('user_images/' . $organization->profile_photo_path) }}" style="
                                                                          height: 65px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                </div>
                            </div>
                        </div>

                        <div class="around-padding-all">
                            <div class="container mt-5 pb-5">
                                <div class="">
                                    <br>
                                    <b class="" id="gtermstext">{!! $termsorg?->cov_sub_title ?? '' !!}</b>
                                    <br>

                                    <p class="" id="add_gterms" style="color: #3B4159; font-size: 16px;"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">
                                    {{$organization->companyPropertyAddress?->website_url}}
                                </b>
                            </div>

                        </div>

                    </div>

                </div>



                <div class="panel panel22 payment_scdul mt-4" id="main_pmnt" style="width: 90%; margin-left: 5%; overflow: hidden;">
                    <div class="">
                        <div class="row">
                            <!-- <div class="leftimg2">
                                <svg style="position: absolute;
        top: 0; left: 0;" width="4" height="244" viewBox="0 0 4 244" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect width="4" height="163" fill="#0074D9"/>
                                    <rect y="175" width="4" height="27" fill="#0074D9"/>
                                    <rect y="214" width="4" height="9" fill="#0074D9"/>
                                    <rect y="235" width="4" height="9" fill="#0074D9"/>
                                    </svg>
                            </div> -->


                        </div>

                        <div class="row" style="display: flex; justify-content: space-between; align-items: baseline;">
                            <div class="col-6">

                                <div class="proposal_personal" style="">
                                    <b>
                                        Payments Scheduled & Terms
                                    </b>
                                </div>

                            </div>
                            <div class="col-6 d-flex px-3">
                                <div class="logo" style="margin-top: 43px; margin-left: auto !important; padding-right: 22px;
                                                      ">
                                    <img id="add_logo_image" class="add_logo_image" src="{{ Storage::url('user_images/' . $organization->profile_photo_path) }}" style="
                                                                          height: 65px;

                                                                          gap: 0px;
                                                                          opacity: 0px;
                                                                          " alt="">
                                </div>
                            </div>
                        </div>
                        <div class="around-padding-all">
                            <div class="container mt-5 pb-5">
                                <div class="">

                                    <div id="expiry_hide_show" style="display: {{isset($paymentorg->expiry) ? 'block' : 'none'}}">
                                        <label id="add_prpslexpiry">Proposal Expires</label>
                                        <br>
                                        <label id="add_date_expiry"><b id="add_fixdate">
                                                @if(isset($paymentorg) && $paymentorg->expiry != null)
                                                    {{ \Carbon\Carbon::parse($paymentorg->expiry)->format('F d, Y') }}
                                                @endif
                                            </b></label>
                                    </div>
                                    <br>

                                    <p id="add_pmntschdle"><span>Payment Schedule</span></p>
                                    <label id="add_payment_schedule">{{ $paymentorg?->cov_sub_title ?? '-' }}</label>
                                    <br>
                                    <br>
                                    <label>Total</label>
                                    <br>
                                    <b id="totalamont">438$</b>
                                    <br>

                                    <label>Down &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Payments</label>
                                    <br>
                                    <div class="d-flex" style="gap: 45px;"><b id="downpaym1">
                                            @php
                                                $paymentSchedule = $paymentorg?->payment_schedule ? json_decode($paymentorg->payment_schedule) : null;
                                            @endphp
                                            @if(isset($paymentSchedule) && isset($paymentSchedule->down_payment))
                                                ${{ number_format($paymentSchedule->down_payment, 2) }}
                                            @endif
                                        </b>
                                        <b id="downpaym2">
                                            @if(isset($paymentSchedule) && $paymentSchedule->down_payment_percent)
                                                {{ number_format($paymentSchedule->down_payment_percent, 2) }}%
                                            @endif</b></div>
                                    <br>

                                    <!-- <span>Balance</span><br>
                                    <b id="totalbalancetext">345$</b> -->
                                </div>
                            </div>
                        </div>



                        <div class="row px-5 justify-content-between py-2 bottom_row" style="background-color: #E6F1FB; margin-top: 276px;">
                            <div class="col-6">
                                <b style="color: #0068C3;">
                                    {{$organization->company_name}}
                                </b>
                            </div>
                            <div class="col-6" style="display: flex;">
                                <b style="color: #0068C3; margin-left: auto;">
                                    {{$organization->companyPropertyAddress?->website_url}}
                                </b>
                            </div>

                        </div>

                    </div>

                </div>



            </div>
            <div class="edit_gallery_section px-3" style="height: auto; padding: 0px 0px;">
                <div class="row px-1">
                    <p style="color: #0074D9;">Customize Gallery Page</p>
                </div>
                <div class="row">
                    <div class="form-group w-100 mt-3">
                        <span for="coverpage">Title</span>
                        <input type="text" value="" id="gallery_title" class="form-control" style="background-color : #FCFCFC;
; border: 1px solid #D6D6D6; " placeholder="Enter title">
                    </div>

                    <div class="form-group w-100">
                        <span for="">Upload Gallery Image</span>

                        <div class="file-upload-contain">
                            <input id="multiplefileuploadgallery" type="file" accept=".jpg,.gif,.png"  />
                        </div>
                    </div>
                    <div class="form-group w-100 px-1" style="margin-top: 67px;">
                        <input type="checkbox" id="remem_settings_gallery" style="width: 12px;
                            height: 12px;
                            gap: 0px;
                            border-radius: 8px;

                            opacity: 0px;
                            border: 1px solid #B0D4F3
                            "><label for="remem_settings_gallery"> &nbsp;&nbsp;Remember these settings for Future Projects</label>

                    </div>
                    <div class="row w-100" style="">
                        <div class="col-6">
                            <button class="dnbtn" id="cancelBtn" onclick="hideeditgallery()">

                                &nbsp;&nbsp;Cancel
                            </button>

                        </div>
                        <div class="col-6">
                            <button class="sendcbtn d-flex justify-content-center" id="doneBtngallery">
                                <svg width="18" height="26" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21.0807 6.25L9.6224 17.7083L4.41406 12.5" stroke="#F7F9FB" stroke-width="1.11727" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <p style="color: white; font-size: 12px;">Save</p>

                            </button>
                        </div>

                    </div>

                </div>
            </div>



            <!-- Modal -->
            <div class="modal fade" id="logoModel" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
                <div class="modal-dialog model-lg modal-logo">
                    <div class="modal-content">
                        <div class="modal-header" style="background: #E1F4FF;
">
                            <h4 class="modal-title px-3" id="addItemModalLabel">
                                <b style="color: #0074d9 !important">Add Logo</b>
                            </h4>
                            <button
                                type="button"
                                class="btn-close hidemodelbtn px-3"
                                data-dismiss="modal"
                                aria-label="Close"
                                style="border: none; background-color: transparent"
                            >
                                <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                            </button>
                        </div>
                        <div class="modal-body">

                            <div class="form-group">
                                <div class="file-upload-contain">
                                    <input id="multiplefileupload5" onchange="changeLog()" type="file" accept=".jpg,.gif,.png, .webp, .svg, .jpeg"/>
                                </div>




                            </div>


                            <button type="button" id="add_logo_btn" class="btn btn-primary form-control">Add Logo</button>

                        </div>

                    </div>
                </div>
            </div>

            <div class="modal fade" id="generateInvoiceConfirm" tabindex="-1" aria-labelledby="deleteModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog modal-sm" style="width: fit-content !important">
                    <div class="modal-content">
                        <div class="modal-body">

                        </div>
                    </div>
                </div>
            </div>
            @include('layouts.admin.confirmation-modal')
            @include('layouts.partials.success-modal')

            <!-- Modal -->

            <!-- <script>
              document.getElementById('multiplefileupload4').addEventListener('change', function(event) {
                  const files = event.target.files;

                  // Check if the number of files exceeds 4
                  if (files.length > 4) {
                      alert('You can only upload a maximum of 4 images.');
                      // Clear the input field
                      event.target.value = '';
                  }
              });
          </script> -->

    </section>
    @push('scripts')
        <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
        {{--        <script src="https://cdn.tiny.cloud/1/8gngu979gm8n88nvs8ktqf0ma89m85gp4s6izjbx4l9tfap4/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>--}}
        <!-- TinyMCE loaded via @push('tinymce-scripts') -->

        <script>
            // Initialize Summernote
            // $('.addtextscope').summernote({
            //     height: 200, // Set the height of the editor
            //     toolbar: [
            //                 ["style", ["italic", "bold", "underline", "strikethrough"]],
            //             ],
            //     callbacks: {
            //         onImageUpload: function (files) {
            //             // Prevent uploading images
            //             // alert('Image upload is disabled!');
            //             return false;
            //         },
            //         onChange: function(contents, $editable) {
            //             // Triggered whenever the content changes
            //             // alert('Content changed');
            //             // $('#scope_des').text('');
            //             const scopeDes = document.getElementById('scope_des');
            //             scopeDes.innerHTML = '';
            //             // document.getElementById('scope_des').innerText = '';
            // // document.getElementById('scope_des').innerText = this.value;
            // scopeDes.innerHTML = this.value.trim();
            //
            //             $('.scopetext').val(contents);
            //             // console.info(contents);
            //         }
            //     }
            // });

        </script>

        <script>
            function uploadFile(input) {
                const file = input.files[0]; // Get the selected file
                if (!file) return; // Exit if no file is selected
                $('.hidetemplate').css('display', 'none');

                const formData = new FormData();
                formData.append('file', file);
                formData.append('_token', '{{ csrf_token() }}'); // CSRF token for security

                $.ajax({
                    url: '{{ route("organization.image.upload.template") }}', // URL to the upload route
                    type: 'POST',
                    data: formData,
                    processData: false, // Prevent jQuery from processing the data
                    contentType: false, // Prevent jQuery from setting content type
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                            $('.hidetemplate').css('display', 'none');
                            // alert('Image uploaded/updated successfully!');
                        } else {
                            alert(response.message || 'Something went wrong!');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        alert('Failed to upload the image.');
                    }
                });
            }
        </script>




        <script>
            function deleteImage(element) {
                const imageName = element.getAttribute('data-image');

                // Perform the AJAX request directly without confirmation
                fetch('{{ route("organization.gallery.delete") }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ image_name: imageName }),
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.message) {
                            // alert(data.message); // Optional: Show success message
                            location.reload(); // Reload the page to reflect the changes
                        }
                    })
                    .catch(error => console.error('Error:', error));
            }
        </script>
        <script>
            $(document).on("sendEmailHandler", sendEmailHandler);

            function sendEmailHandler(e) {
                var formData = {};
                if (e.customData instanceof FormData) {
                    formData = e.customData;
                }

                formData.append('sentEmail', true);

                var downloadUrl = $('.saveAndDownload').attr('data-url');
                var fileName = $('.saveAndDownload').attr('data-file-name');

                $.ajax({
                    url: downloadUrl,
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    xhrFields: {
                        responseType: 'blob'
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        // Check if the response is not empty
                        // if (response.size > 0) {
                        // File download success, use FileSaver.js to trigger the download
                        // saveAs(response, fileName + '.pdf');
                        $('#emailModal').hide();
                        // toastr.success("Proposal Successfully Sent!");

                        $('#generateInvoiceConfirm .modal-body').html('');

                        // $("#successModal").modal("show");

                        // $(".dynamic-success-data").html("");
                        $("#generateInvoiceConfirm .modal-body").append(
                            ` <div class="text-center">
    <svg width="40" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="35" cy="35" r="35" fill="#27AE60" />
        <path d="M22.3369 36.81L29.5732 44.0463L47.6639 25.9556" stroke="white" stroke-width="6" stroke-linecap="round"
            stroke-linejoin="round" />
    </svg>
</div><h2 class="title text-center">Done</h2>
             <p class="para mt-3 text-center">Proposal Submitted Successfully!</p> <a href = "{{ route(getRouteAlias() . '.opportunity.index') }}" class="btn primaryblue w-100 mt-5 ">Close</a>`
                        );
                        $('#generateInvoiceConfirm').modal('show');


// Redirect after delay
// setTimeout(() => {
//     window.location.href = "{{ route(getRouteAlias() . '.opportunity.index') }}";
// }, 1500); // Redirect after 1.5 seconds

                        // toastr.success("Estimate save successfully!");
                        // window.location.href =
                        //     "{{ route(getRouteAlias() . '.opportunity.index') }}";
                        // } else {
                        // toastr.error("File not found!");
                        // }

                    },
                    error: function(xhr, status, error) {
                        console.error(error);
                        toastr.error("Error occurred while downloading the file.");
                    }
                });
            }
        </script>
        <script>
            $(document).ready(function() {
                $(".saveAndDownload").click(function(e) {
                    // alert('alert');
                    e.preventDefault();
                    if ($(e.target).hasClass('send-email')) {
                        // Set Email Modal Header
                        $('#emailModal .modal-title').html(
                            "Email Estimate # to " +
                            "" + ' ' + "")

                        // Set Email Defualt Subject

                        $('#emailModal #subject').val(
                            'Estimate From '
                        )

                        var website = @json(optional($organization->companyAddress)->website_url);
                        var sumhtml = '<span class="im">' +
                            '<h1 style="font-style: normal; font-weight: 700; font-size: 24px; line-height: 33.6px; color: rgb(25, 42, 62);">Hi , </h1>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 24px;">Thank you for choosing {{ $organization->company_name }}. </p>' +
                            '<p style="font-family: Arial, Helvetica, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 24px;">The estimate total is <strong style="color: rgb(25, 42, 62);">' + '$' +
                            +
                                '</strong></p>' +
                            '<p><span class="im" style="color: rgb(80, 0, 80); font-family: Arial, Helvetica, sans-serif; font-size: small;"></span><span class="im" style="color: rgb(80, 0, 80); font-family: Arial, Helvetica, sans-serif; font-size: small;"></span></p>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 10px;">If you have any questions, please do not hesitate to contact us at<span>&nbsp;</span><a target="_blank" rel="noopener noreferrer" href="mailto:{{ optional($organization)->email }}" style="color: rgb(17, 85, 204);"> {{ optional($organization)->email }}</a>.<br>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 10px;"><br>Regards, </p></br>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ $organization->company_name }} </strong> </p>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ $organization->email }} </strong> </p>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ optional($organization->companyAddress)->phone_no }} </strong> </p>' +
                            '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ optional($organization->companyAddress)->address1 }} </strong> </p>';

                        if (website) {
                            sumhtml +=
                                '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> ' +
                                website + ' </strong> </p>';
                        }
                        $('#emailModal .summernote').summernote('code', sumhtml);
                        // Set Email Defualt Message
                        $('#emailModal').modal('show')

                    }

                });

                $(".downloadPDF").click(function(e) {
                    e.preventDefault();
                    var downloadUrl = e.target.getAttribute('data-url');
                    var fileName = e.target.getAttribute('data-file-name');
                    $.ajax({
                        type: 'GET',
                        url: downloadUrl,
                        xhrFields: {
                            responseType: 'blob'
                        },
                        success: function(response) {
                            // Check if the response is not empty
                            if (response.size > 0) {
                                // File download success, use FileSaver.js to trigger the download

                                saveAs(response, fileName + '.pdf');
                            } else {
                                toastr.error("File not found!");
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error(error);
                            toastr.error("Error occurred while downloading the file.");
                        }
                    });
                });
            });
        </script>
        <script>
            // Store original values
            document.addEventListener('DOMContentLoaded', function() {
                let aboutTextElement = document.getElementById("about_text");
                let previewImageElement = document.getElementById("preview_about_image");
                let fileInputElement = document.getElementById("multiplefileupload3");
                let doneBtnAbout = document.getElementById("doneBtnabout");
                let donePaymentSave = document.getElementById("donepaymentsave");
                let cancelBtnAbout = document.getElementById("cancelBtnabout");

                let originalAboutTitle = aboutTextElement ? aboutTextElement.value : "";
                let originalAboutImageSrc = previewImageElement ? previewImageElement.src : "";

                if (!originalAboutImageSrc) {
                    $(".pspdfkit-1thsp3e").hide();
                }

                if (aboutTextElement) {
                    aboutTextElement.addEventListener("input", function () {
                        document.getElementById("add_about_text").innerText = this.value;
                    });
                }

                if (fileInputElement) {
                    fileInputElement.addEventListener("change", function () {

                        $(".pspdfkit-1thsp3e").css("display", "flex");

                        if (this.files.length > 0) {
                            let reader = new FileReader();
                            reader.onload = function (e) {
                                previewImageElement.src = e.target.result;
                            };
                            reader.readAsDataURL(this.files[0]);
                        }
                    });
                }
            });
        </script>
        <script>
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

        </script>
        <script>
            var t=$('#gettotalpaym').text();
            $('#totalamont').text(t);
            var t2=$('#totalbalance').text();
            $('#totalbalancetext').text(t2);

            function getdown1(){
                const max = ""; // Maximum allowed value
                const input = document.getElementById("getdown1");

                // Check if the input value is greater than the maximum
                if (parseFloat(input.value) > max) {
                    input.value = max; // Set input to max if it exceeds
                }
                var id = $('#getdown1').val();
                var ttl="";
                var dd=id/ttl;
                var dd2=dd*100;
                console.info(dd2);
                $('#downpaym1').text(id+'$');
                $('#downpaym2').text(dd2.toFixed(2)+'%');
                $('#getdown2').val(dd2.toFixed(2));


            }
            function getdown2(){
                const input = document.getElementById("getdown2");
                if (parseFloat(input.value) > 100) {
                    input.value = 100; // Set input to max if it exceeds
                }
                var id = $('#getdown2').val();
                var ttl="";
                var dd2=id/100;
                var dd=dd2*ttl;
                $('#downpaym2').text(id+'%');
                $('#downpaym1').text(dd.toFixed(2)+'$');
                $('#getdown1').val(dd);


            }

        </script>
        <!-- <script>
            $(function() {
    $('#date').datepicker({
        onSelect: function(dateText) {
            var selectedDate = new Date(dateText);
            var formattedDate = formatDate(selectedDate);

            alert('Date selected: ' + formattedDate);
            document.getElementById('add_fixdate').innerText = formattedDate;
            document.getElementById('add_date_expiry').style.display = 'block';
            document.getElementById('add_prpslexpiry').style.display = 'block';
        }
    });
});

// Function to format the date as "Month dayth, year"
function formatDate(date) {
    const monthNames = [
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
    ];

    const day = date.getDate();
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();

    // Add ordinal suffix to the day (1st, 2nd, 3rd, 4th, etc.)
    const ordinalSuffix = getOrdinalSuffix(day);

    return `${month} ${day}${ordinalSuffix}, ${year}`;
}

// Function to get ordinal suffix (st, nd, rd, th)
function getOrdinalSuffix(day) {
    if (day > 3 && day < 21) return 'th'; // General case for days 4-20
    switch (day % 10) {
        case 1: return 'st';
        case 2: return 'nd';
        case 3: return 'rd';
        default: return 'th';
    }
}

        </script> -->
        <script>
            function changeLog()
            {
                $('.maind').css('display', 'none');
                $('.deleteprev').css('display', 'block');
            }
        </script>
        <script>
            $(document).ready(function() {
                // Event listener for the remove button inside the deleted_logo div
                $('.deleted_logo').on('click', function() {
                    // Action to perform when the button is clicked
                    var url = "{{ URL::route(getRouteAlias() . '.storeDefaultSettingsLogoDelete') }}";
                    $.ajax({
                        url: url, // Replace with your backend URL
                        type: 'GET', // Use POST method

                        success: function(response) {


// Set the image source
                            $('.add_logo_image').attr('src', '');
                            $('.maind').css('display', 'none');
                            console.log('Data sent successfully:', response);
                            // Optionally, you can perform actions here based on the response
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            // Handle any errors
                            console.error('Error sending data:', textStatus, errorThrown);
                        }
                    });

                });
            });

        </script>
        <script>
            document.getElementById('add_logo_btn').addEventListener('click', function() {
                // Fix current input values and image as final


                let formData = new FormData();
// Get the file input element
                let fileInput = document.getElementById('multiplefileupload5'); // Replace 'multiplefileupload2' with
// Check if a file is selected
                if (fileInput.files.length > 0) {
                    formData.append('image_file', fileInput.files[0]);
                }else{
                    alert('Please select an image file.');
                    return;
                }
                var url = "{{ URL::route(getRouteAlias() . '.storeDefaultSettingsLogo') }}";

// Send the data to the backend using AJAX
                $.ajax({
                    url: url, // Replace with your backend URL
                    type: 'POST', // Use POST method
                    data: formData,
                    processData: false, // Important: prevent jQuery from converting the data to a query string
                    contentType: false, // Important: set content type to false
                    success: function(response) {
                        // Handle the response from the server
                        var data2=response.data.cov_image;
                        console.info(response.data.cov_image)
                        var imageUrl = "{{ Storage::url('') }}" + data2; // Get the base URL from Laravel

                        // To hide the modal programmatically
                        $('#logoModel').modal('hide');
                        $('.maind').css('display', 'inline-block');
                        $('.deleteprev').css('display', 'none');

// Set the image source
                        $('.add_logo_image').attr('src', imageUrl);
                        $('.imgadded').attr('src', imageUrl);
                        console.log('Data sent successfully:', response);
                        // Optionally, you can perform actions here based on the response
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        // Handle any errors
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });

            });

        </script>


        <script>
            // Save original input values with unique names
            let originalPaymentSchedule = document.getElementById('paymentschedle').value;
            let originalDateValue = document.getElementById('date').value;

            // Update text as user types in input fields
            document.getElementById('paymentschedle').addEventListener('input', function() {
                document.getElementById('add_payment_schedule').innerText = this.value;
            });

            // Use 'change' event for the date input
            // document.getElementById('date').addEventListener('change', function() {
            //   document.getElementById('add_fixdate').innerText = this.value;
            //   alert(this.value);
            //   if (this.value === '') {
            //     document.getElementById('add_date_expiry').style.display = 'none';
            //     document.getElementById('add_prpslexpiry').style.display = 'none';
            //   } else {
            //     document.getElementById('add_date_expiry').style.display = 'block';
            //     document.getElementById('add_prpslexpiry').style.display = 'block';
            //   }
            // });


            document.addEventListener('DOMContentLoaded', function () {
                console.log('Script is running');

                const dateInput = document.getElementById('date');

                // Input event for manual typing
                dateInput.addEventListener('input', function() {
                    console.log('Input event triggered: ' + this.value);
                    updateDateDisplay(this.value);
                });

                // Change event for date picker selection
                dateInput.addEventListener('change', function() {
                    console.log('Change event triggered: ' + this.value);
                    updateDateDisplay(this.value);
                });

                function updateDateDisplay(value) {
                    const date = new Date(value);
                    const formattedDate = date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: '2-digit',
                    });
                    document.getElementById('add_fixdate').innerText = formattedDate;
                    if (value === '') {
                        document.getElementById('add_date_expiry').style.display = 'none';
                        document.getElementById('add_prpslexpiry').style.display = 'none';
                    } else {
                        document.getElementById('add_date_expiry').style.display = 'block';
                        document.getElementById('add_prpslexpiry').style.display = 'block';
                    }
                }
            });






            // When "Done" button is clicked, confirm changes
            document.getElementById('doneBtn').addEventListener('click', function() {
                // Fix current input values as final
                originalPaymentSchedule = document.getElementById('paymentschedle').value;
                originalDateValue = document.getElementById('date').value;
                document.querySelector('.main_side_menu').style.display = 'block';
                document.querySelector('.edit_cover_section').style.display = 'none';
            });

            // When "Cancel" button is clicked, revert changes
            document.getElementById('cancelBtn').addEventListener('click', function() {
                // Revert inputs and text to original values
                document.getElementById('paymentschedle').value = originalPaymentSchedule;
                document.getElementById('add_payment_schedule').innerText = originalPaymentSchedule;
                document.getElementById('date').value = originalDateValue;
                document.getElementById('add_fixdate').innerText = originalDateValue;

                if (originalDateValue === '') {
                    document.getElementById('add_date_expiry').style.display = 'none';
                    document.getElementById('add_prpslexpiry').style.display = 'none';
                } else {
                    document.getElementById('add_date_expiry').style.display = 'block';
                    document.getElementById('add_prpslexpiry').style.display = 'block';
                }
            });
        </script>






        <script>
            // document.getElementById('proposalexpiry').style.display = 'none';
            // Function to show or hide the Proposal Expiry Date field based on the radio selection
            document.querySelectorAll('input[name="inlineRadioOptions"]').forEach(function(radio) {
                radio.addEventListener('change', function() {
                    // Check if the "Yes" option is selected
                    if (document.getElementById('inlineRadio1').checked) {
                        document.getElementById('proposalexpiry').style.display = 'block';
                        document.getElementById('expiry_hide_show').style.display = 'block';
                    } else {
                        document.getElementById('proposalexpiry').style.display = 'none';
                        document.getElementById('expiry_hide_show').style.display = 'none';
                    }
                });
            });
        </script>

        <script>
            // Save original input values with unique prefixes
            let originalGtermsText = document.getElementById('gterms').value;
            if (originalGtermsText == '') {
                //$('#gtermstext').css('display', 'none');
            }

            // Update text as user types in the input field
            document.getElementById('gterms').addEventListener('input', function() {
                // alert(this.value);
                document.getElementById('gtermstext').style.display = 'block';

                document.getElementById('add_gterms').innerText = this.value;

            });

            // When "Done" button is clicked, confirm changes
            document.getElementById('cancelgterms').addEventListener('click', function() {
                document.getElementById('gterms').value = originalGtermsText;
                document.getElementById('add_gterms').innerText = originalGtermsText;
                if (originalGtermsText=='') {
                    $('#gtermstext').css('display', 'none');

                }else{
                    $('#gtermstext').css('display', 'block');
                }
            });
        </script>



        <script>
            // Save original input values and image source
            let originalTitleText = document.getElementById('titl_txt')?.value;
            let originalDescriptionText = document.getElementById('des_txt')?.value;
            let originalPreviewImageSrc = document.getElementById('preview_image')?.src;

            // Array to store the original image sources
            let originalImages = [];

            // Update text as user types in input fields
            document.getElementById('titl_txt')?.addEventListener('input', function() {
                document.getElementById('scope_title').innerText = this.value;
            });

            document.getElementById('des_txt')?.addEventListener('input', function() {
                document.getElementById('scope_des').innerText = this.value;
                console.info(this.value);
                // if (this.value === '') {
                //     $('.subtitle-text-main').css('display', 'none');
                // } else {
                //     $('.subtitle-text-main').css('display', 'block');
                // }
            });

            // document.getElementById('multiplefileupload4').addEventListener('change', function() {
            //     const files = this.files;
            //     const maxImages = 4;
            //     const mainBottom = document.getElementById('mainBottom');
            //     const uploadedImagesDiv = document.getElementById('uploded-images');
            //     let currentImageCount = mainBottom.getElementsByClassName('uploadimg').length;

            //     // Check if adding more images would exceed the limit
            //     if (currentImageCount + files.length > maxImages) {
            //         alert('You can only upload a maximum of 4 images.');
            //         this.value = ''; // Reset file input
            //         return;
            //     }

            //     // Loop through the selected files and append them
            //     $(mainBottom).html('');
            //     for (let i = 0; i < files.length; i++) {
            //         const file = files[i];
            //         if (file) {
            //             const fileReader = new FileReader();
            //             fileReader.onload = function(e) {
            //                 // Add to the main section
            //                 const colDiv = document.createElement('div');
            //                 colDiv.classList.add('col-6', 'mt-3', 'uploadimg');

            //                 const img = document.createElement('img');
            //                 img.src = e.target.result;
            //                 img.alt = `Uploaded Image ${currentImageCount + i + 1}`;
            //                 img.style.width = '100%';

            //                 colDiv.appendChild(img);
            //                 mainBottom.appendChild(colDiv);

            //                 // Add a small thumbnail to the uploaded-images div
            //                 const thumbDiv = document.createElement('div');
            //                 thumbDiv.classList.add('thumbnail-container');
            //                 thumbDiv.style.position = 'relative';
            //                 thumbDiv.style.display = 'inline-block';
            //                 thumbDiv.style.marginRight = '10px';

            //                 const thumbImg = document.createElement('img');
            //                 thumbImg.src = e.target.result;
            //                 thumbImg.alt = `Thumbnail Image ${currentImageCount + i + 1}`;
            //                 thumbImg.style.width = '50px'; // Small size for thumbnail

            //                 // Create delete icon (X) on hover
            //                 const deleteIcon = document.createElement('span');
            //                 deleteIcon.innerHTML = 'X';
            //                 deleteIcon.style.position = 'absolute';
            //                 deleteIcon.style.top = '0';
            //                 deleteIcon.style.right = '0';
            //                 deleteIcon.style.background = 'red';
            //                 deleteIcon.style.color = 'white';
            //                 deleteIcon.style.cursor = 'pointer';
            //                 deleteIcon.style.display = 'none';

            //                 // Show delete icon on hover
            //                 thumbDiv.addEventListener('mouseover', function() {
            //                     deleteIcon.style.display = 'block';
            //                 });
            //                 thumbDiv.addEventListener('mouseout', function() {
            //                     deleteIcon.style.display = 'none';
            //                 });

            //                 // Remove image on delete icon click
            //                 deleteIcon.addEventListener('click', function() {
            //                     thumbDiv.remove(); // Remove thumbnail
            //                     colDiv.remove(); // Remove corresponding image from main section
            //                 });

            //                 thumbDiv.appendChild(thumbImg);
            //                 thumbDiv.appendChild(deleteIcon);
            //                 uploadedImagesDiv.appendChild(thumbDiv);
            //             };
            //             fileReader.readAsDataURL(file);
            //         }
            //     }

            //     // Clear the input value so the user can select the same file again if needed
            //     // this.value = '';
            // });

            // Store the original images on page load or on first interaction
            // function storeOriginalImages() {
            //     const mainBottom = document.getElementById('mainBottom');
            //     const images = mainBottom.getElementsByTagName('img');
            //     originalImages = [];
            //     for (let i = 0; i < images.length; i++) {
            //         originalImages.push(images[i].src);
            //     }
            // }

            // // Call storeOriginalImages on page load to save the initial state
            // storeOriginalImages();

            // When "Done" button is clicked, confirm changes
            document.getElementById('doneBtnscope').addEventListener('click', function() {
                const finalTitleText = document.getElementById('titl_txt').value;
                const finalDescriptionText = document.getElementById('des_txt').value;


                originalTitleText = finalTitleText;
                originalDescriptionText = finalDescriptionText;
                // originalPreviewImageSrc = finalPreviewImageSrc;

                let formData = new FormData();

// console.info(fileInput.files);
// Append other form data
                formData.append('cov_title', originalTitleText);
                formData.append('sub_tit', originalDescriptionText);


                console.log(formData);
// Check the checkbox status
                var isChecked = $('#remem_settings_scope').prop('checked');
                formData.append('project_checked', isChecked);



// if (isChecked == true) {
                var url = "{{ URL::route(getRouteAlias() . '.storeDefaultSettingsScope') }}";

                // Send the data to the backend using AJAX
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        console.info('Data sent successfully:', response);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.info('Error sending data:', textStatus, errorThrown);
                    }
                });
// }
                $('.main_side_menu').css('display', 'block');
                $('.edit_sample_section').css('display', 'none');
            });

            // When "Cancel" button is clicked, revert changes
            document.getElementById('cancelBtnscope').addEventListener('click', function() {
                document.getElementById('titl_txt').value = originalTitleText;
                document.getElementById('scope_title').innerText = originalTitleText;
                document.getElementById('des_txt').value = originalDescriptionText;
                document.getElementById('scope_des').innerText = originalDescriptionText;
                document.getElementById('preview_image').src = originalPreviewImageSrc;

                const mainBottom = document.getElementById('mainBottom');
                mainBottom.innerHTML = ''; // Clear the current images
                for (let i = 0; i < originalImages.length; i++) {
                    const colDiv = document.createElement('div');
                    colDiv.classList.add('col-6', 'mt-3', 'uploadimg');

                    const img = document.createElement('img');
                    img.src = originalImages[i];
                    img.alt = `Original Image ${i + 1}`;
                    img.style.width = '100%';

                    colDiv.appendChild(img);
                    mainBottom.appendChild(colDiv);
                }
            });
        </script>



        <script>
            // Save original input values with unique prefixes
            let originalIntroLetter = document.getElementById('intro_letter').value;

            // Update text as user types in the input field
            document.getElementById('intro_letter').addEventListener('input', function() {
                document.getElementById('add_intro_letter').innerText = this.value;
            });

            // When "Cancel" button is clicked, revert changes
            document.getElementById('cancelBtnintro').addEventListener('click', function() {
                // Revert inputs and text to original values
                document.getElementById('intro_letter').value = originalIntroLetter;
                document.getElementById('add_intro_letter').innerText = originalIntroLetter;

                // Note: If there's an image to revert, add the code here
            });




            // When "Done" button is clicked, confirm changes
            document.getElementById('doneBtngallery').addEventListener('click', function() {
                // Fix current input value as final
                let originalIntroLetter = document.getElementById('gallery_title').value;

                // Create FormData object
                let formData = new FormData();

                // Get the file input element
                let fileInput = document.getElementById('multiplefileuploadgallery');
                if (fileInput.files.length === 0) {
                    alert('Please select an image file.');
                    return;
                }

                // Append multiple files to FormData
                // for (let i = 0; i < fileInput.files.length; i++) {
                //      // Note: 'image_files[]' indicates an array
                // }
                formData.append('image_files', fileInput.files[0]);

                // Append the input values to FormData
                formData.append('title', originalIntroLetter);

                // Include CSRF token
                formData.append('_token', "{{ csrf_token() }}");

                // Get the checked status of the checkbox
                let isChecked = $('#remem_settings_gallery').prop('checked');
                formData.append('project_checked', isChecked);



                // If checkbox is checked, proceed with AJAX request
                // if (isChecked) {
                let url = "{{ URL::route(getRouteAlias() . '.storeDefaultSettingsGallery') }}";

                // Send the data to the backend using AJAX
                $.ajax({
                    url: url, // Backend URL
                    type: 'POST', // POST method
                    data: formData,
                    processData: false, // Prevent jQuery from processing FormData
                    contentType: false, // Prevent jQuery from overriding content type
                    success: function(response) {
                        console.log('Data sent successfully:', response);
                        // Optionally, perform actions based on the response
                        // alert('Gallery settings saved successfully!');
                        location.reload();

                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error sending data:', textStatus, errorThrown);
                        alert('Failed to save gallery settings. Please try again.');
                    }
                });
                // }

                // Show/hide sections based on action
                $('.main_side_menu').css('display', 'block');
                $('.edit_gallery_section').css('display', 'none');
            });



        </script>



        <script>

            let originalAboutTitle = document.getElementById('about_text').value || '';

            let originalAboutImageSrc = document.getElementById('preview_about_image').src;
            console.info('1234',originalAboutImageSrc);

            // Update text as user types in the input field
            document.getElementById('about_text').addEventListener('input', function() {
                document.getElementById('add_about_text').innerText = this.value;
            });

            // Update image preview when a new image is selected
            document.getElementById('multiplefileupload3').addEventListener('change', function() {
                alert('1');
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewImage = document.getElementById('preview_about_image');
                        const container = document.querySelector('.pspdfkit-1thsp3e');

                        // Update the image source
                        previewImage.src = e.target.result;

                        // Show the container
                        if (container) {
                            container.style.display = 'block'; // or 'flex' if you prefer
                        }
                    };
                    reader.readAsDataURL(file);
                }
            });
            // When "Done" button is clicked, confirm changes
            document.getElementById('doneBtnabout').addEventListener('click', function() {

                // alert(opportunity_id);
                // Fix current input values and image as final
                originalAboutTitle = document.getElementById('about_text').value;
                originalAboutImageSrc = document.getElementById('preview_about_image').src;


                let formData = new FormData();

// Get the file input element
                let fileInput = document.getElementById('multiplefileupload3'); // Replace 'multiplefileupload2' with your actual file input ID

// Append the input values to the FormData object
                formData.append('intro', originalAboutTitle);


// Check if a file is selected
                if (fileInput.files.length > 0) {
                    formData.append('image_file', fileInput.files[0]);
                }

// Get the checked status of the checkbox

// alert(isChecked); // This will alert true if checked, false if unchecked




// Set the URL for your route
// if (isChecked == true) {


                var url = "{{ URL::route(getRouteAlias() . '.company.default.settings') }}";

// Send the data to the backend using AJAX
                $.ajax({
                    url: url, // Replace with your backend URL
                    type: 'POST', // Use POST method
                    data: formData,
                    processData: false, // Important: prevent jQuery from converting the data to a query string
                    contentType: false, // Important: set content type to false
                    success: function(response) {
                        // Handle the response from the server
                        console.log('Data sent successfully:', response);
                        // Optionally, you can perform actions here based on the response
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        // Handle any errors
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });

// }
                $('.main_side_menu').css('display', 'block');
                $('.edit_about_section').css('display', 'none');
            });

            // When "Cancel" button is clicked, revert changes
            document.getElementById('cancelBtnabout').addEventListener('click', function() {
                // Revert inputs and text to original values
                document.getElementById('about_text').value = originalAboutTitle;
                document.getElementById('add_about_text').innerText = originalAboutTitle;

                // Revert image to original
                document.getElementById('preview_about_image').src = originalAboutImageSrc;
                if (document.getElementById('preview_about_image').src=='') {
                    $('.pspdfkit-1thsp3e').css('display', 'none');
                }
                // $('.pspdfkit-1thsp3e').css('display', 'none');
            });
        </script>

        <script>
            if (document.getElementById('add_sub_tit').innerText=='') {
                $('.subtitle-text-main').css('display', 'none');
            }
            // Save original input values and image source
            let originalcov_title = document.getElementById('cov_title').value;
            let originalsub_tit = document.getElementById('sub_tit').value;
            let originalImageSrc = document.getElementById('preview_image').src;

            // Update text as user types in input fields
            document.getElementById('cov_title').addEventListener('input', function() {
                document.getElementById('add_cov_tit').innerText = this.value;
            });

            document.getElementById('sub_tit').addEventListener('input', function() {
                document.getElementById('add_sub_tit').innerText = this.value;
                if (this.value == '') {
                    $('.subtitle-text-main').css('display', 'none');
                } else {
                    $('.subtitle-text-main').css('display', 'block');
                }
            });

            // Update image preview when a new image is selected
            document.getElementById('multiplefileupload2').addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('preview_image').src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });

            // When "Done" button is clicked, confirm changes
            document.getElementById('doneBtn').addEventListener('click', function() {
                // Create a FormData object to hold the data

                // alert(opportunity_id);
                let formData = new FormData();

                // Get values from input fields
                let originalcov_title = document.getElementById('cov_title').value;
                let originalsub_tit = document.getElementById('sub_tit').value;

                // Get the file input element
                let fileInput = document.getElementById('multiplefileupload2'); // Replace 'multiplefileupload2' with your actual file input ID

                // Append the input values to the FormData object
                formData.append('cov_title', originalcov_title);
                formData.append('sub_tit', originalsub_tit);


                // Check if a file is selected
                if (fileInput.files.length > 0) {
                    formData.append('image_file', fileInput.files[0]);
                }


// alert(isChecked); // This will alert true if checked, false if unchecked

                // Set the URL for your route
                // if (isChecked == true) {


                var url = "{{ URL::route(getRouteAlias() . '.company.default.cover.settings') }}";

                // Send the data to the backend using AJAX
                $.ajax({
                    url: url, // Replace with your backend URL
                    type: 'POST', // Use POST method
                    data: formData,
                    processData: false, // Important: prevent jQuery from converting the data to a query string
                    contentType: false, // Important: set content type to false
                    success: function(response) {
                        // alert(isChecked);
                        // Handle the response from the server
                        console.log('Data sent successfully:', response);
                        // Optionally, you can perform actions here based on the response
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        // Handle any errors
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });

// }

// Show the main side menu and hide the edit section
                $('.main_side_menu').css('display', 'block');
                $('.edit_cover_section').css('display', 'none');
            });

            // When "Cancel" button is clicked, revert changes
            document.getElementById('cancelBtn').addEventListener('click', function() {
                // Revert inputs and text to original values
                document.getElementById('cov_title').value = originalcov_title;
                document.getElementById('add_cov_tit').innerText = originalcov_title;
                document.getElementById('sub_tit').value = originalsub_tit;
                document.getElementById('add_sub_tit').innerText = originalsub_tit;

                // Revert image to original
                document.getElementById('preview_image').src = originalImageSrc;

                if (originalsub_tit == '') {
                    $('.subtitle-text-main').css('display', 'none');
                } else {
                    $('.subtitle-text-main').css('display', 'inline-block');
                }
            });
        </script>
        <script>
            tinymce.init({
                selector: '#textEditor',
                plugins: 'lists',
                toolbar: 'bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist',
                height: 300,
                menubar: false,
                branding: false,
                toolbar_mode: 'wrap',
                content_style: "ul { list-style-type: disc; } ol { list-style-type: decimal; }",
                setup: function (editor) {
                    editor.on('input', function () {
                        // Get content and set it to the div
                        document.getElementById('add_about_text').innerHTML = editor.getContent();
                    });
                }
            });
            tinymce.init({
                selector: '#intro_letter',
                plugins: 'lists',
                toolbar: 'bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist',
                height: 300,
                menubar: false,
                branding: false,
                toolbar_mode: 'wrap',
                content_style: "ul { list-style-type: disc; } ol { list-style-type: decimal; }",
                setup: function (editor) {
                    editor.on('input', function () {
                        // Get content and set it to the div
                        document.getElementById('add_intro_letter').innerHTML = editor.getContent();
                    });
                }
            });

            tinymce.init({
                selector: '#gterms',
                plugins: 'lists',
                toolbar: 'bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist',
                height: 300,
                menubar: false,
                branding: false,
                toolbar_mode: 'wrap',
                content_style: "ul { list-style-type: disc; } ol { list-style-type: decimal; }",
                setup: function (editor) {
                    editor.on('input', function () {
                        // Get content and set it to the div
                        document.getElementById('gtermstext').innerHTML = editor.getContent();
                    });
                }
            });

            document.getElementById('doneBtnabout').addEventListener('click', function() {
                // Create a FormData object to hold the data

                // alert(opportunity_id);
                let formData = new FormData();

                // Get values from input fields
                let originalAboutUs = tinymce.get('textEditor').getContent();

                // Get the file input element
                let fileInput = document.getElementById('multiplefileupload3'); // Replace 'multiplefileupload2' with your actual file input ID

                // Append the input values to the FormData object
                formData.append('cov_title', null);
                formData.append('sub_tit', originalAboutUs);


                // Check if a file is selected
                if (fileInput.files.length > 0) {
                    formData.append('image_file', fileInput.files[0]);
                }


// alert(isChecked); // This will alert true if checked, false if unchecked

                // Set the URL for your route
                // if (isChecked == true) {


                var url = "{{  URL::route(getRouteAlias() . '.company.default.about.settings') }}";

                // Send the data to the backend using AJAX
                $.ajax({
                    url: url, // Replace with your backend URL
                    type: 'POST', // Use POST method
                    data: formData,
                    processData: false, // Important: prevent jQuery from converting the data to a query string
                    contentType: false, // Important: set content type to false
                    success: function(response) {
                        // alert(isChecked);
                        // Handle the response from the server
                        console.log('Data sent successfully:', response);
                        // Optionally, you can perform actions here based on the response
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        // Handle any errors
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });

// }

// Show the main side menu and hide the edit section
                $('.main_side_menu').css('display', 'block');
                $('.edit_about_section').css('display', 'none');
            });

            document.getElementById('doneBtnintro').addEventListener('click', function() {
                // Create a FormData object to hold the data

                // alert(opportunity_id);
                let formData = new FormData();

                // Get values from input fields
                let originalPersonalIntro = tinymce.get('intro_letter').getContent();

                // Get the file input element
                //let fileInput = document.getElementById('multiplefileupload3'); // Replace 'multiplefileupload2' with your actual file input ID

                // Append the input values to the FormData object
                formData.append('cov_title', null);
                formData.append('sub_tit', originalPersonalIntro);
                formData.append('setting_type', 'intro');


                // Check if a file is selected
                /*if (fileInput.files.length > 0) {
                    formData.append('image_file', fileInput.files[0]);
                }*/


                var url = "{{  URL::route(getRouteAlias() . '.company.default.intro.settings') }}";

                // Send the data to the backend using AJAX
                $.ajax({
                    url: url, // Replace with your backend URL
                    type: 'POST', // Use POST method
                    data: formData,
                    processData: false, // Important: prevent jQuery from converting the data to a query string
                    contentType: false, // Important: set content type to false
                    success: function(response) {
                        // alert(isChecked);
                        // Handle the response from the server
                        console.log('Data sent successfully:', response);
                        // Optionally, you can perform actions here based on the response
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        // Handle any errors
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });

// }

// Show the main side menu and hide the edit section
                $('.main_side_menu').css('display', 'block');
                $('.edit_personal_section').css('display', 'none');
            });

            document.getElementById('donebtngterms').addEventListener('click', function() {
                // Create a FormData object to hold the data

                // alert(opportunity_id);
                let formData = new FormData();

                // Get values from input fields
                let originalTerms = tinymce.get('gterms').getContent();

                // Get the file input element
                //let fileInput = document.getElementById('multiplefileupload3'); // Replace 'multiplefileupload2' with your actual file input ID

                // Append the input values to the FormData object
                formData.append('cov_title', null);
                formData.append('sub_tit', originalTerms);
                formData.append('setting_type', 'terms');


                // Check if a file is selected
                /*if (fileInput.files.length > 0) {
                    formData.append('image_file', fileInput.files[0]);
                }*/


                var url = "{{  URL::route(getRouteAlias() . '.company.default.terms.settings') }}";

                // Send the data to the backend using AJAX
                $.ajax({
                    url: url, // Replace with your backend URL
                    type: 'POST', // Use POST method
                    data: formData,
                    processData: false, // Important: prevent jQuery from converting the data to a query string
                    contentType: false, // Important: set content type to false
                    success: function(response) {
                        // alert(isChecked);
                        // Handle the response from the server
                        console.log('Data sent successfully:', response);
                        // Optionally, you can perform actions here based on the response
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        // Handle any errors
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });

// }

// Show the main side menu and hide the edit section
                $('.main_side_menu').css('display', 'block');
                $('.edit_terms_section').css('display', 'none');
            });

            document.getElementById('donepaymentsave').addEventListener('click', function() {
                let paymentSchedle = document.getElementById('paymentschedle').value;
                let downPayment = document.getElementById('getdown1').value;
                let downPaymentPercent = document.getElementById('getdown2').value;

                let formData = new FormData();
                formData.append('down_payment', downPayment);
                formData.append('down_payment_percent', downPaymentPercent);
                formData.append('cov_sub_title', paymentSchedle);
                var url = "{{ URL::route(getRouteAlias() . '.company.default.payment.settings') }}";
                $.ajax({
                    url: url, // Replace with your backend URL
                    type: 'POST', // Use POST method
                    data: formData,
                    processData: false, // Important: prevent jQuery from converting the data to a query string
                    contentType: false, // Important: set content type to false
                    success: function(response) {
                        // Handle the response from the server
                        console.log('Data sent successfully:', response);
                        // Optionally, you can perform actions here based on the response
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        // Handle any errors
                        console.error('Error sending data:', textStatus, errorThrown);
                    }
                });

                $('.main_side_menu').css('display', 'block');
                $('.edit_payment_section').css('display', 'none');
            });
        </script>

        <script>

        </script>
        <script>
            function paymentSection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.paymntbtn').hasClass("active")) {
                    $('.payment_btn').addClass('browntext');
                    $('.payment_btn').removeClass('bluetext');
                    $('.payment_btn').attr('disabled', true);
                } else {
                    $('.payment_btn').removeClass('browntext');
                    $('.payment_btn').addClass('bluetext');
                    $('.payment_btn').attr('disabled', false);
                }
                $('.payment_scdul').toggle();
            }
            function termsSection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.termbtn').hasClass("active")) {
                    $('.terms_btn').addClass('browntext');
                    $('.terms_btn').removeClass('bluetext');
                    $('.terms_btn').attr('disabled', true);
                } else {
                    $('.terms_btn').removeClass('browntext');
                    $('.terms_btn').addClass('bluetext');
                    $('.terms_btn').attr('disabled', false);
                }
                $('.terms_conditions').toggle();
            }
            function scopeSection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.scopbtn').hasClass("active")) {
                    $('.scope_btn').addClass('browntext');
                    $('.scope_btn').removeClass('bluetext');
                    $('.scope_btn').attr('disabled', true);
                } else {
                    $('.scope_btn').removeClass('browntext');
                    $('.scope_btn').addClass('bluetext');
                    $('.scope_btn').attr('disabled', false);
                }
                $('.scop_of_work').toggle();
            }
            function introSection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.introbtn').hasClass("active")) {
                    $('.intro_btn').addClass('browntext');
                    $('.intro_btn').removeClass('bluetext');
                    $('.intro_btn').attr('disabled', true);
                } else {
                    $('.intro_btn').removeClass('browntext');
                    $('.intro_btn').addClass('bluetext');
                    $('.intro_btn').attr('disabled', false);
                }
                $('.personal').toggle();
            }
            function aboutSection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.abutbtn').hasClass("active")) {
                    $('.about_btn').addClass('browntext');
                    $('.about_btn').removeClass('bluetext');
                    $('.about_btn').attr('disabled', true);
                } else {
                    $('.about_btn').removeClass('browntext');
                    $('.about_btn').addClass('bluetext');
                    $('.about_btn').attr('disabled', false);
                }
                $('.about').toggle();
            }
            function coverSection()
            {
                $(this).toggleClass("change-after-color");

                if ($('.coverdbtn').hasClass("active")) {
                    $('.cover_custom').addClass('browntext');
                    $('.cover_custom').removeClass('bluetext');
                    $('.cover_custom').attr('disabled', true);
                } else {
                    $('.cover_custom').removeClass('browntext');
                    $('.cover_custom').addClass('bluetext');
                    $('.cover_custom').attr('disabled', false);
                }
                $('.coverd').toggle();
            }
        </script>
        <script>
            function hideeditcover(id){
                $('.edit_cover_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
            function hideeditgallery(id){
                $('.edit_gallery_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
            function hideeditabout(){
                $('.edit_about_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
            function hideeditpersonal(){
                $('.edit_personal_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
            function hideeditsample(){
                $('.edit_sample_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
            function hideeditterms(){
                $('.edit_terms_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
            function hideeditpayment(){
                $('.edit_payment_section').css('display', 'none');
                $('.main_side_menu').css('display', 'block');

            }
        </script>

        <script>
            function showeditcover(id){
                $('.edit_cover_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
            function showeditabout(){
                $('.edit_about_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
            function showeditpersonal(){
                $('.edit_personal_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
            function showeditsample(){
                $('.edit_sample_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
            function showeditterms(){
                $('.edit_terms_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
            function showeditpayment(){
                $('.edit_payment_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
            function showeditgallery(){
                alert('1');
                $('.edit_gallery_section').css('display', 'block');
                $('.main_side_menu').css('display', 'none');

            }
        </script>
        <script>
            $(document).ready(function() {
                $(".btn-toggle").on("click", function() {
                });
            });

        </script>
        <script src="{{ asset('asset/assets/js/bootstrap.js') }}"></script>
        <script>
            $("#multiplefileuploadtemplate").fileinput({
                theme: "explorer-fas",
                uploadUrl: "#",
                deleteUrl: "#",
                initialPreviewAsData: true,
                overwriteInitial: false,
                dropZoneTitle: '<div class="upload-area"><svg width="41" height="41" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.5" d="M11.334 30.5V30.35C11.334 28.9084 11.334 27.585 11.479 26.51C11.6373 25.325 12.0123 24.0717 13.0423 23.0434C14.0723 22.0117 15.3257 21.6367 16.509 21.4767C17.5857 21.3334 18.909 21.3334 20.3523 21.3334H20.649C22.0923 21.3334 23.4157 21.3334 24.4907 21.4784C25.6757 21.6367 26.929 22.0117 27.9573 23.0417C28.989 24.0717 29.364 25.325 29.524 26.5084C29.6656 27.57 29.6673 28.8684 29.6673 30.2867C33.9557 29.37 37.1673 25.6 37.1673 21.0867C37.1673 16.97 34.489 13.4667 30.759 12.1917C30.229 7.49004 26.1923 3.83337 21.294 3.83337C16.034 3.83337 11.7707 8.04671 11.7707 13.245C11.7707 14.395 11.979 15.495 12.3607 16.515C11.9048 16.4269 11.4416 16.3823 10.9773 16.3817C7.03232 16.3834 3.83398 19.5434 3.83398 23.4417C3.83398 27.34 7.03232 30.5 10.9773 30.5H11.334Z" fill="#0074D9"/><path fill-rule="evenodd" clip-rule="evenodd" d="M20.5007 23.8334C17.3573 23.8334 15.7873 23.8334 14.8107 24.81C13.834 25.7867 13.834 27.3567 13.834 30.5C13.834 33.6434 13.834 35.2134 14.8107 36.19C15.7873 37.1667 17.3573 37.1667 20.5007 37.1667C23.644 37.1667 25.214 37.1667 26.1906 36.19C27.1673 35.2134 27.1673 33.6434 27.1673 30.5C27.1673 27.3567 27.1673 25.7867 26.1906 24.81C25.214 23.8334 23.644 23.8334 20.5007 23.8334ZM23.509 28.9734L21.2857 26.7517C21.0773 26.5439 20.795 26.4272 20.5007 26.4272C20.2063 26.4272 19.924 26.5439 19.7157 26.7517L17.4923 28.9734C17.385 29.0756 17.2991 29.1983 17.2399 29.3342C17.1807 29.4702 17.1492 29.6166 17.1474 29.7648C17.1456 29.9131 17.1735 30.0602 17.2294 30.1975C17.2853 30.3348 17.3681 30.4596 17.4729 30.5644C17.5778 30.6693 17.7025 30.7521 17.8398 30.808C17.9772 30.8639 18.1243 30.8918 18.2725 30.89C18.4208 30.8881 18.5672 30.8567 18.7031 30.7975C18.839 30.7382 18.9617 30.6524 19.064 30.545L19.389 30.22V33.4634C19.389 33.7582 19.5061 34.041 19.7146 34.2494C19.9231 34.4579 20.2058 34.575 20.5007 34.575C20.7955 34.575 21.0782 34.4579 21.2867 34.2494C21.4952 34.041 21.6123 33.7582 21.6123 33.4634V30.22L21.9373 30.545C22.1474 30.7451 22.4274 30.8552 22.7175 30.8516C23.0077 30.8481 23.2849 30.7313 23.4901 30.5261C23.6952 30.3209 23.812 30.0437 23.8156 29.7536C23.8191 29.4635 23.7091 29.1835 23.509 28.9734Z" fill="#0074D9"/></svg><p><label style="color: #7C8091; font-size: 14px">Drop files here or </label> <b style="color: #0074D9">Browse</b></p> <div> </div></div>',
                dropZoneClickTitle: "",
                browseOnZoneClick: true,
                showRemove: false,
                showUpload: false,
                showZoom: false,
                showCaption: false,
                showBrowse: false,
                browseClass: "btn btn-danger",
                browseLabel: "",
                browseIcon: "<i class='fa fa-plus'></i>",
                fileActionSettings: {
                    showUpload: false,
                    showDownload: false,
                    showZoom: false,
                    showDrag: true,
                    removeIcon: "<i class='fa fa-times'></i>",
                    uploadIcon: "<i class='fa fa-upload'></i>",
                    dragIcon: "<i class='fa fa-arrows-alt'></i>",
                    uploadRetryIcon: "<i class='fa fa-undo-alt'></i>",
                    dragClass: "file-drag",
                    removeClass: "file-remove",
                    removeErrorClass: 'file-remove',
                    uploadClass: "file-upload",
                },
                frameClass: "file-sortable",
                layoutTemplates: {
                    preview:
                        '<div class="file-preview {class}">\n' +
                        '    <div class="{dropClass}">\n' +
                        '    <div class="clearfix"></div>' +
                        '    <div class="file-preview-status text-center text-success"></div>\n' +
                        '    <div class="kv-fileinput-error"></div>\n' +
                        "    </div>\n" +
                        "</div>" +
                        ' <div class="file-preview-thumbnails">\n' +
                        " </div>\n",
                    actionDrag: '<button class="file-drag-handle {dragClass}" title="{dragTitle}">{dragIcon}</button>',
                    footer: '<div class="file-thumbnail-footer">\n' + '<div class="file-detail">' + '<div class="file-caption-name">{caption}</div>\n' + '    <div class="file-size">{size}</div>\n' + "</div>" + "   {actions}\n" + "</div>",
                },
            });

        </script>

    @endpush



@endsection
