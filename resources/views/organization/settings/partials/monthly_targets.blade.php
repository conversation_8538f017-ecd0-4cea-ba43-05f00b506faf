@if (isset($monthlyTargets))
    @foreach ($monthlyTargets as $item)
        <tr>
            <td>{{ $item['month'] }}</td>
            <td>
                {{-- @if ($item['price'] != '---')
                    $
                @endif --}}
                @if ($item['price'])
                    $
                @endif
                <span class="dollar d-none">$</span><input
                type="text"
                onkeypress="return onlyNumberKey(event)"
                    style="border: none;border-bottom: 1px solid rgb(146, 142, 142);width: 100px;background:none;
                padding-left: 6px;"
                    class="targetInput" name="{{ $item['month'] }}" value="{{ $item['price'] }}">
            </td>
            <td class="d-flex align-items-center gap-3 text-center">
                {{-- <button type="button" class="bg-transparent border-0 editMonthlyTargetBtn"
                    data-price="{{ $item['price'] }}" data-month="{{ $item['month'] }}" data-id={{ $item['id'] }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
                        <path
                            d="M13.666 3.31731C13.8849 3.09844 14.1447 2.92482 14.4307 2.80637C14.7167 2.68792 15.0232 2.62695 15.3327 2.62695C15.6422 2.62695 15.9487 2.68792 16.2347 2.80637C16.5206 2.92482 16.7805 3.09844 16.9993 3.31731C17.2182 3.53618 17.3918 3.79601 17.5103 4.08198C17.6287 4.36795 17.6897 4.67445 17.6897 4.98398C17.6897 5.2935 17.6287 5.6 17.5103 5.88597C17.3918 6.17194 17.2182 6.43177 16.9993 6.65064L5.74935 17.9006L1.16602 19.1506L2.41602 14.5673L13.666 3.31731Z"
                            stroke="#2FA1F8" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </button> --}}
                @if ($item['price'])
                    <button type="button" class="bg-transparent border-0 deleteMonthlyTargetBtn"
                        data-month="{{ $item['month'] }}" data-id={{ $item['id'] }}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21"
                            fill="none">
                            <path
                                d="M2.5 5.81576H4.16667M4.16667 5.81576H17.5M4.16667 5.81576V17.4824C4.16667 17.9245 4.34226 18.3484 4.65482 18.6609C4.96738 18.9735 5.39131 19.1491 5.83333 19.1491H14.1667C14.6087 19.1491 15.0326 18.9735 15.3452 18.6609C15.6577 18.3484 15.8333 17.9245 15.8333 17.4824V5.81576H4.16667ZM6.66667 5.81576V4.14909C6.66667 3.70706 6.84226 3.28314 7.15482 2.97058C7.46738 2.65802 7.89131 2.48242 8.33333 2.48242H11.6667C12.1087 2.48242 12.5326 2.65802 12.8452 2.97058C13.1577 3.28314 13.3333 3.70706 13.3333 4.14909V5.81576M8.33333 9.98242V14.9824M11.6667 9.98242V14.9824"
                                stroke="#FF4B55" stroke-width="1.66667" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </button>
                @endif
            </td>
        </tr>
    @endforeach

@endif



<!-- confirmation deltee target modal -->
<div class="modal fade" id="deleteTargetModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered delete-modal">
        <div class="modal-content">
            <div class="modal-body">
                {{-- <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button> --}}
                <img src="{{ asset('admin_assets/images/icons/delete-icon.svg') }}" alt="delete icon"
                    class="delete-icon">
                <h2 class="delete-request">Delete Target</h2>
                <p class="are-sure">Are you sure you want to delete this target?</p>
                <div class="buttons-wraper">
                    <input type="hidden" class="TargetDeleteId" value="">
                    <button type="button" class="cancel-btn" data-dismiss="modal">Cancel</button>
                    <button type="button" class="conform-btn" id="confirmDeleteTarget">Yes</button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
    <script>
        $(document).ready(function() {
            $(document).on('keyup', '.targetInput', function(e) {
                $(this).siblings('.dollar').removeClass('d-none');
            });

        });
        function onlyNumberKey(evt) {

            // Only ASCII character in that range allowed
            var ASCIICode = (evt.which) ? evt.which : evt.keyCode
            if (ASCIICode > 31 && (ASCIICode < 48 || ASCIICode > 57))
                return false;
            return true;
        }
    </script>
@endpush
