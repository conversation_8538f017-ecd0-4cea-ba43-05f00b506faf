@extends('layouts.admin.master')
@section('title', 'Settings')
@section('section')
    <section class="dashboard_main">

        <div class="settings_tab_grid">

            <x-settings_component.settings_tab />

            <div class="settings_content margin-setup-screen">
                <form action="{{ route(getRouteAlias() . '.save-margin') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <!-- <div class="panel">
                        <div class="panel_header">
                            <h2 class="panel_title">Company Information</h2>
                        </div>

                        <div class="row mt-4 gx-4 gy-4">
                            <div class="col-lg-4 col-md-6">
                                <div class="field">
                                    <label class="label mb-2"> Company Name </label>
                                    <input type="text" @readonly(true) value="{{ $company->company_name ?? '' }}"
                                        placeholder="Company Name" class="input form-control">
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <div class="field">
                                    <label class="label mb-2"> Address 1 </label>
                                    <input type="text" @readonly(true)
                                        value="{{ $company->companyPropertyAddress?->address1 ?? '' }}"
                                        placeholder="Address 1" class="input form-control">
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <div class="field">
                                    <label class="label mb-2"> Address 2 <span
                                            class="placeholder-text">(Optional)</span></label>
                                    <input type="text" @readonly(true)
                                        value="{{ $company->companyPropertyAddress?->address2 ?? '' }}"
                                        placeholder="Address 2" class="input form-control">
                                </div>
                            </div>


                            <div class="col-lg-4 col-md-6">
                                <div class="field">
                                    <label class="label mb-2">City </label>
                                    <input type="text" @readonly(true)
                                        value="{{ $company->companyPropertyAddress?->city }}" placeholder="City"
                                        class="input form-control">
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <div class="field">
                                    <label class="label mb-2">State </label>
                                    <input type="text" @readonly(true)
                                        value="{{ $company->companyPropertyAddress?->state }}" placeholder="State"
                                        class="input form-control">
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Zip Code </label>
                                    <input type="text" @readonly(true)
                                        value="{{ $company->companyPropertyAddress?->zip }}" placeholder="Zip Code"
                                        class="input form-control">
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <div class="field">
                                    <label class="label mb-2"> Email </label>
                                    <input type="text" @readonly(true) value="{{ $company->email ?? '' }}"
                                        placeholder="Email" class="input form-control">
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <div class="field">
                                    <label class="label mb-2">Website </label>
                                    <input type="text" @readonly(true)
                                        value="{{ $company->companyPropertyAddress->website_url ?? '' }}"
                                        placeholder="Website" class="input form-control">
                                </div>
                            </div>


                        </div>

                    </div> -->

                    <div class="panel mt-4">
                        <div class="panel_header">
                            <h2 class="panel_title">Labor Rates</h2>
                        </div>

                        <div class="table-responsive">
                            <table class="custom_datatable display mt-4" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th style="width:230px;">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse ($labors as $labor)
                                        <tr>
                                            <td>{{ $labor->name }}</td>
                                        <?php
                                        // dd($labor->name);
                                            ?>
                                            <td>
                                                <div class="position-relative">
                                                    <input type="number" name="{{ $labor->name }}"
                                                        value="{{ old($labor->name, $labor->cost) }}" id=""
                                                        class="input form-control" placeholder="18">
                                                    <span class="dollar">$</span>
                                                </div>
                                                @if ($errors->has($labor->name))
                                                    <div class="laravel_error">{{ $errors->first($labor->name) }}</div>
                                                @endif
                                            </td>
                                        </tr>
                                    @empty
                                    @endforelse


                            </table>
                        </div>

                        <div class="table-responsive">
                            <table class="custom_datatable display" style="width:100%">
                                <tbody>
                                    <tr>
                                        <td style="background: #F9F9F9;">Labor Burden%</td>
                                        <td style="width:230px;background: #F9F9F9;">
                                            <input type="number" name="labor_burden" id=""
                                                value="{{ old('labor_burden', $laborBurden) }}" class="input form-control"
                                                placeholder="Enter labor burden" min="0" max="100" oninput="this.value = Math.max(0, Math.min(100, this.value));">
                                            @if ($errors->has('labor_burden'))
                                                <div class="laravel_error">{{ $errors->first('labor_burden') }}</div>
                                            @endif
                                        </td>
                                    </tr>
                            </table>
                        </div>
                    </div>

                    <div class="panel mt-4">
                        <div class="panel_header">
                            <h2 class="panel_title">Margins</h2>
                        </div>

                        <div class="table-responsive">
                            <table class="custom_datatable display mt-4" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th style="width:230px;">Default (%)</th>
                                        <th style="width:230px;">Minimum (%)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse ($transformed as $margin)
                                        <tr>
                                            <td>{{ str_replace('_',' ',$margin['name']) }}</td>
                                            <td>
                                                <input type="number" name="{{ $margin['name'] . 'default' }}"
                                                    value="{{ old($margin['name'] . 'default', $margin['default']) }}"
                                                    id="" class="input form-control" min="0" max= "100" placeholder="Enter default value" oninput="this.value = Math.max(0, Math.min(100, this.value));">
                                                @if ($errors->has($margin['name'] . 'default'))
                                                    <div class="laravel_error">
                                                        {{ $errors->first($margin['name'] . 'default') }}</div>
                                                @endif
                                            </td>
                                            <td>
                                                <input type="text" name="{{ $margin['name'] . 'minimum' }}"
                                                    id=""
                                                    value="{{ old($margin['name'] . 'minimum', $margin['minimum']) }}"
                                                    class="input form-control" min="0" max= "100" placeholder="Enter minimum margin" oninput="this.value = Math.max(0, Math.min(100, this.value));">
                                                @if ($errors->has($margin['name'] . 'minimum'))
                                                    <div class="laravel_error">
                                                        {{ $errors->first($margin['name'] . 'minimum') }}</div>
                                                @endif
                                            </td>
                                        </tr>
                                    @empty
                                    @endforelse
                                    <tr>
                                        <td>Sub-contractor Markup</td>
                                        <td>
                                            <input type="number" name="contractor_default" id=""
                                                class="input form-control"
                                                value="{{ old('contractor_default', $contractor->default ?? null) }}"
                                                placeholder="Enter default value"  oninput="this.value = Math.max(0, Math.min(100, this.value));">
                                            @if ($errors->has('contractor_default'))
                                                <div class="laravel_error">{{ $errors->first('contractor_default') }}
                                                </div>
                                            @endif
                                        </td>
                                        <td>

<input type="numer" name="contractor_minimum" id=""
    class="input form-control"
    value="{{ old('contractor_minimum', $contractor->minimum ?? null) }}"
    placeholder="Enter minimum margin" oninput="this.value = Math.max(0, Math.min(100, this.value));">
@if ($errors->has('contractor_minimum'))
    <div class="laravel_error">{{ $errors->first('contractor_minimum') }}
    </div>
@endif
</td>
                                    </tr>


                            </table>
                        </div>

                    </div>

                    <div class="panel mt-4">
                        <div class="panel_header">
                            <h2 class="panel_title">Sub-contractor Markup</h2>
                        </div>

                        <div class="table-responsive">
                            <table class="custom_datatable display mt-4" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th style="width:230px;">Amount ($)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <tr>
                                        <td>Subcontractor</td>
                                        <td>
                                        <div class="position-relative">
                                            <input type="number" name="contractor_amount" id=""
                                                class="input form-control"
                                                value="{{ old('cost', $contractoram->cost ?? null) }}"
                                                placeholder="Enter default value">
                                                <span class="dollar">$</span>
                                                </div>
                                            @if ($errors->has('contractor_amount'))
                                                <div class="laravel_error">{{ $errors->first('contractor_amount') }}
                                                </div>
                                            @endif
                                        </td>
                                    </tr>


                            </table>
                        </div>




                        <div class="field mt-4">
                            <div class="d-flex align-items-center justify-content-end gap-5">
                                <label for="purchase_id" class="label">Sales Tax &nbsp;&nbsp;</label>
                                <div style="width:136px;">
                                    <input type="text" id="purchase_id" class="input" name="sale_tax"
                                        value="{{ old('sale_tax', $userSaleTax->sale_tax) }}" placeholder="Enter sales tax">
                                    @if ($errors->has('sale_tax'))
                                        <div class="laravel_error">{{ $errors->first('sale_tax') }}
                                        </div>
                                    @endif
                                </div>

                            </div>
                        </div>


                    </div>

                    <div class="d-flex align-items-center justify-content-end gap-3 flex-wrap mt-5">
                        {{-- <button type="reset" class="btn primaryblue transparent px-5 min-w-174">Cancel</button> --}}
                        <button type="submit" class="btn primaryblue px-5 min-w-174">Save</button>
                    </div>

                </form>

            </div>

        </div>

    </section>
@endsection
