@extends('layouts.admin.master')
@section('title', 'Settings')
@section('styles')

    <style>
        .monthpicker .monthpicker_selector {
            position: absolute;
            top: 101%;
            z-index: 999;
            left: 0;
            right: 0;
            box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
            background: #FFFFFF;
            padding: 10px 20px;
            border-radius: 10px;
        }

        .monthpicker .monthpicker_selector table tr td {
            text-align: center;
            font-family: "Poppins" !important;
            font-style: normal !important;
            font-weight: 500 !important;
            font-size: 14px !important;
            line-height: 22px !important;
            color: #192a3e;
            padding-top: 5px;
            padding-bottom: 5px;
        }

        .monthpicker .monthpicker_selector table tr:first-child td {
            padding-bottom: 20px;
        }

    .select2-container--default{
        margin-top: 21px !important;
    }
    .select2-dropdown{
        margin-top: -45px !important;
    }

    </style>

@endsection


@section('section')
    @auth
        <section class="dashboard_main">

            <div class="settings_tab_grid">

                <x-settings_component.settings_tab />

                <div class="settings_content">

                    <div class="table_filter_header mb-4">
                        <h2 class="sub_heading">Targets</h2>

                        <div class="d-flex align-items-center gap-3">
                            <div class="filters">
                                {{-- <input type="search" placeholder="Search" name="" id=""
                                    class="clients_Detail_Search filter_search" style="max-width: 340px"> --}}
                                <div class="field">
                                    <div class="absolute-error position-relative" style="height: 100%;width:120px;">
                                        <select name="due_date" id="monthlyTargetFilter" class="input basic-single-select">
                                            @foreach ($organizationYears as $item)
                                                <option value="{{ $item }}">Year {{ $item }}
                                                </option>
                                            @endforeach

                                        </select>
                                        <!-- Hidden input field for the datepicker to attach to -->
                                        <input type="text" id="customDateInput" value="" style="display: none;">
                                    </div>
                                </div>
                            </div>
                            <a type="button" class="btn primaryblue text-decoration-none bg-transparent editMonthlyTarget"
                                style="color:#003366">Save</a>
                        </div>
                    </div>
                    <form id="editTargetForm">
                        <input type="hidden" name="targetYear" class="targetYear" value="">
                        <div class="table-responsive">
                            <table id="clients_Detail" class="table table-striped custom_datatable display" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>Months</th>
                                        <th>Sales Goal</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="monthlyTargets">
                                    @include('organization.settings.partials.monthly_targets')
                                </tbody>


                            </table>
                        </div>
                    </form>

                </div>

            </div>


        </section>


        <div class="modal fade propert-modal" id="addTargetModal" data-backdrop="static" data-keyboard="false"
            tabindex="-1" aria-labelledby="addTargetModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content" style="overflow: unset;">
                    <div class="modal-header" style="border-radius: 8px 8px 0 0">
                        <h1 class="modal-title fs-5" id="addTargetModalLabel">Add New Target</h1>
                        <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="alertDiv"></div>
                    <form id="addTargetForm">
                        <div class="modal-body">
                            <div class="body-wraped" style="gap: 20px;">
                                <div class="filed-wraper">
                                    <label for="name" class="modal-label">Set Price</label>
                                    <input class="modal-field" type="number" id="setPrice" name="price"
                                        placeholder="Enter amount" min="1">
                                </div>

                                <div class="field position-relative">
                                    <label class="label">Months</label>
                                    <input type="text" id="month" name="month" value=""
                                        placeholder="Select month" class="modal-field datepicker-icon" readonly=""
                                        style="border: 1px solid #90a0b7;background-color:white;">
                                </div>
                                <button type="submit" class="add-target cursor-pointer mt-3">Add Target</button>
                            </div>
                    </form>

                </div>
            </div>
        </div>



    @endauth
@endsection

@push('scripts')
    <script>
        // monthpicker.js
        "use strict";
        var Monthpicker = (function() {
            function c(a, b) {
                this.selectedMonth = this.selectedYear = this.currentYear = null;
                this.id = c.next_id++;
                c.instances[this.id] = this;
                this.original_input = a;
                this.InitOptions(b);
                this.InitValue();
                this.Init();
                this.RefreshInputs();
            }
            c.Get = function(a) {
                if ("undefined" === typeof a.parentElement.dataset.mp)
                    throw "Unable to retrieve the Monthpicker of element " + a;
                return c.instances[a.parentElement.dataset.mp];
            };
            c.prototype.InitValue = function() {
                var a = new Date();
                this.currentYear = a.getFullYear();
                var b = !1;
                this.original_input.value.match("[0-9]{1,2}/[0-9]{4}") &&
                    ((b = this.original_input.value.split("/")),
                        (this.selectedMonth = parseInt(b[0])),
                        (this.currentYear = this.selectedYear = parseInt(b[1])),
                        (b = !0));
                this.opts.allowNull ||
                    b ||
                    ((this.selectedMonth = a.getMonth()),
                        (this.selectedYear = a.getFullYear()),
                        null !== this.bounds.min.year &&
                        (this.selectedYear < this.bounds.min.year ?
                            ((this.selectedYear = this.bounds.min.year),
                                (this.selectedMonth = this.bounds.min.month ?
                                    this.bounds.min.month :
                                    1)) :
                            this.selectedYear == this.bounds.min.year &&
                            this.selectedMonth < this.bounds.min.month &&
                            (this.selectedMonth = this.bounds.min.month)),
                        null !== this.bounds.max.year &&
                        (this.selectedYear > this.bounds.max.year ?
                            ((this.selectedYear = this.bounds.max.year),
                                (this.selectedMonth = this.bounds.max.month ?
                                    this.bounds.max.month :
                                    12)) :
                            this.selectedYear == this.bounds.max.year &&
                            this.selectedMonth > this.bounds.max.month &&
                            (this.selectedMonth = this.bounds.max.month)),
                        (this.currentYear = this.selectedYear));
            };
            c.prototype.InitOptions = function(a) {
                this.opts = c._clone(c.defaultOpts);
                this.MergeOptions(a);
                this.EvaluateOptions();
            };
            c.prototype.UpdateOptions = function(a) {
                this.MergeOptions(a);
                this.EvaluateOptions();
                this.RefreshUI();
            };
            c.prototype.MergeOptions = function(a) {
                if (a)
                    for (var b in a) this.opts[b] = a[b];
            };
            c.prototype.EvaluateOptions = function() {
                var a = {
                    min: {
                        year: null,
                        month: null
                    },
                    max: {
                        year: null,
                        month: null
                    }
                };
                if (null !== this.opts.minValue || null !== this.opts.minYear)
                    if (null !== this.opts.minValue && null !== this.opts.minYear) {
                        var b = this.opts.minValue.split("/"),
                            c = parseInt(this.opts.minYear),
                            d = parseInt(b[1]);
                        c > d ?
                            ((a.min.year = c), (a.min.month = 1)) :
                            ((a.min.year = d), (a.min.month = parseInt(b[0])));
                    } else
                        null !== this.opts.minValue ?
                        ((b = this.opts.minValue.split("/")),
                            (a.min.year = parseInt(b[1])),
                            (a.min.month = parseInt(b[0]))) :
                        ((a.min.year = parseInt(this.opts.minYear)), (a.min.month = 1));
                if (null !== this.opts.maxValue || null !== this.opts.maxYear)
                    null !== this.opts.maxValue && null !== this.opts.maxYear ?
                    ((b = this.opts.maxValue.split("/")),
                        (c = parseInt(this.opts.maxYear)),
                        (d = parseInt(b[1])),
                        c < d ?
                        ((a.max.year = c), (a.max.month = 12)) :
                        ((a.max.year = d), (a.max.month = parseInt(b[0])))) :
                    null !== this.opts.maxValue ?
                    ((b = this.opts.maxValue.split("/")),
                        (a.max.year = parseInt(b[1])),
                        (a.max.month = parseInt(b[0]))) :
                    ((a.max.year = parseInt(this.opts.maxYear)), (a.max.month = 12));
                this.bounds = a;
            };
            c.prototype.RefreshInputs = function() {
                this.selectedYear && this.selectedMonth ?
                    ((this.original_input.value =
                            (10 > this.selectedMonth ?
                                "0" + this.selectedMonth :
                                this.selectedMonth.toString()) +
                            "/" +
                            this.selectedYear),
                        (this.input.innerHTML =
                            this.opts.monthLabels[this.selectedMonth - 1] +
                            " " +
                            this.selectedYear)) :
                    (this.input.innerHTML =
                        '<span class="placeholder">' +
                        this.original_input.placeholder +
                        "</span>");
            };
            c.prototype.RefreshUI = function() {
                this.UpdateCalendarView();
                null !== this.currentYear &&
                    (this.year_input.innerHTML = this.currentYear.toString());
                this.UpdateYearSwitches();
            };
            c.prototype.InitIU = function() {
                this.parent = document.createElement("div");
                this.parent.classList.add("monthpicker");
                this.parent.tabIndex = -1;
                var a = getComputedStyle(this.original_input, null);
                /*this.parent.style.width=a.getPropertyValue("width");*/
                "auto" ===
                this.parent.style.width &&
                    (this.parent.style.width =
                        0 === this.original_input.offsetWidth ?
                        "100px" :
                        this.original_input.offsetWidth + "px");
                this.original_input.parentElement.insertBefore(
                    this.parent,
                    this.original_input
                );
                this.parent.appendChild(this.original_input);
                this.original_input.style.display = "none";
                this.input = document.createElement("div");
                this.input.classList.add("monthpicker_input");
                this.input.style.height = a.getPropertyValue("height");
                "auto" === this.input.style.height &&
                    (this.input.style.height =
                        0 === this.original_input.offsetHeight ?
                        "" :
                        this.original_input.offsetHeight + "px");
                /*this.input.style.padding=a.getPropertyValue("padding");this.input.style.border=a.getPropertyValue("border");*/
                this.parent.appendChild(
                    this.input
                );
                this.selector = document.createElement("div");
                this.selector.classList.add("monthpicker_selector");
                this.selector.style.display = "none";
                for (
                    var a =
                        "<table><tr><td><span class='yearSwitch down'><i class='fas fa-chevron-left'></i></span></td><td><div class='yearValue'>" +
                        this.currentYear +
                        "</div> </td><td><span class='yearSwitch up'><i class='fas fa-chevron-right'></i></span> </td></tr> ",
                        b = 0; 4 > b; b++
                )
                    var c = 3 * b,
                        d = this.opts.monthLabels.slice(c, c + 3),
                        a =
                        a +
                        ("<tr><td class='month month" +
                            (c + 1) +
                            "' data-m='" +
                            (c + 1) +
                            "'>" +
                            d[0] +
                            "</td><td class='month month" +
                            (c + 2) +
                            "' data-m='" +
                            (c + 2) +
                            "'>" +
                            d[1] +
                            "</td><td class='month month" +
                            (c + 3) +
                            "' data-m='" +
                            (c + 3) +
                            "'>" +
                            d[2] +
                            "</td></tr>");
                this.selector.innerHTML = a + "</table>";
                this.parent.appendChild(this.selector);
            };
            c.prototype.Init = function() {
                this.InitIU();
                this.year_input = this.selector.querySelector(".yearValue");
                this.parent.dataset.mp = this.id.toString();
                this.parent.addEventListener(
                    "focusin",
                    function() {
                        c.instances[this.dataset.mp].Show();
                    },
                    !0
                );
                this.parent.addEventListener(
                    "focusout",
                    function() {
                        c.instances[this.dataset.mp].Hide();
                    },
                    !0
                );
                this.parent
                    .querySelector(".yearSwitch.down")
                    .addEventListener("click", function() {
                        c.instances[this.closest(".monthpicker").dataset.mp].PrevYear();
                    });
                this.parent
                    .querySelector(".yearSwitch.up")
                    .addEventListener("click", function() {
                        c.instances[this.closest(".monthpicker").dataset.mp].NextYear();
                    });
                for (
                    var a = this.parent.querySelectorAll(
                            ".monthpicker_selector>table tr:not(:first-child) td.month"
                        ),
                        b = 0; b < a.length; b++
                )
                    a[b].addEventListener("click", function() {
                        this.classList.contains("off") ||
                            c.instances[this.closest(".monthpicker").dataset.mp].SelectMonth(
                                this.dataset.m
                            );
                    });
            };
            c.prototype.SelectMonth = function(a) {
                var b = parseInt(a);
                if (isNaN(b)) throw "Selected month is not a number : " + a;
                if (1 > b || 12 < b)
                    throw "Month is out of range (should be in [1:12], was " + a + ")";
                this.selectedMonth = b;
                this.selectedYear = this.currentYear;
                this.RefreshUI();
                this.RefreshInputs();
                this.ReleaseFocus();
                if (null !== this.opts.onSelect) this.opts.onSelect();
            };
            c.prototype.UpdateCalendarView = function() {
                for (
                    var a = this.selector.querySelectorAll(".month"), b = 0; b < a.length; b++
                )
                    a[b].classList.remove("selected");
                null !== this.selectedYear &&
                    this.currentYear === this.selectedYear &&
                    a[this.selectedMonth - 1].classList.add("selected");
                for (b = 0; b < a.length; b++) a[b].classList.remove("off");
                if (
                    null !== this.bounds.min.year &&
                    this.currentYear <= this.bounds.min.year
                )
                    for (b = 1; b < this.bounds.min.month; b++) a[b - 1].classList.add("off");
                if (
                    null !== this.bounds.max.year &&
                    this.currentYear >= this.bounds.max.year
                )
                    for (b = 12; b > this.bounds.max.month; b--)
                        a[b - 1].classList.add("off");
            };
            c.prototype.ReleaseFocus = function() {
                this.parent.blur();
            };
            c.prototype.Show = function() {
                this.RefreshUI();
                this.selector.style.display = "block";
            };
            c.prototype.Hide = function() {
                null !== this.selectedYear && (this.currentYear = this.selectedYear);
                this.selector.style.display = "none";
            };
            c.prototype.ShowYear = function(a) {
                this.currentYear = a;
                this.RefreshUI();
            };
            c.prototype.UpdateYearSwitches = function() {
                var a = this.selector.querySelector(".yearSwitch.down"),
                    b = this.selector.querySelector(".yearSwitch.up");
                null !== this.bounds.min.year && this.currentYear <= this.bounds.min.year ?
                    a.classList.add("off") :
                    a.classList.remove("off");
                null !== this.bounds.max.year && this.currentYear >= this.bounds.max.year ?
                    b.classList.add("off") :
                    b.classList.remove("off");
            };
            c.prototype.PrevYear = function() {
                this.ShowYear(this.currentYear - 1);
            };
            c.prototype.NextYear = function() {
                this.ShowYear(this.currentYear + 1);
            };
            c._clone = function(a) {
                var b;
                if (null == a || "object" != typeof a) return a;
                if (a instanceof Date) return (b = new Date()), b.setTime(a.getTime()), b;
                if (a instanceof Array) {
                    b = [];
                    for (var e = 0, d = a.length; e < d; e++) b[e] = c._clone(a[e]);
                    return b;
                }
                if (a instanceof Object) {
                    b = {};
                    for (e in a) a.hasOwnProperty(e) && (b[e] = c._clone(a[e]));
                    return b;
                }
                throw Error("Unable to copy obj! Its type isn't supported.");
            };
            c.next_id = 1;
            c.instances = [];
            c.defaultOpts = {
                minValue: null,
                minYear: null,
                maxValue: null,
                maxYear: null,
                monthLabels: "Jan Feb Mar Apr May Jun Jui Aug Sep Oct Nov Dec".split(" "),
                onSelect: null,
                onClose: null,
                allowNull: !0
            };
            return c;
        })();
        window.jQuery &&
            (window.jQuery.fn.Monthpicker = function(c, a) {
                var b;
                if ("undefined" === typeof c || "object" === typeof c) b = "ctor";
                else if ("string" === typeof c && "option" === c) b = "option";
                else {
                    console.error("Error : Monthpicker - bad argument (1)");
                    return;
                }
                window.jQuery(this).each(function(e, d) {
                    switch (b) {
                        case "ctor":
                            "INPUT" != d.tagName ||
                                ("text" != d.getAttribute("type") && null !== d.getAttribute("type")) ?
                                console.error("Monthpicker must be called on a text input") :
                                new Monthpicker(d, c);
                            break;
                        case "option":
                            "INPUT" != d.tagName ||
                                ("text" != d.getAttribute("type") && null !== d.getAttribute("type")) ?
                                console.error("Monthpicker must be called on a text input") :
                                Monthpicker.Get(d).UpdateOptions(a);
                    }
                });
            });

        $("#month").Monthpicker({
            onSelect: function() {
                $("#endDate").Monthpicker("option", {
                    minValue: $("#startDate").val()
                });
            }
        });
    </script>
    <script>
        $(document).ready(function(e) {

            monthlyTargetListing();

            //Add target script
            $('#addTargetForm').submit(function(e) {
                e.preventDefault();
                let formData = $(this).serialize();
                $.ajax({
                    method: "POST",
                    url: "{{ route(getRouteAlias() . '.add.target') }}",
                    data: formData,
                    headers: {
                        'X-CSRF-TOKEN': "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        monthlyTargetListing();
                        $('#addTargetModal .alertDiv .alert, .error-text').remove();
                        $('#addTargetForm')[0].reset();
                        $('#addTargetModal').modal('hide');
                        $('.monthpicker_input').text('');
                        toastr.success("Target added Successfully");

                    },
                    error: function(request, error) {
                        $('#addTargetModal .alertDiv .alert, .error-text').remove();

                        if (request.status == 419) {
                            $('#addTargetModal .alertDiv').append(
                                '<div class="alert alert-danger" role="alert">Target for the requested month already exists.</div>'
                            );

                        };
                        let errorResponse = JSON.parse(request.responseText);
                        let id = "addTargetForm";
                        $.each(errorResponse.errors, function(field_name, error) {
                            $(document)
                                .find("#" + id + " [name=" + field_name + "]")
                                .after(
                                    '<span class="position-absolute top-100 text-strong text-danger error-text span2-3">' +
                                    error +
                                    "</span>"
                                );
                        });

                    }
                })
            })

            $('#monthlyTargetFilter').on('change', function(e) {
                monthlyTargetListing();
            })

            //Target listing script
            function monthlyTargetListing() {
                let year = $('#monthlyTargetFilter').val();
                $.ajax({
                    method: "GET",
                    url: "{{ route(getRouteAlias() . '.set.target') }}",
                    data: {
                        year: year
                    },
                    success: function(response) {
                        $('#monthlyTargets').html(response.html);

                    },
                    error: function(request, error) {
                        console.log(error);
                    }
                })
            }


            //Get Edit Target details script
            $(".editMonthlyTarget").on('click', function(e) {
                let year = $('#monthlyTargetFilter').val();
                $('.targetYear').val(year);
                e.preventDefault();
                let formData = $('#editTargetForm').serialize();
                console.log(formData);
                $.ajax({
                    method: "POST",
                    url: "{{ route(getRouteAlias() . '.edit.target') }}",
                    data: formData,
                    headers: {
                        'X-CSRF-TOKEN': "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        monthlyTargetListing();
                        toastr.success("Targets updated Successfully");
                    },
                    error: function(request, error) {
                        console.log(error);
                    }
                })


            })
            // $('#editTargetForm').submit(function(e) {
            //     e.preventDefault();

            //     alert('yes');
            //     // let formData = $('#editTargetForm').serialize();
            //     // console.log(formData);
            //     // $.ajax({
            //     //     method: "POST",
            //     //     url: "{{ route(getRouteAlias() . '.edit.target') }}",
            //     //     data: formData,
            //     //     headers: {
            //     //         'X-CSRF-TOKEN': "{{ csrf_token() }}"
            //     //     },
            //     //     success: function(response) {
            //     //         // monthlyTargetListing();
            //     //         // $('#editTargetModal').modal('hide');
            //     //         // toastr.success("Target updated Successfully");
            //     //     },
            //     //     error: function(request, error) {
            //     //         console.log(error);
            //     //     }
            //     // })
            // })


            //update Target script


            //delete Target script
            $(document).on('click', '.deleteMonthlyTargetBtn', function(e) {
                $('.TargetDeleteId').val($(this).data('id'));
                $('#deleteTargetModal').modal('show');

            })

            $('#confirmDeleteTarget').on('click', function(e) {
                let id = $('.TargetDeleteId').val();
                $.ajax({
                    method: "GET",
                    url: "{{ route(getRouteAlias() . '.delete.target') }}",
                    data: {
                        id: id
                    },
                    success: function(response) {
                        monthlyTargetListing();
                        $('#deleteTargetModal').modal('hide');
                        toastr.success("Target deleted Successfully");
                    },
                    error: function(request, error) {
                        console.log(error);
                    }
                })

            })

        })
    </script>
@endpush
