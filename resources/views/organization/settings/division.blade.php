@extends('layouts.admin.master')
@section('title', 'Division Settings')
@section('section')
    <style>
        .checkbox-group.single-select {
            display: block;
            /* Changed from flex to block for vertical layout */
        }

        .checkbox-option {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            /* Added space between options */
        }

        .checkbox-option input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .checkbox-option label {
            cursor: pointer;
            user-select: none;
        }

        .buttons-wraper {
            margin-top: 20px;
        }

        .buttons-wraper .btn {
            min-width: 120px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
        }

        .buttons-wraper .cancel-btn {
            background-color: #fff;
            color: #004080;
            border: 2px solid #000000;
            height: 38px;
        }

        .buttons-wraper .cancel-btn:hover {
            background-color: #004080;
            color: #fff;
        }

        .buttons-wraper .conform-btn {
            background-color: #0074d9;
            border: none;
        }

        .conform-btn:hover {
            background-color: #012951;
        }

        .delete-modal {
            max-width: 400px;
            margin-top: 40px;
        }

        .modal-content {
            border-radius: 12px;
            border: none;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .delete-icon {
            display: block;
            margin: 0 auto 20px;
            width: 60px;
            height: 60px;
        }

        .delete-request {
            font-size: 20px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 10px;
            color: #333;
        }

        .are-sure {
            text-align: center;
            color: #666;
            margin-bottom: 25px;
        }

        .buttons-wraper {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .cancel-btn,
        .conform-btn {
            padding: 8px 25px;
            border-radius: 6px;
            font-weight: 500;
            border: none;
            cursor: pointer;
        }

        .conform-btn {
            background: #dc3545;
            color: white;
        }

        .conform-btn:hover {
            background: #c82333;
        }

        /* put this after Bootstrap or in your own stylesheet */
        /* Modal at top with smooth fade-in */
        #deleteModal .modal-dialog {
            position: absolute;
            top: 40px;
            /* spacing from top */
            left: 50%;
            transform: translateX(-50%);
            margin: 0;
            width: 100%;
            max-width: 500px;
        }

        /* Smooth fade-in + slight slide from top */
        .modal.fade .modal-dialog {
            transition: transform 0.3s ease-out, opacity 0.3s ease-out;
            transform: translate(-50%, -20px);
            opacity: 0;
        }

        .modal.fade.show .modal-dialog {
            transform: translate(-50%, 0);
            opacity: 1;
        }

        /* Allow scroll if modal is taller than screen */
        .modal {
            overflow-y: auto;
        }

        .add-inline {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            margin-bottom: 15px;
            margin-left: 84px;
        }

        .input-container {
            flex: 1;
            min-width: 200px;
            position: relative;
            /* Added for error positioning */
        }

        .action-buttons {
            display: flex;
            align-items: flex-start;
            /* Changed to flex-start */
            gap: 10px;
            /* Match input height */
        }

        .error-message {
            color: red;
            font-size: 13px;
            position: absolute;
            /* Changed to absolute */
            bottom: -20px;
            /* Position below input */
            left: 0;
            width: 100%;
        }

        .form-control {
            width: 100%;
        }

        .add-inline {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 10px;
        }

        .input-container {
            flex: 1;
            min-width: 200px;
        }


        .error-message {
            color: red;
            font-size: 13px;
            margin-top: 5px;
            width: 100%;
        }

        .is-invalid {
            border-color: #dc3545 !important;
        }

        .error-message {
            display: none;
            /* Hidden by default */
            font-size: 13px;
            color: red;
            margin-top: 5px;
            width: 100%;
        }

        .add-inline {
            position: relative;
        }

        .btn-primary:disabled {
            background-color: #6c757d;
            border-color: #6c757d;
            cursor: not-allowed;
        }

        .settings-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 20px;
        }

        .settings-tab {
            padding: 10px 20px;
            cursor: pointer;
            color: #6b7280;
            font-size: 16px;
            border-bottom: 2px solid transparent;
        }

        .settings-tab.active {
            color: #2563eb;
            border-color: #3fd78a;
            font-size: 16px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .section-title {
            font-weight: 600;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .low-title {
            font-weight: 400;
            margin-bottom: 10px;
            font-size: 13px;
        }

        .item-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 15px;
        }

        .item-row i {
            color: #dc2626;
            cursor: pointer;
        }

        .add-link {
            color: #2563eb;
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            display: inline-block;
            margin-top: 8px;
        }

        .add-inline {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 16px;
            max-width: 600px;
        }

        .add-inline input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .add-inline .remove-input {
            color: #dc2626;
            cursor: pointer;
            font-size: 18px;
            margin-top: 9px;
        }

        .add-inline button {
            padding: 8px 16px;
            border-radius: 6px;
            background-color: #2563eb;
            border: none;
            color: white;
            font-weight: 500;
            font-size: 14px;
            white-space: nowrap;
        }

        .divider {
            height: 1px;
            background-color: #e5e7eb;
            margin: 45px 0;
        }

        .container-landscape {
            padding: 0 190px;
        }

        .form-check-custom {
            display: flex;
            align-items: center;
            /* gap: 10px; */
            font-size: 14px;
            color: #374151;
            font-weight: 400;
        }

        /* Hide default calendar icon */
        input[type="month"]::-webkit-calendar-picker-indicator {
            display: none;
        }

        input[type="date"]::-webkit-calendar-picker-indicator {
            display: none;
        }

        input[type="time"]::-webkit-calendar-picker-indicator {
            display: none;
        }
    </style>


    <section class="dashboard_main">
        <div class="settings_tab_grid">
            <x-settings_component.settings_tab />
            <div class="settings_content h-fit">
                <div class="panel">
                    <div class="panel_header">
                        <h2 class="panel_title">Division Settings</h2>
                    </div>

                    <div class="settings-tabs">
                        @foreach ($divisions as $index => $division)
                            <div class="settings-tab {{ $loop->first ? 'active' : '' }}"
                                data-tab="division-{{ $division->id }}">
                                {{ $division->name }}
                            </div>
                        @endforeach
                    </div>

                    @foreach ($divisions as $division)
                        @php
                            $tabId = 'division-' . $division->id;
                            $currentRenewalSettings = $renewalSettings[$division->id] ?? null;
                        @endphp
                        <div id="{{ $tabId }}-tab"
                            class="tab-content col-12 {{ $loop->first ? 'active' : '' }}">
                            <!-- Service Lines Section -->
                            <div class="section-title">Service Line</div>
                            <div class="container-landscape">
                                <div class="service-line-list" style="margin-left: 84px;"
                                    id="service-line-list-{{ $division->id }}">
                                    @foreach ($serviceLines[$division->id] ?? [] as $line)
                                        <div class="item-row" id="service-line-{{ $line->id }}">
                                            <span>{{ $line->name }}</span>
                                            <i class="delete-service-line" data-id="{{ $line->id }}"
                                                style="background-image: url('{{ asset('trash.png') }}'); 
                                                width: 20px; height: 20px; display: inline-block; 
                                                background-size: contain; background-repeat: no-repeat; 
                                                cursor: pointer;">
                                            </i>

                                        </div>
                                    @endforeach
                                </div>
                                <div class="add-link add-service-line-link float-right"
                                    data-division-id="{{ $division->id }}">
                                    + Add Service Line
                                </div>

                                <div class="add-inline serviceline-add-block d-none">
                                    <div class="input-container" style="width: 100%;">
                                        <input type="text" class="form-control service-line-input"
                                            placeholder="Add service line">
                                    </div>
                                    <div class="action-buttons">
                                        <button type="button"
                                            class="btn btn-primary btn-add-service-line"
                                            data-division-id="{{ $division->id }}">
                                            Add
                                        </button>
                                        <i class="remove-input"
                                            style="background-image: url('{{ asset('trash.png') }}'); 
                                            width: 20px; height: 20px; display: inline-block; 
                                            background-size: contain; background-repeat: no-repeat; 
                                            cursor: pointer;">
                                        </i>
                                    </div>
                                </div>
                            </div>

                            <!-- Work Types Section -->
                            <div class="divider"></div>
                            <div class="section-title">Work Type</div>
                            <div class="container-landscape">
                                <div class="work-type-list" id="work-type-list-{{ $division->id }}"
                                    style="margin-left: 84px;">
                                    @foreach ($workTypes[$division->id] ?? [] as $type)
                                        <div class="item-row" id="work-type-{{ $type->id }}">
                                            <span>{{ $type->name }}</span>
                                            <!-- Replace the Font Awesome trash icon with an image -->
                                            <i class="delete-work-type" data-id="{{ $type->id }}"
                                                style="background-image: url('{{ asset('trash.png') }}'); 
                                                width: 20px; height: 20px; display: inline-block; background-size: contain; background-repeat: no-repeat; cursor: pointer;">
                                            </i>

                                        </div>
                                    @endforeach
                                </div>
                                <div class="add-link add-work-type-link float-right"
                                    data-division-id="{{ $division->id }}">
                                    + Add Work Type
                                </div>

                                <div class="add-inline worktype-add-block d-none">
                                    <div class="input-container" style="width: 100%;">
                                        <input type="text" class="form-control work-type-input"
                                            placeholder="Add work type">
                                    </div>
                                    <div class="action-buttons">
                                        <button type="button" class="btn btn-primary btn-add-work-type"
                                            data-division-id="{{ $division->id }}">
                                            Add
                                        </button>
                                        <i class="remove-input"
                                            style="background-image: url('{{ asset('trash.png') }}'); 
                                            width: 20px; height: 20px; display: inline-block; 
                                            background-size: contain; background-repeat: no-repeat; 
                                            cursor: pointer;">
                                        </i>

                                    </div>
                                </div>
                            </div>
                            <br>

                            <div class="divider"></div>
                            <div class="section-title">Renewal Preferences</div>
                            <form id="renewal-settings-form-{{ $division->id }}" method="POST">
                                @csrf
                                <input type="hidden" name="division_id" value="{{ $division->id }}">

                                <!-- Maintenance Contract Section -->
                                <div class="low-title">Snow Contract</div>
                                <div class="container-landscape" style="margin-top: -50px;">
                                    <div class="add-inline">
                                        <div class="month-input-container"
                                            style="position: relative; width: 100%;">
                                            <input type="month" required
                                                class="form-control renewal-month" name="renewal_month"
                                                placeholder="Select renewal month"
                                                id="renewal-month-{{ $division->id }}"
                                                value="{{ optional($currentRenewalSettings)->renewal_month ?? '' }}"
                                                style="appearance: none; -webkit-appearance: none; -moz-appearance: none; padding-right: 30px; width: 100%;">

                                            <!-- Custom calendar icon -->
                                            <div class="calendar-icon"
                                                style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); pointer-events: all; width: 20px; height: 20px; cursor: pointer;">
                                                <img src="{{ asset('vector.png') }}" alt="">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Multi-Year Contract Section -->
                                <div class="divider"></div>
                                <div class="section-title">Multi-Year Contract Setting</div>
                                <div class="low-title">Start Date</div>
                                <div class="low-title">Start Time</div>
                                <div class="container-landscape" style="margin-top: -90px;">
                                    <div class="add-inline">
                                        <!-- Start Date Input -->
                                        <div class="month-input-container"
                                            style="position: relative; width: 100%;">
                                            <input type="date" required
                                                class="form-control start-date" name="start_date"
                                                id="start-date-{{ $division->id }}"
                                                value="{{ optional($currentRenewalSettings)->start_date ? \Carbon\Carbon::parse($currentRenewalSettings->start_date)->format('Y-m-d') : '' }}"
                                                style="appearance: none; -webkit-appearance: none; -moz-appearance: none; padding-right: 30px; width: 100%;">

                                            <!-- Custom Calendar Icon -->
                                            <div class="calendar-icon"
                                                style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); pointer-events: all; width: 20px; height: 20px; cursor: pointer;">
                                                <img src="{{ asset('vector.png') }}" alt="">
                                            </div>
                                        </div>

                                        <div class="month-input-container"
                                            style="position: relative; width: 100%;">
                                            <input type="time" required
                                                class="form-control renewal-time" name="renewal_time"
                                                id="renewal-time-{{ $division->id }}"
                                                value="{{ optional($currentRenewalSettings)->renewal_time ?? '' }}"
                                                style="appearance: none; -webkit-appearance: none; -moz-appearance: none; padding-right: 30px; width: 100%;">

                                            <!-- Custom calendar icon -->
                                            <div class="calendar-icon"
                                                style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); pointer-events: all; width: 20px; height: 20px; cursor: pointer;">
                                                <img src="{{ asset('vector.png') }}" alt="">
                                            </div>
                                        </div>

                                    </div>

                                    <br>

                                    <!-- Auto-Activate Checkbox Section -->
                                    <div class="form-check-custom" style="margin-left: 77px;">
                                        <input type="checkbox" class="form-control auto-activate"
                                            id="auto-activate" name="auto_activate" value="1"
                                            {{ optional($currentRenewalSettings)->auto_activate ? 'checked' : '' }}>
                                        <label for="auto-activate">
                                            Automatically activate next year based on selected date
                                        </label>
                                    </div>
                                </div>




                                <div class="divider"></div>
                                <div class="section-title">Expired contract</div>
                                <div class="low-title">Show contract that Have already expired <br> and
                                    are still awaiting renewal</div>
                                <div class="container-landscape"
                                    style="margin-top: -80px; margin-left: 82px;">
                                    <div class="form-check-custom">
                                        <div class="checkbox-group single-select">
                                            <div class="checkbox-option">
                                                <input type="checkbox"
                                                    id="show-expired-yes-{{ $division->id }}"
                                                    name="expiry_contract_yes" value="1"
                                                    class="exclusive-checkbox"
                                                    {{ optional($currentRenewalSettings)->expiry_contract ? 'checked' : '' }}>
                                                <label for="show-expired-yes-{{ $division->id }}">
                                                    Yes
                                                </label>
                                            </div>

                                            <br> <!-- Line break between options -->

                                            <div class="checkbox-option" style="margin-top: -32px;">
                                                <input type="checkbox"
                                                    id="show-expired-no-{{ $division->id }}"
                                                    name="expiry_contract_no" value="0"
                                                    class="exclusive-checkbox"
                                                    {{ !optional($currentRenewalSettings)->expiry_contract ? 'checked' : '' }}>
                                                <label for="show-expired-no-{{ $division->id }}">
                                                    No
                                                </label>
                                            </div>

                                            <!-- Hidden input for form submission -->
                                            <input type="hidden" name="expiry_contract"
                                                id="expiry_contract_value"
                                                value="{{ optional($currentRenewalSettings)->expiry_contract ? '1' : '0' }}">
                                        </div>
                                    </div>
                                </div>
                                <!-- Save Button -->
                                <div class="text-right mt-3">
                                    <button type="submit"
                                        class="btn primaryblue btn-save-preferences"
                                        style="float: right">
                                        Save Preferences
                                    </button>
                                </div>
                                <br><br>
                            </form>
                        </div>
                    @endforeach


                </div>
            </div>
        </div>
    </section>




    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmationModal" tabindex="-1" role="dialog"
        aria-labelledby="deleteConfirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteConfirmationModalLabel">Delete Contact</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>This contact is permanently deleted from the system. Are you sure you want to
                        delete this contact?</p>
                    <div class="contact-info mt-3 p-2 bg-light rounded">
                        <span id="contactDetails"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"
                        data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Yes</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel"
        aria-hidden="true">
        <div class="modal-dialog delete-modal">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <img src="{{ asset('admin_assets/images/icons/delete-icon.svg') }}"
                        alt="delete icon" class="delete-icon mb-3">
                    <h2 class="delete-request">Want to remove</h2>
                    <p class="are-sure">Are you sure you want to remove this request?</p>

                    <div class="buttons-wraper">
                        <button type="button" class="cancel-btn" data-dismiss="modal"
                            style="height: 41px; color: #0074d9; border: 2px solid black;">Cancel</button>
                        <button type="button" class="conform-btn"
                            style="background-color: #0074d9;height: 41px; ">Yes</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('extra-scripts')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css"
        rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // For month input fields
            const monthInputs = document.querySelectorAll('.renewal-month');
            monthInputs.forEach(input => {
                // Remove default styling
                input.style.backgroundImage = 'none';

                // Get the container and custom calendar icon
                const container = input.closest('.month-input-container');
                const icon = container.querySelector('.calendar-icon');

                // Make the calendar icon clickable to open the month picker
                icon.addEventListener('click', function() {
                    input.showPicker(); // This will open the month picker
                });

                // Make the entire container clickable
                container.addEventListener('click', function() {
                    input
                        .showPicker(); // Open the month picker when clicking anywhere inside the container
                });
            });

            // For date input fields
            const dateInputs = document.querySelectorAll('.start-date');
            dateInputs.forEach(input => {
                // Remove default styling
                input.style.backgroundImage = 'none';

                // Get the container and custom calendar icon
                const container = input.closest('.month-input-container');
                const icon = container.querySelector('.calendar-icon');

                // Make the calendar icon clickable to open the date picker
                icon.addEventListener('click', function() {
                    input.showPicker(); // This will open the date picker
                });

                // Make the entire container clickable
                container.addEventListener('click', function() {
                    input
                        .showPicker(); // Open the date picker when clicking anywhere inside the container
                });
            });

            // For time input fields
            const timeInputs = document.querySelectorAll('.renewal-time');
            timeInputs.forEach(input => {
                // Remove default styling
                input.style.backgroundImage = 'none';

                // Get the container and custom calendar icon
                const container = input.closest('.month-input-container');
                const icon = container.querySelector('.calendar-icon');

                // Make the calendar icon clickable to open the time picker
                icon.addEventListener('click', function() {
                    input.showPicker(); // This will open the time picker
                });

                // Make the entire container clickable
                container.addEventListener('click', function() {
                    input
                        .showPicker(); // Open the time picker when clicking anywhere inside the container
                });
            });
        });



        $(document).ready(function() {
            // Initialize Toastr
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "timeOut": "3000"
            };

            // Tab switching functionality
            $('.settings-tab').click(function() {
                $('.settings-tab').removeClass('active');
                $(this).addClass('active');
                $('.tab-content').removeClass('active');
                const tabId = $(this).data('tab');
                $(`#${tabId}-tab`).addClass('active');
            });

            // Show/hide Service Line input block
            $('.add-service-line-link').click(function(e) {
                e.preventDefault();
                const workTypeInputBlock = $(this).closest('.tab-content.active').find(
                    '.worktype-add-block');

                if (workTypeInputBlock.length && !workTypeInputBlock.hasClass(
                        'd-none')) {
                    workTypeInputBlock.find('.error-message').remove();
                    workTypeInputBlock.find('.input-container').append(`
                <div class="error-message" style="color: red; font-size: 13px; margin-top: 5px; display: block;">
                    Please complete or Remove the Work Type input first
                </div>
            `);
                    return;
                }

                $(this).addClass('d-none');
                const block = $(this).siblings('.serviceline-add-block');
                block.removeClass('d-none').find('.service-line-input').focus();
                block.find('.error-message').remove();
                block.find('.btn-add-service-line').prop('disabled', true);
            });

            // Show/hide Work Type input block
            $('.add-work-type-link').click(function(e) {
                e.preventDefault();
                const serviceLineInputBlock = $(this).closest('.tab-content.active')
                    .find('.serviceline-add-block');

                if (serviceLineInputBlock.length && !serviceLineInputBlock.hasClass(
                        'd-none')) {
                    serviceLineInputBlock.find('.error-message').remove();
                    serviceLineInputBlock.find('.input-container').append(`
                <div class="error-message" style="color: red; font-size: 13px; margin-top: 5px; display: block;">
                    Please complete or Remove the Service Line input first
                </div>
            `);
                    return;
                }

                $(this).addClass('d-none');
                const block = $(this).siblings('.worktype-add-block');
                block.removeClass('d-none').find('.work-type-input').focus();
                block.find('.error-message').remove();
                block.find('.btn-add-work-type').prop('disabled', true);
            });

            // Remove input block
            $('.remove-input').click(function(e) {
                e.preventDefault();
                $(this).closest('.add-inline').addClass('d-none');
                $(this).closest('.add-inline').siblings('.add-link').removeClass(
                    'd-none');
                $(this).closest('.add-inline').find('.error-message').remove();
            });

            // Enable/disable add buttons based on input
            $(document).on('input', '.service-line-input, .work-type-input', function() {
                const value = $(this).val().trim();
                const addButton = $(this).closest('.input-container').siblings(
                    '.action-buttons').find('button');
                addButton.prop('disabled', value === '');
            });

            // Add Service Line item (AJAX)
            $(document).on('click', '.btn-add-service-line', function(e) {
                e.preventDefault();
                const divisionId = $(this).data('division-id');
                const input = $(this).closest('.action-buttons').siblings(
                    '.input-container').find('.service-line-input');
                const value = input.val().trim();
                const block = $(this).closest('.serviceline-add-block');

                // Clear previous errors
                block.find('.error-message').remove();
                input.removeClass('is-invalid');

                if (!value) {
                    toastr.error('Please enter a Service Line value');
                    input.addClass('is-invalid');
                    block.find('.input-container').append(
                        '<div class="error-message">Please enter a service line</div>'
                    );
                    return;
                }

                $.ajax({
                    url: '/organization/add-service-line',
                    type: 'POST',
                    data: {
                        division_id: divisionId,
                        name: value,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            const newItem = `
                <div class="item-row" id="service-line-${response.data.id}">
                    <span>${response.data.name}</span>
                    <!-- Change icon to the custom image -->
                    <i class="delete-service-line" data-id="${response.data.id}" 
                        style="background-image: url('{{ asset('trash.png') }}'); 
                               width: 20px; height: 20px; display: inline-block; 
                               background-size: contain; background-repeat: no-repeat; 
                               cursor: pointer;">
                    </i>
                </div>`;
                            $(`#service-line-list-${divisionId}`).append(
                                newItem);
                            input.val('');
                            block.addClass('d-none');
                            block.siblings('.add-service-line-link')
                                .removeClass('d-none');
                            // toastr.success('Service line added successfully');
                        }
                    },
                    error: function(xhr) {
                        const errorMsg = xhr.responseJSON.message ||
                            'Failed to add service line';
                        toastr.error(errorMsg);
                        input.addClass('is-invalid');
                        block.find('.input-container').append(
                            `<div class="error-message">${errorMsg}</div>`
                        );
                    }
                });
            });

            // Add Work Type item (AJAX)
            $(document).on('click', '.btn-add-work-type', function(e) {
                e.preventDefault();
                // The same logic can be applied for adding work type, like the service line
                const divisionId = $(this).data('division-id');
                const input = $(this).closest('.action-buttons').siblings(
                    '.input-container').find('.work-type-input');
                const value = input.val().trim();
                const block = $(this).closest('.worktype-add-block');

                // Clear previous errors
                block.find('.error-message').remove();
                input.removeClass('is-invalid');

                if (!value) {
                    toastr.error('Please enter a Work Type value');
                    input.addClass('is-invalid');
                    block.find('.input-container').append(
                        '<div class="error-message">Please enter a work type</div>'
                    );
                    return;
                }

                $.ajax({
                    url: '/organization/add-work-type',
                    type: 'POST',
                    data: {
                        division_id: divisionId,
                        name: value,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            const newItem = `
                <div class="item-row" id="work-type-${response.data.id}">
                    <span>${response.data.name}</span>
                    <!-- Change icon to the custom image -->
                    <i class="delete-work-type" data-id="${response.data.id}" 
                        style="background-image: url('{{ asset('trash.png') }}'); 
                               width: 20px; height: 20px; display: inline-block; 
                               background-size: contain; background-repeat: no-repeat; 
                               cursor: pointer;">
                    </i>
                </div>`;
                            $(`#work-type-list-${divisionId}`).append(
                                newItem);
                            input.val('');
                            block.addClass('d-none');
                            block.siblings('.add-work-type-link')
                                .removeClass('d-none');
                            // toastr.success('Work type added successfully');
                        }
                    },
                    error: function(xhr) {
                        const errorMsg = xhr.responseJSON.message ||
                            'Failed to add work type';
                        toastr.error(errorMsg);
                        input.addClass('is-invalid');
                        block.find('.input-container').append(
                            `<div class="error-message">${errorMsg}</div>`
                        );
                    }
                });
            });


            // Delete functionality variables
            let currentDeleteId = null;
            let currentDeleteType = null;

            // Show delete confirmation modal
            function showDeleteConfirmation(type, id, name) {
                currentDeleteType = type;
                currentDeleteId = id;

                // Update modal content
                $('#deleteModal .delete-request').text(
                    `Delete`);
                $('#deleteModal .are-sure').text(
                    `Are you sure you want to remove this ${type === 'service' ? 'service line' : 'work type'}?`
                );

                // Show the modal
                $('#deleteModal').modal('show');
            }

            // Handle delete confirmation
            $(document).on('click', '#deleteModal .conform-btn', function() {
                if (currentDeleteType && currentDeleteId) {
                    if (currentDeleteType === 'service') {
                        deleteServiceLine(currentDeleteId);
                    } else {
                        deleteWorkType(currentDeleteId);
                    }
                }
                $('#deleteModal').modal('hide');
            });

            // Delete Service Line function
            function deleteServiceLine(id) {
                $.ajax({
                    url: '/organization/delete-service-line/' + id,
                    type: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            $(`#service-line-${id}`).remove();
                            // toastr.success('Service line deleted successfully');
                        }
                    },
                    error: function(xhr) {
                        toastr.error('Failed to delete service line');
                    }
                });
            }

            // Delete Work Type function
            function deleteWorkType(id) {
                $.ajax({
                    url: '/organization/delete-work-type/' + id,
                    type: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            $(`#work-type-${id}`).remove();
                            // toastr.success('Work type deleted successfully');
                        }
                    },
                    error: function(xhr) {
                        toastr.error('Failed to delete work type');
                    }
                });
            }

            // Delete Service Line handler
            $(document).on('click', '.delete-service-line', function(e) {
                e.preventDefault();
                const id = $(this).data('id');
                const name = $(this).siblings('span').text();
                showDeleteConfirmation('service', id, name);
            });

            // Delete Work Type handler
            $(document).on('click', '.delete-work-type', function(e) {
                e.preventDefault();
                const id = $(this).data('id');
                const name = $(this).siblings('span').text();
                showDeleteConfirmation('work', id, name);
            });

            // Make checkboxes behave like radio buttons (single selection)
            $(document).on('change', '.exclusive-checkbox', function() {
                const $otherCheckbox = $(this).closest('.checkbox-group').find(
                    '.exclusive-checkbox').not(this);

                if ($(this).is(':checked')) {
                    // Uncheck the other checkbox
                    $otherCheckbox.prop('checked', false);

                    // Update the hidden input value
                    $('#expiry_contract_value').val($(this).val());
                } else {
                    // If trying to uncheck, re-check if no other option is selected
                    if ($otherCheckbox.is(':checked') === false) {
                        $(this).prop('checked', true);
                    }
                }
            });

            // Form submission handler
            $('[id^="renewal-settings-form-"]').on('submit', function(e) {
                e.preventDefault();

                // Get the current value from hidden input
                var expiryContractValue = $('#expiry_contract_value').val();

                // Build form data
                var formData = $(this).serializeArray();
                formData = formData.filter(item => !item.name.startsWith(
                    'expiry_contract_'));
                formData.push({
                    name: 'expiry_contract',
                    value: expiryContractValue
                });

                $.ajax({
                    url: '/organization/save-renewal-settings',
                    method: 'POST',
                    data: $.param(formData),
                    success: function(response) {
                        if (response.success) {
                            // Update UI based on response
                            $('#show-expired-yes-' + response.division_id)
                                .prop('checked', response.expiry_contract ==
                                    1);
                            $('#show-expired-no-' + response.division_id)
                                .prop('checked', response.expiry_contract ==
                                    0);
                            $('#expiry_contract_value').val(response
                                .expiry_contract ? '1' : '0');

                            // toastr.success('Settings saved successfully');
                        } else {
                            toastr.error(response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        toastr.error(
                            'There was an error saving the settings!');
                        console.error('Error:', error);
                        console.error('Response:', xhr.responseText);
                    }
                });
            });
        });
    </script>
@endsection
