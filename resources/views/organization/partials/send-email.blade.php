{{-- @section('styles') --}}
<link href="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.css" rel="stylesheet" type="text/css" />
<style>
    .write-email-container {
        display: grid;
        grid-template-columns: 1.2fr 0.8fr;
        gap: 10px;

    }

    .strikethrough {
        text-decoration: line-through;
    }

    .input-main {
        display: flex;
        align-items: center;
    }

    .email-menus {
        border: 1px solid
    }

    .email-menus li {
        cursor: pointer;
    }

    .email-menus li:hover {
        background-color: var(--primaryblue);
        color: white
    }

    .to {
        font-family: "Cabin";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: var(--placeholdertext);
        background: rgb(172, 170, 170);
        height: 100%;
        display: flex;
        align-items: center;
        padding-left: 5px
    }

    .my-input {
        border-radius: 0;
        height: auto;
    }

    .note-editable p,
    .subject {
        font-family: "Cabin";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #192a3e;
    }

    .subject-wraper {
        display: flex;
        flex-direction: column;
        gap: 4px;
        padding: 0px;
        /* border: 1px solid #ced4da;
                                                                                                                                        border-radius: 6px; */
    }

    .subject-wraper input {
        border-color: #DDD;
        border-radius: 3px !important;
    }

    .subject-wraper input:focus {
        border-color: #DDD;
        outline: 0;
    }

    .attachments {
        font-family: "Cabin";
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        color: #000;
    }

    .files-container {
        display: flex;
        flex-wrap: wrap;
        column-gap: 20px;
        row-gap: 10px;
    }

    .file-container img {
        width: 20px;
        height: 20px;
    }

    .choose-file {
        padding: 5px 10px;
        border: 1px solid #000;
    }

    .email-modal .modal-header {
        background: #dcf2ff;
    }

    .tagify-email .tagify__tag:only-of-type .tagify__tag__removeBtn {
        display: none;
    }

    @media (min-width: 576px) {
        .modal-dialog {
            max-width: 1100px !important;
        }
    }

    .hidemodelbtn {
        font-size: 18px !important;
        color: #7e8a9d !important;
    }
    .hidemodelbtn:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .hidemodelbtn:hover {
        cursor: pointer !important;
    }
    @media screen and (max-width: 580px) {

        .hidemodelbtn {
            font-size: 15px !important;
            color: #7e8a9d !important;
        }
    }
    .to {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 9px;
    color: var(--placeholdertext);
    background: rgb(172, 170, 170);
    height: 100%;
    /* width: 40px !important; */
    display: flex;
    align-items: center;
    /* padding-left: 5px; */
    padding: 0px 10px !important;
}
.preview-file-choose{

gap: 0px;
border-radius: 8px;
border: 1px solid #E4E4E4;
opacity: 0px;
text-align: center;
display: none;
}
.dz-started .preview-file-choose{
    display: inline !important;

gap: 0px;
border-radius: 8px !important;
border: 1px solid #E4E4E4  !important;

text-align: center !important;
}
.dz-preview{
    /* display: none !important; */
    min-height: 110px;
    width: 81px;
    margin: 0px 0px 0px 12px !important;

}
.dz-started .dz-preview
    {
        display: inline-block !important;
    }
    .dropzone_library_customize_ss_email{
        border: 2px dashed #90a0b7 !important;
    }
    .dropzone_library_customize_ss_email .dz-preview {
    margin: 8px;
    min-height: 100px;
    border: 1px solid #90a0b7;
    border-radius: 4px;
    padding: 8px;
}
.dropzone_library_customize_ss_email .dz-preview .dz-remove {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    background: #003366;
    border-radius: 6px;
    color: #ffffff;
    text-decoration: none;
    margin-top: 8px;
}
    .dz-filename span{
        /* display: none; */
    }
    .dz-filename {
    white-space: nowrap !important;
    word-wrap: none !important;
}
.dz-filename span{
    white-space: nowrap !important;
}

</style>
{{-- @endsection --}}
<!-- Modal -->
<div class="modal fade email-modal" id="emailModal" tabindex="-1" aria-labelledby="emailModalLabel"
    data-backdrop="static" data-keyboard="false" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content border-0">
            <div class="modal-header">
                <h5 class="modal-title" id="emailModalLabel">Email</h5>
                <button
                    type="button"
                    class="btn-close hidemodelbtn px-3"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                </button>
            </div>
            <form id="client-send-email" class="file-upload-form-email" method="POST" action="{{ route(getRouteAlias() . '.send-mail.client') }}"
                enctype="multipart/form-data">

                <div class="modal-body">
                    <div class="write-email-container">
                        <div class="write-email d-flex flex-column gap-4">
                            <div class="emails d-flex flex-column gap-3">
                                <div class="input-main to-email">
                                    <label class="to pe-3" style="overflow-wrap: normal !important; word-break: normal !important;">To</label>
                                    <input class="my-input input form-control h-full tagify-email"
                                        value="{{ isset($to_email) ? $to_email : '' }}" type="email" name="email[]"
                                        id="email" style="border-radius: 0" readonly>
                                    <div class="dropdown dropstart">
                                        <button class="bg-transparent border-0" type="button" id="dropdownMenuButton2"
                                            data-toggle="dropdown" aria-expanded="false" style="cursor: pointer">
                                            <img height="24px" width="24px"
                                                src="{{ asset('admin_assets/images/icons/vertical-dots.svg') }}"
                                                alt="vertical dots">
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-dark email-menus" aria-labelledby="dropdownMenuButton2">
                                            <li id="cc">Cc</li>
                                            <li id="bcc">Bcc</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="subject-wraper mb-3">
                                <label class="subject">Subject</label>
                                <input type="text" placeholder="Enter Subject" class="input form-control" id="subject" name="subject">
                            </div>
                            <textarea id="summernote" class="summernote" required="required" data-msg="Please write something in message">
                                <span class="im">
                                    <br>
                                    <br>
                                    <p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 10px;">Regards, </p><br>
                                    <p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong>{{ $organization?->company_name }}</strong> </p>
                                    <p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong>{{ $organization?->email }} </strong></p>
                                    <p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong>{{ optional($organization->companyAddress)?->phone_no }} </strong></p>
                                    <p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong>{{ optional($organization->companyAddress)?->address1 }} </strong></p>
                                    @if (optional($organization->companyAddress)?->website_url)
                                    <p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong>{{ optional($organization->companyAddress)?->website_url }} </strong></p>
                                    @endif
                                </span>
                            </textarea>

                            <div class="check-box-wraper">
                                <input type="checkbox" id="phoneall" name="copy" value="yes">
                                <label for="phoneall" class="radio-label"></label>
                                <label for="phoneall" class="checkLabel">Send me a copy</label>
                            </div>
                            @if (isset($sentInvoiceAttachment) && $sentInvoiceAttachment == true)
                                <div class="check-box-wraper">
                                    <input type="checkbox" id="attachment" name="invoiceAttachment" value="yes">
                                    <label for="attachment" class="radio-label"></label>
                                    <label for="attachment" class="checkLabel">Send Invoice Attachment</label>
                                </div>
                            @endif
                            @if (isset($isOperational) && $isOperational == true)
                                <div class="check-box-wraper d-none">
                                    <input type="checkbox" id="estimateAttachment" name="estimateAttachment" value="yes" checked>
                                    <label for="estimateAttachment" class="radio-label"></label>
                                    <label for="estimateAttachment" class="checkLabel">
                                        <img width="20" src="{{ asset('admin_assets/images/icons/pdf-icon.svg') }}"
                                            alt="image">
                                        <span
                                            style="font-family: 'Poppins';font-style: normal;font-weight: 500;font-size: 16px;line-height: 24px;color: #868686;">Estimate
                                            #{{ $sales_order_number ?? '' }}.pdf</span> </label>
                                </div>
                            @endif
                        </div>
                        <div class="write-attachments">
                            <div>
                                <h2 class="attachments">Attachments</h2>
                                <div class="mt-2">
                    <div class="dropzone_library_customize_ss_email dropzone" >
                    <div class="dz-preview dz-file-preview dz-processing dz-success dz-complete needsclick" id="my-dropzone" style="width: 81px !important;
height: 110px !important; text-align: center; cursor: pointer;">
                        <svg width="20" height="20" style="margin-top: 33px;" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                        </div>
                    </div>
                    <div id="imageSizeError"></div>
                </div>
                                <!-- <label class="btn primaryblue" id="choose-file">Choose a file</label>
                                <input type="file" name="select_file[]" id="selectFile" class="d-none" multiple>
                                <div class="d-flex align-items-center gap-5 mt-4">
                                    <div class="check-box-wraper">
                                        {{-- <input type="checkbox" id="selectItems" name="select_items" value="phone">
                                    <label for="selectItems" class="radio-label"></label> --}}
                                    </div>
                                    <div class="files-container">

                                    </div>
                                </div> -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn transparent bg-transparent" style="height: 40px;"
                        data-dismiss="modal">Close</button>
                    <button type="submit" class="btn primaryblue" style="height: 40px;">Send Email</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
    <!---------------------- Tgify JS ---------------------->
    <script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>

    <script>
                   $(document).ready(function() {
                $('.dropzone_library_customizess .dz-message').html(
                    '<div class="drop-zone__prompt text-center"><svg width="34" height="34" viewBox="0 0 34 34" fill="none"xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd"d="M9.36275 2.85175C11.4866 1.02029 14.1955 0.00879434 17 0C22.7162 0 27.4614 4.25 27.9778 9.73038C31.3608 10.2085 34 13.0411 34 16.5176C34 20.3341 30.8168 23.375 26.9599 23.375H21.25C20.9682 23.375 20.698 23.2631 20.4987 23.0638C20.2994 22.8645 20.1875 22.5943 20.1875 22.3125C20.1875 22.0307 20.2994 21.7605 20.4987 21.5612C20.698 21.3619 20.9682 21.25 21.25 21.25H26.962C29.7054 21.25 31.875 19.0995 31.875 16.5176C31.875 13.9336 29.7075 11.7831 26.9599 11.7831H25.8974V10.7206C25.8995 6.00313 21.947 2.125 17 2.125C14.7047 2.13417 12.4883 2.96317 10.7504 4.4625C9.14175 5.848 8.30025 7.51825 8.30025 8.82938V9.78137L7.35462 9.8855C4.386 10.2106 2.125 12.648 2.125 15.5507C2.125 18.6681 4.73875 21.25 8.03463 21.25H12.75C13.0318 21.25 13.302 21.3619 13.5013 21.5612C13.7006 21.7605 13.8125 22.0307 13.8125 22.3125C13.8125 22.5943 13.7006 22.8645 13.5013 23.0638C13.302 23.2631 13.0318 23.375 12.75 23.375H8.03463C3.6295 23.375 0 19.9028 0 15.5507C0 11.8044 2.69025 8.70187 6.25175 7.91562C6.55562 6.08175 7.735 4.25425 9.36275 2.85175Z"fill="#90A0B7" /><path fill-rule="evenodd" clip-rule="evenodd"d="M16.2471 8.8102C16.3458 8.71125 16.4631 8.63275 16.5922 8.57918C16.7213 8.52562 16.8596 8.49805 16.9994 8.49805C17.1391 8.49805 17.2775 8.52562 17.4066 8.57918C17.5357 8.63275 17.6529 8.71125 17.7516 8.8102L24.1266 15.1852C24.3261 15.3847 24.4382 15.6553 24.4382 15.9374C24.4382 16.2196 24.3261 16.4902 24.1266 16.6897C23.9271 16.8892 23.6565 17.0013 23.3744 17.0013C23.0922 17.0013 22.8216 16.8892 22.6221 16.6897L18.0619 12.1273V30.8124C18.0619 31.0942 17.9499 31.3645 17.7507 31.5637C17.5514 31.763 17.2812 31.8749 16.9994 31.8749C16.7176 31.8749 16.4473 31.763 16.2481 31.5637C16.0488 31.3645 15.9369 31.0942 15.9369 30.8124V12.1273L11.3766 16.6897C11.1771 16.8892 10.9065 17.0013 10.6244 17.0013C10.3422 17.0013 10.0716 16.8892 9.87214 16.6897C9.67263 16.4902 9.56055 16.2196 9.56055 15.9374C9.56055 15.6553 9.67263 15.3847 9.87214 15.1852L16.2471 8.8102Z"fill="#90A0B7" /></svg><p class="placeholder-text font-14">Drop your logo here, or <span>browse</span></p><p class="placeholder-text font-14">PNG, JPEG, PDF , XlS, XLSX Max size: 5MB</p></div>'
                )
            })

            $(document).on('click', '#my-dropzone', function() {
    $('#imageSizeError').html('');
});

    Dropzone.autoDiscover = false; // Disable auto-discovery



    var uploadedDocumentMap = {};
    const myDropzone = new Dropzone("#my-dropzone", {
    url: "{{ route(getRouteAlias() . '.send.emails.file-store') }}", // Replace with your server upload URL
    addRemoveLinks: true,
    dictRemoveFile: "Remove", // Proper text for remove button
    previewTemplate: `
        <div class="dz-preview dz-file-preview">

         <img class="dz-details-imagess-ss-email" style="height: 60px;" src="" />
            <div class="dz-details">

                <div class="dz-icon"></div>
                <div class="dz-filename"><span data-dz-name></span></div>
            </div>
            <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>
            <div class="dz-error-message"><span data-dz-errormessage></span></div>

        </div>
    `,
    previewsContainer: ".dropzone_library_customize_ss_email",
    acceptedFiles: ".webp,.jpeg,.jpg,.png,.gif,.pdf,.xls,.xlsx,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content') // Include CSRF token in the headers
    },
    init: function () {


        this.on("success", function (file, response) {
            // Assuming your API response contains a key 'filePath'
            $('.file-upload-form-email').append('<input type="hidden" name="select_file[]" value="' + response.name + '">');
            uploadedDocumentMap[file.name] = response.name;
        });
        this.on("addedfile", function (file) {
            // Extract file extension
            let ext = file.name.split('.').pop().toLowerCase();

            // Default icon (if no match found)
            let iconSrc = "default-icon.png";

            // Icons for specific file types
            if (ext === "pdf") {
                iconSrc = "data:image/svg+xml;base64,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";
            } else if (ext === "xls" || ext === "xlsx") {
                iconSrc = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48cGF0aCBkPSdNMi44NTkwMiAyLjg3ODIyTDE1LjQyOSAxLjA4MjIyQzE1LjUgMS4wNzIwNCAxNS41NzIzIDEuMDc3MjMgMTUuNjQxIDEuMDk3NDRDMTUuNzA5OCAxLjExNzY1IDE1Ljc3MzQgMS4xNTI0IDE1LjgyNzUgMS4xOTkzNUMxNS44ODE3IDEuMjQ2MjkgMTUuOTI1MSAxLjMwNDMzIDE1Ljk1NDkgMS4zNjk1MkMxNS45ODQ2IDEuNDM0NzIgMTYgMS41MDU1NSAxNiAxLjU3NzIyVjIyLjQyNDJDMTYgMjIuNDk1OCAxNS45ODQ2IDIyLjU2NjUgMTUuOTU0OSAyMi42MzE2QzE1LjkyNTIgMjIuNjk2NyAxNS44ODE5IDIyLjc1NDcgMTUuODI3OSAyMi44MDE3QzE1Ljc3MzggMjIuODQ4NiAxNS43MTAzIDIyLjg4MzQgMTUuNjQxNyAyMi45MDM2QzE1LjU3MzEgMjIuOTIzOSAxNS41MDA5IDIyLjkyOTIgMTUuNDMgMjIuOTE5MkwyLjg1ODAyIDIxLjEyMzJDMi42MTk2NCAyMS4wODkzIDIuNDAxNTIgMjAuOTcwNCAyLjI0MzcxIDIwLjc4ODZDMi4wODU5MSAyMC42MDY3IDEuOTk5MDMgMjAuMzc0IDEuOTk5MDIgMjAuMTMzMlYzLjg2ODIyQzEuOTk5MDMgMy42Mjc0MyAyLjA4NTkxIDMuMzk0NzMgMi4yNDM3MSAzLjIxMjg2QzIuNDAxNTIgMy4wMzA5OSAyLjYyMDY0IDIuOTEyMTcgMi44NTkwMiAyLjg3ODIyWk0xNyAzLjAwMDIySDIxQzIxLjI2NTIgMy4wMDAyMiAyMS41MTk2IDMuMTA1NTcgMjEuNzA3MSAzLjI5MzExQzIxLjg5NDcgMy40ODA2NCAyMiAzLjczNSAyMiA0LjAwMDIyVjIwLjAwMDJDMjIgMjAuMjY1NCAyMS44OTQ3IDIwLjUxOTggMjEuNzA3MSAyMC43MDczQzIxLjUxOTYgMjAuODk0OSAyMS4yNjUyIDIxLjAwMDIgMjEgMjEuMDAwMkgxN1YzLjAwMDIyWk0xMC4yIDEyLjAwMDJMMTMgOC4wMDAyMkgxMC42TDkuMDAwMDIgMTAuMjg2Mkw3LjQwMDAyIDguMDAwMjJINS4wMDAwMkw3LjgwMDAyIDEyLjAwMDJMNS4wMDAwMiAxNi4wMDAySDcuNDAwMDJMOS4wMDAwMiAxMy43MTQyTDEwLjYgMTYuMDAwMkgxM0wxMC4yIDEyLjAwMDJaJyBmaWxsPSdibGFjaycvPjwvc3ZnPgo=";
            } else if (ext === "doc" || ext === "docx") {
                iconSrc = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgaWQ9IndvcmQiPjxwYXRoIGQ9Ik0yMy41IDIuNUgxNFYxYS41LjUgMCAwIDAtLjYxMi0uNDg3bC0xMyAzQS41LjUgMCAwIDAgMCA0djE3YS41LjUgMCAwIDAgLjQyNC40OTRsMTMgMmEuNDk2LjQ5NiAwIDAgMCAuNDAxLS4xMTVBLjQ5NC40OTQgMCAwIDAgMTQgMjN2LTEuNWg5LjVhLjUuNSAwIDAgMCAuNS0uNVYzYS41LjUgMCAwIDAtLjUtLjV6TTEwLjk4MSA5LjEzOGwtMiA3Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMEw2Ljk4IDEyLjUwM2MtLjA2LS4yMTUtLjI1Ni0uMzYzLS40OC0uMzYzcy0uNDIuMTQ4LS40OC4zNjNsLTEuMDM4IDMuNjM0Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMGwtMi03YS41LjUgMCAwIDEgLjk2Mi0uMjc0bDEuMDM4IDMuNjM1Yy4xMjEuNDMuODQuNDMuOTYxIDBsMS4wMzgtMy42MzRjLjEyMy0uNDI3Ljg0LS40MjYuOTYzIDBsMS4wMzggMy42MzVjLjEyMS40My44NC40My45NjEgMGwxLjAzOC0zLjYzNGEuNS41IDAgMCAxIC45NjMuMjczek0yMyAyMC41aC05di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoNy41YS41LjUgMCAwIDAgMC0xSDE0di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoOXYxN3oiPjwvcGF0aD48L3N2Zz4=";
            }else if (ext === "png" || ext === "jpg" || ext === "webp" || ext === "jpeg" || ext === "jpeg") {
                const reader = new FileReader();
                reader.onload = function (e) {


                    $('.dz-details-imagess-ss-email').css('width', '100%');
                    // Find the preview image element and set the source
                    $(file.previewElement).find(".dz-details-imagess-ss-email").attr("src", e.target.result);
                };
                reader.readAsDataURL(file);
            }

            // Update the preview element with the corresponding icon
            if (ext === "docx" || ext === "doc" || ext === "xlsx" || ext === "xls" || ext === "pdf") {
            $(file.previewElement).find(".dz-details-imagess-ss-email").attr("src", iconSrc);
            }
        });

        this.on("removedfile", function (file) {
            console.info("File removed: ", file.name); // Log file removal

            file.previewElement.remove();
        var name = '';
        if (typeof file.file_name !== 'undefined') {
            name = file.file_name;
        } else {
            name = uploadedDocumentMap[file.name];
        }

            // Send an AJAX request to delete the file from the server
            fetch("{{ route(getRouteAlias() . '.delete.send.email.file-delete') }}", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ filename: name })
            })
            .then(response => response.json())
            .then(data => {
                console.info('File deleted from server:', data);
                // You can add additional logic here to update UI or handle response data
            })
            .catch(error => {
                console.error('Error deleting file from server:', error);
            });
            $('.file-upload-form-email').find('input[name="select_file[]"][value="' + name + '"]').remove();
        });

        // Add error handling if needed
        this.on("error", function (file, errorMessage) {
            console.error("Dropzone error:", errorMessage);
        });
    }
});

        </script>

    <!---------------------- Tgify JS ---------------------->
    <script>
        let isExternalSubmitHandler = @json($isExternalSubmitHandler ?? 'null');

        var emailFormData;
        let selectedFiles = [];
        var validator;
        var toEmails = null;
        var ccEmails = null;
        var bccEmails = null;

        // Custom validation method for cc_email array
        $.validator.addMethod('ccEmailArrayValidation', function(value, element) {
            // Split the value into an array of email addresses using a comma as the delimiter
            var emails = value.split(',');

            // Validate each email in the array
            for (var i = 0; i < emails.length; i++) {
                if (!/^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/.test(emails[i].trim())) {
                    // If any email is invalid, return false
                    return false;
                }
            }

            return true;
        }, 'Please enter valid email addresses in the Cc field');

        $("#cc").click(function() {
            $(".emails").append(
                '<div class="input-main cc-email-class">\
                                                                                                                                                                                    <label class="to pe-3" style="overflow-wrap: normal !important; word-break: normal !important;">Cc</label>\
                                                                                                                                                                                    <input class="my-input input h-full form-control" type="email" name="cc_email[]" id="emailcc" style="border-radius: 0"></div>'
            );
            ccEmails = initalizeTagify(document.getElementById("emailcc"));
            if ($(this).next().length == 0 && $(this).prev().length == 0) {
                $("#dropdownMenuButton2").css("display", "none");
            } else {
                $("#dropdownMenuButton2").css("display", "block");
            }

            $(this).remove();
        });

        $("#bcc").click(function() {
            $(".emails").append(
                '<div class="input-main bcc-email-class">\
                                                                                                                                                                                <label class="to pe-3"  style="overflow-wrap: normal !important; word-break: normal !important; padding: 0px 7px !important;">Bcc</label>\
                                                                                                                                                                                <input class="my-input input form-control h-full" type="email" name="bcc_email[]" id="emailbcc" style="border-radius: 0"></div>'
            );
            bccEmails = initalizeTagify(document.getElementById("emailbcc"));
            if ($(this).next().length == 0 && $(this).prev().length == 0) {
                $("#dropdownMenuButton2").css("display", "none");
            } else {
                $("#dropdownMenuButton2").css("display", "block");
            }

            $(this).remove();
        });



        $(document).ready(function() {
            if ($("#client-send-email").length) {
                emailValidateForm("#client-send-email");
            }

            function emailValidateForm(formSelector) {

                var form = $(formSelector);

                validator = form.validate({
                    ignore: ':hidden:not(.summernote),.note-editable.card-block,input[name="cc_email[]"],input[name="bcc_email[]"],input[name="email[]"]',
                    rules: {
                        copy: {
                            required: false,
                        },
                        subject: {
                            required: true,
                            minlength: 2,
                            maxlength: 998,
                        },
                    },
                    messages: {
                        subject: {
                            required: "Subject is required",
                            minlength: "Subject cannot be less than 2 characters",
                            maxlength: "Subject cannot be more than 998 characters",
                        },
                    },
                    success: function(label, element) {
                        $(element).removeClass("error-field").addClass("success-field");
                    },
                    errorPlacement: function(error, element) {
                        error.addClass("error-message");
                        if (element.hasClass("select2-hidden-accessible")) {
                            error.insertAfter(element.next(".select2-container"));
                        } else if (element.attr("name") === "format_file") {
                            // Show the error message below the format_radio_fields class
                            error.insertAfter(".format_radio_fields");
                        } else if (element.hasClass("summernote")) {
                            error.insertAfter(element.siblings(".note-editor"));
                        } else {
                            error.insertAfter(element);
                        }
                    },
                });

                var summernoteElement = $('.summernote');
                if (summernoteElement.length) {
                    summernoteElement.summernote({
                        placeholder: "Message",
                        tabsize: 2,
                        height: 250,
                        toolbar: [
                            ["style", ["italic", "bold", "underline"]],
                        ],
                        callbacks: {
                            onChange: function(contents, $editable) {
                                summernoteElement.val(summernoteElement.summernote('isEmpty') ? "" :
                                    contents);
                                validator.element(summernoteElement);
                            },
                        },
                    });
                }
            }


            $(document).on("submit", "#client-send-email", function(event) {
                event.preventDefault();


                $(".subject-error").remove();

                // Create a new FormData object
                emailFormData = new FormData(this);

                // Add each selected file to the FormData object
                if (selectedFiles.length > 0) {
                    emailFormData.delete("select_file[]");
                    for (var i = 0; i < selectedFiles.length; i++) {
                        emailFormData.append('select_file[]', selectedFiles[i]);
                    }
                }

                if (ccEmails != null) {
                    emailFormData.delete("cc_email[]");
                    var ccEmailsArray = Array.from(ccEmails.value.map(function(tag) {
                        return tag.value;
                    }));
                    for (var i = 0; i < ccEmailsArray.length; i++) {
                        emailFormData.append('cc_email[]', ccEmailsArray[i]);
                    }
                }

                if (toEmails.value.length == 0) {
                    event.preventDefault();
                    $('.to-email').after(
                        '<label id="subject-error" class="error error-message" for="subject">Email is required</label>'
                    );
                    return;
                }
                if (toEmails != null) {
                    emailFormData.delete("email[]");
                    var toEmailsArray = Array.from(toEmails.value.map(function(tag) {
                        return tag.value;
                    }));
                    for (var i = 0; i < toEmailsArray.length; i++) {
                        emailFormData.append('email[]', toEmailsArray[i]);
                    }
                }

                if (bccEmails != null) {
                    emailFormData.delete("bcc_email[]");
                    var bccEmailsArray = Array.from(bccEmails.value.map(function(tag) {
                        return tag.value;
                    }));
                    for (var i = 0; i < bccEmailsArray.length; i++) {
                        emailFormData.append('bcc_email[]', bccEmailsArray[i]);
                    }
                }

                // Add other form data as needed
                let message = $('.summernote').summernote('code');
                emailFormData.append('message', message);

                console.log("isExternalSubmitHandler", isExternalSubmitHandler);

                if (isExternalSubmitHandler !== 'null') {
                    console.info('jjjjjj2');
                    emailFormData.append('send-message', message);
                    emailFormData.append('send-email[]', emailFormData.get('email[]'));
                    emailFormData.append('send-subject', emailFormData.get('subject'));

                    emailFormData.delete('subject');
                    emailFormData.delete('email[]');
                    emailFormData.delete('message');

                    $.event.trigger({
                        type: isExternalSubmitHandler,
                        customData: emailFormData,
                        time: new Date()
                    });
                } else {

                    console.info('jjjjjj');
                    $.ajaxSetup({
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        }
                    });

                    $.ajax({
                        url: $(this).attr("action"),
                        type: $(this).attr("method"),
                        dataType: "JSON",
                        data: emailFormData,
                        processData: false,
                        contentType: false,
                        success: function(response, status) {
                            toastr.success(response.message);
                            $('.email-modal').modal('hide');
                            $('#client-send-email')[0].reset();
                            $('.summernote').summernote('reset');
                            $('#summernote-error').remove();
                            $('.files-container').html('');
                            $("#client-send-email :input").removeClass("valid");

                            if (ccEmails != null) {
                                ccEmails.removeAllTags();
                            }

                            if (bccEmails != null) {
                                bccEmails.removeAllTags();
                            }

                            $('.cc-email-class').remove();
                            $('.bcc-email-class').remove();
                            validator.resetForm();

                            $('.email-menus').html('<li id="cc">Cc</li> <li id="bcc">Bcc</li>');

                            selectedFiles = [];
                        },
                        error: function(request, error) {
                            let errorResponse = JSON.parse(request.responseText);
                            if (request.status == 500) {
                                toastr.error("Email Not Sent Successfully!");
                            }
                            if (errorResponse.hasOwnProperty('errors')) {
                                console.log("errorResponse.errors", errorResponse.errors);
                                $.each(errorResponse.errors, function(field_name,
                                    error) {
                                    console.log("filed", field_name);
                                    if (field_name == 'email' || field_name ==
                                        'send-email') {
                                        $('.to-email').after(
                                            `<label id="subject-error" class="error error-message" for="subject">${error[0]}</label>`
                                        );
                                    }

                                    if (field_name == 'message' || field_name ==
                                        'send-message') {
                                        $(document).find('#summernote').after(
                                            `<label id="subject-error" class="error error-message" for="subject">${error[0]}</label>`
                                        );
                                    }

                                    if (field_name == 'subject' || field_name ==
                                        'send-subject') {
                                        $(document).find('[name=subject]').after(
                                            `<label id="subject-error" class="error error-message" for="subject">${error[0]}</label>`
                                        );
                                    }

                                    if (field_name == 'cc_email') {
                                        $('.cc-email-class').after(
                                            `<label id="subject-error" class="error error-message" for="subject">${error[0]}</label>`
                                        );
                                    }

                                    if (field_name == 'bcc_email') {
                                        $('.bcc-email-class').after(
                                            `<label id="subject-error" class="error error-message" for="subject">${error[0]}</label>`
                                        );
                                    }

                                });
                            }
                        }
                    });
                }





            });

        });



        $(document).ready(function() {
            // Get the input element with the class "tagify-email"
            var input = document.querySelector(".tagify-email"); // Replace with your input element
            toEmails = initalizeTagify(input, true);
        });

        function initalizeTagify(input, isLastRemains = false) {
            var tagify = new Tagify(input, {
                dropdown: {
                    maxItems: 0, // Maximum number of items in the dropdown (0 for unlimited)
                    enabled: 0, // Whether the dropdown is enabled (0 for disabled)
                },
                callbacks: {
                    // add: console.log, // callback when adding a tag
                    // remove: function (e) {
                    //     // Check if it's the last tag, and if so, prevent removal
                    //     console.log(tagify.value.length);
                    //     if (tagify.value.length === 1) {
                    //         e.preventDefault();
                    //     }
                    // }, // callback when removing a tag
                },
                whitelist: ["a", "aa", "aaa", "b", "bb", "ccc"], // List of allowed values
                originalInputValueFormat: (valuesArr) =>
                    valuesArr.map((item) => item.value).join(","), // Format the input value
                pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, // Regular expression pattern for email validation
                validators: {
                    maxLength: function(input, value, tagify) {
                        return value.trim().length <= 20; // Validator for maximum length of input
                    },
                },
            });
            tagify.addTags([]);
            return tagify;
        }

        $(document).ready(function() {
            // Get references to the label, input file element, and files container
            const chooseFileLabel = document.getElementById("choose-file");
            const selectFileInput = document.getElementById("selectFile");
            const filesContainer = document.querySelector(".files-container");

            // Function to get the file type based on the file name
            function getFileType(fileName) {
                const extension = fileName.split(".").pop().toLowerCase();
                switch (extension) {
                    case "pdf":
                        return '{{ asset('admin_assets/images/icons/txt.svg') }}'; // You can provide the path to your PDF icon
                    case "doc":
                    case "docx":
                        return '{{ asset('admin_assets/images/icons/txt.svg') }}'; // You can provide the path to your DOC/DOCX icon
                    case "txt":
                        return '{{ asset('admin_assets/images/icons/txt.svg') }}'; // You can provide the path to your TXT icon
                        // Add more cases for other file types as needed
                    default:
                        return '{{ asset('admin_assets/images/icons/txt.svg') }}'; // Use a default icon for unknown file types
                }
            }

            // Add an event listener to the label that triggers a click on the input file element
            chooseFileLabel.addEventListener("click", () => {
                selectFileInput.click();
            });

            // Add an event listener to the input file element to handle file selection
            selectFileInput.addEventListener("change", (event) => {
                const newlySelectedFiles = event.target.files;

                if (newlySelectedFiles.length > 0) {
                    // Append the newly selected files to the existing array
                    selectedFiles = [...selectedFiles, ...newlySelectedFiles];

                    // Clear the existing filesContainer
                    filesContainer.innerHTML = "";

                    // Loop through all selected files and display them
                    for (const selectedFile of selectedFiles) {
                        const fileContainer = document.createElement("div");
                        fileContainer.classList.add("file-container");

                        const fileType = getFileType(selectedFile.name);
                        const fileIcon = document.createElement("img");
                        fileIcon.src = fileType;
                        fileIcon.alt = "image";

                        const fileNameSpan = document.createElement("span");
                        fileNameSpan.textContent = selectedFile.name;

                        fileContainer.appendChild(fileIcon);
                        fileContainer.appendChild(fileNameSpan);

                        filesContainer.appendChild(fileContainer);
                    }
                }
            });
        });
    </script>
@endpush
