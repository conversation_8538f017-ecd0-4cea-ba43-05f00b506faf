@extends('layouts.admin.master')

@push('tinymce-scripts')
<!-- TinyMCE -->
<script src="{{ asset('assets/tinymce/js/tinymce/tinymce.min.js') }}"></script>
@endpush
@section('title', 'Customer Issue')
@section('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.0.8/css/fileinput.min.css">
    <style>
        .basic-single-select + .select2 .select2-selection, .custom_selectBox + .select2 .select2-selection {
            background: #ffffff;
            border: 1px solid var(--bordercolor);
            border-top-right-radius: 6px !important;
            border-top-left-radius: 6px !important;
            border-bottom-left-radius: 6px !important;
            border-bottom-right-radius: 6px !important;
            height: 40px;
        }

        @import url('https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900&display=swap');

        button:focus,
        input:focus{
            outline: none;
            box-shadow: none;
        }
        a,
        a:hover{
            text-decoration: none;
        }
        .input-group.file-caption-main{
            display: none;
        }
        .close.fileinput-remove{
            display: none;
        }
        .file-drop-zone{
            margin: 0px;
            border: 1px solid #fff;
            background-color: #fff;
            padding: 0px;
            display: contents;
        }
        .file-drop-zone.clickable:hover{
            border-color: #fff;
        }
        .file-preview-status{
            display: none !important;
        }
        .file-drop-zone .file-preview-thumbnails{
            display: inline;
        }
        .file-drop-zone-title{
            padding: 15px;
            height: 120px;
            width: 120px;
            font-size: 12px;
        }
        .file-input-ajax-new{
            display: inline-block;
        }
        /* .file-input{
            display: flex !important;
        } */
        .file-input.theme-fas{
            display: inline-block;
            width: 100%;
        }
        .file-input.theme-fa{
            display: flex;
        }
        .file-preview{
            padding: 0px;
            border: none;
            display: inline;
            width: fit-content !important;

        }
        .file-drop-zone-title{
            display: none;
        }
        .file-footer-caption{
            display: none !important;
        }
        .kv-file-upload{
            display: none;
        }
        .file-upload-indicator{
            display: none;
        }
        .file-drag-handle.drag-handle-init.text-info{
            display: none;
        }
        .krajee-default.file-preview-frame .kv-file-content{
            width: 90px;
            height: 90px;
            display: flex;
            text-align: center;
            align-items: center;
        }
        .krajee-default.file-preview-frame{
            background-color: #fff;
            margin: 3px;
            border-radius: 15px;
            overflow: hidden;
        }
        .krajee-default.file-preview-frame:not(.file-preview-error):hover{
            box-shadow: none;
            border-color: #0074D9;
        }
        .krajee-default.file-preview-frame:not(.file-preview-error):hover .file-preview-image{
            transform: scale(1.1);
        }
        .krajee-default.file-preview-frame{
            box-shadow: none;
            border-color: #fff;
            max-width: 150px;
            margin: 5px;
            padding: 0px;
            transition: 0.5s;
        }
        .file-thumbnail-footer,
        .file-actions{
            width: 20px;
            height: 20px !important;
            position: absolute !important;
            top: 3px;
            right: 3px;
        }
        .kv-file-remove:focus,
        .kv-file-remove:active{
            outline: none !important;
            box-shadow: none !important;
        }
        .kv-file-remove{
            border-radius: 50%;
            z-index: 1;
            right: 0;
            position: absolute;
            top: 0;
            text-align: center;
            color: #fff;
            background-color: #0074D9;
            border: 1px solid #0074D9;
            padding: 2px 6px;
            font-size: 11px;
            transition: 0.5s;
        }
        .kv-file-remove:hover{
            border-color: #DCF2FF;
            background-color: #DCF2FF;
            color: #0074D9;
        }
        .kv-preview-data.file-preview-video{
            width: 100% !important;
            height: 100% !important;
        }
        .btn-outline-secondary.focus, .btn-outline-secondary:focus{
            box-shadow: none;
        }
        .btn-toggleheader,
        .btn-fullscreen,
        .btn-borderless{
            display: none;
        }
        .btn-kv.btn-close{
            color: #fff;
            border: none;
            background-color: #0074D9;
            font-size: 11px;
            width: 18px;
            height: 18px;
            text-align: center;
            padding: 0px;
        }
        .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
        .btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
        .show>.btn-outline-secondary.dropdown-toggle:focus{
            background-color: #DCF2FF;
            color: #DCF2FF;
            box-shadow: none;
            color: #0074D9;
        }
        .kv-file-content .file-preview-image{
            width: 90px !important;
            height: 90px !important;
            max-width: 90px !important;
            max-height: 90px !important;
            transition: 0.5s;
        }
        .btn-danger.btn-file:focus{
            box-shadow: none !important;
            outline: none !important;
        }
        .btn-danger.btn-file{
            padding: 0px;
            height: 95px;
            width: 95px;
            display: inline-block;
            margin: 5px;
            /* border-color: #DCF2FF; */
            border: 1px solid #E4E4E4;
            background-color: white;
            color: #0074D9;
            border-radius: 15px;
            padding-top: 30px;
            transition: 0.5s;
        }
        .btn-danger.btn-file:active,
        .btn-danger.btn-file:hover{
            background-color: #DCF2FF;
            color: #0074D9;
            border-color: #DCF2FF;
            box-shadow: none;
        }
        .btn-danger.btn-file i{
            font-size: 30px;
            color: #0074D9 !important;
        }


        @media (max-width: 350px){
            .krajee-default.file-preview-frame:not([data-template=audio]) .kv-file-content{
                width: 90px;
            }
        }
        .btn-danger:not(:disabled):not(.disabled).active, .btn-danger:not(:disabled):not(.disabled):active, .show>.btn-danger.dropdown-toggle {
            color: #0074D9 !important;
            background-color: #DCF2FF !important;
            border-color: #DCF2FF !important;
        }
        .select2-container{
            height: 48% !important;
        }
        .preview-file-choose{

            gap: 0px;
            border-radius: 8px;
            border: 1px solid #E4E4E4;
            opacity: 0px;
            text-align: center;
            display: none;
        }
        .dz-started .preview-file-choose{
            display: inline !important;

            gap: 0px;
            border-radius: 8px !important;
            border: 1px solid #E4E4E4  !important;

            text-align: center !important;
        }
        .dz-preview{
            /* display: none !important; */
            min-height: 110px;
            width: 81px;
            /* margin: 0px 0px 0px 12px !important; */

        }
        .dz-started .dz-preview
        {
            display: inline-block !important;
        }
        .dropzone_library_customize{
            /* display: flex; */
        }
        .dz-filename span{
            /* display: none; */
        }
    </style>
@endsection
@section('section')

    <section class="dashboard_main pb-5">
        <form id="add_issue_form" action="{{ $action }}" method="POST" class="file-upload-form-issue" enctype="multipart/form-data">
            @method('post')
            @csrf
            <!-- <div class="panel mt-4">
                <div class="panel_header">
                    <h2 class="panel_title">Property Information</h2>

                    <div class="image_upload_btn upload_file_wrapper upload_image_btn w-fit">
                        <label class="btn primaryblue" for="upload_image" title="{{ $issue->image ?? '' }}"
                            data-toggle="modal" data-target="#propertyModal">
                            Select Property
                        </label>
                    </div>
                </div>
                <div class="row pt-3 gy-4">
                    <div class="col-lg-4 col-md-6">
                        <div class="field">
                            <label class="label mb-2">Property Name<span class="steric">*</span></label>
                            <div class="field_wrapper">
                                <input type="text" placeholder="Property Name" name="property_name"
                                    value="{{ old('property_name', $issue->property_name ?? '') }}" class="input form-control"
                                    minlength="2" maxlength="40" required>
                                @if ($errors->has('property_name'))
                                    <div class="laravel_error">{{ $errors->first('property_name') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="field">
                            <label class="label mb-2">Address 1<span class="steric">*</span></label>
                            <div class="field_wrapper">
                                <input type="text" placeholder="Address 1" name="address1"
                                    value="{{ old('address1', $issue->address1 ?? '') }}" class="input form-control"
                                    minlength="2" maxlength="40" required>
                                @if ($errors->has('address1'))
                                    <div class="laravel_error">{{ $errors->first('address1') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="field">
                            <label class="label mb-2">Address 2 <span class="placeholder-text">(Optional)</span></label>
                            <div class="field_wrapper">
                                <input type="text" placeholder="Address 2" name="address2"
                                    value="{{ old('address2', $issue->address2 ?? '') }}"
                                    class="input form-control" minlength="10" maxlength="150">
                                @if ($errors->has('address2'))
                                    <div class="laravel_error">{{ $errors->first('address2') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 mt-2 col-md-6">
                        <div class="field">
                            <label class="label mb-2">City <span class="steric">*</span></label>
                            <div class="field_wrapper">
                                <input type="text" placeholder="City" name="city"
                                    value="{{ old('city', $issue->city ?? '') }}" class="input form-control"
                                    minlength="2" maxlength="150">
                                @if ($errors->has('city'))
                                    <div class="laravel_error">{{ $errors->first('city') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 mt-2 col-md-6">
                        <div class="field">
                            <label class="label mb-2">State<span class="steric">*</span></label>
                            <div class="field_wrapper">
                                <input type="text" placeholder="State" name="state" id="state"
                                    value="{{ old('state', $issue->state ?? '') }}" class="input form-control"
                                    maxlength="255" required>
                                @if ($errors->has('state'))
                                    <div class="laravel_error">{{ $errors->first('state') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 mt-2 col-md-6">
                        <div class="field">
                            <label class="label mb-2">Zip Code <span class="steric">*</span></label>
                            <div class="field_wrapper">
                                <input type="text" placeholder="Zip Code" name="zip_code" id="zip_code"
                                    value="{{ old('zip_code', $issue->zip_code ?? '') }}"
                                    class="input form-control" maxlength="40">
                                @if ($errors->has('zip_code'))
                                    <div class="laravel_error">{{ $errors->first('zip_code') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 mt-2 col-md-6">
                        <div class="field">
                            <div class="label-heading d-flex justify-content-between"> <label class="label">Account</label> <div class="add-new"> + Add New Account</div> </div>
                            <div class="absolute-error">
                                <select name="company_id" onchange="getContact()" id="company_id" class="input custom_selectBox basic-single-select compnyids">
                                    <option value="" selected disabled>Select Account</option>
                                    @foreach ($accounts as $item)
                                    <option value="{{$item->id}}">{{$item->company_name}}</option>
                                    @endforeach
                                </select>
                                @if ($errors->has('company_id'))
                                    <div class="laravel_error">{{ $errors->first('company_id') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <a style="cursor: pointer" id="addNewProperty" class="addNewProperty"> + Add New Property</a>
            </div> -->

            <!-- <div class="panel mt-4">
                <div class="panel_header">
                    <h2 class="panel_title">Contact Information</h2>
                    <div class="image_upload_btn upload_file_wrapper upload_image_btn w-fit">
                        <label class="btn primaryblue" for="upload_image" title="{{ $issue->image ?? '' }}"
                            data-toggle="modal" data-target="#contactModal">
                            Select Contact
                        </label>
                    </div>
                </div>

                <div class="panel_fields mt-4">
                    <div class="field">
                        <label class="label">First Name<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <input type="text" placeholder="First Name" name="first_name"
                                value="{{ old('first_name', $issue->first_name ?? '') }}"
                                class="input form-control" required>
                            @if ($errors->has('first_name'))
                                <div class="laravel_error">{{ $errors->first('first_name') }}</div>
                            @endif
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Last Name<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <input type="text" placeholder="Last Name" name="last_name"
                                value="{{ old('last_name', $issue->last_name ?? '') }}"
                                class="input form-control">
                            @if ($errors->has('last_name'))
                                <div class="laravel_error">{{ $errors->first('last_name') }}</div>
                            @endif
                        </div>
                    </div>
                    <div class="field">
                        <label class="label">Title</label>
                        <div class="field_wrapper">
                            <input type="text" placeholder="Title" name="title_role"
                                value="{{ old('title_role', $issue->title_role ?? '') }}"
                                class="input form-control">
                            @if ($errors->has('title_role'))
                                <div class="laravel_error">{{ $errors->first('title_role') }}</div>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="panel_fields mt-4">
                    <div class="field">
                        <label class="label">Role<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <select
                            name="role"
                            id="role"
                            style="height: 30px !important"
                            class="form-control"
                            required

                        >
                            @foreach($roles as $item)
                            <option value="{{$item->id}}">
                                {{$item->name}}
                            </option>
                            @endforeach
                        </select>

                            @if ($errors->has('role'))
                                <div class="laravel_error">{{ $errors->first('role') }}</div>
                            @endif
                        </div>
                    </div>
                <div class="field">
                    <label class="label">Phone Number</label>
                    <div class="field_wrapper">
                        <input type="text" placeholder="Phone Number" name="phone_number"
                            value="{{ old('phone_number', $issue->phone_number ?? '') }}"
                            class="input form-control">
                        @if ($errors->has('phone_number'))
                            <div class="laravel_error">{{ $errors->first('phone_number') }}</div>
                        @endif
                    </div>
                </div>

                <div class="field">
                    <label class="label">Second Phone<span> (Optional)</span></label>
                    <div class="field_wrapper">
                        <input type="text" placeholder="Second phone" name="second_phone" id="phone_number"
                            class="input form-control" minlength="5" maxlength="50">
                        @if ($errors->has('second_phone'))
                            <div class="laravel_error">{{ $errors->first('second_phone') }}</div>
                        @endif
                    </div>
                </div>
                </div>
                <div class="panel_fields mt-4">
                    <div class="field">
                        <label class="label">Email<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <input type="email" placeholder="Email" name="email" id="email"
                                value="{{ old('email', $client->email ?? '') }}"
                                class="input form-control c_emaiil" required>
                            @if ($errors->has('email'))
                                <div class="laravel_error">{{ $errors->first('email') }}</div>
                            @endif
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Second Email<span class=""> (Optional)</span></label>
                        <div class="field_wrapper">
                            <input type="email" placeholder="Second email" name="second_email" id="second_email"
                                class="input form-control">

                        </div>
                    </div>
                    <div class="field">
                        <label class="label">Mailing Address<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <input type="text" placeholder="Mailing address" name="mailing_address"
                                class="input form-control">
                            @if ($errors->has('mailing_address'))
                                <div class="laravel_error">{{ $errors->first('mailing_address') }}</div>
                            @endif
                        </div>
                    </div>
                </div>

            </div> -->
            <div class="panel mt-4">
                <!-- Panel Heading -->
                <div class="panel_header">
                    <h2 class="panel_title">Assign job</h2>
                </div>
                <div class="panel_fields mt-4">

                <!-- Job Selection -->
                <div class="field">
                    <label class="label">Select Job<span class="steric">*</span></label>
                    <div class="absolute-error">
                        <select name="assigned_job" id="assigned_job" class="input custom_selectBox basic-single-select">
                            <option value="" selected disabled>Select Job</option>
                            @foreach($operations as $job)
                            <option value="{{$job->opportunityid->id}}">{{$job->opportunityid->opportunity_name}}</option>
                            @endforeach
                            <!-- <option value="2">Project Manager</option>
                            <option value="3">UI/UX Designer</option>
                            <option value="4">Quality Assurance</option>
                            <option value="5">System Administrator</option> -->
                        </select>
                        <!-- Error handling -->
                        @if ($errors->has('assigned_job'))
                            <div class="laravel_error">{{ $errors->first('assigned_job') }}</div>
                        @endif
                    </div>
                </div>
            </div>
            </div>


            <div class="panel mt-4">
                <div class="panel_header">
                    <h2 class="panel_title">Issue Details</h2>
                </div>

                <div class="panel_fields mt-4">
                    <div class="field">
                        <label class="label">Issue Category<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <!-- <input type="text" placeholder="Issue Category" name="issue_category" id="issue_category"
                                value="{{ old('issue_category', $issue->issue_category ?? '') }}"
                                class="input form-control" minlength="5" maxlength="50" required> -->

                                <select name="issue_category" id="issue_category" class="input custom_selectBox basic-single-select">
                                    <option value="" selected disabled>Select Category</option>
                                    <option value="Complaint">Complaint</option>
                                    <option value="Service Request">Service Request</option>
                                    <option value="QA">QA</option>
                                    <option value="Opportunity">Opportunity</option>
                                    <option value="Punch List">Punch List</option>
                                    <option value="Other">Other</option>
                                </select>
                                <!-- Error handling -->
                            @if ($errors->has('issue_category'))
                                <div class="laravel_error">{{ $errors->first('issue_category') }}</div>
                            @endif
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Subject</label>
                        <div class="field_wrapper">
                            <input type="text" placeholder="Subject" name="subject" id="subject"
                                value="{{ old('subject', $issue->subject ?? '') }}"
                                class="input form-control" minlength="5" maxlength="100">
                            @if ($errors->has('subject'))
                                <div class="laravel_error">{{ $errors->first('subject') }}</div>
                            @endif
                        </div>
                    </div>


                    <div class="field">
                        <label class="label">Issue Created By<span class="steric">*</span></label>
                        <div class="absolute-error">
                            <select name="issue_created_by" id="issue_created_by" class="input custom_selectBox basic-single-select">
                                <option value="" selected disabled>Select Creator</option>
                                @foreach($loginuser as $item)
                                <option value="{{$item->id}}">
                                    {{$item->first_name}} {{$item->last_name}}
                                </option>
                                @endforeach
                            </select>
                            @if ($errors->has('issue_created_by'))
                                <div class="laravel_error">{{ $errors->first('issue_created_by') }}</div>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="panel_fields mt-4">
                    <div class="field">
                        <label class="label">Assign To</label>
                        <div class="absolute-error">
                            <select name="assign_to" id="assign_to" class="input custom_selectBox basic-single-select">
                                <option value="" selected disabled>Select Assignee</option>
                                @foreach($user as $item)
                                <option value="{{$item->id}}">
                                    {{$item->first_name}} {{$item->last_name}}
                                </option>
                                @endforeach
                            </select>
                            @if ($errors->has('assign_to'))
                                <div class="laravel_error">{{ $errors->first('assign_to') }}</div>
                            @endif
                        </div>
                    </div>


                    <div class="field">
                        <label class="label">Status</label>
                        <div class="absolute-error">
                            <select name="status" id="status" class="input custom_selectBox basic-single-select">
                                <!-- <option value="" selected disabled>Select Status</option> -->
                                <option value="1" >Pending</option>
                                <option value="2" >Open</option>
                                <option value="3" >Completed</option>
                            </select>
                            @if ($errors->has('status'))
                                <div class="laravel_error">{{ $errors->first('status') }}</div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="px-14 mt-5">
                    <textarea class="wycwyg_editor issue-textarea" name="description" id="issue-textarea" style="height:200px">
                        {!! $issue?->notes !!}
                        </textarea>
                    @error('description')
                        <label id="requsetTitle-error" class="error">{{ $message }}</label>
                    @enderror
                </div>
            </div>
            <div class="panel mt-4">
                <div class="panel_header mb-4">
                    <h2 class="panel_title">Attach Documents</h2>
                </div>

                <!-- <div class="verify-sub-box d-flex">
                    <div class="file-loading">
                        <input id="multiplefileupload" name="images[]" type="file" accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.csv" multiple>
                     </div>
               </div> -->

               <div class="mt-2">
                    <div class="dropzone_library_customize dropzone" >
                    <div class="dz-preview dz-file-preview dz-processing dz-success dz-complete needsclick" id="my-dropzone" style="width: 81px !important;
height: 110px !important; text-align: center; cursor: pointer;">
                        <svg width="26" height="26" style="margin-top: 28px;" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                        </div>
                    </div>
                    <div id="imageSizeError"></div>
                </div>

                <!-- <div class="upload-container">
                    <div id="upload-boxes">

                    </div>
                    <div class="upload-box">
                        <span class="upload-icon">
                            <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </span>
                        <input type="file" name="images[]" id="file-upload" class="file-input" multiple onchange="handleFileUpload(this)" style="opacity: 0; position: absolute; width: 100%; height: 100%; top: 0; left: 0; cursor: pointer;">
                    </div>
                </div> -->
            </div>
            <div class="d-flex justify-content-between gap-3 flex-wrap mt-5">
                <a type="button"
                {{-- href="{{ previous_route()->getName() == getRouteAlias() . '.customer-issues.index' ? route(getRouteAlias() . '.customer-issues.index') : route(getRouteAlias() . '.customer-issue.index', []) }}" --}}
                class="btn cancel py-3 px-5">Cancel</a>

                    <button type="submit" class="btn primaryblue">Save</button>

            </div>
        </form>

        <div class="modal fade propert-modal" id="propertyModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="propertyModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg" style="max-width: 700px !important">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="propertyModalLabe">Add Property</h1>
                    </div>
                    <div class="modal-body">
                        <div class="body-wraped" style="gap: 20px;">
                            <!-- Search Filter -->
                            <div class="search-filter mb-3">
                                <input type="text" id="propertySearch" style="width: 40%;border-radius: 20px;" class="form-control" placeholder="Search...">
                            </div>

                            <!-- Property List -->
                            <table class="table table-striped border" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>{{ __('Name') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($properties as $property)
                                        <tr>
                                            <td class="property-item d-flex">
                                                <input type="radio" style="margin-top: 13px" id="property_{{ $property->id }}" name="property_id" value="{{ $property->id }}">
                                                <label style="margin-left: 10px !important; margin-top: 9px;" for="property_{{ $property->id }}">{{ $property->name }}</label>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>

                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-lg" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary btn-lg" id="addClientBtn">Add Client</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade propert-modal" id="contactModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="propertyModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg" style="max-width: 700px !important">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="propertyModalLabe">Add Contact</h1>
                    </div>
                    <div class="modal-body">
                        <div class="body-wraped" style="gap: 20px;">
                            <!-- Search Filter -->
                            <div class="search-filter mb-3">
                                <input type="text" id="propertySearch" style="width: 40%;border-radius: 20px;" class="form-control" placeholder="Search...">
                            </div>

                            <!-- Property List -->
                            <table class="table table-striped border" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>{{ __('Name') }}</th>
                                    </tr>
                                </thead>
                                <tbody id="totalContact">
                                    <!-- @foreach ($properties as $property)
                                        <tr>
                                            <td class="property-item">
                                                <input type="radio" id="property_{{ $property->id }}" name="property_id" value="{{ $property->id }}">
                                                <label for="property_{{ $property->id }}">{{ $property->name }}</label>
                                            </td>
                                        </tr>
                                    @endforeach -->
                                </tbody>
                            </table>

                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-lg" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary btn-lg" id="addContactBtn">Add Contact</button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.0.8/js/fileinput.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.0.8/js/plugins/sortable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.0.8/themes/fas/theme.min.js"></script>
    <!-- TinyMCE loaded via @push('tinymce-scripts') -->

    <script>
        $(document).ready(function() {
            $('.dropzone_library_customizess .dz-message').html(
                '<div class="drop-zone__prompt text-center"><svg width="34" height="34" viewBox="0 0 34 34" fill="none"xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd"d="M9.36275 2.85175C11.4866 1.02029 14.1955 0.00879434 17 0C22.7162 0 27.4614 4.25 27.9778 9.73038C31.3608 10.2085 34 13.0411 34 16.5176C34 20.3341 30.8168 23.375 26.9599 23.375H21.25C20.9682 23.375 20.698 23.2631 20.4987 23.0638C20.2994 22.8645 20.1875 22.5943 20.1875 22.3125C20.1875 22.0307 20.2994 21.7605 20.4987 21.5612C20.698 21.3619 20.9682 21.25 21.25 21.25H26.962C29.7054 21.25 31.875 19.0995 31.875 16.5176C31.875 13.9336 29.7075 11.7831 26.9599 11.7831H25.8974V10.7206C25.8995 6.00313 21.947 2.125 17 2.125C14.7047 2.13417 12.4883 2.96317 10.7504 4.4625C9.14175 5.848 8.30025 7.51825 8.30025 8.82938V9.78137L7.35462 9.8855C4.386 10.2106 2.125 12.648 2.125 15.5507C2.125 18.6681 4.73875 21.25 8.03463 21.25H12.75C13.0318 21.25 13.302 21.3619 13.5013 21.5612C13.7006 21.7605 13.8125 22.0307 13.8125 22.3125C13.8125 22.5943 13.7006 22.8645 13.5013 23.0638C13.302 23.2631 13.0318 23.375 12.75 23.375H8.03463C3.6295 23.375 0 19.9028 0 15.5507C0 11.8044 2.69025 8.70187 6.25175 7.91562C6.55562 6.08175 7.735 4.25425 9.36275 2.85175Z"fill="#90A0B7" /><path fill-rule="evenodd" clip-rule="evenodd"d="M16.2471 8.8102C16.3458 8.71125 16.4631 8.63275 16.5922 8.57918C16.7213 8.52562 16.8596 8.49805 16.9994 8.49805C17.1391 8.49805 17.2775 8.52562 17.4066 8.57918C17.5357 8.63275 17.6529 8.71125 17.7516 8.8102L24.1266 15.1852C24.3261 15.3847 24.4382 15.6553 24.4382 15.9374C24.4382 16.2196 24.3261 16.4902 24.1266 16.6897C23.9271 16.8892 23.6565 17.0013 23.3744 17.0013C23.0922 17.0013 22.8216 16.8892 22.6221 16.6897L18.0619 12.1273V30.8124C18.0619 31.0942 17.9499 31.3645 17.7507 31.5637C17.5514 31.763 17.2812 31.8749 16.9994 31.8749C16.7176 31.8749 16.4473 31.763 16.2481 31.5637C16.0488 31.3645 15.9369 31.0942 15.9369 30.8124V12.1273L11.3766 16.6897C11.1771 16.8892 10.9065 17.0013 10.6244 17.0013C10.3422 17.0013 10.0716 16.8892 9.87214 16.6897C9.67263 16.4902 9.56055 16.2196 9.56055 15.9374C9.56055 15.6553 9.67263 15.3847 9.87214 15.1852L16.2471 8.8102Z"fill="#90A0B7" /></svg><p class="placeholder-text font-14">Drop your logo here, or <span>browse</span></p><p class="placeholder-text font-14">PNG, JPEG, PDF , XlS, XLSX Max size: 5MB</p></div>'
            )

            tinymce.init({
                selector: '#issue-textarea',
                plugins: 'lists',
                toolbar: 'bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist',
                height: 300,
                menubar: false,
                branding: false,
                toolbar_mode: 'wrap',
                content_style: "ul { list-style-type: disc; } ol { list-style-type: decimal; }",
                /*setup: function (editor) {
                    editor.on('input', function () {
                        // Get content and set it to the div
                        document.getElementById('add_intro_letter').innerHTML = editor.getContent();
                    });
                }*/
            });
        })

        $(document).on('click', '#my-dropzone', function() {
            $('#imageSizeError').html('');
        });

        Dropzone.autoDiscover = false; // Disable auto-discovery


        var uploadedDocumentMap = {};
        const myDropzone = new Dropzone("#my-dropzone", {
            url: "{{ route(getRouteAlias() . '.issue.file-store') }}", // Replace with your server upload URL
            addRemoveLinks: true,
            dictRemoveFile: "Remove", // Proper text for remove button
            previewTemplate: `
        <div class="dz-preview dz-file-preview">

         <img class="dz-details-imagess" style="height: 60px;" src="" />
            <div class="dz-details">

                <div class="dz-icon"></div>
                <div class="dz-filename"><span data-dz-name></span></div>
            </div>
            <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>
            <div class="dz-error-message"><span data-dz-errormessage></span></div>

        </div>
    `,
            previewsContainer: ".dropzone_library_customize",
            acceptedFiles: ".webp,.jpeg,.jpg,.png,.gif,.pdf,.xls,.xlsx,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content') // Include CSRF token in the headers
            },
            init: function () {


                this.on("success", function (file, response) {
                    // Assuming your API response contains a key 'filePath'
                    $('.file-upload-form-issue').append('<input type="hidden" name="images_opportunity[]" value="' + response.name + '">');
                    uploadedDocumentMap[file.name] = response.name;
                });
                this.on("addedfile", function (file) {
                    // Extract file extension
                    let ext = file.name.split('.').pop().toLowerCase();

                    // Default icon (if no match found)
                    let iconSrc = "default-icon.png";

                    // Icons for specific file types
                    if (ext === "pdf") {
                        iconSrc = "data:image/svg+xml;base64,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";
                    } else if (ext === "xls" || ext === "xlsx") {
                        iconSrc = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48cGF0aCBkPSdNMi44NTkwMiAyLjg3ODIyTDE1LjQyOSAxLjA4MjIyQzE1LjUgMS4wNzIwNCAxNS41NzIzIDEuMDc3MjMgMTUuNjQxIDEuMDk3NDRDMTUuNzA5OCAxLjExNzY1IDE1Ljc3MzQgMS4xNTI0IDE1LjgyNzUgMS4xOTkzNUMxNS44ODE3IDEuMjQ2MjkgMTUuOTI1MSAxLjMwNDMzIDE1Ljk1NDkgMS4zNjk1MkMxNS45ODQ2IDEuNDM0NzIgMTYgMS41MDU1NSAxNiAxLjU3NzIyVjIyLjQyNDJDMTYgMjIuNDk1OCAxNS45ODQ2IDIyLjU2NjUgMTUuOTU0OSAyMi42MzE2QzE1LjkyNTIgMjIuNjk2NyAxNS44ODE5IDIyLjc1NDcgMTUuODI3OSAyMi44MDE3QzE1Ljc3MzggMjIuODQ4NiAxNS43MTAzIDIyLjg4MzQgMTUuNjQxNyAyMi45MDM2QzE1LjU3MzEgMjIuOTIzOSAxNS41MDA5IDIyLjkyOTIgMTUuNDMgMjIuOTE5MkwyLjg1ODAyIDIxLjEyMzJDMi42MTk2NCAyMS4wODkzIDIuNDAxNTIgMjAuOTcwNCAyLjI0MzcxIDIwLjc4ODZDMi4wODU5MSAyMC42MDY3IDEuOTk5MDMgMjAuMzc0IDEuOTk5MDIgMjAuMTMzMlYzLjg2ODIyQzEuOTk5MDMgMy42Mjc0MyAyLjA4NTkxIDMuMzk0NzMgMi4yNDM3MSAzLjIxMjg2QzIuNDAxNTIgMy4wMzA5OSAyLjYyMDY0IDIuOTEyMTcgMi44NTkwMiAyLjg3ODIyWk0xNyAzLjAwMDIySDIxQzIxLjI2NTIgMy4wMDAyMiAyMS41MTk2IDMuMTA1NTcgMjEuNzA3MSAzLjI5MzExQzIxLjg5NDcgMy40ODA2NCAyMiAzLjczNSAyMiA0LjAwMDIyVjIwLjAwMDJDMjIgMjAuMjY1NCAyMS44OTQ3IDIwLjUxOTggMjEuNzA3MSAyMC43MDczQzIxLjUxOTYgMjAuODk0OSAyMS4yNjUyIDIxLjAwMDIgMjEgMjEuMDAwMkgxN1YzLjAwMDIyWk0xMC4yIDEyLjAwMDJMMTMgOC4wMDAyMkgxMC42TDkuMDAwMDIgMTAuMjg2Mkw3LjQwMDAyIDguMDAwMjJINS4wMDAwMkw3LjgwMDAyIDEyLjAwMDJMNS4wMDAwMiAxNi4wMDAySDcuNDAwMDJMOS4wMDAwMiAxMy43MTQyTDEwLjYgMTYuMDAwMkgxM0wxMC4yIDEyLjAwMDJaJyBmaWxsPSdibGFjaycvPjwvc3ZnPgo=";
                    } else if (ext === "doc" || ext === "docx") {
                        iconSrc = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgaWQ9IndvcmQiPjxwYXRoIGQ9Ik0yMy41IDIuNUgxNFYxYS41LjUgMCAwIDAtLjYxMi0uNDg3bC0xMyAzQS41LjUgMCAwIDAgMCA0djE3YS41LjUgMCAwIDAgLjQyNC40OTRsMTMgMmEuNDk2LjQ5NiAwIDAgMCAuNDAxLS4xMTVBLjQ5NC40OTQgMCAwIDAgMTQgMjN2LTEuNWg5LjVhLjUuNSAwIDAgMCAuNS0uNVYzYS41LjUgMCAwIDAtLjUtLjV6TTEwLjk4MSA5LjEzOGwtMiA3Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMEw2Ljk4IDEyLjUwM2MtLjA2LS4yMTUtLjI1Ni0uMzYzLS40OC0uMzYzcy0uNDIuMTQ4LS40OC4zNjNsLTEuMDM4IDMuNjM0Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMGwtMi03YS41LjUgMCAwIDEgLjk2Mi0uMjc0bDEuMDM4IDMuNjM1Yy4xMjEuNDMuODQuNDMuOTYxIDBsMS4wMzgtMy42MzRjLjEyMy0uNDI3Ljg0LS40MjYuOTYzIDBsMS4wMzggMy42MzVjLjEyMS40My44NC40My45NjEgMGwxLjAzOC0zLjYzNGEuNS41IDAgMCAxIC45NjMuMjczek0yMyAyMC41aC05di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoNy41YS41LjUgMCAwIDAgMC0xSDE0di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoOXYxN3oiPjwvcGF0aD48L3N2Zz4=";
                    }else if (ext === "png" || ext === "jpg" || ext === "webp" || ext === "jpeg" || ext === "jpeg") {
                        const reader = new FileReader();
                        reader.onload = function (e) {
                            $(file.previewElement).find(".dz-details-imagess").css("width", '100%');
                            // Find the preview image element and set the source
                            $(file.previewElement).find(".dz-details-imagess").attr("src", e.target.result);
                        };
                        reader.readAsDataURL(file);
                    }
                    $(file.previewElement).find(".dz-details-imagess").css("width", '100%');
                    // Update the preview element with the corresponding icon
                    if (ext === "docx" || ext === "doc" || ext === "xlsx" || ext === "xls" || ext === "pdf") {
                        $(file.previewElement).find(".dz-details-imagess").attr("src", iconSrc);
                    }
                });

                this.on("removedfile", function (file) {
                    console.info("File removed: ", file.name); // Log file removal

                    file.previewElement.remove();
                    var name = '';
                    if (typeof file.file_name !== 'undefined') {
                        name = file.file_name;
                    } else {
                        name = uploadedDocumentMap[file.name];
                    }

                    // Send an AJAX request to delete the file from the server
                    fetch("{{ route(getRouteAlias() . '.issue.file-delete') }}", {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({ filename: name })
                    })
                        .then(response => response.json())
                        .then(data => {
                            console.info('File deleted from server:', data);
                            // You can add additional logic here to update UI or handle response data
                        })
                        .catch(error => {
                            console.error('Error deleting file from server:', error);
                        });
                    $('.file-upload-form-issue').find('input[name="images_opportunity[]"][value="' + name + '"]').remove();
                });

                // Add error handling if needed
                this.on("error", function (file, errorMessage) {
                    console.error("Dropzone error:", errorMessage);
                });
            }
        });

    </script>

    <script>
        $("#multiplefileupload").fileinput({
            'theme': 'fa',
            'uploadUrl': '#',
            showRemove: false,
            showUpload: false,
            showZoom: false,
            showCaption: false,
            browseClass: "btn btn-danger",
            browseLabel: "",
            browseIcon: `<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
            overwriteInitial: false,
            showUploadStats: false,
            initialPreviewAsData: true,

            fileActionSettings: {
                showUpload: false,
                showZoom: false,
                removeIcon: "<i class='fa fa-times'></i>",
            },

            filetypeSettings: {
                'pdf': function(vType, vName) { return vType === 'application/pdf' || vName.match(/\.(pdf)$/i); },
                'doc': function(vType, vName) { return vType === 'application/msword' || vName.match(/\.(doc|docx)$/i); },
                'excel': function(vType, vName) { return vType === 'application/vnd.ms-excel' || vType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || vName.match(/\.(xls|xlsx|csv)$/i); }
            },

            previewFileIconSettings: {
                'pdf': '<img src="{{ asset('admin_assets/images/pdf.png') }}" alt="PDF File" style="width:100%; height:100%; margin-top: 27px;">',
                'doc': '<img src="{{ asset('admin_assets/images/doc.png') }}" alt="Word File" style="width:100%; height:100%; margin-top: 27px;">',
                'excel': '<img src="{{ asset('admin_assets/images/xlsx.png') }}" alt="Excel File" style="width:100%; height:100%; margin-top: 27px;">',
                'other': '<img src="{{ asset('admin_assets/images/file.png') }}" alt="Excel File" style="width:100%; height:100%; margin-top: 27px;">'
            },

            previewFileExtSettings: {
                'pdf': function(ext) { return ext.match(/(pdf)$/i); },
                'doc': function(ext) { return ext.match(/(doc|docx)$/i); },
                'excel': function(ext) { return ext.match(/(xls|xlsx|csv)$/i); }
            }
        });
    </script>
    <!-- <script>
         $("#multiplefileupload").fileinput({
        'theme': 'fa',
        'uploadUrl': '#',
        showRemove: false,
        showUpload: false,
        showZoom: false,
        showCaption: false,
        browseClass: "btn btn-danger",
        browseLabel: "",
        browseIcon: `<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
        overwriteInitial: false,
        initialPreviewAsData: true,
        fileActionSettings :{
        	showUpload: false,
        	showZoom: false,
          removeIcon: "<i class='fa fa-times'></i>",
        }
    });
    </script> -->
    <script>
        function getContact(){
            var acc_id=$('.compnyids').val();
            // alert(acc_id);
            // alert(acc_id);
            var url = "{{ route('organization.get-contact-details', ['contact_id' => ':id']) }}";
            url = url.replace(':id', acc_id);

            $.ajax({
                method: "GET",
                url: url,
                success: function(response) {
                    console.info(response);
                    var dr = ``;
                    $.each(response, function(index, item) {
                        dr += `<tr>
                <td class="property-item d-flex">
                    <input type="radio" style="margin-top: 13px" id="contact_${item.id}" name="contact_selected_id" value="${item.id}">
                    <label style="margin-left: 10px !important; margin-top: 9px;" for="contact_${item.id}">${item.first_name} ${item.last_name}</label>
                </td>
            </tr>`;
                    });
                    $('#totalContact').html(dr);

// $('input[name="first_name"]').val('');
// $('input[name="last_name"]').val('');
// $('input[name="title_role"]').val('');
// $('input[name="phone_number"]').val('');
// $('input[name="second_phone"]').val('');
// $('input[name="email"]').val('');
// $('input[name="second_email"]').val('');
// $('input[name="mailing_address"]').val('');


// Set the value for the select field and disable it
// $('select[name="role"]').unselect;


                    // $('#contactModal').modal('hide');
                    toastr.success('Contact details successfully loaded.');
                },
                error: function(response) {
                    // $('#contactModal').modal('hide');
                    toastr.warning('Failed to fetch contact details.');
                }
            });
        }

    </script>
    <script>
        $(document).ready(function() {

            $('#propertySearch').on('input', function() {
                var searchValue = $(this).val().toLowerCase();

                $('.property-item').each(function() {
                    var propertyName = $(this).find('label').text().toLowerCase();
                    // Show the item if the property name contains the search value
                    $(this).toggle(propertyName.indexOf(searchValue) > -1);
                });
            });
            $('#addClientBtn').on('click', function() {
                var propertyId = $('input[name="property_id"]:checked').val();

                if (!propertyId) {
                    $('#propertyModal').modal('hide');
                    toastr.warning('Please select a property first.');
                    return;
                }

                var url = "{{ route('organization.get-property-details', ['property_id' => ':id']) }}";
                url = url.replace(':id', propertyId);

                $.ajax({
                    method: "GET",
                    url: url,
                    success: function(response) {
                        console.info(response);
                        var response2=response.default;
                        // Assuming your response contains the property details
                        $('input[name="property_name"]').val(response.name);
                        $('input[name="address1"]').val(response.address1);
                        $('input[name="address2"]').val(response.address2);
                        $('input[name="city"]').val(response.city);
                        $('input[name="state"]').val(response.state);
                        $('input[name="zip_code"]').val(response.zip_code);
                        $('select[name="company_id"]').val(response.company_id).trigger('change');



                        $('input[name="first_name"]').val(response2.first_name);
                        $('input[name="last_name"]').val(response2.last_name);
                        $('input[name="title_role"]').val(response2.title);
                        $('input[name="phone_number"]').val(response2.phone_number);
                        $('input[name="second_phone"]').val(response2.second_phone);
                        $('input[name="email"]').val(response2.email);
                        $('input[name="second_email"]').val(response2.second_email);
                        $('input[name="mailing_address"]').val(response2.mailing_address);


// Set the value for the select field and disable it
                        $('select[name="role"]').val(response2.role).trigger('change');

                        $('#propertyModal').modal('hide');
                        toastr.success('Property details successfully loaded.');
                    },
                    error: function(response) {
                        $('#propertyModal').modal('hide');
                        toastr.warning('Failed to fetch property details.');
                    }
                });
            });
        });
        function handleFileUpload(input) {
            const files = input.files;
            const uploadBoxesContainer = document.getElementById('upload-boxes');

            // Clear previous previews
            // uploadBoxesContainer.innerHTML = '';

            Array.from(files).forEach(file => {
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewBox = document.createElement('div');
                        previewBox.classList.add('upload-box');
                        previewBox.innerHTML = `
                    <img src="${e.target.result}" alt="Uploaded File" class="file-preview">
                    `;
                        uploadBoxesContainer.appendChild(previewBox);
                    };
                    reader.readAsDataURL(file);
                }
            });
        }
    </script>

    @endpush
@endsection
