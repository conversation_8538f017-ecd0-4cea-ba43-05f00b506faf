<x-jet.listing-table>
    <x-slot name="header">
        <th>{{ __('Issue ID') }}</th>
        <th>{{ __('Property Name') }}</th>
        <th>{{ __('Account') }}</th>
        <th>{{ __('Category') }}</th>
        <th>{{ __('Subject') }}</th>
        <th>{{ __('Created By') }}</th>
        <th>{{ __('Date and Time') }}</th>
        <th>{{ __('Status') }}</th>
        <th>{{ __('Action') }}</th>
    </x-slot>
    <x-slot name="body">
        @forelse($issues as $issue)
            <tr>
                <td>{{ $issue->id }}</td>
                <td>{{ $issue->property_name }}</td>
                <td>{{ $issue->contactInformation->first_name ?? 'N/A' }} {{ $issue->contactInformation->last_name ?? 'N/A' }}</td>
                <td>{{ $issue->category }}</td>
                <td>{{ $issue->subject }}</td>
                <td>{{ $issue->createdBy->name ?? 'N/A' }}</td>
                <td>{{ $issue->created_at->format('Y-m-d H:i') }}</td>
                <td>{{ $issue->status }}</td>
                <td>
                    <div class="dropdown mx-auto w-fit">
                        <div id="dropdown{{ $issue->id }}" data-toggle="dropdown" aria-expanded="false">
                            <img height="24px" width="24px"
                                src="{{ asset('admin_assets/images/icons/vertical-dots.svg') }}" alt="vertical dots">
                        </div>
                        <ul class="dropdown-menu" aria-labelledby="dropdown{{ $issue->id }}">
                            <li><a class="dropdown-item"
                                    href="{{ route(getRouteAlias() . '.customer-issue.detail', encodeId($issue->id)) }}">View
                                    Details</a></li>
                            <li><a class="dropdown-item"
                                    href="{{ route(getRouteAlias() . '.customer-issue.edit', encodeId($issue->id)) }}">Edit
                                    Details</a></li>
                            <li><a class="dropdown-item"
                                    href="#" onclick="showModal({{ $issue->id }})">Delete</a></li>
                        </ul>
                    </div>
                </td>
            </tr>
        @empty
            @include('layouts.partials.no-data')
        @endforelse
    </x-slot>
    <x-slot name="pagination">
        @if (isset($issues))
            {{ $issues->links() }}
        @endif
    </x-slot>
</x-jet.listing-table>
