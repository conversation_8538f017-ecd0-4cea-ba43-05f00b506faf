@extends('layouts.admin.master')
@section('title', 'Issues')
@section('section')
<style>
    .status-completed{
        color: #22B8D6 !important;
    }
    .dropdown-toggle::after {
    display: none !important;

}
</style>
    <section class="dashboard_main pb-5 issues_table_filters">
    <div class="showMessage"></div>
        <div class="table_filter_header mb-4">
            <h2 class="sub_heading">Issues</h2>

            <div class="filters">
                <input type="search" placeholder="Search" id="filter_search" class="issues_Detail_Search filter_search">

                <form method="POST" action="{{ route(getRouteAlias() . '.export.issues') }}">
                    @csrf
                    <!-- <button type="submit" class="btn primaryblue transparent px-5">Export Issues</button> -->
                    <button
                        type="submit"

                        class="btn primaryblue transparent px-5"
                        style="border: 1px solid var(--lightgray); color: black !important"
                    >
                        <svg
                            width="16"
                            height="16"
                            style="margin-top: 3px"
                            viewBox="0 0 18 18"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M6 12L9 9M9 9L12 12M9 9V15.75M15 12.5571C15.9161 11.8005 16.5 10.656 16.5 9.375C16.5 7.09683 14.6532 5.25 12.375 5.25C12.2111 5.25 12.0578 5.1645 11.9746 5.0233C10.9965 3.36363 9.19082 2.25 7.125 2.25C4.0184 2.25 1.5 4.7684 1.5 7.875C1.5 9.42458 2.12659 10.8278 3.14021 11.8451"
                                stroke="#51566C"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                        <span
                            class=""
                            style="font-size: 12px; color: #514f6e !important"
                            >&nbsp;Export</span
                        >
                    </button>
                </form>


                {{-- @can('add_issue') --}}
{{--
                    <form method="POST" action="" id="issue-import" class="file_upload_button_form upload_file_wrapper w-fit" enctype="multipart/form-data">
                        @csrf
                        <!-- <label class="btn primaryblue transparent px-5" for="import_file_data">Import Issues</label> -->
                        <label
                        class="btn primaryblue primaryblue22 transparent px-5"
                        for="import_file_data"
                        style="
                            border: 1px solid var(--lightgray);
                            background-color: white !important;
                            margin-top: 3px !important;
                        "
                        ><svg
                            style="margin-top: 3px"
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M5 5L8 8M8 8L11 5M8 8V1.25M14 4.44287C14.9161 5.19947 15.5 6.34405 15.5 7.625C15.5 9.90317 13.6532 11.75 11.375 11.75C11.2111 11.75 11.0578 11.8355 10.9746 11.9767C9.99654 13.6364 8.19082 14.75 6.125 14.75C3.0184 14.75 0.5 12.2316 0.5 9.125C0.5 7.57542 1.12659 6.17219 2.14021 5.15485"
                                stroke="#51566C"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                        &nbsp;
                        <span style="font-size: 12px; color: #514f6e !important"
                            >Import</span
                        ></label
                    >
                        <input class="input_file d-none" type="file" name="file" id="import_file_data" accept=".xls, .xlsx">
                    </form>
--}}
                    <a href="{{ route('organization.create.customer-issue') }}" class="btn primaryblue">+ Add Issue</a>
                {{-- @endcan --}}
            </div>
        </div>

        <div class="showMessage"></div>

        <table class="table table-striped custom_datatable yajra-datatable" style="width:100%">
            <thead>
                <tr>
                    <th>{{ __('ISSUE #') }}</th>
                    <th>{{ __('Subject') }}</th>
                    <th>{{ __('Property Name') }}</th>
                    <th>{{ __('Account') }}</th>
                    <th>{{ __('Category') }}</th>

                    <th>{{ __('Created By') }}</th>
                    <th>{{ __('Date & Time') }}</th>
                    <th class="text-center">{{ __('Status') }}</th>
                    <th>{{ __('Action') }}</th>
                </tr>
            </thead>
            <tbody>

            </tbody>
        </table>

    </section>
    @include('layouts.admin.confirmation-modal')
    @include('layouts.partials.success-modal')
    <div class="modal-small success-modal modal fade" id="requestModal" data-keyboard="false" tabindex="-1"
        aria-labelledby="requestModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">

                <div class="modal-body">
                    <div class="text-center mb-4">
                        <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/check-icon.png') }}"
                            alt="check icon">
                    </div>

                    <h2 class="title text-center">Request Created</h2>
                    <p class="para mt-3 text-center">Your request has been successfully created.</p>
                    {{-- <a href="{{ route('opportunity.customer-issues.index') }}" type="button"
                        class="btn primaryblue w-100 mt-5 " data-dismiss="modal">Go to list</a> --}}
                </div>

            </div>

        </div>
    </div>

    @push('scripts')
        @include('organization.customer-issue.script') <!-- Adjust script path as needed -->
        @if (!empty(Session::get('showModal')))
            <script>
                $(function() {
                    $('#requestModal').modal('show');
                });
            </script>
        @endif
    @endpush
@endsection
