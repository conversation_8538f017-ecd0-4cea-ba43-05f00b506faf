<script type="text/javascript">
    var issues_table = null;
    $(document).ready(function() {
        // Initialize DataTable
        issues_table = $('.yajra-datatable').DataTable({
            processing: true,
            responsive: true,
            serverSide: true,
            ajax: {
                url: "{{ route(getRouteAlias() . '.customer-issues.index') }}",
                data: function(d) {
                    // Add custom search filter value
                    d.status = status;
                }
            },
            columns: [
                { data: 'issue_id', name: 'issue_id', orderable: false },
                { data: 'subject', name: 'subject', orderable: false },
                { data: 'property_name', name: 'property_name', orderable: false },
                { data: 'account', name: 'account', orderable: false },
                { data: 'category', name: 'category', orderable: false },

                { data: 'created_by', name: 'created_by', orderable: false },
                { data: 'date_time', name: 'date_time', orderable: false },
                { data: 'status', name: 'status', orderable: false },
                { data: 'action', name: 'action', orderable: false }
            ],
            language: {
                zeroRecords: "Sorry we could not find any results",
                paginate: {
                    "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                    "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
                }
            },
            dom: '<"top">rt<"bottom"lip><"clear">',
        });

        // Custom Search Filter
        $('#filter_search').on('keyup', function() {
            // issues_table.draw();
            issues_table.search(this.value).draw();
        });

        // Handle file import
        var fileInput = $(".upload_file_wrapper .input_file");
        var label = $(".upload_file_wrapper label");
        var selectedFile;

        fileInput.on("change", function(event) {
            selectedFile = event.target.files[0];

            // Check if the selected file is an Excel file
            if (
                selectedFile.type === "application/vnd.ms-excel" ||
                selectedFile.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            ) {
                var fileName = selectedFile.name;
                label.text(fileName);

                $(this).parent(".file_upload_button_form").submit();
            } else {
                fileInput.val("");
                label.text("Import Issues");

                var newElement = $("<label class='error custom_error'>Only Excel File Is Allowed</label>");
                $(this).after(newElement);
                newElement.fadeOut(3000, function() {
                    $(this).remove();
                });
            }
        });

        $(".file_upload_button_form").on("submit", function(e) {
            e.preventDefault();
            let myForm = document.getElementById("issue-import");
            let formData = new FormData(myForm);
            var url = "{{ URL::route(getRouteAlias() . '.customer-issue.file-import') }}";

            $.ajax({
                method: "post",
                url: url,
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function(response) {
                    $('.showMessage').html(`
                        <div class="alert alert-success alert-dismissible fade show showMessage">
                            File imported successfully!.
                        </div>
                    `);
                    issues_table.draw();
                },
                error: function(response) {
                    $('#issue-import')[0].reset();
                    $.each(response.responseJSON.errors, function(field_name, error) {
                        $('.showMessage').html('');
                        $('.showMessage').append(`
                            <div class="alert alert-danger alert-dismissible fade show showMessage">
                                ${error}
                            </div>
                        `);
                    });
                }
            });
        });
    });

    // Function to show modal for deletion
    function showModal(id) {
        $('.dynamic-content-data').html('');
        $('.dynamic-content-data').append(
            `<h2 class="title text-center">Delete Request</h2>
            <p class="para mt-3 text-center">Are you sure you want to delete this request.</p>`
        );
        deleteId = id;
    }

    // Handle delete confirmation
    $('.deleteConfirmation').on('click', function() {
        deleteData();
    });

    // Function to handle delete request
    function deleteData() {
        var url = "{{ URL::route(getRouteAlias() . '.customer-issue.delete', ':id') }}";
        url = url.replace(':id', deleteId);

        $.ajax({
            method: "DELETE",
            url: url,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            success: function(response) {
                $('#DeleteModal').modal('hide');
                $('#successModal').modal('show');
                $('.dynamic-success-data').html('');
                $('.dynamic-success-data').append(
                    `<h2 class="title text-center">Done</h2>
                    <p class="para mt-3 text-center">Customer Issue Deleted Successfully!</p>`
                );
                issues_table.draw();
            },
            error: function(response) {
                $('#DeleteModal').modal('hide');
            }
        });
    }
</script>
