let searchTimeout;
$(document).ready(function() {
    // Initialize DataTable
    window.bookOfBusinessTable = initDataTableForBookOfBusiness();

    // Custom Search Filter
    $('#filter_search').on('keyup', function() {
        window.bookOfBusinessTable.search(this.value).draw();
    });

    // Handle Bulk Upload button click - Show modal
    $('#bulk-upload-btn').on('click', function() {
        $('#fileUploadModal').modal('show');
        resetFileUploadModal();
    });

    // Handle upload area click
    $(document).on('click', '#uploadArea, #uploadTrigger', function() {
        $('#import_file_input').click();
    });

    // Handle file selection
    $('#import_file_input').on('change', function() {
        const files = Array.from(this.files);
        if (files.length > 0) {
            displaySelectedFiles(files);
        }
    });

    // Handle drag and drop
    $('#uploadArea').on('dragover', function(e) {
        e.preventDefault();
        $(this).css('border-color', '#3b82f6');
        $(this).css('background-color', '#eff6ff');
    });

    $('#uploadArea').on('dragleave', function(e) {
        e.preventDefault();
        $(this).css('border-color', '#d1d5db');
        $(this).css('background-color', '#f9fafb');
    });

    $('#uploadArea').on('drop', function(e) {
        e.preventDefault();
        $(this).css('border-color', '#d1d5db');
        $(this).css('background-color', '#f9fafb');

        const files = Array.from(e.originalEvent.dataTransfer.files);
        if (files.length > 0) {
            displaySelectedFiles(files);
        }
    });

    // Handle attach files button
    $('#attachFilesBtn').on('click', function() {
        const selectedFiles = getSelectedFiles();
        if (selectedFiles.length > 0) {
            $('#fileUploadModal').modal('hide');
            // Process files one by one
            processFiles(selectedFiles);
        } else {
            showModalError('Please select at least one file to upload.');
        }
    });

    // Handle file removal
    $(document).on('click', '.remove-file', function() {
        const fileIndex = $(this).data('file-index');
        removeFileFromList(fileIndex);
    });

    $(document).on('click','.changeAccountOwnerBtn',function () {
        let opportunityId = $(this).attr('data-opportunity-id');
        $('#account-manager-oppor-id').val(opportunityId);
    })

    $(document).on('click','.changeOPManagerBtn',function () {
        let opportunityId = $(this).attr('data-opportunity-id');
        $('#account-manager-oppor-id').val(opportunityId);
        getOperationManagerListing();
        $('#changeOPManager').modal('show');
    });

    $(document).on("keyup", "#account-manager-search", function () {
        clearTimeout(searchTimeout); // reset timer on every keyup

        let input = $(this); // cache input element

        searchTimeout = setTimeout(function () {
            let value = input.val().toLowerCase();
            let rows = $("#operationManagersList tr");
            let visibleCount = 0;

            rows.each(function () {
                let match = $(this).text().toLowerCase().indexOf(value) > -1;
                $(this).toggle(match);
                if (match) visibleCount++;
            });

            // Remove any old "no data" row first
            $("#operationManagersList .no-data").remove();

            if (visibleCount === 0) {
                $("#operationManagersList").append(
                    `<tr class="no-data">
                    <td class="text-center fs-16">No data found</td>
                </tr>`
                );
            }
        }, 500);
    });

    $(document).on('click','.changeOPManagerBtn',function () {
        let opportunityId = $(this).attr('data-opportunity-id');
        $('#account-manager-oppor-id').val(opportunityId);
        getOperationManagerListing();
        $('#changeOPManager').modal('show');
    });

    $(document).on('click','#addOPManager', function() {
        let selectedManager = $('input[name="operation_manager"]:checked').val();
        let opportunityId = $('#account-manager-oppor-id').val();

        if (selectedManager && opportunityId) {

            $.ajax({
                url: '/organization/update-operation-manager',
                method: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    opportunity_id: opportunityId,
                    operation_manager_id: selectedManager
                },
                success: function(response) {
                    toastr.success(response.message);
                    window.bookOfBusinessTable.ajax.reload();
                    $('#changeOPManager').modal('hide');
                },
                error: function(xhr, status, error) {
                    toastr.error(xhr.responseJSON.message);
                }
            })
        } else {
            toastr.warning('Please select an operation manager');
        }
    });

    // Handle account owner selection
    $(document).on('click','.changeAccountOwnerBtn', function() {
        let opportunityId = $(this).attr('data-opportunity-id');
        $('#account-owner-oppor-id').val(opportunityId);
        getAccountOwnerListing();
        $('#changeAccountOwner').modal('show');
    });

    $(document).on('click','#addAccountOwner', function() {
        let selectedOwner = $('input[name="account_owner"]:checked').val();
        let opportunityId = $('#account-owner-oppor-id').val();

        if (selectedOwner && opportunityId) {

            $.ajax({
                url: '/organization/update-account-owner',
                method: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    opportunity_id: opportunityId,
                    account_owner_id: selectedOwner
                },
                success: function(response) {
                    toastr.success(response.message);
                    window.bookOfBusinessTable.ajax.reload();
                    $('#changeAccountOwner').modal('hide');
                },
                error: function(xhr, status, error) {
                    toastr.error(xhr.responseJSON.message);
                }
            })
        } else {
            toastr.warning('Please select an account owner');
        }
    });
});

function initDataTableForBookOfBusiness() {
    return $('#bookOfBusinessDataTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        searching: false,
        ajax: {
            url: window.location.origin + '/organization/book-of-business/data',
            type: 'GET',
            error: function(xhr, error, thrown) {
                console.error('DataTable Ajax Error:', error, thrown);
                console.error('Response:', xhr.responseText);
            }
        },
        columnDefs: [
            { orderable: false, targets: '_all' }
        ],
        columns: [
            { data: 'job_no', name: 'job_no' },
            { data: 'opportunity_name', name: 'opportunity_name' },
            { data: 'property_name', name: 'property_name' },
            { data: 'account_name', name: 'account_name' },
            { data: 'account_owner', name: 'account_owner' },
            { data: 'maintenance_contract', name: 'maintenance_contract', orderable: false },
            { data: 'operation_manager', name: 'operation_manager' },
            { data: 'contract_start_date', name: 'contract_start_date' },
            { data: 'contract_end_date', name: 'contract_end_date' },
            // { data: 'status', name: 'status' },
            {
                data: 'action',
                name: 'action',
                orderable: false,
                searchable: false,
                className: 'fixed-col'
            }
        ],
        language: {
            processing: '<div class="d-flex justify-content-center"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>',
            zeroRecords: "No matching records found",
            emptyTable: "No data available in table",
            paginate: {
                "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
            }
        },
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        // dom: '<"top"l>rt<"bottom"ip><"clear">',
        dom: '<"top">rt<"bottom"lip><"clear">',
        order: [[0, 'desc']], // Order by Job # descending
        drawCallback: function(settings) {
            // Reinitialize dropdowns after table redraw
            $('[data-toggle="dropdown"]').dropdown();
        }
    });
}

function getOperationManagerListing() {
    $.ajax({
        method: "GET",
        data: {
            'search': search
        },
        url: "/organization/get-operation-manager",
        success: function(response) {
            if(response.length > 0) {
                $('#operationManagersList').empty();
                response.forEach(function(manager) {
                    $('#operationManagersList').append(
                        `<tr class="table-item">
                            <td class="table-item d-flex">
                                <input type="radio" id="om_item_${manager.id}" name="operation_manager" value="${manager.id}">
                                <label for="om_item_${manager.id}">${manager.first_name} ${manager.last_name}</label>
                            </td>
                        </tr>`
                    );
                });
            } else {
                $('#operationManagersList').empty().append(
                    `<tr class="table-item">
                        <td class="table-item fs-16">No data found</td>
                    </tr>`
                );
            }
        },
        error: function(response) {}
    })
}

function getAccountOwnerListing() {
    $.ajax({
        method: "GET",
        url: "/organization/get-account-owner",
        success: function(response) {
            if(response.length > 0) {
                $('#accountOwnersList').empty();
                response.forEach(function(owner) {
                    $('#accountOwnersList').append(
                        `<tr class="table-item">
                            <td class="table-item d-flex">
                                <input type="radio" id="aq_item_${owner.id}" name="account_owner" value="${owner.id}">
                                <label for="aq_item_${owner.id}">${owner.first_name} ${owner.last_name}</label>
                            </td>
                        </tr>`
                    );
                });
            } else {
                $('#accountOwnersList').empty().append(
                    `<tr class="table-item">
                        <td class="table-item fs-16">No data found</td>
                    </tr>`
                );
            }
        },
        error: function(response) {}
    })
}

function handleSingleFileUpload(file) {
    // Create FormData
    const formData = new FormData();
    formData.append('import_file', file);
    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

    // Make AJAX request
    $.ajax({
        url: '/organization/book-of-business/import',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            hideImportProgress();
            if (response.success) {
                showImportSuccess(response);
                // Reload the DataTable to show new data
                window.bookOfBusinessTable.ajax.reload();
            } else {
                showImportError(response.message, response.errors);
            }
            // Reset file input
            $('#import_file_input').val('');
        },
        error: function(xhr) {
            hideImportProgress();
            let errorMessage = 'Import failed. Please try again.';
            let errors = [];

            if (xhr.responseJSON) {
                errorMessage = xhr.responseJSON.message || errorMessage;
                errors = xhr.responseJSON.errors || [];
            }

            showImportError(errorMessage, errors);
            $('#import_file_input').val('');
        }
    });
}

function handleMultipleFileUpload(files) {
    // Create FormData
    const formData = new FormData();

    // Append multiple files
    files.forEach((file, index) => {
        formData.append('import_files[]', file);
    });
    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

    // Make AJAX request
    $.ajax({
        url: '/organization/book-of-business/import',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            hideImportProgress();
            if (response.success) {
                showMultipleFileImportSuccess(response);
                // Reload the DataTable to show new data
                window.bookOfBusinessTable.ajax.reload();
            } else {
                showImportError(response.message, response.errors);
            }
            // Reset file input
            $('#import_file_input').val('');
        },
        error: function(xhr) {
            hideImportProgress();
            let errorMessage = 'Import failed. Please try again.';
            let errors = [];

            if (xhr.responseJSON) {
                errorMessage = xhr.responseJSON.message || errorMessage;
                errors = xhr.responseJSON.errors || [];
            }

            showImportError(errorMessage, errors);
            $('#import_file_input').val('');
        }
    });
}

function showImportProgress() {
    $('#import-progress').show();
    $('#import-results').hide();
}

function hideImportProgress() {
    $('#import-progress').hide();
}

function showImportSuccess(response) {
    let html = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Success!</strong> ${response.message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;

    if (response.errors && response.errors.length > 0) {
        html += `
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <strong>Some errors occurred:</strong>
                <ul class="mb-0 mt-2">
                    ${response.errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;
    }

    $('#import-results').html(html).show();

    // Auto-hide success message after 5 seconds
    setTimeout(function() {
        $('.alert-success').fadeOut();
    }, 5000);
}

function showMultipleFileImportSuccess(response) {
    let alertType = response.success ? 'success' : 'danger';
    let icon = response.success ? 'Success!' : 'Error!';

    let html = `
        <div class="alert alert-${alertType} alert-dismissible fade show" role="alert">
            <strong>${icon}</strong> ${response.message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;

    // Show detailed file processing results
    if (response.processed_files && response.processed_files.length > 0) {
        html += `
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <strong>File Processing Details:</strong>
                <div class="mt-2">
        `;

        response.processed_files.forEach(file => {
            const statusClass = file.error_count > 0 ? 'text-warning' : 'text-success';
            const statusIcon = file.error_count > 0 ? '⚠️' : '✅';

            html += `
                <div class="mb-2 p-2 border rounded">
                    <strong class="${statusClass}">${statusIcon} ${file.file_name}</strong><br>
                    <small>Success: ${file.success_count} records | Errors: ${file.error_count}</small>
            `;

            if (file.errors && file.errors.length > 0) {
                html += `
                    <details class="mt-1">
                        <summary class="text-muted" style="cursor: pointer;">View errors</summary>
                        <ul class="mt-1 mb-0">
                            ${file.errors.map(error => `<li class="small text-danger">${error}</li>`).join('')}
                        </ul>
                    </details>
                `;
            }

            html += `</div>`;
        });

        html += `
                </div>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;
    }

    // Show overall errors if any
    if (response.errors && response.errors.length > 0) {
        html += `
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <strong>All Errors Summary:</strong>
                <ul class="mb-0 mt-2">
                    ${response.errors.slice(0, 10).map(error => `<li>${error}</li>`).join('')}
                    ${response.errors.length > 10 ? `<li class="text-muted">...and ${response.errors.length - 10} more errors</li>` : ''}
                </ul>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;
    }

    $('#import-results').html(html).show();

    // Auto-hide success message after 8 seconds (longer for multiple files)
    setTimeout(function() {
        $('.alert-success').fadeOut();
    }, 8000);
}

function showImportError(message, errors = []) {
    let html = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>Error!</strong> ${message}
    `;

    if (errors && errors.length > 0) {
        html += `
            <ul class="mb-0 mt-2">
                ${errors.map(error => `<li>${error}</li>`).join('')}
            </ul>
        `;
    }

    html += `
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;

    $('#import-results').html(html).show();
}

// Modal file management functions
let selectedFiles = [];

function resetFileUploadModal() {
    selectedFiles = [];
    $('#fileList').empty();
    $('#import_file_input').val('');
    $('.modal-error').remove();
}

function displaySelectedFiles(files) {
    files.forEach((file, index) => {
        // Validate file type
        const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];
        if (!allowedTypes.includes(file.type)) {
            showModalError(`${file.name} is not a valid Excel file. Please select .xlsx or .xls files only.`);
            return;
        }

        // Validate file size (10MB max)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            showModalError(`${file.name} is too large. Please select files smaller than 10MB.`);
            return;
        }

        // Add to selected files if not already present
        const existingIndex = selectedFiles.findIndex(f => f.name === file.name && f.size === file.size);
        if (existingIndex === -1) {
            selectedFiles.push(file);
            addFileToList(file, selectedFiles.length - 1);
        }
    });
}

function addFileToList(file, index) {
    const fileSize = formatFileSize(file.size);
    const fileExtension = getFileExtension(file.name);

    const fileHtml = `
        <div class="file-item" data-file-index="${index}">
            <div class="file-icon">
                <span>${fileExtension}</span>
            </div>
            <div class="file-info">
                <div class="file-name">${file.name}</div>
                <div class="file-size">${fileSize}</div>
                <div class="progress-container">
                    <div class="progress">
                        <div class="progress-bar"></div>
                    </div>
                    <div class="progress-text">100%</div>
                </div>
            </div>
            <button type="button" class="remove-file" data-file-index="${index}">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </div>
    `;

    $('#fileList').append(fileHtml);
}

function removeFileFromList(index) {
    selectedFiles.splice(index, 1);
    $(`.file-item[data-file-index="${index}"]`).remove();

    // Update indices for remaining files
    $('.file-item').each(function(i) {
        $(this).attr('data-file-index', i);
        $(this).find('.remove-file').attr('data-file-index', i);
    });
}

function getSelectedFiles() {
    return selectedFiles;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function getFileExtension(filename) {
    return filename.split('.').pop().toUpperCase();
}

function showModalError(message) {
    $('.modal-error').remove();
    const errorHtml = `
        <div class="alert alert-danger modal-error">
            ${message}
        </div>
    `;
    $('#uploadArea').after(errorHtml);
}

function processFiles(files) {
    if (files.length === 0) return;

    showImportProgress();

    if (files.length === 1) {
        // Single file upload (backward compatibility)
        handleSingleFileUpload(files[0]);
    } else {
        // Multiple files upload
        handleMultipleFileUpload(files);
    }
}

