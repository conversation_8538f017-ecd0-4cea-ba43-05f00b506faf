<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Proposal extends Model
{
    use HasFactory;

    protected $fillable = [
        'opportunity_id',
        'contract_start_date',
        'contract_end_date',
        'contract_terms_months',
        'contract_amount'
    ];

    public function opportunity()
    {
        return $this->belongsTo(Opportunity::class);
    }
}
