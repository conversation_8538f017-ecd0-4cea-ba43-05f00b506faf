<?php

namespace App\Models;

use App\Enums\OpportunityStatus;
use App\Models\Traits\OpportunityRelationsTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Opportunity extends Model
{
    use HasFactory, OpportunityRelationsTrait;

    protected $fillable = [
        'opportunity_name',
        'account_id',
        'property_id',
        'contact_id',
        'opportunity_type',
        'customer_bid_form',
        'bid_due_date',
        'net_new',
        'lead_source',
        'opportunity_owner_id',
        'sale_person_id',
        'estimator_id',
        'status',
        'reason',
        'closed_lost_reasons',
        'created_by',
        'request_information',
        'organization_id',
        'creator_id',
        'job_no',
        'sales_order_number',
        'client_show',
        'opportunity_count',
        'division_id',
        'service_line_id',
        'price_model',
    ];

    const OPPORTUNITY_STATUS_CLOSED_LOST = OpportunityStatus::CLOSED_LOST;

    protected $casts = [
        'closed_lost_reasons' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $highestJobNo = static::where('organization_id', $model->organization_id)->latest()->max('job_no') ?? 0; // Get the highest job_no value
            // $model->organization_id = $organization_id;
            $model->job_no = $highestJobNo + 1; // Increment the highest value by 1
            $model->sales_order_number = 0;
        });

        // Logic for setting opportunity_count
        static::creating(function ($model) {
            // Get the maximum opportunity_count for the user and increment it
            $highestOpportunityCount = static::where('organization_id', $model->organization_id)->max('opportunity_count') ?? 0;
            $model->opportunity_count = $highestOpportunityCount + 1; // Increment by 1
        });
        static::created(function ($model) {});
    }

    public function property()
    {
        return $this->belongsTo(Property::class);
    }

    public function organizations()
    {
        return $this->belongsTo(User::class, 'organization_id');
    }

    public function contactInformation()
    {
        return $this->belongsTo(Contact::class, 'contact_id');
    }

    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id');
    }

    public function scopeShow($query)
    {
        return $query->where('client_show', 1); // Replace 'status' with the column name representing the status
    }

    public function propertyInformation()
    {
        return $this->belongsTo(PropertyInformation::class, 'property_id');
    }

    public function proposal()
    {
        return $this->hasOne(Proposal::class);
    }

    public function dealnotes()
    {
        return $this->hasOne(OpportunityDealNote::class);
    }

    public function companyAddress()
    {
        return $this->belongsTo(CompanyAddress::class, 'organization_id', 'company_id');
    }

    public function divisionDetails()
    {
        return $this->hasMany(DivisionDetail::class);
    }

    public function opportunityOwner()
    {
        return $this->belongsTo(User::class, 'opportunity_owner_id');
    }

    public function estimator()
    {
        return $this->belongsTo(User::class, 'estimator_id');
    }

    public function salesman()
    {
        return $this->belongsTo(User::class, 'sale_person_id');
    }

    public function documents()
    {
        return $this->hasMany(OpportunityDocument::class);
    }

    public function estimatedocuments()
    {
        return $this->hasMany(EstimateStatus::class);
    }

    public function generateEstimate()
    {
        return $this->hasOne(GenerateEstimate::class, 'opportunity_id');
    }

    public function client()
    {
        return $this->belongsTo(Contact::class, 'contact_id');
    }
}
