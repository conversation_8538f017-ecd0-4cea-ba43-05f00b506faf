<?php

namespace App\Imports;

use App\Models\Account;
use App\Models\Contact;
use App\Models\Division;
use App\Models\Opportunity;
use App\Models\PropertyInformation;
use App\Models\Proposal;
use App\Models\ServiceLine;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class BookOfBusinessImport implements ToCollection, WithHeadingRow
{
    private $errors = [];
    private $successCount = 0;
    private $errorCount = 0;

    public function collection(Collection $rows): void
    {
        foreach ($rows as $index => $row) {
            $rowNumber = $index + 2; // +2 because Excel starts at 1 and we have a header row

            try {
                DB::beginTransaction();

                $this->validateRow($row->toArray(), $rowNumber);

                if (!empty($this->errors)) {
                    DB::rollBack();
                    $this->errorCount++;
                    continue;
                }

                $this->processRow($row->toArray(), $rowNumber);

                DB::commit();
                $this->successCount++;

            } catch (\Exception $e) {
                DB::rollBack();
                $this->addError($rowNumber, "Unexpected error: " . $e->getMessage());
                $this->errorCount++;
                Log::error("BookOfBusinessImport error at row {$rowNumber}: " . $e->getMessage());
            }
        }
    }

    private function validateRow(array $row, int $rowNumber): void
    {
        $rules = [
            // Account validation
            'account_company_name' => 'required|string|max:255',
            'account_company_email' => 'required|email|max:255',
            'account_company_phone' => 'nullable|string|max:20',
            'account_city' => 'nullable|string|max:100',
            'account_state' => 'nullable|string|max:100',
            'account_zip' => 'nullable|string|max:20',
            'account_billing_address' => 'nullable|string|max:500',
            'account_billing_city' => 'nullable|string|max:100',
            'account_billing_state' => 'nullable|string|max:100',
            'account_billing_zip' => 'nullable|string|max:20',
            'account_address' => 'nullable|string|max:500',

            // Property validation
            'property_name' => 'required|string|max:255',
            'property_address1' => 'required|string|max:255',
            'property_address2' => 'nullable|string|max:255',
            'property_city' => 'required|string|max:100',
            'property_state' => 'required|string|max:100',
            'property_zip' => 'required|string|max:20',

            // Contact validation
            'contact_first_name' => 'required|string|max:100',
            'contact_last_name' => 'required|string|max:100',
            'contact_email' => 'required|email|max:255',
            'contact_phone' => 'required|string|max:20',
            'contact_title' => 'nullable|string|max:100',

            // Opportunity validation
            'opportunity_property_name' => 'nullable|string|max:255',
            'opportunity_address1' => 'nullable|string|max:255',
            'opportunity_city' => 'nullable|string|max:100',
            'opportunity_state' => 'nullable|string|max:100',
            'opportunity_zip_code' => 'nullable|string|max:20',
            'opportunity_first_name' => 'nullable|string|max:100',
            'opportunity_last_name' => 'nullable|string|max:100',
            'opportunity_title_role' => 'nullable|string|max:100',
            'opportunity_phone_number' => 'nullable|string|max:20',
            'opportunity_opportunity_name' => 'required|string|max:255',
            'opportunity_customer_bid_form' => 'nullable|string|max:255',
            'opportunity_bid_due_date' => 'nullable|date',
            'opportunity_net_new' => 'nullable',
            'opportunity_company_id' => 'nullable|string|max:255',
            'opportunity_images' => 'nullable|string|max:1000',
            'opportunity_email' => 'nullable|email|max:255',
            'opportunity_division' => 'nullable|string|max:255',
            'opportunity_service_line' => 'nullable|string|max:255',
            'opportunity_price_model' => 'nullable|string|max:255',
        ];

        $messages = [
            'required' => 'The :attribute field is required.',
            'email' => 'The :attribute must be a valid email address.',
            'max' => 'The :attribute may not be greater than :max characters.',
            'date' => 'The :attribute must be a valid date.',
            'boolean' => 'The :attribute must be true or false.',
        ];

        $validator = Validator::make($row, $rules, $messages);

        if ($validator->fails()) {
            foreach ($validator->errors()->all() as $error) {
                $this->addError($rowNumber, $error);
            }
        }

        // Additional validation for business logic
        $this->validateBusinessRules($row, $rowNumber);
    }

    private function validateBusinessRules(array $row, int $rowNumber): void
    {
        $organizationId = getOrganizationId();

        // Note: We don't validate for existing records here since we now handle duplicates
        // by reusing existing records instead of throwing errors

        // You can add other business validation rules here if needed
        // For example, checking if required service lines or divisions exist
        $serviceLine = ServiceLine::where('account_id', $organizationId)
            ->where('name', 'Maintenance')
            ->first();

        if (!$serviceLine) {
            $this->addError($rowNumber, "Required service line 'Maintenance' not found for organization.");
        }

        $division = Division::where('name', 'Landscape')->first();
        if (!$division) {
            $this->addError($rowNumber, "Required division 'Landscape' not found.");
        }
    }

    private function processRow(array $row, int $rowNumber): void
    {
        $organizationId = getOrganizationId();
        $currentUserId = auth()->id();
        $serviceLine = ServiceLine::where('account_id', getOrganizationId())
            ->where('name', 'Maintenance')
            ->first();
        $division = Division::where('name','Landscape')->first();

        // Check for existing Account by email
        $account = Account::where('email', $row['account_company_email'])
            ->where('parent_id', $organizationId)
            ->first();

        if (!$account) {
            // Create new Account if not exists
            $account = Account::create([
                'company_name' => $row['account_company_name'],
                'email' => $row['account_company_email'],
                'mobile_no' => $row['account_company_phone'] ?? null,
                'city' => $row['account_city'] ?? null,
                'state' => $row['account_state'] ?? null,
                'zip' => $row['account_zip'] ?? null,
                'billing_address' => $row['account_billing_address'] ?? null,
                'billing_city' => $row['account_billing_city'] ?? null,
                'billing_state' => $row['account_billing_state'] ?? null,
                'billing_zip' => $row['account_billing_zip'] ?? null,
                'address' => $row['account_address'] ?? null,
                'account_owner' => $currentUserId, // Set to current authenticated user
                'parent_id' => $organizationId,
                'password' => bcrypt('defaultpassword'), // Default password
            ]);
            Log::info("Created new account for row {$rowNumber}: Account ID {$account->id}");
        } else {
            Log::info("Using existing account for row {$rowNumber}: Account ID {$account->id}");
        }

        // Check for existing Property by name and company_id
        $property = PropertyInformation::where('name', $row['property_name'])
            ->where('company_id', $account->id)
            ->where('organization_id', $organizationId)
            ->first();

        if (!$property) {
            // Create new Property if not exists
            $property = PropertyInformation::create([
                'name' => $row['property_name'],
                'address1' => $row['property_address1'],
                'address2' => $row['property_address2'] ?? null,
                'city' => $row['property_city'],
                'state' => $row['property_state'],
                'zip' => $row['property_zip'],
                'company_id' => $account->id,
                'organization_id' => $organizationId,
                'property_health' => $row['Property Health'] ? strtolower($row['Property Health']) : null,
            ]);
            Log::info("Created new property for row {$rowNumber}: Property ID {$property->id}");
        } else {
            Log::info("Using existing property for row {$rowNumber}: Property ID {$property->id}");
        }

        // Check for existing Contact by email and organization_id
        $contact = Contact::where('email', $row['contact_email'])
            ->where('organization_id', $organizationId)
            ->first();

        if (!$contact) {
            // Create new Contact if not exists
            $contact = Contact::create([
                'first_name' => $row['contact_first_name'],
                'last_name' => $row['contact_last_name'],
                'email' => $row['contact_email'],
                'phone_number' => $row['contact_phone'],
                'title' => $row['contact_title'] ?? null,
                'account' => $account->id,
                'organization_id' => $organizationId,
                'property_id' => $property->id,
                'password' => bcrypt('defaultpassword'), // Default password
            ]);
            Log::info("Created new contact for row {$rowNumber}: Contact ID {$contact->id}");
        } else {
            Log::info("Using existing contact for row {$rowNumber}: Contact ID {$contact->id}");
        }

        // Create Opportunity (always create new opportunity for each import row)
        $opportunity = Opportunity::create([
            'opportunity_name' => $row['opportunity_opportunity_name'],
            'account_id' => $account->id,
            'property_id' => $property->id,
            'contact_id' => $contact->id,
            'organization_id' => $organizationId,
            'created_by' => $currentUserId,
            'creator_id' => $currentUserId,
            'opportunity_owner_id' => $currentUserId,
            'customer_bid_form' => $row['opportunity_customer_bid_form'] ?? null,
            'bid_due_date' => $row['opportunity_bid_due_date'] ? date('Y-m-d', strtotime($row['opportunity_bid_due_date'])) : null,
//            'net_new' => $this->convertToBoolean($row['opportunity_net_new'] ?? null),
            'net_new' => false,
            'status' => 4, // Default to Closed Won status
            'client_show' => 1, // Default to show to client
            'price_model' => $row['opportunity_price_model'] ? strtolower($row['opportunity_price_model']) : null,
            'opportunity_type' => 'Maintenance',
            'service_line_id' => $serviceLine->id,
            'division_id' => $division->id,
        ]);

        //create a proposal for opportunity
        Proposal::create([
            'opportunity_id' => $opportunity->id,
            'contract_start_date' => $row['contract_start_date'] ? date('Y-m-d', strtotime($row['contract_start_date'])) : null,
            'contract_end_date' => $row['contract_end_date'] ? date('Y-m-d', strtotime($row['contract_end_date'])) : null,
            'contract_amount' => $row['contract_amount'] ?? null,
        ]);

        Log::info("Successfully imported row {$rowNumber}: Account ID {$account->id}, Property ID {$property->id}, Contact ID {$contact->id}, Opportunity ID {$opportunity->id}");
    }

    private function convertToBoolean($value): bool
    {
        if (is_bool($value)) {
            return $value;
        }

        if (is_string($value)) {
            $value = strtolower($value);
            return in_array($value, ['true', '1', 'yes', 'on']);
        }

        if (is_numeric($value)) {
            return (bool) $value;
        }

        return false;
    }

    private function addError(int $rowNumber, string $message): void
    {
        $this->errors[] = "Row {$rowNumber}: {$message}";
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getSuccessCount(): int
    {
        return $this->successCount;
    }

    public function getErrorCount(): int
    {
        return $this->errorCount;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }
}
