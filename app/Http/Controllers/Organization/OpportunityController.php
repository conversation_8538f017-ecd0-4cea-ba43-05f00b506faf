<?php

namespace App\Http\Controllers\Organization;

use App\Enums\ClosedLostReason;
use App\Enums\OpportunityStatus;
use App\Exports\OpportunitiesExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Organization\StoreOpportunityRequest;
use App\Models\Account;
use App\Models\AdjustSellingPrice;
use App\Models\ClientEstimateAction;
use App\Models\CompanyAddress;
use App\Models\Contact;
use App\Models\ContactInformation;
use App\Models\Division;
use App\Models\Equipment;
use App\Models\EstimateItem;
use App\Models\GenerateEstimate;
use App\Models\HardMaterial;
use App\Models\Labor;
use App\Models\Margin;
use App\Models\Opportunity;
use App\Models\OpportunityDealNote;
use App\Models\OpportunityDocument;
use App\Models\OtherCost;
use App\Models\PlantMaterial;
use App\Models\PropertyInformation;
use App\Models\Proposal;
use App\Models\section;
use App\Models\ServiceLine;
use App\Models\SnowSetup;
use App\Models\Subcontractor;
use App\Models\User;
use App\Traits\PermissionMiddlewareTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\PdfToImage\Pdf;
use Yajra\DataTables\Facades\DataTables;

class OpportunityController extends Controller
{
    use PermissionMiddlewareTrait;

    public function __construct()
    {
        $permissionsMap = [
            'index' => ['opportunity_listing', 'opportunity_detail', 'add_opportunity', 'edit_opportunity'],
            'create' => ['add_opportunity'],
            'store' => ['add_opportunity'],
            'edit' => ['edit_opportunity'],
            'update' => ['edit_opportunity'],
            'detail' => ['opportunity_listing', 'opportunity_detail', 'edit_opportunity'],
            'delete' => ['edit_opportunity'],
            'history' => ['opportunity_listing', 'opportunity_detail', 'estimate_listing', 'generate_estimate'],
        ];

        $this->applyPermissionMiddleware($permissionsMap);
    }

    public function index(Request $request)
    {
        $statusMappings = OpportunityStatus::getStatusMapping();

        $organizationId = getOrganizationId();
        if ($request->ajax()) {
            $query = Opportunity::with(['propertyInformation', 'contactInformation', 'divisionDetails.division', 'account', 'opportunityOwner', 'companyAddress', 'division', 'serviceLine'])
                ->where('organization_id', $organizationId)
                ->latest();

            return DataTables::eloquent($query)
                ->filter(function ($query) use ($request) {
                    if ($search = $request->input('search.value')) {
                        $query->whereHas('propertyInformation', function ($q) use ($search) {
                            $q->where('name', 'LIKE', "%{$search}%");
                        })

                            ->orWhereHas('contactInformation', function ($q) use ($search) {
                                $q->where('first_name', 'LIKE', "%{$search}%")
                                    ->orWhere('last_name', 'LIKE', "%{$search}%");
                            })
                            ->orWhereHas('account', function ($q) use ($search) {
                                $q->where('company_name', 'LIKE', "%{$search}%")
                                    ->orWhere('company_name', 'LIKE', "%{$search}%");
                            })
                            ->orWhereHas('opportunityOwner', function ($q) use ($search) {
                                $q->whereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$search}%"]);
                            })

                            ->orWhereHas('divisionDetails', function ($q) use ($search) {
                                $q->whereHas('division', function ($q) use ($search) {
                                    $q->where('name', 'LIKE', "%{$search}%");
                                });
                            });
                        $query->orWhere('opportunity_name', 'LIKE', "%{$search}%")

                            ->orWhere('opportunity_type', 'LIKE', "%{$search}%"); // Adjust field name 'date' to your actual column name

                    }
                })
                ->addColumn('opportunity_id', fn (Opportunity $opportunity) => $opportunity->opportunity_count)
                ->addColumn('opportunity_name', fn (Opportunity $opportunity) => $opportunity->opportunity_name)
                ->addColumn('opportunity_name', function (Opportunity $opportunity) {
                    // dd($opportunity->opportunity_name);
                    return $opportunity->opportunity_name;
                })
                ->addColumn('property_name', function (Opportunity $opportunity) {
                    return optional($opportunity->propertyInformation)->name ?? 'N/A';
                })
                ->addColumn('account', function (Opportunity $opportunity) {
                    // Fetch the contact using the contact_id from the opportunity
                    return optional($opportunity->account)->company_name ?? 'N/A';
                })

                ->addColumn('account_owner', function (Opportunity $opportunity) {
                    $contact = $opportunity->opportunityOwner;
                    $name = optional($contact)->first_name.' '.optional($contact)->last_name ?? 'N/A';
                    if ($contact?->image) {

                        $image = asset('storage/user_images/'.$contact?->image);
                    } else {
                        $image = asset('admin_assets/images/dummy_image.webp');
                    }
                    // dd($contact);
                    if ($contact != null) {
                        return '<div class="d-flex align-items-center">
                                <img loading="lazy" class="rounded-circle mt-1 mr-3" height="30px" width="30px" src="'.$image.'" alt="account owner">
                                <span>'.$name.'</span>
                            </div>';
                    }
                })
                ->addColumn('opportunity_type', function (Opportunity $opportunity) {
                    return $opportunity->opportunity_type;
                })
                ->addColumn('division_name', function (Opportunity $opportunity) {
                    return optional($opportunity->division)?->name ?? 'N/A';
                })
                ->addColumn('service_line_name', function (Opportunity $opportunity) {
                    return optional($opportunity->serviceLine)?->name ?? 'N/A';
                })
                ->addColumn('date_time', function (Opportunity $opportunity) {
                    $timezone = $opportunity->companyAddress->timezone ?? 'UTC';
                    $createdAt = $opportunity->created_at->setTimezone($timezone);

                    return $createdAt->format('F d, Y - h:i A');
                })
                ->addColumn('status', function (Opportunity $opportunity) use ($statusMappings) {
                    $status = $statusMappings[$opportunity->status]['name'] ?? 'Unknown';
                    $class = $statusMappings[$opportunity->status]['class'] ?? 'status-default';
                    if ($opportunity->status == 5) {
                        if ($opportunity->generateEstimate?->client_status == 'reject') {
                            $status = 'Closed Lost';
                            $class = 'status-lost-closed';
                        } elseif ($opportunity->generateEstimate?->client_status == 'approve') {
                            $status = 'Closed Win';
                            $class = 'status-win-closed';
                        } else {
                            $status = 'Closed Lost';
                            $class = 'status-lost-closed';
                        }
                    } elseif ($opportunity->status == 7) {
                        $status = 'Closed Win';
                        $class = 'status-win-closed';
                    }

                    return '<div class="status-label '.$class.'">'.$status.'</div>';
                })
                ->addColumn('date_time', function (Opportunity $opportunity) {
                    $timezone = $opportunity->companyAddress->timezone ?? 'UTC';
                    $createdAt = $opportunity->created_at->setTimezone($timezone);

                    return $createdAt->format('F d, Y - h:i A');
                })
                ->addColumn('action', function (Opportunity $opportunity) {
                    $user = auth('web')->user();
                    if (! $user->canany(['add_opportunity', 'edit_opportunity', 'opportunity_detail'])) {
                        return '';
                    }

                    $html = '<div class="dropdown mx-auto w-fit">
                                <div id="dropdown'.encodeID($opportunity->id).'" data-toggle="dropdown" aria-expanded="false">
                                    <img height="24px" width="24px" src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                                </div>
                                <ul class="dropdown-menu" aria-labelledby="dropdown'.encodeID($opportunity->id).'">';

                    if ($user->canany(['add_opportunity', 'edit_opportunity', 'opportunity_detail'])) {
                        $html .= '<li><a class="dropdown-item" href="'.route(getRouteAlias().'.opportunity.detail', encodeId($opportunity->id)).'">View Details</a></li>';
                    }

                    if ($user->can('edit_opportunity')) {
                        $html .= '<li><a href="'.route(getRouteAlias().'.edit.opportunity', encodeId($opportunity->id)).'" style="cursor: pointer" data-opportunity-id="'.encodeId($opportunity->id).'" class="dropdown-item">Edit</a></li>';
                    }
                    if ($user->can('edit_opportunity')) {
                        $html .= '<li><a style="cursor: pointer" data-toggle="modal" data-target="#deleteModal" data-opportunity-id="'.encodeId($opportunity->id).'" class="dropdown-item">Delete</a></li>';
                    }

                    $html .= '</ul></div>';

                    return $html;
                })
                ->rawColumns(['status', 'account_owner', 'action'])
                ->only(['opportunity_id', 'opportunity_name', 'property_name', 'account', 'account_owner', 'opportunity_type', 'division_name', 'service_line_name', 'date_time', 'status', 'action'])
                ->toJson();
        }

        return view('organization.opportunity.index');
    }

    public function create()
    {
        $opportunity = new Opportunity;
        $organizationId = getOrganizationId();
        $action = URL::route(getRouteAlias().'.opportunity.store');
        $divisions = Division::all();
        $sales = $this->getSalesMan();
        $estimators = $this->getEstimator();
        $properties = PropertyInformation::where('organization_id', $organizationId)->get(['id', 'name']);
        $opportunityOwner = User::where('type', EMPLOYEE)->where('parent_id', $organizationId)->orWhere('id', getOrganizationId())->get();
        $accounts = Account::where('parent_id', $organizationId)->get();
        $roles = DB::table('roles')->get();
        $user = User::where('type', EMPLOYEE)->where('parent_id', $organizationId)->get();

        return view('organization.opportunity.create', get_defined_vars());
    }

    public function store(StoreOpportunityRequest $request)
    {
        $validated = $request->validated();
        $getTimeZone = CompanyAddress::where('company_id', getOrganizationId())->value('timezone');
        $createdAt = $getTimeZone ? now($getTimeZone) : now();
        DB::beginTransaction();

        try {
            $prop = DB::table('property_information')
                ->where('organization_id', getOrganizationId())
                ->where('name', $validated['property_name'])
                ->where('address1', $validated['address1'])
                ->where('city', $validated['city'])
                ->where('state', $validated['state'])
                ->where('zip', $validated['zip_code'])
                ->where('company_id', $validated['company_id'])
                ->get();
            if (count($prop) > 0) {
                $prop_id = $prop[0]->id;
                PropertyInformation::where('id', $prop_id)->update([
                    'organization_id' => getOrganizationId(),
                    'name' => $validated['property_name'],
                    'address1' => $validated['address1'],
                    'address2' => $validated['address2'],
                    'city' => $validated['city'],
                    'state' => $validated['state'],
                    'zip' => $validated['zip_code'],
                    'company_id' => $validated['company_id'],
                ]);
            } else {
                $props = PropertyInformation::create([
                    'organization_id' => getOrganizationId(),
                    'name' => $validated['property_name'],
                    'address1' => $validated['address1'],
                    'address2' => $validated['address2'],
                    'city' => $validated['city'],
                    'state' => $validated['state'],
                    'zip' => $validated['zip_code'],
                    'company_id' => $validated['company_id'],
                ]);
                $prop_id = $props->id;
            }

            $conts = DB::table('contacts')
                ->where('organization_id', getOrganizationId())
                ->where('first_name', $validated['first_name'])
                ->where('last_name', $validated['last_name'])
                ->where('title', $validated['title_role'])
                ->where('phone_number', $validated['phone_number'])
                ->where('email', $validated['email'])
                ->where('account', $validated['company_id'])
                ->get();

            if (count($conts) > 0) {
                $cont_id = $validated['contact_ids'] ?? null;
            } else {
                $cn = DB::table('contacts')->where('property_id', $prop_id)->count();
                if ($cn == 0) {
                    $dft = 1;
                } else {
                    $dft = 0;
                }
                $cnt = Contact::create([
                    'organization_id' => getOrganizationId(),
                    'first_name' => $validated['first_name'],
                    'last_name' => $validated['last_name'],
                    'title' => $validated['title_role'],
                    'property_id' => $prop_id,
                    'phone_number' => $validated['phone_number'],
                    'email' => $validated['email'],
                    'second_phone' => $validated['second_phone'],
                    'account' => $validated['company_id'],
                    'property_default' => $dft,

                ]);
                $cont_id = $cnt->id;
            }

            // Ensure contact_id is not null
            if (empty($cont_id)) {
                \Log::error('Contact ID is null during opportunity creation', [
                    'validated_contact_ids' => $validated['contact_ids'] ?? 'not_set',
                    'found_contacts_count' => count($conts),
                    'organization_id' => getOrganizationId(),
                    'form_data' => [
                        'first_name' => $validated['first_name'],
                        'last_name' => $validated['last_name'],
                        'email' => $validated['email'],
                        'company_id' => $validated['company_id'],
                    ],
                ]);
                throw new \Exception('Contact ID cannot be null. Unable to create opportunity.');
            }

            // Log opportunity creation data for debugging
            \Log::info('Creating opportunity with data', [
                'contact_id' => $cont_id,
                'property_id' => $prop_id,
                'opportunity_owner_id' => $validated['opportunity_owner_id'] ?? 'null',
                'organization_id' => getOrganizationId(),
            ]);

            $opportunity = Opportunity::create([
                'opportunity_name' => $validated['opportunity_name'],
                'contact_id' => $cont_id,
                'property_id' => $prop_id,
                'account_id' => $validated['company_id'],
                'opportunity_type' => $validated['opportunity_type'] ?? null,
                'customer_bid_form' => $validated['customer_bid_form'],
                'bid_due_date' => $validated['bid_due_date'],
                'net_new' => $validated['net_new'],
                'lead_source' => $validated['lead_source'],
                'opportunity_owner_id' => $validated['opportunity_owner_id'],
                'sale_person_id' => $validated['sale_person_ids'],
                'estimator_id' => $validated['estimator_ids'],
                'request_information' => $validated['request_information'],
                'status' => OpportunityStatus::OPEN->value,
                'created_by' => 'company',
                'created_at' => $createdAt,
                'organization_id' => getOrganizationId(),
                'creator_id' => auth('web')->user()->id,
                'division_id' => $validated['division'],
                'service_line_id' => $validated['service_line'],
                'price_model' => $validated['price_model'],
            ]);

            /** @var \Illuminate\Http\Request $request */
            if ($request->hasFile('images_opportunity')) {
                $images = $request->file('images_opportunity');

                foreach ($images as $image) {
                    $fileExtension = pathinfo($image, PATHINFO_EXTENSION);
                    $filePath = 'public/opportunity_documents/'.$image;
                    OpportunityDocument::create([
                        'name' => $image,
                        'path' => $filePath,
                        'type' => $fileExtension,
                        'opportunity_id' => $opportunity->id,
                    ]);
                }
            }

            $this->copyDefaultProposalValuesToOpportunity($opportunity->id);
            DB::commit();

            return redirect()->route(getRouteAlias().'.opportunity.index')->with('success', 'Your opportunity has been successfully created.');
        } catch (\Exception $e) {
            DB::rollback();
            info($e->getMessage());

            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function updateLaborItemName(Request $request)
    {
        try {
            $item = EstimateItem::find($request->id);

            if (! $item) {
                return response()->json(['success' => false, 'message' => 'Item not found']);
            }

            $item->item_name = $request->name;
            $item->save();

            return response()->json(['success' => true, 'message' => 'Item name updated successfully']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error updating item name: '.$e->getMessage()]);
        }
    }

    public function addDealNotes(Request $request)
    {
        // Validate the incoming request
        $request->validate([
            'description' => 'required|string|max:5000',  // Adjust validation rules as needed
        ]);

        // Assuming you want to update a model with the new description
        $dealNote = OpportunityDealNote::updateOrCreate(
            ['opportunity_id' => $request->opp_id],  // Condition to match existing record
            ['deal_notes' => $request->description]  // Data to update or create
        );

        return response()->json(['message' => 'Description saved successfully']);
    }

    public function imageStore(Request $request)
    {
        $file = $request->file('file');
        $name = uniqid().'_'.trim($file->getClientOriginalName());
        $filePath = $file->storeAs('public/opportunity_documents', $name);

        return response()->json([
            'name' => $name,
            'original_name' => $file->getClientOriginalName(),
        ], HTTP_OK);
    }

    public function imageStoreEmail(Request $request)
    {
        $file = $request->file('file');
        $name = uniqid().'_'.trim($file->getClientOriginalName());
        $filePath = $file->storeAs('public/email_documents', $name);

        return response()->json([
            'name' => $name,
            'original_name' => $file->getClientOriginalName(),
        ], HTTP_OK);
    }

    public function imageDelete($id = null)
    {
        if (File::exists($filePath = storage_path('app/public/opportunity_documents/'.request('filename')))) {
            File::delete(storage_path('app/public/opportunity_documents/'.request('filename')));
        }

        return response()->json('File Removed Successfully!', HTTP_OK);
    }

    public function imageDeleteEmail($id = null)
    {
        if (File::exists($filePath = storage_path('app/public/email_documents/'.request('filename')))) {
            File::delete(storage_path('app/public/email_documents/'.request('filename')));
        }

        return response()->json('File Removed Successfully!', HTTP_OK);
    }

    public function imageDeleteServer(Request $request)
    {
        $fileId = $request->input('file_id'); // Fetch the file_id from the request

        // Find the document using the primary key (id)
        if (isset($fileId)) {
            $document = OpportunityDocument::find($fileId);

            if (! $document) {
                return response()->json(['error' => 'Document not found'], 404); // Return error if not found
            }

            // Delete the file from storage
            if ($document->path && \Illuminate\Support\Facades\Storage::exists($document->path)) {
                \Illuminate\Support\Facades\Storage::delete($document->path); // Delete the file from storage
            }
            $document->delete();
        } else {
            File::delete(storage_path('app/public/opportunity_documents/'.request('filename')));
        }

        // Delete the record from the database

        return response()->json(['message' => 'Document deleted successfully'], 200);
    }

    public function storeDocument(Request $request, $opp_id)
    {

        $oppId = decodeId($opp_id);
        // dd($oppId);

        if ($request->images_opportunity) {
            $images = $request->images_opportunity;

            foreach ($images as $image) {
                // dd($image);
                // $randomPart = Str::random(10);

                $fileExtension = pathinfo($image, PATHINFO_EXTENSION);
                $filePath = 'public/opportunity_documents/'.$image;

                $result = OpportunityDocument::create([
                    'name' => $image,
                    'path' => $filePath,
                    'type' => $fileExtension,
                    'opportunity_id' => $oppId,
                ]);
            }
        }

        return redirect()->back()->with('success', 'Files uploaded successfully.');
    }

    public function updateFileName(Request $req)
    {
        $id = $req->id;
        $name = $req->file_name;
        $data = [
            'name' => $name,
        ];
        DB::table('opportunity_documents')->where('id', $id)->update($data);

        return response()->json(['success' => 'File name updated successfully.']);
    }

    public function edit($id)
    {
        $op_id = decodeId($id);
        $opportunity = new Opportunity;
        $organizationId = getOrganizationId();
        $action = URL::route(getRouteAlias().'.opportunity.update', $id);
        $divisions = Division::all();
        $sales = $this->getSalesMan();
        $estimators = $this->getEstimator();
        $properties = PropertyInformation::where('organization_id', $organizationId)->get(['id', 'name']);
        $opportunityOwner = User::where('type', EMPLOYEE)->where('parent_id', $organizationId)->orWhere('id', getOrganizationId())->get();
        $accounts = Account::where('parent_id', $organizationId)->get();
        $roles = DB::table('roles')->get();
        $user = User::where('type', EMPLOYEE)->where('parent_id', $organizationId)->get();
        $opportunities = Opportunity::findOrFail($op_id);
        $scontact = Contact::findOrFail($opportunities->contact_id);

        $cnts = Contact::where('property_id', $opportunities->property_id)->get();

        $sproperty = PropertyInformation::findOrFail($opportunities->property_id);
        $docs = OpportunityDocument::where('opportunity_id', $op_id)->get();
        $serviceLines = ServiceLine::where('division_id', $opportunities->division_id)->get();

        return view('organization.opportunity.edit', get_defined_vars());
    }

    public function delete($id)
    {
        // dd($id);
        $opportunity = Opportunity::find(decodeId($id));
        if (! is_object($opportunity)) {
            return response()->json([
                'success' => 'false',
            ], HTTP_BAD_REQUEST);
        } else {
            $opportunity->delete();

            return response()->json(['success' => 'true'], HTTP_OK);
        }
    }

    public function deleteTemplate($id)
    {
        // dd($id);
        $sett = decodeId($id);
        $opportunity = DB::table('default_settings')->where('id', $sett)->delete();

        return redirect()->back();
    }

    public function update(Request $request, $id)
    {
        $oppor_id = decodeId($id);

        // Validation rules
        $rules = [
            'property_name' => 'required|string|max:255',
            'address1' => 'required|string|max:255',
            'address2' => 'nullable|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'zip_code' => 'required|string|max:10',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'title_role' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20',
            'second_phone' => 'nullable|string|max:20',
            'opportunity_name' => 'required|string|max:255',
            'opportunity_type' => 'sometimes|string',
            'customer_bid_form' => 'required|in:yes,no',
            'bid_due_date' => 'required|date',
            'net_new' => 'required|in:yes,no',
            'lead_source' => 'nullable|string|max:255',
            'opportunity_owner_id' => 'nullable',
            'company_id' => 'required',
            'sale_person_ids' => 'nullable',
            'estimator_ids' => 'nullable',
            'request_information' => 'nullable|string|max:3000',
            'images.*' => 'required',
            'division' => 'required',
            'service_line' => 'required',
            'price_model' => 'required',
        ];

        // Validate request
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $validated = $validator->validated();

        // Start transaction
        DB::beginTransaction();

        try {
            // Find or create property information
            $propertyData = [
                'organization_id' => getOrganizationId(),
                'name' => $validated['property_name'],
                'address1' => $validated['address1'],
                'address2' => $validated['address2'],
                'city' => $validated['city'],
                'state' => $validated['state'],
                'zip' => $validated['zip_code'],
                'company_id' => $validated['company_id'],
            ];

            // Use firstOrNew to simplify property lookup and creation
            $property = PropertyInformation::firstOrNew([
                'organization_id' => getOrganizationId(),
                'name' => $validated['property_name'],
                'address1' => $validated['address1'],
                'city' => $validated['city'],
                'state' => $validated['state'],
                'zip' => $validated['zip_code'],
                'company_id' => $validated['company_id'],
            ]);

            // Fill with all property data and save
            $property->fill($propertyData);
            $property->save();

            // Update opportunity
            Opportunity::where('id', $oppor_id)->update([
                'opportunity_name' => $validated['opportunity_name'],
                'contact_id' => $request->contact_ids,
                'property_id' => $property->id,
                'account_id' => $validated['company_id'],
                'opportunity_type' => $validated['opportunity_type'],
                'customer_bid_form' => $validated['customer_bid_form'],
                'bid_due_date' => $validated['bid_due_date'],
                'net_new' => $validated['net_new'],
                'lead_source' => $validated['lead_source'],
                'opportunity_owner_id' => $validated['opportunity_owner_id'],
                'sale_person_id' => $validated['sale_person_ids'],
                'estimator_id' => $validated['estimator_ids'],
                'request_information' => $validated['request_information'],
                'created_by' => 'company',
                'organization_id' => getOrganizationId(),
                'creator_id' => auth('web')->user()->id,
                'division_id' => $validated['division'],
                'service_line_id' => json_encode($validated['service_line']),
                'price_model' => $validated['price_model'],
            ]);

            // Handle document uploads
            if ($request->images_opportunity) {
                $this->saveOpportunityDocuments($request->images_opportunity, $oppor_id);
            }

            DB::commit();

            // Redirect with success message
            $refererUrl = session()->get('referer_url');
            session()->forget('referer_url');

            return redirect()->to($refererUrl ?? route(getRouteAlias().'.opportunity.index'))
                ->with('success', 'Your opportunity has been successfully updated.');
        } catch (\Exception $e) {
            DB::rollback();
            info($e->getMessage());

            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    /**
     * Save opportunity documents
     *
     * @param  array  $images
     * @param  int  $opportunityId
     * @return void
     */
    private function saveOpportunityDocuments($images, $opportunityId)
    {
        foreach ($images as $image) {
            $fileExtension = pathinfo($image, PATHINFO_EXTENSION);
            $filePath = 'public/opportunity_documents/'.$image;

            OpportunityDocument::create([
                'name' => $image,
                'path' => $filePath,
                'type' => $fileExtension,
                'opportunity_id' => $opportunityId,
            ]);
        }
    }

    public function detail($id)
    {
        $decodedId = decodeId($id);
        $organizationId = getOrganizationId();
        $organization = User::where('id', $organizationId)->select('company_name', 'first_name', 'last_name')->first();
        $opportunity = Opportunity::with('propertyInformation', 'address.company', 'contactInformation', 'divisionDetails.division.serviceLines', 'proposal', 'dealnotes')
            ->findOrFail($decodedId);

        $accountOwn = Contact::where('id', $opportunity->contact_id)->first();

        $roles = DB::table('roles')->where('id', $accountOwn->role)->first();
        $cont = Account::where('id', $accountOwn->account)->first();
        if ($cont) {
            $accnt = User::where('id', $cont->account_owner)->first();
        }
        $opportunityDocuments = OpportunityDocument::where('opportunity_id', $decodedId)->get();
        $sales = User::where('id', $opportunity->sale_person_id)->first();
        $estimator = User::where('id', $opportunity->estimator_id)->first();
        $opportunityowner = User::where('id', $opportunity->opportunity_owner_id)->first();
        $estimates = GenerateEstimate::with('opportunityid')->where('opportunity_id', $decodedId)->where('organization_id', getOrganizationId())->first();

        $lastStatus = DB::table('estimate_status as es')
            ->leftJoin('generate_estimates as ge', 'es.generate_estimate_id', '=', 'ge.id')
            ->leftJoin('client_estimate_actions as cea', 'ge.id', '=', 'cea.generate_estimate_id')
            ->where('es.opportunity_id', $decodedId)
            ->select('es.*', 'cea.description as client_action_description')
            ->orderBy('es.id', 'desc') // Order by ID in descending order
            ->first(); // Get only the last (most recent) record

        if ($lastStatus) {
            $statusesss = DB::table('estimate_status as es')
                ->leftJoin('generate_estimates as ge', 'es.generate_estimate_id', '=', 'ge.id')
                ->leftJoin('client_estimate_actions as cea', 'ge.id', '=', 'cea.generate_estimate_id')
                ->where('es.opportunity_id', $decodedId)
                ->where('es.id', '!=', $lastStatus->id)
                ->select('es.*', 'cea.description as client_action_description')
                ->get();
        } else {
            // Handle case where no records are found for the given opportunity_id
            $statusesss = [];
        }
        $totalPriceSumss = DB::table('estimate_items')
            ->where('opportunity_id', $decodedId)
            ->sum('total_price');

        // details
        return view('organization.opportunity.details', compact('cont', 'opportunity', 'organization', 'opportunityDocuments', 'accnt', 'roles', 'sales', 'estimator', 'opportunityowner', 'estimates', 'totalPriceSumss', 'statusesss', 'lastStatus'));
    }

    public function docDelete($id)
    {
        try {
            // Decode the ID if encoded
            $decodedId = decodeId($id);

            // Find the document by ID
            $document = OpportunityDocument::findOrFail($decodedId);

            // Delete the file from storage
            Storage::delete($document->path);

            // Delete the document record from the database
            $document->delete();

            // Return success response
            return response()->json(['success' => 'Document deleted successfully!'], 200);
        } catch (\Exception $e) {
            // Handle error
            return response()->json(['error' => 'Document could not be deleted!'], 500);
        }
    }

    // SHERAZ CODE
    public function saveSection(Request $request)
    {
        // Validate the request
        $request->validate([
            'opportunity_id' => 'required|exists:opportunities,id',
            'section_name' => 'required|string|max:255',
        ]);

        $section = Section::updateOrCreate(['id' => $request->section_id], [
            'opportunity_id' => $request->opportunity_id,
            'section_name' => $request->section_name,
            'organization_id' => getOrganizationId(),
        ]);

        return response()->json([
            'message' => $request->section_id ? 'Section updated successfully' : 'Section created successfully',
            'section_id' => $section->id,
        ]);
    }

    public function deleteSection(Request $request)
    {
        // Validate the incoming request
        $validated = $request->validate([
            'section_id' => 'required|string',
            'opportunityId' => 'required|integer',
        ]);
        $sec = Section::find($validated['section_id']);
        $sec->delete();

        // Get all matching records based on section_name and opportunity_id
        $matchingItems = EstimateItem::where('section_name', $validated['section_id'])
            ->where('opportunity_id', $validated['opportunityId'])
            ->get();

        // Check if there are any matching records

        // Delete all matching records
        EstimateItem::where('section_name', $validated['section_id'])
            ->where('opportunity_id', $validated['opportunityId'])
            ->delete();

        return response()->json(['message' => 'Matching items deleted successfully.']);
    }

    public function deleteItem(Request $request)
    {
        // Validate the incoming request
        $validated = $request->validate([
            'rowId' => 'required|string',
        ]);

        // Get all matching records based on section_name and opportunity_id
        $matchingItems = EstimateItem::where('id', $validated['rowId'])
            ->get();

        // Check if there are any matching records
        if ($matchingItems->isEmpty()) {
            return response()->json(['message' => 'No matching items found.'], 404);
        }

        // Delete all matching records
        EstimateItem::where('id', $validated['rowId'])
            ->delete();

        return response()->json(['message' => 'Matching item deleted successfully.']);
    }

    public function OpportunityEstimation($id)
    {

        $decodedId = decodeId($id);
        $organizationId = getOrganizationId();

        // Get organization data
        $organization = User::find($organizationId);
        $saleTax = $organization->sale_tax ?? 0;
        $taxRate = $saleTax / 100;

        // Get opportunity with related data
        $opportunity = Opportunity::with([
            'propertyInformation',
            'salesman',
            'estimator',
            'opportunityOwner',
            'address.company',
            'contactInformation',
            'division',
            'divisionDetails.division.serviceLines',
            'proposal',
        ])->findOrFail($decodedId);

        // Get labor burden data
        $burderlabor = Labor::where('organization_id', $organizationId)
            ->whereNotNull('labor_burden')
            ->first();

        // Get all divisions
        $div = DB::table('divisions')->get();

        // Get sections for this opportunity
        $sectionss = Section::where('opportunity_id', $decodedId)
            ->where('organization_id', $organizationId)
            ->get();

        // Get scope settings
        $scope = DB::table('default_settings')
            ->where('organization_id', $organizationId)
            ->where('opportunity_id', $decodedId)
            ->where('setting_type', 'scope')
            ->first();

        // Get estimation data
        $estimationData = EstimateItem::with('section:id,section_name')
            ->where('opportunity_id', $opportunity->id)
            ->get()
            ->groupBy(function ($item) {
                return optional($item->section)->section_name ?? 'Unassigned';
            });

        // Get items without section
        $gtems = DB::table('estimate_items')
            ->where('opportunity_id', $decodedId)
            ->whereNull('section_name')
            ->get();

        // Function to get items with favorite status
        $getItemsWithFavorites = function ($table, $type) use ($organizationId) {
            return DB::table($table)
                ->leftJoin('fav_items', function ($join) use ($type, $table) {
                    $join->on("$table.id", '=', 'fav_items.item_id')
                        ->where('fav_items.type', $type);
                })
                ->where("$table.organization_id", $organizationId)
                ->select("$table.*", DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
                ->get();
        };

        // Get all item types with favorite status
        $equipment = $getItemsWithFavorites('equipment', 'equipment');
        $hard_materials = $getItemsWithFavorites('hard_materials', 'hard_materials');
        $plant_materials = $getItemsWithFavorites('plant_materials', 'plant_materials');
        $other_job_costs = $getItemsWithFavorites('other_costs', 'other_costs');
        $sub_contractor = $getItemsWithFavorites('subcontractors', 'contractors');
        $labors = $getItemsWithFavorites('labors', 'labors');

        // Get estimate item IDs by category
        $estimateItemsQuery = DB::table('estimate_items')->where('opportunity_id', $decodedId);

        $categoryItems = [
            'equipitems' => $estimateItemsQuery->where('category_type', 'labors')->pluck('item_id')->toArray(),
            'contractorsitems' => $estimateItemsQuery->where('category_type', 'contractors')->pluck('item_id')->toArray(),
            'othersitems' => $estimateItemsQuery->where('category_type', 'other_costs')->pluck('item_id')->toArray(),
            'plantsitems' => $estimateItemsQuery->where('category_type', 'plant_materials')->pluck('item_id')->toArray(),
            'hardssitems' => $estimateItemsQuery->where('category_type', 'hard_materials')->pluck('item_id')->toArray(),
            'equipmentsitems' => $estimateItemsQuery->where('category_type', 'equipment')->pluck('item_id')->toArray(),
        ];

        // Extract variables from the array for backward compatibility
        extract($categoryItems);

        // Calculate price sums by category
        $priceQuery = DB::table('estimate_items')->where('opportunity_id', $decodedId);

        $totalPriceSumequip = $priceQuery->where('category_type', 'equipment')->sum('total_price');
        $totalPriceSumlabor = $priceQuery->where('category_type', 'labors')->sum('total_price');
        $totalPriceSumlaborhourcount = $priceQuery->where('category_type', 'labors')
            ->where('labor_type', 'Laborers')->sum('quantity');
        $totalPriceSumsupervisionhourcount = $priceQuery->where('category_type', 'labors')
            ->where('labor_type', 'Supervision')->sum('quantity');
        $totalPriceSummaterial = $priceQuery->whereIn(
            'category_type',
            ['hard_materials', 'plant_materials', 'contractors']
        )->sum('total_price');
        $totalPriceSumother = $priceQuery->where('category_type', 'other_costs')->sum('total_price');
        $totalPriceSumcontractor = $priceQuery->where('category_type', 'contractors')->sum('total_price');
        $totalPriceSumss = $priceQuery->sum('total_price');

        // Calculate cost sums by category
        $costQuery = DB::table('estimate_items')->where('opportunity_id', $decodedId);

        $totalCostSumss = $costQuery->sum('total_cost');
        $totalCostSumequip = $costQuery->where('category_type', 'equipment')->sum('total_cost');
        $totalCostSumlabor = $costQuery->where('category_type', 'labors')->sum('total_cost');
        $totalCostSummaterial = $costQuery->whereIn(
            'category_type',
            ['hard_materials', 'plant_materials']
        )->sum('total_cost');
        $totalCostSumother = $costQuery->where('category_type', 'other_costs')->sum('total_cost');
        $totalCostSumcontractor = $costQuery->where('category_type', 'contractors')->sum('total_cost');

        // Calculate labor burden
        $laborBurdenPercent = $burderlabor->labor_burden ?? 0;
        $laborBurdenRate = $laborBurdenPercent / 100;
        $lbdn = $totalPriceSumlabor * $laborBurdenRate;

        // Calculate material tax
        $mtst = $taxRate * $totalPriceSummaterial;
        $opportunityId = $decodedId;
        $adjustSellingPrice = AdjustSellingPrice::where('opportunity_id', $decodedId)->first();

        // my code goes here

        if ($opportunity->division->name == Division::SNOW_DIVISION) {

            $equipments = SnowSetup::getEquipment();
            $labor = SnowSetup::getLabor();
            $material = SnowSetup::getMaterial();
            $standby = SnowSetup::getStandBy();

            return view('organization.opportunity.snow-opportunity-estimation', compact(
                'equipments',
                'labor',
                'material',
                'standby',
                'opportunity'

            ));

            // return view('organization.opportunity.snow-opportunity-estimation', compact(
            //     'opportunity',
            //     'div',
            //     'organization',
            //     'burderlabor',
            //     'estimationData',
            //     'equipment',
            //     'hard_materials',
            //     'plant_materials',
            //     'other_job_costs',
            //     'sub_contractor',
            //     'labors',
            //     'sectionss',
            //     'gtems',
            //     'equipitems',
            //     'contractorsitems',
            //     'equipmentsitems',
            //     'hardssitems',
            //     'plantsitems',
            //     'othersitems',
            //     'totalPriceSumequip',
            //     'totalPriceSumlabor',
            //     'lbdn',
            //     'totalPriceSummaterial',
            //     'totalPriceSumother',
            //     'totalPriceSumcontractor',
            //     'totalPriceSumss',
            //     'mtst',
            //     'totalPriceSumlaborhourcount',
            //     'totalCostSumss',
            //     'totalCostSumequip',
            //     'totalCostSumlabor',
            //     'totalCostSummaterial',
            //     'totalCostSumother',
            //     'totalCostSumcontractor',
            //     'scope',
            //     'totalPriceSumsupervisionhourcount',
            //     'decodedId',
            //     'saleTax',
            //     'opportunityId',
            //     'taxRate',
            //     'adjustSellingPrice'
            // ));
        }

        return view('organization.opportunity.opportunityEstimation', compact(
            'div',
            'organization',
            'opportunity',
            'burderlabor',
            'estimationData',
            'equipment',
            'hard_materials',
            'plant_materials',
            'other_job_costs',
            'sub_contractor',
            'labors',
            'sectionss',
            'gtems',
            'equipitems',
            'contractorsitems',
            'equipmentsitems',
            'hardssitems',
            'plantsitems',
            'othersitems',
            'totalPriceSumequip',
            'totalPriceSumlabor',
            'lbdn',
            'totalPriceSummaterial',
            'totalPriceSumother',
            'totalPriceSumcontractor',
            'totalPriceSumss',
            'mtst',
            'totalPriceSumlaborhourcount',
            'totalCostSumss',
            'totalCostSumequip',
            'totalCostSumlabor',
            'totalCostSummaterial',
            'totalCostSumother',
            'totalCostSumcontractor',
            'scope',
            'totalPriceSumsupervisionhourcount',
            'decodedId',
            'saleTax',
            'opportunityId',
            'taxRate',
            'adjustSellingPrice'
        ));
    }

    public function searchItems(Request $req)
    {
        $search = $req->search;

        // Organization ID from authenticated user
        $organizationId = getOrganizationId();

        // Retrieve equipment items
        $equipment = DB::table('equipment')
            ->leftJoin('fav_items', function ($join) {
                $join->on('equipment.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'equipment');
            })
            ->where('equipment.organization_id', $organizationId)
            ->where('equipment.name', 'like', '%'.$search.'%')
            ->select('equipment.*', DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
            ->get();

        // Retrieve hard materials
        $hard_materials = DB::table('hard_materials')
            ->leftJoin('fav_items', function ($join) {
                $join->on('hard_materials.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'hard_materials');
            })
            ->where('hard_materials.organization_id', $organizationId)
            ->where('hard_materials.name', 'like', '%'.$search.'%')
            ->select('hard_materials.*', DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
            ->get();

        // Retrieve plant materials
        $plant_materials = DB::table('plant_materials')
            ->leftJoin('fav_items', function ($join) {
                $join->on('plant_materials.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'plant_materials');
            })
            ->where('plant_materials.organization_id', $organizationId)
            ->where('plant_materials.name', 'like', '%'.$search.'%')
            ->select('plant_materials.*', DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
            ->get();

        // Retrieve labor items
        $labors = DB::table('labors')
            ->leftJoin('fav_items', function ($join) {
                $join->on('labors.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'labors');
            })
            ->where('labors.organization_id', $organizationId)
            ->where('labors.name', 'like', '%'.$search.'%')
            ->select('labors.*', DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
            ->get();

        $other_costs = DB::table('other_costs')
            ->leftJoin('fav_items', function ($join) {
                $join->on('other_costs.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'other_costs');
            })
            ->where('other_costs.organization_id', $organizationId)
            ->where('other_costs.name', 'like', '%'.$search.'%')
            ->select('other_costs.*', DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
            ->get();

        // Combine all results into a single response
        $results = [
            'equipment' => $equipment,
            'hard_materials' => $hard_materials,
            'plant_materials' => $plant_materials,
            'labors' => $labors,
            'other_costs' => $other_costs,
        ];

        // Return the response as JSON
        return response()->json($results);
    }

    public function previewEstimation($opportunityId)
    {
        $div = DB::table('divisions')->get();
        // $decodedId = decodeId($id);
        $opportunityId = decodeId($opportunityId);
        $opportunity_id = $opportunityId;
        $organizationId = getOrganizationId();
        $organization = User::where('id', $organizationId)->first();
        $burderlabor = Labor::where('organization_id', $organizationId)->where('labor_burden', '!=', null)->first();
        // $opportunity = Opportunity::with('propertyInformation', 'salesman', 'estimator', 'opportunityOwner', 'address.company', 'contactInformation', 'divisionDetails.division.serviceLines','proposal')
        //     ->findOrFail($decodedId);
        // Check if this is a snow opportunity
        $opportunityRecord = Opportunity::find($opportunityId);
        $isSnowOpportunity = $opportunityRecord && $opportunityRecord->division && $opportunityRecord->division->name === Division::SNOW_DIVISION;

        // Load estimate items with appropriate relations based on opportunity type
        if ($isSnowOpportunity) {
            $opportunity = EstimateItem::where('opportunity_id', $opportunityId)->with(['snowSetup'])->get();
            $hardMaterialImages = collect(); // Snow items don't have images
        } else {
            $opportunity = EstimateItem::where('opportunity_id', $opportunityId)->with(['material'])->get();
            $hardMaterialImages = $opportunity->map(function ($item) {
                // Check if material relation exists and has an image
                if ($item->material && $item->material->image) {
                    return [
                        'name' => $item->material->name,
                        'image' => $item->material->image,
                    ];
                }

                return null;
            })->filter();
        }
        $cover = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'cover')
            ->first();

        $about = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'about')
            ->first();
        $intro = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'intro')
            ->first();
        // dd($intro);
        $scope = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'scope')
            ->first();
        $terms = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'terms')
            ->first();
        $logo = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'logo')
            ->first();
        $payment = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'payment')
            ->first();

        $gallerys = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'gallery')
            ->get();

        $galleryss = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'gallery')
            ->first();

        $imagePaths = $scope ? json_decode($scope->project_image) : [];
        $totalPriceSumss = DB::table('estimate_items')
            ->where('opportunity_id', $opportunityId)
            ->sum('total_price');

        $estimate = Opportunity::with('contactInformation', 'account', 'generateEstimate.serviceLine:id,name', 'generateEstimate.workType:id,name')->where('id', $opportunityId)->firstorfail();
        // dd($estimate);
        // $data = EstimateService::getEstimateInvoiceDetail($id);
        $data['estimate'] = $estimate;
        $organization = User::where('id', getOrganizationId())->first();

        $template = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'template')
            ->first();

        $coverorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'cover')
            ->first();
        // dd($coverorg);
        $aboutorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'about')
            ->first();

        $introorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'intro')
            ->first();

        $scopeorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'scope')
            ->first();

        $termsorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'terms')
            ->first();
        $paymentorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'payment')
            ->first();
        $gec = GenerateEstimate::where('opportunity_id', $opportunityId)->first();
        $estimateAction = ClientEstimateAction::where('generate_estimate_id', $gec?->id)->first();
        // dd($estimateAction);

        return view('organization.opportunity.previewEstimation', compact('terms', 'scope', 'intro', 'about', 'cover', 'imagePaths', 'logo', 'opportunity', 'opportunity_id', 'totalPriceSumss', 'payment', 'organization', 'gallerys', 'template', 'opportunityId', 'coverorg', 'aboutorg', 'introorg', 'scopeorg', 'termsorg', 'paymentorg', 'estimateAction', 'galleryss', 'hardMaterialImages'), $data);
    }

    public function preview($opportunityId)
    {
        $div = DB::table('divisions')->get();
        // $decodedId = decodeId($id);
        $opportunityId = decodeId($opportunityId);
        $opportunity_id = $opportunityId;
        $organizationId = getOrganizationId();
        $organization = User::where('id', $organizationId)->first();
        $burderlabor = Labor::where('organization_id', $organizationId)->where('labor_burden', '!=', null)->first();
        $opportunityRecord = Opportunity::find($opportunityId);
        $isSnowOpportunity = $opportunityRecord && $opportunityRecord->division && $opportunityRecord->division->name === Division::SNOW_DIVISION;

        // Load estimate items with appropriate relations based on opportunity type
        if ($isSnowOpportunity) {
            $opportunity = EstimateItem::where('opportunity_id', $opportunityId)->with(['snowSetup'])->get();
            $hardMaterialImages = collect(); // Snow items don't have images
        } else {
            $opportunity = EstimateItem::where('opportunity_id', $opportunityId)->with(['hardMaterial'])->get();
            $hardMaterialImages = $opportunity->map(function ($item) {
                // Check if hardMaterial relation exists and has an image
                if ($item->hardMaterial && $item->hardMaterial->image) {
                    return [
                        'name' => $item->hardMaterial->name,
                        'image' => $item->hardMaterial->image,
                    ];
                }

                return null;
            })->filter();
        }
        $cover = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'cover')
            ->first();

        $about = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'about')
            ->first();
        $intro = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'intro')
            ->first();
        // dd($intro);
        $scope = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'scope')
            ->first();
        $terms = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'terms')
            ->first();
        $logo = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'logo')
            ->first();
        $payment = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'payment')
            ->first();

        $gallerys = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'gallery')
            ->get();

        $galleryss = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'gallery')
            ->first();

        $imagePaths = $scope ? json_decode($scope->project_image) : [];
        $totalPriceSumss = DB::table('estimate_items')
            ->where('opportunity_id', $opportunityId)
            ->sum('total_price');

        $estimate = Opportunity::with('contactInformation', 'account', 'generateEstimate.serviceLine:id,name', 'generateEstimate.workType:id,name')->where('id', $opportunityId)->firstorfail();
        // dd($estimate);
        // $data = EstimateService::getEstimateInvoiceDetail($id);
        $data['estimate'] = $estimate;
        $organization = User::where('id', getOrganizationId())->first();

        $template = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'template')
            ->first();

        $coverorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'cover')
            ->first();
        // dd($coverorg);
        $aboutorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'about')
            ->first();

        $introorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'intro')
            ->first();

        $scopeorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'scope')
            ->first();

        $termsorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'terms')
            ->first();
        $paymentorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'payment')
            ->first();
        $gec = GenerateEstimate::where('opportunity_id', $opportunityId)->first();
        $estimateAction = ClientEstimateAction::where('generate_estimate_id', $gec?->id)->first();

        //        return view('organization.opportunity.preview1', compact('terms', 'scope', 'intro', 'about', 'cover', 'imagePaths', 'logo','opportunity', 'opportunity_id', 'template', 'totalPriceSumss', 'payment', 'organization', 'gallerys', 'opportunityId'), $data);
        return view('organization.opportunity.preview1', compact('terms', 'scope', 'intro', 'about', 'cover', 'imagePaths', 'logo', 'opportunity', 'opportunity_id', 'template', 'totalPriceSumss', 'payment', 'organization', 'gallerys', 'opportunityId', 'hardMaterialImages', 'paymentorg'), $data);
    }

    public function deleteImageFromGallery(Request $request)
    {
        $request->validate([
            'image_name' => 'required|string', // Name of the image to delete
        ]);

        $imageName = $request->image_name;

        // Fetch the existing gallery column
        $defaultSettings = DB::table('default_settings')->where('id', $imageName)->delete();

        return response()->json(['message' => 'Image deleted successfully!', 'gallery' => $defaultSettings]);
    }

    public function uploadtemplate(Request $request)
    {

        $file = $request->file('file');
        $opportunity_id = $request->input('opportunity_id');
        $fileName = time().'_'.$file->getClientOriginalName(); // Adding timestamp to the filename
        $uploadPath = 'public/uploads/templates/'; // Define the path where images are saved
        $pdfPath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');
        $existingRecord = DB::table('default_settings')->where('opportunity_id', $opportunity_id)->where('setting_type', 'template')->first();

        if ($existingRecord) {
            // Update the existing record with the new file path
            DB::table('default_settings')
                ->where('organization_id', getOrganizationId())->where('setting_type', 'template')
                ->update(['project_image' => $pdfPath]);
        } else {
            // Insert a new record with the file path if it doesn't exist
            DB::table('default_settings')->insert([
                'setting_type' => 'template',  // You can adjust this as needed
                'project_image' => $pdfPath,
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
            ]);
        }

        // Return success response
        return response()->json([
            'success' => true,
            'message' => 'File uploaded and path saved in database successfully.',
            'file_path' => $pdfPath,
        ]);
    }

    public function deleteDataItem(Request $request)
    {
        $id = $request->id;
        $item = EstimateItem::find($id);
        $item->delete();
        $totalPrice = DB::table('estimate_items')
            ->where('opportunity_id', $request->opp_id)
            ->where('category_type', $request->type)
            ->sum('total_price');

        return response()->json(['message' => 'Item deleted successfully', 'data' => $request->type]);
    }

    public function downloadPdf($opportunityId)
    {
        $div = DB::table('divisions')->get();
        // $decodedId = decodeId($id);
        $organizationId = getOrganizationId();
        $organization = User::where('id', $organizationId)->first();
        $burderlabor = Labor::where('organization_id', $organizationId)->where('labor_burden', '!=', null)->first();
        // $opportunity = Opportunity::with('propertyInformation', 'salesman', 'estimator', 'opportunityOwner', 'address.company', 'contactInformation', 'divisionDetails.division.serviceLines','proposal')
        //     ->findOrFail($decodedId);
        $opportunity = EstimateItem::where('opportunity_id', $opportunityId)->get();
        $cover = DB::table('default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'cover')
            ->first();
        $about = DB::table('default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'about')
            ->first();
        $intro = DB::table('default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'intro')
            ->first();
        $scope = DB::table('default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'scope')
            ->first();
        $terms = DB::table('default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'terms')
            ->first();
        $logo = DB::table('default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'logo')
            ->first();

        $imagePaths = $scope ? json_decode($scope->project_image) : [];

        return view('organization.opportunity.downloadpdf', compact('terms', 'scope', 'intro', 'about', 'cover', 'imagePaths', 'logo', 'opportunity'));
    }

    public function storeDefaultSettingsLogoDelete(Request $request)
    {

        // $imagePath = null;

        $existingRecord = DB::table('default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'logo')
            ->delete();

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function storeDefaultSettingsLogo(Request $request)
    {

        // $imagePath = null;

        $existingRecord = DB::table('default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'logo')
            ->first();

        if ($existingRecord) {
            $file = $request->file('image_file');
            $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');

            DB::table('default_settings')->where('id', $existingRecord->id)->update([
                'cov_image' => $imagePath,
                'updated_at' => now(),
            ]);
            $data = DB::table('default_settings')->where('id', $existingRecord->id)->first();
        } else {

            $file = $request->file('image_file');
            $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');

            $id = DB::table('default_settings')->insertGetId([

                'cov_image' => $imagePath,
                'setting_type' => 'logo',
                'organization_id' => getOrganizationId(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $data = DB::table('default_settings')->where('id', $id)->first();
        }

        return response()->json(['message' => 'Record saved successfully.', 'data' => $data]);
    }

    public function storeDefaultSettings(Request $request)
    {
        $imagePath = null;
        $opportunity_id = $request->input('opportunity_id');

        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'cover')
            ->first();

        if ($existingRecord) {
            if ($request->hasFile('image_file')) {

                $file = $request->file('image_file');
                $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');
                DB::table('default_settings')->where('id', $existingRecord->id)->update([
                    'cov_title' => $request->input('cov_title'),
                    'cov_sub_title' => $request->input('sub_tit'),
                    'cov_image' => $imagePath,
                    'updated_at' => now(),
                ]);
            } else {
                DB::table('default_settings')->where('id', $existingRecord->id)->update([
                    'cov_title' => $request->input('cov_title'),
                    'cov_sub_title' => $request->input('sub_tit'),
                    'updated_at' => now(),
                ]);
            }
        } else {

            DB::table('default_settings')->insert([
                'cov_title' => $request->input('cov_title'),
                'cov_sub_title' => $request->input('sub_tit'),
                'cov_image' => $imagePath,
                'setting_type' => 'cover',
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        $project_checked = (! empty($request->input('project_checked')) && $request->input('project_checked') != 'false') ? true : false;
        // dd($project_checked);
        if (! empty($project_checked)) {
            $existingRecordOrg = DB::table('organization_default_settings')
                ->where('organization_id', getOrganizationId())
                ->where('setting_type', 'cover')
                ->first();

            if ($existingRecordOrg) {
                if ($request->hasFile('image_file')) {

                    $file = $request->file('image_file');
                    $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');
                    DB::table('organization_default_settings')->where('id', $existingRecordOrg->id)->update([
                        'cov_title' => $request->input('cov_title'),
                        'cov_sub_title' => $request->input('sub_tit'),
                        'cov_image' => $imagePath,
                        'updated_at' => now(),
                    ]);
                } else {
                    DB::table('organization_default_settings')->where('id', $existingRecordOrg->id)->update([
                        'cov_title' => $request->input('cov_title'),
                        'cov_sub_title' => $request->input('sub_tit'),
                        'updated_at' => now(),
                    ]);
                }
            } else {

                DB::table('organization_default_settings')->insert([
                    'cov_title' => $request->input('cov_title'),
                    'cov_sub_title' => $request->input('sub_tit'),
                    'cov_image' => $imagePath,
                    'setting_type' => 'cover',
                    'organization_id' => getOrganizationId(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function changeCoverToggle(Request $request)
    {

        $opportunity_id = $request->opp_id;
        // dd($opportunity_id);

        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'cover')
            ->first();
        // dd($existingRecord);
        if ($existingRecord) {
            DB::table('default_settings')->where('id', $existingRecord->id)->update([
                'is_shown' => $request->input('case'),
            ]);
            // dd($request->input('case'));
        } else {

            DB::table('default_settings')->insert([
                'is_shown' => $request->input('case'),
                'setting_type' => 'cover',
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function storeDefaultSettingsAbout(Request $request)
    {

        // $imagePath = null;
        $opportunity_id = $request->input('opportunity_id');

        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'about')
            ->first();

        if ($existingRecord) {
            if ($request->hasFile('image_file')) {

                $file = $request->file('image_file');
                $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');

                DB::table('default_settings')->where('id', $existingRecord->id)->update([
                    'payment_schedule' => $request->input('intro'),
                    'cov_image' => $imagePath,
                    'updated_at' => now(),
                ]);
            } else {
                DB::table('default_settings')->where('id', $existingRecord->id)->update([
                    'payment_schedule' => $request->input('intro'),
                    'updated_at' => now(),
                ]);
            }
        } else {
            if ($request->hasFile('image_file')) {

                $file = $request->file('image_file');
                $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');

                DB::table('default_settings')->insert([
                    'payment_schedule' => $request->input('intro'),
                    'cov_image' => $imagePath,
                    'setting_type' => 'about',
                    'organization_id' => getOrganizationId(),
                    'opportunity_id' => $opportunity_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            } else {
                DB::table('default_settings')->insert([
                    'payment_schedule' => $request->input('intro'),
                    // 'cov_image' => $imagePath,
                    'setting_type' => 'about',
                    'organization_id' => getOrganizationId(),
                    'opportunity_id' => $opportunity_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
        $project_checked = (! empty($request->input('project_checked')) && $request->input('project_checked') != 'false') ? true : false;
        if (! empty($project_checked)) {
            $existingRecordOrg = DB::table('organization_default_settings')
                ->where('organization_id', getOrganizationId())
                ->where('setting_type', 'about')
                ->first();
            if ($existingRecordOrg) {
                if ($request->hasFile('image_file')) {

                    $file = $request->file('image_file');
                    $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');

                    DB::table('organization_default_settings')->where('id', $existingRecordOrg->id)->update([
                        'payment_schedule' => $request->input('intro'),
                        'cov_image' => $imagePath,
                        'updated_at' => now(),
                    ]);
                } else {
                    DB::table('organization_default_settings')->where('id', $existingRecordOrg->id)->update([
                        'payment_schedule' => $request->input('intro'),
                        'updated_at' => now(),
                    ]);
                }
            } else {
                if ($request->hasFile('image_file')) {

                    $file = $request->file('image_file');
                    $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');

                    DB::table('organization_default_settings')->insert([
                        'payment_schedule' => $request->input('intro'),
                        'cov_image' => $imagePath,
                        'setting_type' => 'about',
                        'organization_id' => getOrganizationId(),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                } else {
                    DB::table('organization_default_settings')->insert([
                        'payment_schedule' => $request->input('intro'),
                        // 'cov_image' => $imagePath,
                        'setting_type' => 'about',
                        'organization_id' => getOrganizationId(),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function changeAboutToggle(Request $request)
    {

        $opportunity_id = $request->opp_id;
        // dd($opportunity_id);

        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'about')
            ->first();
        // dd($existingRecord);
        if ($existingRecord) {
            DB::table('default_settings')->where('id', $existingRecord->id)->update([
                'is_shown' => $request->input('case'),
            ]);
            // dd($request->input('case'));
        } else {

            DB::table('default_settings')->insert([
                'is_shown' => $request->input('case'),
                'setting_type' => 'about',
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function changeIntroToggle(Request $request)
    {
        $opportunity_id = $request->opp_id;
        // dd($opportunity_id);

        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'intro')
            ->first();
        // dd($existingRecord);
        if ($existingRecord) {
            DB::table('default_settings')->where('id', $existingRecord->id)->update([
                'is_shown' => $request->input('case'),
            ]);
            // dd($request->input('case'));
        } else {

            DB::table('default_settings')->insert([
                'is_shown' => $request->input('case'),
                'setting_type' => 'intro',
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function changeScopeToggle(Request $request)
    {
        $opportunity_id = $request->opp_id;
        // dd($opportunity_id);

        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'scope')
            ->first();
        // dd($existingRecord);
        if ($existingRecord) {
            DB::table('default_settings')->where('id', $existingRecord->id)->update([
                'is_shown' => $request->input('case'),
            ]);
            // dd($request->input('case'));
        } else {

            DB::table('default_settings')->insert([
                'is_shown' => $request->input('case'),
                'setting_type' => 'scope',
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function changeTermsToggle(Request $request)
    {
        $opportunity_id = $request->opp_id;
        // dd($opportunity_id);

        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'terms')
            ->first();
        // dd($existingRecord);
        if ($existingRecord) {
            DB::table('default_settings')->where('id', $existingRecord->id)->update([
                'is_shown' => $request->input('case'),
            ]);
            // dd($request->input('case'));
        } else {

            DB::table('default_settings')->insert([
                'is_shown' => $request->input('case'),
                'setting_type' => 'terms',
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function changePaymentsToggle(Request $request)
    {
        $opportunity_id = $request->opp_id;
        // dd($opportunity_id);

        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'payment')
            ->first();
        // dd($existingRecord);
        if ($existingRecord) {
            DB::table('default_settings')->where('id', $existingRecord->id)->update([
                'is_shown' => $request->input('case'),
            ]);
            // dd($request->input('case'));
        } else {

            DB::table('default_settings')->insert([
                'is_shown' => $request->input('case'),
                'setting_type' => 'payment',
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function changeGalleryToggle(Request $request)
    {
        $opportunity_id = $request->opp_id;
        // dd($opportunity_id);

        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'gallery')
            ->first();
        // dd($existingRecord);
        if ($existingRecord) {
            DB::table('default_settings')->where('id', $existingRecord->id)->update([
                'is_shown' => $request->input('case'),
            ]);
            // dd($request->input('case'));
        } else {

            DB::table('default_settings')->insert([
                'is_shown' => $request->input('case'),
                'setting_type' => 'gallery',
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function storeDefaultSettingsPayment(Request $request)
    {
        // $imagePath = null;
        $opportunity_id = $request->input('opportunity_id');
        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'payment')
            ->first();

        if ($existingRecord) {

            DB::table('default_settings')->where('id', $existingRecord->id)->update([
                'expiry' => $request->input('date'),
                'cov_title' => $request->input('getdown2'),
                'cov_sub_title' => $request->input('getdown1'),
                'payment_schedule' => $request->input('paymentschedle'),
                'updated_at' => now(),
            ]);
        } else {

            DB::table('default_settings')->insert([
                'expiry' => $request->input('date'),
                'cov_title' => $request->input('getdown2'),
                'cov_sub_title' => $request->input('getdown1'),
                'payment_schedule' => $request->input('paymentschedle'),
                'setting_type' => 'payment',
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        $project_checked = (! empty($request->input('project_checked')) && $request->input('project_checked') != 'false') ? true : false;
        if (! empty($project_checked)) {
            $existingRecordOrg = DB::table('organization_default_settings')
                ->where('organization_id', getOrganizationId())
                ->where('setting_type', 'payment')
                ->first();

            if ($existingRecordOrg) {

                DB::table('organization_default_settings')->where('id', $existingRecordOrg->id)->update([
                    'expiry' => $request->input('date'),
                    'cov_title' => $request->input('getdown2'),
                    'cov_sub_title' => $request->input('getdown1'),
                    'payment_schedule' => $request->input('paymentschedle'),
                    'updated_at' => now(),
                ]);
            } else {

                DB::table('organization_default_settings')->insert([
                    'expiry' => $request->input('date'),
                    'cov_title' => $request->input('getdown2'),
                    'cov_sub_title' => $request->input('getdown1'),
                    'payment_schedule' => $request->input('paymentschedle'),
                    'setting_type' => 'payment',
                    'organization_id' => getOrganizationId(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function storeDefaultSettingsGallery(Request $request)
    {
        $request->validate([
            'image_files' => 'required|mimes:jpg,jpeg,png,gif|max:2048', // Validate each file
            'title' => 'required|string|max:255',
        ]);

        $file = $request->file('image_files'); // Get all uploaded files

        $filename = time().'_'.$file->getClientOriginalName();
        $file->storeAs('uploads/gallery', $filename, 'public');
        $savedFileNames = $filename;

        $opportunity_id = $request->input('opportunity_id');

        // Fetch the existing gallery column if exists
        $defaultSettings = DB::table('default_settings')->where('organization_id', getOrganizationId())
            ->where('setting_type', 'gallery')->first();

        DB::table('default_settings')->insert([
            'payment_schedule' => $savedFileNames,
            'cov_title' => $request->title,
            'setting_type' => 'gallery',
            'organization_id' => getOrganizationId(),
            'opportunity_id' => $opportunity_id,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return response()->json(['message' => 'Gallery updated successfully!', 'gallery' => $savedFileNames]);
    }

    public function storeDefaultSettingsIntro(Request $request)
    {
        $opportunity_id = $request->input('opportunity_id');
        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'intro')
            ->first();

        if ($existingRecord) {

            DB::table('default_settings')->where('id', $existingRecord->id)->update([
                'payment_schedule' => $request->input('intro'),
                'updated_at' => now(),
            ]);
        } else {

            DB::table('default_settings')->insert([
                'payment_schedule' => $request->input('intro'),
                // 'cov_image' => $imagePath,
                'setting_type' => 'intro',
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        $project_checked = (! empty($request->input('project_checked')) && $request->input('project_checked') != 'false') ? true : false;
        if (! empty($project_checked)) {
            $existingRecordOrg = DB::table('organization_default_settings')
                ->where('organization_id', getOrganizationId())
                ->where('setting_type', 'intro')
                ->first();

            if ($existingRecordOrg) {

                DB::table('organization_default_settings')->where('id', $existingRecordOrg->id)->update([
                    'payment_schedule' => $request->input('intro'),
                    'updated_at' => now(),
                ]);
            } else {

                DB::table('organization_default_settings')->insert([
                    'payment_schedule' => $request->input('intro'),
                    // 'cov_image' => $imagePath,
                    'setting_type' => 'intro',
                    'organization_id' => getOrganizationId(),
                    // 'opportunity_id' => $opportunity_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function storeDefaultSettingsTerms(Request $request)
    {

        // $imagePath = null;
        $opportunity_id = $request->input('opportunity_id');
        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'terms')
            ->first();

        if ($existingRecord) {

            DB::table('default_settings')->where('id', $existingRecord->id)->update([
                'payment_schedule' => $request->input('intro'),
                'updated_at' => now(),
            ]);
        } else {

            DB::table('default_settings')->insert([
                'payment_schedule' => $request->input('intro'),
                // 'cov_image' => $imagePath,
                'setting_type' => 'terms',
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        $project_checked = (! empty($request->input('project_checked')) && $request->input('project_checked') != 'false') ? true : false;
        if (! empty($project_checked)) {
            $existingRecordOrg = DB::table('organization_default_settings')
                ->where('organization_id', getOrganizationId())
                ->where('setting_type', 'terms')
                ->first();

            if ($existingRecordOrg) {

                DB::table('organization_default_settings')->where('id', $existingRecordOrg->id)->update([
                    'payment_schedule' => $request->input('intro'),
                    'updated_at' => now(),
                ]);
            } else {

                DB::table('organization_default_settings')->insert([
                    'payment_schedule' => $request->input('intro'),
                    // 'cov_image' => $imagePath,
                    'setting_type' => 'terms',
                    'organization_id' => getOrganizationId(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function storeDefaultSettingsScope(Request $request)
    {
        // Check for an existing record in 'default_settings'
        $opportunity_id = $request->input('opportunity_id');
        $existingRecord = DB::table('default_settings')
            ->where('opportunity_id', $opportunity_id)
            ->where('setting_type', 'scope')
            ->first();

        // Check if multiple files are uploaded

        // Convert paths array to JSON for storage

        if ($existingRecord) {
            // Update record if it exists
            $updateData = [
                'cov_title' => $request->input('cov_title'),
                'cov_sub_title' => $request->input('sub_tit'),
                'updated_at' => now(),
            ];

            // Only update image if new files are uploaded

            DB::table('default_settings')->where('id', $existingRecord->id)->update($updateData);
        } else {
            // Insert a new record if one doesn't exist
            DB::table('default_settings')->insert([
                'cov_title' => $request->input('cov_title'),
                'cov_sub_title' => $request->input('sub_tit'),
                'setting_type' => 'scope',
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunity_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        $project_checked = (! empty($request->input('project_checked')) && $request->input('project_checked') != 'false') ? true : false;
        if (! empty($project_checked)) {
            $existingRecordOrg = DB::table('organization_default_settings')
                ->where('organization_id', getOrganizationId())
                ->where('setting_type', 'scope')
                ->first();

            // Check if multiple files are uploaded

            // Convert paths array to JSON for storage

            if ($existingRecordOrg) {
                // Update record if it exists
                $updateData = [
                    'cov_title' => $request->input('cov_title'),
                    'cov_sub_title' => $request->input('sub_tit'),
                    'updated_at' => now(),
                ];

                // Only update image if new files are uploaded

                DB::table('organization_default_settings')->where('id', $existingRecordOrg->id)->update($updateData);
            } else {
                // Insert a new record if one doesn't exist
                DB::table('organization_default_settings')->insert([
                    'cov_title' => $request->input('cov_title'),
                    'cov_sub_title' => $request->input('sub_tit'),
                    'setting_type' => 'scope',
                    'organization_id' => getOrganizationId(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function getDivisionData(Request $req)
    {
        $divisionId = $req->input('material');
        $organizationId = getOrganizationId();

        $equipment = DB::table('equipment')
            ->leftJoin('fav_items', function ($join) {
                $join->on('equipment.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'equipment'); // Assuming item_type is used to distinguish items
            })
            ->where('equipment.organization_id', $organizationId)
            ->select('equipment.*', DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
            ->get();
        $hard_materials = DB::table('hard_materials')
            ->leftJoin('fav_items', function ($join) {
                $join->on('hard_materials.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'hard_materials'); // Assuming item_type is used to distinguish items
            })
            ->where('hard_materials.organization_id', $organizationId)
            ->select('hard_materials.*', DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
            ->get();

        $plant_materials = DB::table('plant_materials')
            ->leftJoin('fav_items', function ($join) {
                $join->on('plant_materials.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'plant_materials'); // Assuming item_type is used to distinguish items
            })
            ->where('plant_materials.organization_id', $organizationId)
            ->select('plant_materials.*', DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
            ->get();

        $other_job_costs = DB::table('other_costs')
            ->leftJoin('fav_items', function ($join) {
                $join->on('other_costs.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'other_costs'); // Assuming item_type is used to distinguish items
            })
            ->where('other_costs.organization_id', $organizationId)
            ->select('other_costs.*', DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
            ->get();
        $labors = DB::table('labors')
            ->leftJoin('fav_items', function ($join) {
                $join->on('labors.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'labors'); // Assuming item_type is used to distinguish items
            })
            ->where('labors.organization_id', $organizationId)
            ->select('labors.*', DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
            ->get();

        $sub_contractor = DB::table('subcontractors')
            ->leftJoin('fav_items', function ($join) {
                $join->on('subcontractors.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'contractors'); // Assuming item_type is used to distinguish items
            })
            ->where('subcontractors.organization_id', $organizationId)
            ->select('subcontractors.*', DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
            ->get();

        // Return JSON response with all data for AJAX
        return response()->json([
            'equipment' => $equipment,
            'hard_materials' => $hard_materials,
            'plant_materials' => $plant_materials,
            'other_job_costs' => $other_job_costs,
            'labors' => $labors,
            'sub_contractor' => $sub_contractor,
        ]);
    }

    public function getDivisionDataFav(Request $req)
    {
        $divisionId = $req->input('material');
        $organizationId = getOrganizationId();

        // Equipment with fav_items inner join
        $equipment = DB::table('equipment')
            ->join('fav_items', function ($join) {
                $join->on('equipment.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'equipment');
            })

            ->where('equipment.organization_id', $organizationId)
            ->select(
                'equipment.*',
                DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite')
            )
            ->get();

        // dd($equipment);
        // Hard Materials with fav_items inner join
        $hard_materials = DB::table('hard_materials')
            ->join('fav_items', function ($join) {
                $join->on('hard_materials.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'hard_materials');
            })

            ->where('hard_materials.organization_id', $organizationId)
            ->select(
                'hard_materials.*',
                DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite')
            )
            ->get();

        // Plant Materials with fav_items inner join
        $plant_materials = DB::table('plant_materials')
            ->join('fav_items', function ($join) {
                $join->on('plant_materials.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'plant_materials');
            })

            ->where('plant_materials.organization_id', $organizationId)
            ->select(
                'plant_materials.*',
                DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite')
            )
            ->get();

        // Other Job Costs with fav_items inner join
        $other_job_costs = DB::table('other_costs')
            ->join('fav_items', function ($join) {
                $join->on('other_costs.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'other_costs'); // Assuming item_type is used to distinguish items
            })

            ->where('other_costs.organization_id', $organizationId)
            ->select(
                'other_costs.*',
                DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite')
            )
            ->get();

        // Labors with fav_items inner join
        $labors = DB::table('labors')
            ->join('fav_items', function ($join) {
                $join->on('labors.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'labors'); // Assuming item_type is used to distinguish items
            })

            ->where('labors.organization_id', $organizationId)
            ->select(
                'labors.*',
                DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite')
            )
            ->get();

        $sub_contractor = DB::table('subcontractors')
            ->join('fav_items', function ($join) {
                $join->on('subcontractors.id', '=', 'fav_items.item_id')
                    ->where('fav_items.type', 'contractors'); // Assuming item_type is used to distinguish items
            })
            ->leftJoin('estimate_items', function ($join) {
                $join->on('subcontractors.id', '=', 'estimate_items.item_id')
                    ->where('estimate_items.category_type', 'contractors');
            })
            ->where('subcontractors.organization_id', $organizationId)
            ->select(
                'subcontractors.*',
                DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite')
            )
            ->get();

        // Return JSON response with all filtered data for AJAX
        return response()->json([
            'equipment' => $equipment,
            'hard_materials' => $hard_materials,
            'plant_materials' => $plant_materials,
            'other_job_costs' => $other_job_costs,
            'labors' => $labors,
            'sub_contractor' => $sub_contractor,
        ]);
    }

    public function addNewItem(Request $req)
    {
        if ($req->input('category_list') == 'equipment') {
            $data = [
                'name' => $req->input('item_name'),
                'gross_margin' => $req->input('item_gross_margin'),
                'cost' => $req->input('item_unitcost'),
                'uom' => $req->input('item_uom'),
                'organization_id' => getOrganizationId(),
                'division_id' => $req->input('division_id'),
            ];
            $equipment = new Equipment;
            $equipment->fill($data);
            $equipment->save();
            $addedEquipment = $equipment;
            $margin = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', "Equipment's")->first();
        } elseif ($req->input('category_list') == 'labors') {
            $data = [
                'name' => $req->input('item_name'),
                'gross_margin' => $req->input('item_gross_margin'),
                'cost' => $req->input('item_unitcost'),
                'uom' => $req->input('item_uom'),
                'organization_id' => getOrganizationId(),
                'division_id' => $req->input('division_id'),
            ];
            $labors = new Labor;
            $labors->fill($data);
            $labors->save();
            $addedEquipment = $labors;
            $margin = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', 'Labor')->first();
        } elseif ($req->input('category_list') == 'hard_materials') {
            $data = [
                'name' => $req->input('item_name'),
                // 'gross_margin'=>$req->input('item_gross_margin'),
                'cost' => $req->input('item_unitcost'),
                'uom' => $req->input('item_uom'),
                'organization_id' => getOrganizationId(),
                'division_id' => $req->input('division_id'),
                'depth' => $req->input('depth'),
                'sqft' => $req->input('sqft'),
            ];
            $hardMaterials = new HardMaterial;
            $hardMaterials->fill($data);
            $hardMaterials->save();
            $addedEquipment = $hardMaterials;
            $margin = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', 'Hard Material')->first();
        } elseif ($req->input('category_list') == 'plant_materials') {
            $data = [
                'name' => $req->input('item_name'),
                'gross_margin' => $req->input('item_gross_margin'),
                'cost' => $req->input('item_unitcost'),
                'size' => $req->input('item_uom'),
                'organization_id' => getOrganizationId(),
                'division_id' => $req->input('division_id'),
            ];
            $plantMaterials = new PlantMaterial;
            $plantMaterials->fill($data);
            $plantMaterials->save();
            $addedEquipment = $plantMaterials;
            $margin = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', 'Plant Material')->first();
        } elseif ($req->input('category_list') == 'other_costs') {
            $data = [
                'name' => $req->input('item_name'),
                // 'gross_margin'=>$req->input('item_gross_margin'),
                'cost' => $req->input('item_unitcost'),
                'uom' => $req->input('item_uom'),
                'organization_id' => getOrganizationId(),
                'division_id' => $req->input('division_id'),
            ];
            $otherJobCosts = new OtherCost;
            $otherJobCosts->fill($data);
            $otherJobCosts->save();
            $addedEquipment = $otherJobCosts;
            $margin = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', 'Other Job Costs')->first();
        } elseif ($req->input('category_list') == 'contractors') {
            $data = [
                'name' => $req->input('item_name'),
                'gross_margin' => $req->input('item_gross_margin'),
                'cost' => $req->input('item_unitcost'),
                'uom' => $req->input('item_uom'),
                'organization_id' => getOrganizationId(),
                'division_id' => $req->input('division_id'),
            ];
            $contr = new Subcontractor;
            $contr->fill($data);
            $contr->save();
            $addedEquipment = $contr;
            $margin = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', 'contractor')->first();
        }

        return response()->json([
            'success' => true,
            'message' => 'Equipment Added Successfully',
            'data' => $addedEquipment,
            'margin' => $margin,
        ]);
    }

    public function addFavItem(Request $req)
    {
        $data = [
            'type' => $req->input('type'),
            'item_id' => $req->input('id'),
            'organization_id' => getOrganizationId(),
        ];
        DB::table('fav_items')->insert($data);

        return response()->json([
            'success' => true,
            'message' => 'Item Added To Favourite Successfully',
        ]);
    }

    public function deleteFavItem(Request $req)
    {

        $id = $req->input('id');
        $type = $req->input('type');
        $orgid = getOrganizationId();
        DB::table('fav_items')->where('item_id', $id)->where('type', $type)->where('organization_id', $orgid)->delete();

        return response()->json([
            'success' => true,
            'message' => 'Item Deleted From Favourite Successfully',
        ]);
    }

    public function getDataId(Request $req)
    {
        $id = $req->input('id');
        $equipment = DB::table('equipment')->where('id', $id)->first();
        $margins = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', "Equipment's")->first();

        return response()->json([
            'equipment' => $equipment,
            'margins' => $margins,
        ]);
    }

    public function getDataHard(Request $req)
    {
        $id = $req->input('id');
        $equipment = DB::table('hard_materials')->where('id', $id)->first();
        $margins = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', 'Hard Material')->first();

        return response()->json([
            'equipment' => $equipment,
            'margins' => $margins,
        ]);
    }

    public function getDataPlant(Request $req)
    {
        $id = $req->input('id');
        $equipment = DB::table('plant_materials')->where('id', $id)->first();
        $margins = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', 'Plant Material')->first();

        return response()->json([
            'equipment' => $equipment,
            'margins' => $margins,
        ]);
    }

    public function getDataCost(Request $req)
    {
        $id = $req->input('id');
        $equipment = DB::table('other_costs')->where('id', $id)->first();
        $margins = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', 'Other Job Costs')->first();

        return response()->json([
            'equipment' => $equipment,
            'margins' => $margins,
        ]);
    }

    public function getDataLabor(Request $req)
    {
        $id = $req->input('id');
        $equipment = DB::table('labors')->where('id', $id)->first();
        $margins = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', 'Labor')->first();

        return response()->json([
            'equipment' => $equipment,
            'margins' => $margins,
        ]);
    }

    public function getDataContractor(Request $req)
    {
        $id = $req->input('id');
        $equipment = DB::table('subcontractors')->where('id', $id)->first();
        // dd($equipment);
        $margins = DB::table('margins')->where('organization_id', getOrganizationId())->where('name', 'Contractor')->first();

        return response()->json([
            'equipment' => $equipment,
            'margins' => $margins,
        ]);
    }

    public function opportunityEstimatesHistory($id)
    {
        // Logic for showing the estimates history related to an opportunity
    }

    public function sendEmailToOpportunity(Request $request)
    {
        // Logic for sending an email related to an opportunity
    }

    public function opportunityFileImport(Request $request)
    {
        // Logic for importing opportunities from a file
    }

    public function getServiceLines($division_id)
    {
        $serviceLines = ServiceLine::where('division_id', $division_id)->get();

        return response()->json($serviceLines);
    }

    public function exportOpportunities(Request $request)
    {
        $organizationId = getOrganizationId();
        $opportunities = Opportunity::where('organization_id', $organizationId)
            ->with(['propertyInformation', 'contactInformation', 'divisionDetails.division'])
            ->get();

        return Excel::download(new OpportunitiesExport($opportunities), 'organization.opportunities.xlsx');
    }

    private function getSalesMan()
    {
        return User::wherehas('roles', function ($q) {
            $q->where('name', 'salesman');
        })->where([
            'parent_id' => getOrganizationId(),
            'status' => 'Active',
        ])->get();
    }

    private function getEstimator()
    {

        return User::wherehas('roles', function ($q) {
            $q->where('name', 'estimator');
        })
            ->where([
                'parent_id' => getOrganizationId(),
                'status' => 'Active',
            ])->get();
    }

    private function accountManager()
    {

        return User::wherehas('roles', function ($q) {
            $q->where('name', 'manager');
        })
            ->where([
                'parent_id' => getOrganizationId(),
                'status' => 'Active',
            ])->get();
    }

    public function getPropertyDetails(Request $request, $property_id)
    {
        $property = PropertyInformation::find($property_id);
        $def = DB::table('contacts')->where('property_id', $property_id)->where('property_default', 1)->first();

        if ($property) {
            return response()->json([
                'default' => $def,
                'name' => $property->name,
                'address1' => $property->address1,
                'address2' => $property->address2,
                'city' => $property->city,
                'state' => $property->state,
                'zip_code' => $property->zip,
                'company_id' => $property->company_id,
            ]);
        } else {
            return response()->json(['error' => 'Property not found'], 404);
        }
    }

    public function getContactDetails(Request $request, $contact_id)
    {
        $contact = Contact::where('account', $contact_id)->get();
        $account = Account::where('id', $contact_id)->first();
        $users = User::where('id', $account->account_owner)->first();

        if ($contact) {
            return response()->json(['contact' => $contact, 'users' => $users]);
        } else {
            return response()->json(['error' => 'Contact not found'], 404);
        }
    }

    public function getContactDataDetails(Request $request, $contact_id)
    {
        $contact = Contact::find($contact_id);

        if ($contact) {
            return response()->json([
                'fname' => $contact->first_name,
                'id' => $contact->id,
                'lname' => $contact->last_name,
                'title' => $contact->title,
                'role' => $contact->role,
                'phone_number' => $contact->phone_number,
                'second_phone' => $contact->second_phone,
                'email' => $contact->email,
                'second_email' => $contact->second_email,
                'mailing_address' => $contact->mailing_address,
            ]);
        } else {
            return response()->json(['error' => 'Contact not found'], 404);
        }
    }

    public function storeOrUpdateProposalDetail(Request $request)
    {
        // Validate the request data if needed
        $validatedData = $request->validate([
            'opportunity_id' => 'required',
            'contract_start_date' => 'required|date',
            'contract_end_date' => 'required|date',
            'contract_terms_months' => 'required|integer',
        ]);

        // Use updateOrCreate to efficiently handle updates or creations
        $proposal = Proposal::updateOrCreate(
            ['opportunity_id' => $request->input('opportunity_id')],
            $validatedData
        );

        // Return a response
        return response()->json(['success' => true, 'data' => $proposal, 'message' => 'Proposal saved successfully']);
    }

    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|integer|between:1,7',
        ]);

        $opportunity = Opportunity::findOrFail($id);
        $currentStatus = OpportunityStatus::from($opportunity->status);
        $newStatus = OpportunityStatus::from($request->status);

        // Check if moving from Open status to Estimating
        if ($currentStatus === OpportunityStatus::OPEN && $newStatus === OpportunityStatus::ESTIMATING) {
            // Check if opportunity has estimates
            $hasEstimates = $opportunity->generateEstimate()->exists() || $opportunity->estimateItem()->exists();

            if (! $hasEstimates) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please add estimate to update the status',
                    'redirect' => true,
                ], 400);
            }
        }

        // Special handling for status 3 (proposed) - replicate manual proposal sending behavior
        if ($request->status == 3) {
            // Update GenerateEstimate status to 'proposed' (same as manual proposal sending)
            $generateEstimate = $opportunity->generateEstimate()->first();
            if ($generateEstimate) {
                $generateEstimate->update([
                    'status' => 'proposed',
                ]);

                // Calculate total price for the estimate
                $totalPrice = $opportunity->estimateItem()->sum('total_price') ?? 0;

                // Create EstimateStatus record (same as manual proposal sending)
                \App\Models\EstimateStatus::create([
                    'generate_estimate_id' => $generateEstimate->id,
                    'status_name' => 'proposed',
                    'created_at' => now(),
                    'estimate_total_price' => $totalPrice,
                    'pdf_path' => null, // Will be null since we're not generating PDF automatically
                    'opportunity_id' => $opportunity->id,
                ]);
            }

            // Update opportunity with client_show = 1 (same as manual proposal sending)
            $opportunity->update([
                'status' => $request->status,
                'client_show' => 1,
            ]);
        } else {
            // For other statuses, update normally
            $opportunity->status = $request->status;
        }

        if ($request->filled('reason')) {
            $opportunity->reason = $request->reason;
        }

        $opportunity->save();

        return response()->json(['success' => true, 'message' => 'Status updated successfully']);
    }

    public function saveFinalStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|integer|in:5,7',
            'closed_lost_reasons' => 'array|nullable',
            'closed_lost_reasons.*' => 'string|in:'.implode(',', ClosedLostReason::values()),
        ]);

        $opportunity = Opportunity::findOrFail($id);
        $generateEstimate = $opportunity->generateEstimate()->first();

        if (! $generateEstimate) {
            return response()->json(['success' => false, 'message' => 'No estimate found for this opportunity'], 400);
        }

        // Calculate total price for EstimateStatus
        $totalPrice = $opportunity->estimateItem()->sum('total_price') ?? 0;

        if ($request->status == OpportunityStatus::CLOSED_LOST->value) {
            // Replicate client reject behavior

            // Create ClientEstimateAction (simulate client reject)
            \App\Models\ClientEstimateAction::updateOrCreate([
                'client_id' => $opportunity->contact_id,
                'generate_estimate_id' => $generateEstimate->id,
            ], [
                'status' => \App\Models\GenerateEstimate::$CLIENT_REJECT_STATUS,
                'description' => $request->filled('closed_lost_reasons') ? implode(', ', $request->closed_lost_reasons) : 'Closed Lost',
            ]);

            // Update GenerateEstimate status to 'lost'
            $generateEstimate->update([
                'status' => \App\Models\GenerateEstimate::$LOST,
                'client_status' => \App\Models\GenerateEstimate::$CLIENT_REJECT_STATUS,
                'reason' => $request->filled('closed_lost_reasons') ? implode(', ', $request->closed_lost_reasons) : 'Closed Lost',
            ]);

            // Create EstimateStatus record (like client reject)
            \App\Models\EstimateStatus::create([
                'generate_estimate_id' => $generateEstimate->id,
                'status_name' => 'reject',
                'created_at' => now(),
                'opportunity_id' => $opportunity->id,
                'estimate_total_price' => $totalPrice,
            ]);

        } elseif ($request->status == OpportunityStatus::CLOSED_WIND->value) {
            // Replicate client approve behavior

            // Create ClientEstimateAction (simulate client approve)
            \App\Models\ClientEstimateAction::updateOrCreate([
                'client_id' => $opportunity->contact_id,
                'generate_estimate_id' => $generateEstimate->id,
            ], [
                'status' => \App\Models\GenerateEstimate::$CLIENT_APPROVE_STATUS,
                'signature_text' => 'System Generated - Mark as Complete',
                'signature_date' => now(),
            ]);

            // Update GenerateEstimate status to 'won'
            $generateEstimate->update([
                'status' => \App\Models\GenerateEstimate::$WON,
                'client_status' => \App\Models\GenerateEstimate::$CLIENT_APPROVE_STATUS,
                'won_date' => now(),
            ]);

            // Generate sales order number if not exists
            if ($opportunity->sales_order_number == 0 || $opportunity->sales_order_number == null) {
                $opportunity->sales_order_number = \Illuminate\Support\Facades\DB::table('opportunities')
                    ->where('organization_id', $opportunity->organization_id)
                    ->max('sales_order_number') + 1;
            }

            // Create EstimateStatus record (like client approve)
            \App\Models\EstimateStatus::create([
                'generate_estimate_id' => $generateEstimate->id,
                'status_name' => 'approve',
                'created_at' => now(),
                'opportunity_id' => $opportunity->id,
                'estimate_total_price' => $totalPrice,
            ]);
        }

        $updateData = ['status' => $request->status];

        // If status is Closed Lost (5) and reasons are provided, store them
        if ($request->status == OpportunityStatus::CLOSED_LOST->value && $request->filled('closed_lost_reasons')) {
            $updateData['closed_lost_reasons'] = $request->closed_lost_reasons;
        }

        $opportunity->update($updateData);

        return response()->json(['success' => true, 'message' => 'Final status updated successfully']);
    }

    public function simulateClientRequestChange(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:1000',
        ]);

        $opportunity = Opportunity::findOrFail($id);
        $generateEstimate = $opportunity->generateEstimate()->first();

        if (! $generateEstimate) {
            return response()->json(['success' => false, 'message' => 'No estimate found for this opportunity'], 400);
        }

        // Calculate total price for EstimateStatus
        $totalPrice = $opportunity->estimateItem()->sum('total_price') ?? 0;

        // Replicate client request for change behavior

        // Create ClientEstimateAction (simulate client request change)
        \App\Models\ClientEstimateAction::updateOrCreate([
            'client_id' => $opportunity->contact_id,
            'generate_estimate_id' => $generateEstimate->id,
        ], [
            'status' => \App\Models\GenerateEstimate::$CLIENT_REQUEST_CHANGE_STATUS,
            'description' => $request->reason,
        ]);

        // Update GenerateEstimate client_status to 'request_change'
        $generateEstimate->update([
            'client_status' => \App\Models\GenerateEstimate::$CLIENT_REQUEST_CHANGE_STATUS,
        ]);

        // Create EstimateStatus record (like client request change)
        \App\Models\EstimateStatus::create([
            'generate_estimate_id' => $generateEstimate->id,
            'status_name' => 'request_change',
            'created_at' => now(),
            'opportunity_id' => $opportunity->id,
            'estimate_total_price' => $totalPrice,
        ]);

        // Update opportunity status to 4 (revision)
        $opportunity->update(['status' => 4]);

        return response()->json(['success' => true, 'message' => 'Client request for change simulated successfully']);
    }

    public function updateSectionDrag(Request $request)
    {
        // Validate the incoming request
        $request->validate([
            'item_id' => 'required', // Ensure item exists
            'section_id' => 'nullable', // Ensure section exists
        ]);

        try {
            $item = EstimateItem::where('id', $request->item_id)
                ->update(['section_name' => $request->section_id]);

            return response()->json([
                'status' => 'success',
                'message' => 'Section updated successfully',
                'data' => $item,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update section',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function updateDimensions($id, Request $request)
    {
        $item = EstimateItem::findOrFail($id);

        $item->update([
            'depth' => $request->depth,
            'sqft' => $request->sqft,
            'material-qty' => $request->finalMaterialQty,
        ]);

        return response()->json(['success' => true]);
    }

    public function copyDefaultProposalValuesToOpportunity($opportunityId)
    {
        $organizationSettings = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->get();
        foreach ($organizationSettings as $setting) {
            DB::table('default_settings')->insert([
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $opportunityId,
                'cov_title' => $setting->cov_title,
                'cov_sub_title' => $setting->cov_sub_title,
                'cov_image' => $setting->cov_image,
                'payment_schedule' => $setting->payment_schedule,
                'setting_type' => $setting->setting_type,
                'intro' => $setting->intro,
                'project_image' => $setting->project_image,
                'expiry' => $setting->expiry,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    public function OpportunityEstimationNew($id)
    {
        $decodedId = decodeId($id);
        $organizationId = getOrganizationId();
        $organization = User::find($organizationId);
        $saleTax = $organization->sale_tax ?? 0;
        $taxRate = $saleTax / 100;

        $opportunity = Opportunity::with([
            'propertyInformation',
            'salesman',
            'estimator',
            'opportunityOwner',
            'address.company',
            'contactInformation',
            'divisionDetails.division.serviceLines',
            'proposal',
        ])->findOrFail($decodedId);

        $divisions = Division::get();

        // Function to get items with favorite status
        $getItemsWithFavorites = function ($table, $type) use ($organizationId) {
            return DB::table($table)
                ->leftJoin('fav_items', function ($join) use ($type, $table) {
                    $join->on("$table.id", '=', 'fav_items.item_id')
                        ->where('fav_items.type', $type);
                })
                ->where("$table.organization_id", $organizationId)
                ->select("$table.*", DB::raw('IF(fav_items.id IS NOT NULL, true, false) as is_favorite'))
                ->get();
        };

        // Get all item types with favorite status
        $equipments = $getItemsWithFavorites('equipment', 'equipment');
        $hard_materials = $getItemsWithFavorites('hard_materials', 'hard_materials');
        $plant_materials = $getItemsWithFavorites('plant_materials', 'plant_materials');
        $other_job_costs = $getItemsWithFavorites('other_costs', 'other_costs');
        $sub_contractor = $getItemsWithFavorites('subcontractors', 'contractors');
        $labors = $getItemsWithFavorites('labors', 'labors');

        // Get estimate item IDs by category
        $estimateItemsQuery = DB::table('estimate_items')->where('opportunity_id', $decodedId);

        $categoryItems = [
            'equipitems' => $estimateItemsQuery->where('category_type', 'labors')->pluck('item_id')->toArray(),
            'contractorsitems' => $estimateItemsQuery->where('category_type', 'contractors')->pluck('item_id')->toArray(),
            'othersitems' => $estimateItemsQuery->where('category_type', 'other_costs')->pluck('item_id')->toArray(),
            'plantsitems' => $estimateItemsQuery->where('category_type', 'plant_materials')->pluck('item_id')->toArray(),
            'hardssitems' => $estimateItemsQuery->where('category_type', 'hard_materials')->pluck('item_id')->toArray(),
            'equipmentsitems' => $estimateItemsQuery->where('category_type', 'equipment')->pluck('item_id')->toArray(),
        ];

        // Extract variables from the array for backward compatibility
        extract($categoryItems);

        return view('organization.opportunity.opportunity-estimation', compact(
            'opportunity',
            'divisions',
            'equipments',
            'hard_materials',
            'plant_materials',
            'other_job_costs',
            'sub_contractor',
            'labors',
            'estimateItemsQuery',
            'categoryItems'
        ));
    }

    public function updateEstimateItemsMargin(Request $request)
    {
        $opportunityId = $request->get('opportunity_id');
        $inputMargin = $request->get('margin'); // Original input
        $organizationId = getOrganizationId();

        $organization = User::find($organizationId);
        $saleTax = $organization->sale_tax ?? 0;
        $taxRate = $saleTax / 100;

        DB::beginTransaction();

        try {
            $items = EstimateItem::where('opportunity_id', $opportunityId)->get();

            foreach ($items as $item) {
                $unitCost = $item->unit_cost;

                if ($item->category_type === 'contractors') {
                    // Contractors use markup
                    $markup = $inputMargin == 0 ? $this->getDefaultMargin($item->category_type) // default is already markup
                        : $this->calculateMarkupFromGivenGrossMargin($inputMargin); // convert margin to markup
                    $unitPrice = $unitCost * (1 + ($markup / 100));
                    $appliedMargin = $markup; // Save as gross_margin

                } else {
                    // Non-contractors use margin
                    $appliedMargin = $inputMargin == 0 ? $this->getDefaultMargin($item->category_type) : $inputMargin;

                    $unitPrice = $unitCost / (1 - ($appliedMargin / 100));
                }

                $totalPrice = $item->quantity * $unitPrice;

                $item->update([
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice,
                    'gross_margin' => number_format($appliedMargin, 2),
                ]);
            }

            $supervisorHours = $items->where('labor_type', 'Supervision')->sum('quantity');

            $burderlabor = Labor::where('organization_id', $organizationId)->whereNotNull('labor_burden')->first();

            $sectionss = Section::where('opportunity_id', $opportunityId)->where('organization_id', $organizationId)->get();

            $gtems = DB::table('estimate_items')->where('opportunity_id', $opportunityId)->whereNull('section_name')->get();

            $AdjustSellingPriceData = [
                'opportunity_id' => $opportunityId,
                'margin' => $request->get('margin'),
                'total_price' => $request->get('new_selling_price'),
                'added_margin' => $request->get('added_margin'),
                'added_total_price' => $request->get('add_selling_price'),
            ];

            $this->createORUpdateAdjustSellingPriceModel($opportunityId, $AdjustSellingPriceData);

            DB::commit();

            $response = [
                'success' => true,
                'html' => view('organization.opportunity.partials.opportunity-datatable', compact('burderlabor', 'sectionss', 'gtems'))->render(),
                'estimateTotal' => view('organization.opportunity.partials.estimate-total', compact('burderlabor', 'sectionss', 'gtems', 'supervisorHours', 'taxRate'))->render(),
            ];

            return response()->json($response);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to update estimate items: '.$e->getMessage(),
            ], 500);
        }
    }

    private function getDefaultMargin($type)
    {
        return match ($type) {
            'equipment' => Margin::where('organization_id', getOrganizationId())->where('name', "Equipment's")->first()?->default,
            'labors' => Margin::where('organization_id', getOrganizationId())->where('name', 'Labor')->first()?->default,
            'hard_materials' => Margin::where('organization_id', getOrganizationId())->where('name', 'Hard Material')->first()?->default,
            'plant_materials' => Margin::where('organization_id', getOrganizationId())->where('name', 'Plant Material')->first()?->default,
            'other_costs' => Margin::where('organization_id', getOrganizationId())->where('name', 'Other Job Costs')->first()?->default,
            'contractors' => Margin::where('organization_id', getOrganizationId())->where('name', 'Contractor')->first()?->default,
            default => throw new \InvalidArgumentException("Invalid job cost type: $type"),
        };
    }

    private function calculateMarkupFromGivenGrossMargin($grossMargin)
    {
        $margin = $grossMargin / 100;

        return ($margin / (1 - $margin)) * 100;
    }

    private function createORUpdateAdjustSellingPriceModel($opportunity_id, $data)
    {
        AdjustSellingPrice::updateOrCreate(['opportunity_id' => $opportunity_id], $data);
    }
}
