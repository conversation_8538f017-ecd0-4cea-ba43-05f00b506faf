<?php

namespace App\Http\Controllers;

use App\Exports\BookOfBusinessExport;
use App\Models\Account;
use App\Models\CompanyAddress;
use App\Models\Division;
use App\Models\Labor;
use App\Models\Margin;
use App\Models\Opportunity;
use App\Models\RenewalSetting;
use App\Models\ServiceLine;
use App\Models\ServiceLineWorkType;
use App\Models\Subcontractor;
use App\Models\User;
use App\Traits\PermissionMiddlewareTrait;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;

class SettingsController extends Controller
{
    use PermissionMiddlewareTrait;

    public function __construct()
    {
        $permissionsMap = [
            // functionName => [permissions]
            'materials' => ['material_setting'],
            'accountSettings' => ['account_setting'],
            'generalSettings' => ['general_setting'],
            'updateGeneralDetail' => ['general_setting'],
            'updateAddress' => ['account_setting'],
            'branding' => ['estimate_branding'],
            'storeBranding' => ['estimate_branding'],
            'marginSetup' => ['margin_setting'],
            'storeMargin' => ['margin_setting'],

        ];
        $this->applyPermissionMiddleware($permissionsMap);
    }

    public function materials()
    {
        $div = DB::table('divisions')->get();

        return view('organization.material.hard.index', compact('div'));
    }

    public function accountSettings(Request $request)
    {
        $organization = User::where('id', getOrganizationId())->firstOrFail();
        $address = $this->getCompanyAddress(BILLING);

        return view('organization.settings.subscription', get_defined_vars());
    }

    public function generalSettings()
    {
        $company = User::where('id', getOrganizationId())->first();
        $address = $this->getCompanyAddress(PROPERTY);

        return view('organization.settings.general_settings', get_defined_vars());
    }

    public function updateGeneralDetail(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'company_name' => 'required|max:255',
            // 'email' => 'required|email|max:50|unique:users,email,' . ($id),
            'phone_no' => 'required|phone_format',

            'address1' => 'required|max:100',
            'address2' => 'nullable|max:100',
            'state' => 'required|regex: /[a-zA-Z]+$/|max:100',
            'city' => 'required|regex: /[a-zA-Z]+$/|max:100',
            'zip' => 'required|alpha_num|max:10',
            'first_name' => 'required',
            'last_name' => 'required',
            'timezone' => 'required',
        ], [
            'required' => 'This field is required',
        ])->validate();
        CompanyAddress::updateOrCreate([
            'company_id' => getOrganizationId(),
            'type' => PROPERTY,

        ], [
            'address1' => $request->address1,
            'address2' => $request->address2,
            'state' => $request->state,
            'zip' => $request->zip,
            'city' => $request->city,
            'phone_no' => $request->phone_no,
            // 'date_format' => $request->date_format,
            // 'time_format' => $request->time_format,
            'timezone' => $request->timezone,
            'website_url' => $request->website_url,
        ]);
        User::where('id', getOrganizationId())->update([
            // 'email' => $request->email,
            'company_name' => $request->company_name,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
        ]);
        $organizationId = (string) getOrganizationId();

        $cacheKeys = [
            'getOrgDateFormat-'.$organizationId,
            'customTimeFormat-'.$organizationId,
            'customDateFormat-'.$organizationId,
        ];
        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }

        return redirect()->back()->with('success', 'Address Updated Successfully! ');
    }

    public function updateAddress(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'address1' => 'required|max:100',
            'address2' => 'nullable|max:100',
            'phone_no' => 'required|phone_format',
            'state' => 'required|regex: /[a-zA-Z]+$/|max:100',
            'city' => 'required|regex: /[a-zA-Z]+$/|max:100',
            'zip' => 'required|alpha_num|max:10',
        ], [
            'required' => 'This field is required',
        ])->validate();
        CompanyAddress::updateOrCreate([
            'company_id' => getOrganizationId(),
            'type' => BILLING,
        ], [
            'address1' => $request->address1,
            'address2' => $request->address2,
            'state' => $request->state,
            'city' => $request->city,
            'zip' => $request->zip,
            'phone_no' => $request->phone_no,
        ]);
        User::where('id', getOrganizationId())->update(['address' => $request->address1]);

        return redirect()->back()->with('success', 'Address Updated Successfully! ');
    }

    public function branding()
    {
        $organization = User::where('id', getOrganizationId())->firstorfail();

        return view('organization/settings/branding', compact('organization'));
    }

    public function storeBranding(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'image' => 'nullable|image',
            'primary_color' => 'required',
            // 'secondary_color' => 'required',
        ], [
            'primary_color.required' => 'Select color otherwise default color will be saved',
            // 'secondary_color.required' => 'Select color otherwise default color will be saved',
            'image.image' => 'Company logo must be an image.',
        ])->validate();

        if ($request->has('image')) {
            $image = $request->file('image');
            $imageName = uniqid().'_'.trim($image->getClientOriginalName());

            // Open the image using Intervention Image
            $img = \Image::make($image);

            // Check if the image dimensions are not 84x44 pixels
            if ($img->width() > 84 || $img->height() > 44) {
                // Resize the image to 84x44 pixels
                $img->resize(84, 44);
            }

            // Save the resized image
            $filePath = $img->stream()->getContents();
            Storage::disk('public')->put('user_images/'.$imageName, $filePath);
        }

        User::where('id', getOrganizationId())->update([
            'primary_color' => request('primary_color'),
            'secondary_color' => request('primary_color'),
            'profile_photo_path' => $request->has('image') ? $imageName : User::where('id', getOrganizationId())->value('profile_photo_path'),
        ]);

        return redirect()->back()->with('success', 'Settings updated Successfully!');
    }

    public function storeMargin(Request $request)
    {
        // dd($request->all());

        $validator = Validator::make($request->all(), [
            'sale_tax' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'max:99'],
            'Laborers' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/'],
            'contractor_amount' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/'],
            'Supervision' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/'],
            'labor_burden' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'lte:100'],
            "Equipment'sdefault" => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', "gte:Equipment'sminimum", 'lte:100'],
            "Equipment'sminimum" => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'lte:100'],
            'Labordefault' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'gte:Laborminimum', 'lte:100'],
            'Laborminimum' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'lte:100'],
            'Hard_Materialdefault' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'gte:Hard_Materialminimum', 'lte:100'],
            'Hard_Materialminimum' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'lte:100'],
            'Plant_Materialdefault' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'gte:Plant_Materialminimum', 'lte:100'],
            'Plant_Materialminimum' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'lte:100'],
            'Other_Job_Costsdefault' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'gte:Other_Job_Costsminimum', 'lte:100'],
            'Other_Job_Costsminimum' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'lte:100'],
            'contractor_default' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'gte:contractor_minimum'],
            'contractor_minimum' => ['required', 'numeric', 'regex:/^\d+(\.\d+)?$/', 'lte:100'],

        ], [
            'required' => 'This field is required',
            'numeric' => 'This field contains only positive value',
            'regex' => 'This field contains only positive value',
            'gte' => 'Default value cannot be less than minimum value',
            'lte' => 'Value cannot be greater than 100.',
        ])->validate();
        // dd('store margin function');
        Labor::updateOrCreate(
            [
                'name' => 'Laborers',
                'organization_id' => getOrganizationId(),
            ],
            [
                'cost' => $request->Laborers,
                'labor_burden' => $request->labor_burden,
            ]
        );

        Subcontractor::updateOrCreate(
            [
                'name' => 'subcontractor',
                'organization_id' => getOrganizationId(),
            ],
            [
                'cost' => $request->contractor_amount,
                'uom' => 'Hours',
            ]
        );

        Labor::updateOrCreate(
            [
                'name' => 'Supervision',
                'organization_id' => getOrganizationId(),
            ],
            [
                'cost' => $request->Supervision,
                'labor_burden' => $request->labor_burden,
            ]
        );
        Margin::updateOrCreate([
            'name' => "Equipment's",

            'organization_id' => getOrganizationId(),
        ], [
            'default' => request("Equipment'sdefault"),
            'minimum' => request("Equipment'sminimum"),
        ]);
        Margin::updateOrCreate(['name' => 'Labor', 'organization_id' => getOrganizationId()], [
            'default' => request('Labordefault'),
            'minimum' => request('Laborminimum'),
        ]);
        Margin::updateOrCreate(['name' => 'Hard Material', 'organization_id' => getOrganizationId()], [
            'default' => request('Hard_Materialdefault'),
            'minimum' => request('Hard_Materialminimum'),
        ]);
        Margin::updateOrCreate(['name' => 'Plant Material', 'organization_id' => getOrganizationId()], [
            'default' => request('Plant_Materialdefault'),
            'minimum' => request('Plant_Materialminimum'),
        ]);
        Margin::updateOrCreate(['name' => 'Other Job Costs', 'organization_id' => getOrganizationId()], [
            'default' => request('Other_Job_Costsdefault'),
            'minimum' => request('Other_Job_Costsminimum'),
        ]);
        Margin::updateOrCreate(['name' => 'Contractor', 'organization_id' => getOrganizationId()], [

            'default' => request('contractor_default'),
            'minimum' => request('contractor_minimum'),
        ]);
        User::where('id', getOrganizationId())->update([
            'sale_tax' => request('sale_tax'),
        ]);

        return redirect()->back()->with('success', 'Data updated successfully!');
    }

    public function marginSetup()
    {
        $company = User::with('companyBillingAddress')->where('id', getOrganizationId())->first();
        $labors = Labor::where('organization_id', getOrganizationId())->get();
        $contractoram = Subcontractor::where('organization_id', getOrganizationId())->first();
        $margins = Margin::where('organization_id', getOrganizationId())->get();

        $transformed = $margins->map(function ($value) {
            if ($value->name !== 'Contractor') {
                return [
                    'id' => $value->id,
                    'organization_id' => $value->organization_id,
                    'name' => str_replace(' ', '_', $value->name),
                    'default' => $value->default,
                    'minimum' => $value->minimum,
                ];
            }
        })->filter(function ($value) {
            // Filter out empty values
            return ! empty($value);
        });
        $contractor = Margin::where(['organization_id' => getOrganizationId(), 'name' => 'Contractor'])->first();
        $laborBurden = Labor::where(['organization_id' => getOrganizationId()])->value('labor_burden');
        $userSaleTax = User::find(getOrganizationId());

        return view('organization.settings.margin_setup', get_defined_vars());
    }

    private function getCompanyAddress($type)
    {
        return CompanyAddress::where(['company_id' => getOrganizationId(), 'type' => $type])->first();
    }

    public function defaultSettings()
    {

        $organizationId = getOrganizationId();

        $organization = User::where('id', getOrganizationId())->first();

        $coverorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'cover')
            ->first();
        // dd($coverorg);
        $aboutorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'about')
            ->first();

        $introorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'intro')
            ->first();

        $termsorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'terms')
            ->first();

        $paymentorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'payment')
            ->first();

        return view('organization.settings.default_settings', compact('organization', 'coverorg', 'aboutorg', 'introorg', 'termsorg', 'paymentorg'));
    }

    public function storeDefaultSettingsAbout(Request $request)
    {
        // $imagePath = null;
        $existingRecordOrg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'about')
            ->first();
        if ($existingRecordOrg) {
            if ($request->hasFile('image_file')) {

                $file = $request->file('image_file');
                $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');

                DB::table('organization_default_settings')->where('id', $existingRecordOrg->id)->update([
                    'payment_schedule' => $request->input('intro'),
                    'cov_image' => $imagePath,
                    'updated_at' => now(),
                ]);
            } else {
                DB::table('organization_default_settings')->where('id', $existingRecordOrg->id)->update([
                    'payment_schedule' => $request->input('intro'),
                    'updated_at' => now(),
                ]);
            }
        } else {
            if ($request->hasFile('image_file')) {

                $file = $request->file('image_file');
                $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');

                DB::table('organization_default_settings')->insert([
                    'payment_schedule' => $request->input('intro'),
                    'cov_image' => $imagePath,
                    'setting_type' => 'about',
                    'organization_id' => getOrganizationId(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            } else {
                DB::table('organization_default_settings')->insert([
                    'payment_schedule' => $request->input('intro'),
                    // 'cov_image' => $imagePath,
                    'setting_type' => 'about',
                    'organization_id' => getOrganizationId(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function storeDefaultSettingsIntro(Request $request)
    {
        $data = [
            'cov_sub_title' => $request->input('sub_tit'),
            'organization_id' => getOrganizationId(),
            'setting_type' => 'intro',
            'created_at' => now(),
            'updated_at' => now(),
            'project_checked' => (! empty($request->input('project_checked')) && $request->input('project_checked') != 'false') ? true : false,
        ];

        $settings = DB::table('organization_default_settings')->updateOrInsert(
            [
                'organization_id' => getOrganizationId(),
                'setting_type' => 'intro',
            ],
            $data
        );

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function storeDefaultSettingsTerms(Request $request)
    {
        $data = [
            'cov_sub_title' => $request->input('sub_tit'),
            'organization_id' => getOrganizationId(),
            'setting_type' => 'terms',
            'created_at' => now(),
            'updated_at' => now(),
        ];
        $settings = DB::table('organization_default_settings')->updateOrInsert(
            [
                'organization_id' => getOrganizationId(),
                'setting_type' => 'terms',
            ],
            $data
        );

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function storeDefaultSettingsPayment(Request $request)
    {
        $payment_schedule = [
            'down_payment' => $request->input('down_payment'),
            'down_payment_percent' => $request->input('down_payment_percent'),
        ];
        $data = [
            'cov_sub_title' => $request->input('cov_sub_title'),
            'organization_id' => getOrganizationId(),
            'payment_schedule' => json_encode($payment_schedule),
            'setting_type' => 'payment',
            'created_at' => now(),
            'updated_at' => now(),
        ];

        $settings = DB::table('organization_default_settings')->updateOrInsert(
            [
                'organization_id' => getOrganizationId(),
                'setting_type' => 'payment',
            ],
            $data
        );

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function storeDefaultSettings(Request $request)
    {

        $imagePath = null;

        $existingRecordOrg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'cover')
            ->first();

        if ($existingRecordOrg) {
            if ($request->hasFile('image_file')) {

                $file = $request->file('image_file');
                $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');
                \DB::table('organization_default_settings')->where('id', $existingRecordOrg->id)->update([
                    'cov_title' => $request->input('cov_title'),
                    'cov_sub_title' => $request->input('sub_tit'),
                    'cov_image' => $imagePath,
                    'updated_at' => now(),
                ]);
            } else {
                \DB::table('organization_default_settings')->where('id', $existingRecordOrg->id)->update([
                    'cov_title' => $request->input('cov_title'),
                    'cov_sub_title' => $request->input('sub_tit'),
                    'updated_at' => now(),
                ]);
            }
        } else {

            \DB::table('organization_default_settings')->insert([
                'cov_title' => $request->input('cov_title'),
                'cov_sub_title' => $request->input('sub_tit'),
                'cov_image' => $imagePath,
                'setting_type' => 'cover',
                'organization_id' => getOrganizationId(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function storeDefaultSettingForAboutUs(Request $request)
    {
        $data = [
            'cov_sub_title' => $request->input('sub_tit'),
            'organization_id' => getOrganizationId(),
            'setting_type' => 'about',
            'created_at' => now(),
            'updated_at' => now(),
        ];
        $file = $request->file('image_file');
        if ($file) {
            $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');
            $data['cov_image'] = $imagePath;
        }
        $settings = DB::table('organization_default_settings')->updateOrInsert(
            [
                'organization_id' => getOrganizationId(),
                'setting_type' => 'about',
            ],
            $data
        );

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function updateDefaultSettingForOpportunity(Request $request)
    {
        $data = [
            'organization_id' => getOrganizationId(),
            'opportunity_id' => $request->input('opportunity_id'),
            'cov_title' => $request->input('cov_title'),
            'cov_sub_title' => $request->input('cov_sub_title'),
            'setting_type' => $request->input('setting_type'),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'project_checked' => (! empty($request->input('project_checked')) && $request->input('project_checked') != 'false') ? true : false,
        ];

        if ($request->input('setting_type') == 'payment') {
            $payment_schedule = [
                'down_payment' => $request->input('down_payment'),
                'down_payment_percent' => $request->input('down_payment_percent'),
            ];
            $data['payment_schedule'] = json_encode($payment_schedule);
            $data['expiry'] = $request->input('date');
        }
        if ($request->hasFile('image_file')) {
            $file = $request->file('image_file');
            $imagePath = $file->storeAs('assets/uploads', $file->getClientOriginalName(), 'public');
            $data['cov_image'] = $imagePath;
        }

        // For gallery type, always insert new records to accumulate images
        if ($request->input('setting_type') == 'gallery') {
            DB::table('default_settings')->insert($data);
        } else {
            // For other types, use updateOrInsert to replace existing records
            DB::table('default_settings')->updateOrInsert([
                'organization_id' => getOrganizationId(),
                'opportunity_id' => $request->input('opportunity_id'),
                'setting_type' => $request->input('setting_type'),
            ], $data);
        }

        if (! empty($data['project_checked']) && $data['setting_type'] != 'gallery') {
            unset($data['opportunity_id']);
            DB::table('organization_default_settings')->updateOrInsert([
                'organization_id' => getOrganizationId(),
                'setting_type' => $data['setting_type'],
            ], $data);
        }

        return response()->json(['message' => 'Record saved successfully.']);
    }

    public function divisionSettings()
    {
        $getOrganizationId = getOrganizationId();  // Get logged-in user's account ID

        // Fetch divisions, service lines, and work types
        $divisions = Division::all();
        $serviceLines = ServiceLine::whereNull('account_id')
            ->orWhere('account_id', $getOrganizationId)
            ->get()
            ->groupBy('division_id');

        $workTypes = ServiceLineWorkType::whereNull('account_id')
            ->orWhere('account_id', $getOrganizationId)
            ->get()
            ->groupBy('division_id');

        // Fetch renewal settings for each division
        $renewalSettings = [];
        foreach ($divisions as $division) {
            $renewalSettings[$division->id] = RenewalSetting::where('account_id', $getOrganizationId)
                ->where('division_id', $division->id)
                ->first();
        }

        return view('organization.settings.division', compact(
            'divisions',
            'serviceLines',
            'workTypes',
            'renewalSettings'
        ));
    }

    public function saveRenewalSettings(Request $request)
    {
        $validated = $request->validate([
            'division_id' => 'required|exists:divisions,id',
            'renewal_month' => 'nullable|date_format:Y-m',
            'start_date' => 'nullable|date',
            'auto_activate' => 'sometimes|boolean',
            'expiry_contract' => 'required|in:0,1',
            'renewal_time' => 'nullable|date_format:H:i',
        ]);

        try {
            $accountId = Auth::id();
            $expiryContract = (bool) $validated['expiry_contract']; // Convert to boolean

            $settings = RenewalSetting::updateOrCreate(
                [
                    'division_id' => $validated['division_id'],
                    'account_id' => $accountId,
                ],
                [
                    'renewal_month' => $validated['renewal_month'],
                    'start_date' => $validated['start_date'],
                    'auto_activate' => $request->has('auto_activate'),
                    'expiry_contract' => $expiryContract,
                    'renewal_time' => $validated['renewal_time'],
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Renewal settings saved successfully',
                'division_id' => $validated['division_id'],
                'expiry_contract' => $expiryContract,
                // ... other fields ...
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save settings: '.$e->getMessage(),
            ], 500);
        }
    }

    public function addServiceLine(Request $request)
    {
        $getOrganizationId = getOrganizationId();
        try {
            $serviceLine = ServiceLine::create([
                'division_id' => $request->division_id,
                'account_id' => $getOrganizationId,
                'name' => $request->name,
            ]);

            return response()->json([
                'success' => true,
                'data' => $serviceLine,
                'message' => 'Service line added successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function addWorkType(Request $request)
    {
        $getOrganizationId = getOrganizationId();
        try {
            $workType = ServiceLineWorkType::create([
                'division_id' => $request->division_id,
                'account_id' => $getOrganizationId,
                'name' => $request->name,
            ]);

            return response()->json([
                'success' => true,
                'data' => $workType,
                'message' => 'Work type added successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function deleteServiceLine($id)
    {
        $getOrganizationId = getOrganizationId();
        try {
            $serviceLine = ServiceLine::where('id', $id)
                ->where('account_id', $getOrganizationId)
                ->firstOrFail();

            $serviceLine->delete();

            return response()->json([
                'success' => true,
                'message' => 'Service line deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function deleteWorkType($id)
    {
        $getOrganizationId = getOrganizationId();
        try {
            $workType = ServiceLineWorkType::where('id', $id)
                ->where('account_id', $getOrganizationId)
                ->firstOrFail();

            $workType->delete();

            return response()->json([
                'success' => true,
                'message' => 'Work type deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function bookOfBusiness()
    {
        return view('organization.settings.book-of-business');
    }

    public function bookOfBusinessData(Request $request)
    {
        $serviceLine = ServiceLine::where('account_id', getOrganizationId())
            ->where('name', 'Maintenance')
            ->first();

        if (! $serviceLine) {
            return DataTables::of(collect([]))->make(true);
        }

        $query = Opportunity::where('organization_id', getOrganizationId())
            ->where('service_line_id', $serviceLine->id)
            ->with([
                'account.accountowner:id,first_name,last_name',
                'propertyInformation:id,name',
                'opportunityOwner:id,first_name,last_name',
            ]);

        return DataTables::of($query)
            ->addColumn('job_no', function ($opportunity) {
                return $opportunity->job_no ?? 'N/A';
            })
            ->addColumn('opportunity_name', function ($opportunity) {
                return $opportunity->opportunity_name ?? 'N/A';
            })
            ->addColumn('property_name', function ($opportunity) {
                return $opportunity->propertyInformation->name ?? 'N/A';
            })
            ->addColumn('account_name', function ($opportunity) {
                return $opportunity->account->company_name ?? 'N/A';
            })
            ->addColumn('account_owner', function ($opportunity) {
                if ($opportunity->account && $opportunity->account->accountowner) {
                    return $opportunity->account->accountowner->first_name.' '.$opportunity->account->accountowner->last_name;
                }

                return 'N/A';
            })
            ->addColumn('maintenance_contract', function ($opportunity) {
                return $opportunity?->contract_amount ?? '$0.00';
                // return '$'.number_format(0, 2);
            })
            ->addColumn('operation_manager', function ($opportunity) {
                if ($opportunity->opportunityOwner) {
                    return $opportunity->opportunityOwner->first_name.' '.$opportunity->opportunityOwner->last_name;
                }

                return 'N/A';
            })
            ->addColumn('contract_start_date', function ($opportunity) {
                // For now, use created_at as start date - you can add proper contract dates later
                return $opportunity?->contract_start_date ? $opportunity->contract_start_date->format('m/d/Y') : '---';
            })
            ->addColumn('contract_end_date', function ($opportunity) {
                return $opportunity->contract_end_date ? $opportunity->contract_end_date->format('m/d/Y') : '---';
            })
//            ->addColumn('status', function ($opportunity) {
//                $statusMap = [
//                    1 => 'Open',
//                    2 => 'Estimating',
//                    3 => 'Proposed',
//                    4 => 'Closed Won',
//                    5 => 'Closed Lost',
//                    6 => 'Completed',
//                ];
//
//                return $statusMap[$opportunity->status] ?? '---';
//            })
            ->addColumn('action', function ($opportunity) {
                return '
                    <div class="dropdown mx-auto w-fit">
                        <div class="cursor-pointer" id="dropdown'.$opportunity->id.'" data-toggle="dropdown" aria-expanded="false">
                            <img height="24px" width="24px" src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                        </div>
                        <ul class="dropdown-menu" aria-labelledby="dropdown'.$opportunity->id.'">
                            <li>
                                <a class="dropdown-item changeAccountOwnerBtn" href="javascript:void(0)" data-opportunity-id="'.$opportunity->id.'">Change Account Owner</a>
                            </li>
                            <li>
                                <a href="javascript:void(0)" class="dropdown-item changeOPManagerBtn" data-opportunity-id="'.$opportunity->id.'">Change Operation Manager</a>
                            </li>
                        </ul>
                    </div>
                ';
            })
            ->rawColumns(['action'])
            ->make(true);
    }

    public function exportBookOfBusiness()
    {
        $serviceLine = ServiceLine::where('account_id', getOrganizationId())
            ->where('name', 'Maintenance')
            ->first();

        if (! $serviceLine) {
            return response()->json(['error' => 'No maintenance service line found.'], 404);
        }

        $bookOfBusiness = Opportunity::where('organization_id', getOrganizationId())
            ->where('service_line_id', $serviceLine->id)
            ->with([
                'account.accountowner',
                'propertyInformation',
                'opportunityOwner',
            ])
            ->get();

        if ($bookOfBusiness->isEmpty()) {
            return response()->json(['error' => 'No data available for export.'], 404);
        }

        $fileName = 'book-of-business-'.date('Y-m-d').'.xlsx';

        try {
            return Excel::download(new BookOfBusinessExport($bookOfBusiness), $fileName, \Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            \Log::error('Book of Business Export Error: '.$e->getMessage());
            \Log::error('Stack trace: '.$e->getTraceAsString());

            return response()->json([
                'error' => 'Failed to export data: '.$e->getMessage(),
            ], 500);
        }
    }

    public function updateOperationManager(Request $request)
    {
        $request->validate([
            'opportunity_id' => 'required|exists:opportunities,id',
            'operation_manager_id' => 'required|exists:users,id',
        ]);

        Opportunity::where('id', $request->opportunity_id)->update([
            'opportunity_owner_id' => $request->operation_manager_id,
        ]);

        return response()->json(['message' => 'Operation manager updated successfully.']);
    }

    public function updateAccountOwner(Request $request)
    {
        $request->validate([
            'opportunity_id' => 'required|exists:opportunities,id',
            'account_owner_id' => 'required|exists:users,id',
        ]);
        $opportunity = Opportunity::where('id', $request->opportunity_id)->first();
        $oldAccountId = $opportunity->account_id;
//        $opportunity->update([
//            'account_id' => $request->account_owner_id,
//        ]);
        Account::where('id', $oldAccountId)->update([
            'account_owner' => $request->account_owner_id,
        ]);

        return response()->json(['message' => 'Account owner updated successfully.']);
    }
}
