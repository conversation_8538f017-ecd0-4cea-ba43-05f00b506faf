<?php

namespace App\Http\Controllers;

use App\Imports\BookOfBusinessImport;
use App\Models\User;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class CommonController extends Controller
{
    public function getOperationManager()
    {
        return User::where('parent_id', getOrganizationId())
            ->whereRelation('roles', 'name', 'operation manager')
            ->select(['id', 'first_name', 'last_name','email'])->get();
    }

    public function getAccountOwner()
    {
        return User::where('parent_id', getOrganizationId())
            ->whereRelation('roles', 'name', 'salesman')
            ->select(['id', 'first_name', 'last_name','email'])->get();
    }

    public function importBookOfBusiness(Request $request)
    {
        // Handle both single and multiple file uploads
        if ($request->hasFile('import_files')) {
            // Multiple files
            $request->validate([
                'import_files' => 'required|array|max:10', // Max 10 files
                'import_files.*' => 'required|file|mimes:xlsx,xls|max:10240', // 10MB max per file
            ]);
            $files = $request->file('import_files');
        } else {
            // Single file (backward compatibility)
            $request->validate([
                'import_file' => 'required|file|mimes:xlsx,xls|max:10240', // 10MB max
            ]);
            $files = [$request->file('import_file')];
        }

        $totalSuccessCount = 0;
        $totalErrorCount = 0;
        $allErrors = [];
        $processedFiles = [];

        try {
            foreach ($files as $index => $file) {
                $fileName = $file->getClientOriginalName();

                try {
                    $import = new BookOfBusinessImport();
                    Excel::import($import, $file);

                    $fileResult = [
                        'file_name' => $fileName,
                        'success_count' => $import->getSuccessCount(),
                        'error_count' => $import->getErrorCount(),
                        'errors' => $import->getErrors(),
                    ];

                    $totalSuccessCount += $import->getSuccessCount();
                    $totalErrorCount += $import->getErrorCount();

                    // Add file-specific errors to the all errors array
                    if ($import->hasErrors()) {
                        foreach ($import->getErrors() as $error) {
                            $allErrors[] = "[{$fileName}] {$error}";
                        }
                    }

                    $processedFiles[] = $fileResult;

                } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
                    $failures = $e->failures();
                    $fileErrors = [];
                    foreach ($failures as $failure) {
                        $fileErrors[] = "Row {$failure->row()}: " . implode(', ', $failure->errors());
                    }

                    foreach ($fileErrors as $error) {
                        $allErrors[] = "[{$fileName}] {$error}";
                    }

                    $totalErrorCount++;
                    $processedFiles[] = [
                        'file_name' => $fileName,
                        'success_count' => 0,
                        'error_count' => 1,
                        'errors' => $fileErrors,
                    ];

                } catch (\Exception $e) {
                    $error = "File processing failed: " . $e->getMessage();
                    $allErrors[] = "[{$fileName}] {$error}";
                    $totalErrorCount++;
                    $processedFiles[] = [
                        'file_name' => $fileName,
                        'success_count' => 0,
                        'error_count' => 1,
                        'errors' => [$error],
                    ];
                }
            }

            // Prepare response
            $fileCount = count($files);
            $response = [
                'success' => true,
                'total_files' => $fileCount,
                'success_count' => $totalSuccessCount,
                'error_count' => $totalErrorCount,
                'processed_files' => $processedFiles,
            ];

            if ($totalErrorCount > 0) {
                $response['errors'] = $allErrors;
                if ($totalSuccessCount > 0) {
                    $response['message'] = "Import completed with some issues. {$totalSuccessCount} records imported from {$fileCount} file(s), {$totalErrorCount} errors encountered.";
                } else {
                    $response['message'] = "Import failed for all files. {$totalErrorCount} errors encountered.";
                    $response['success'] = false;
                }
            } else {
                $response['message'] = "Import completed successfully! {$totalSuccessCount} records imported from {$fileCount} file(s).";
            }

            return response()->json($response, HTTP_OK);

        } catch (\Exception $e) {
            dd($e);
            return response()->json([
                'success' => false,
                'message' => 'Import process failed: ' . $e->getMessage(),
                'total_files' => count($files),
                'success_count' => $totalSuccessCount,
                'error_count' => $totalErrorCount + 1,
                'processed_files' => $processedFiles,
            ], HTTP_BAD_REQUEST);
        }
    }
}
