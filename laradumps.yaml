app:
  dispatcher: curl
  primary_host: 127.0.0.1
  secondary_host: host.docker.internal
  port: 9191
  workdir: /var/www/html/
  project_path: /home/<USER>/Projects/onsite/
  wsl_config: wsl+Ubuntu/
config:
  sleep: 0
  macos_auto_launch: false
observers:
  auto_invoke_app: false
  enabled_in_testing: false
  dump: true
  original_dump: true
  queries: true
  slow_queries: true
  mail: true
  logs: true
  http: true
  jobs: true
  commands: true
  scheduled_commands: true
  gate: true
  cache: true
xdebug:
  client_host: 0.0.0.0
  client_port: 9003
code_snippet:
  above: 9
  below: 3
logs:
  info: true
  warning: true
  emergency: true
  alert: false
  debug: true
  error: true
  critical: true
  notice: true
  vendor: true
  deprecated_message: true
slow_queries:
  threshold_in_ms: 500
extra:
  context: false
queries:
  explain: false
